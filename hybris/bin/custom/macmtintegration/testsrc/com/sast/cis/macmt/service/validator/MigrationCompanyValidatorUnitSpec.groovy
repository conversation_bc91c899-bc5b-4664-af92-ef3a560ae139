package com.sast.cis.macmt.service.validator

import com.sast.cis.core.model.IoTCompanyModel
import com.sast.cis.core.model.IoTCustomerModel
import de.hybris.bootstrap.annotations.UnitTest
import de.hybris.platform.testframework.JUnitPlatformSpecification
import spock.lang.Unroll

import static com.sast.cis.core.enums.CompanyApprovalStatus.*
import static com.sast.cis.core.enums.CompanyOperationalStage.*
import static com.sast.cis.macmt.exception.ValidationErrorCode.*
import static java.util.Set.of

@UnitTest
class MigrationCompanyValidatorUnitSpec extends JUnitPlatformSpecification {

    private final migrationCompanyValidator = new MigrationCompanyValidator()

    private IoTCompanyModel company = Mock()
    private IoTCustomerModel employee = Mock()

    void setup() {
        company.getAaImported() >> true
        company.getOperationalStage() >> OPERATIONAL
        company.getEmployees() >> of(employee)
        company.getBpmdId() >> "bpmdId"
        company.getApprovalStatus() >> APPROVED_COMMERCIAL
    }

    void 'given company when validate then return no errors'() {
        when:
        def errors = migrationCompanyValidator.validate(company)

        then:
        errors.isEmpty()
    }

    void 'given company is not imported when validate then return error'() {
        when:
        def errors = migrationCompanyValidator.validate(company)

        then:
        company.getAaImported() >> false
        errors == of(NOT_READY_FOR_MIGRATION.name())

    }

    @Unroll
    void 'given company in unexpected operational stage #operationalStage  when validate then return error'() {
        when:
        def errors = migrationCompanyValidator.validate(company)

        then:
        company.getOperationalStage() >> operationalStage
        errors == of(COMPANY_IN_UNSUPPORTED_OPERATIONAL_STAGE.name())

        where:
        operationalStage << [RESTRICTED, AWAITING_DETAILS]
    }

    void 'given company in unexpected approval status UNAPPROVED when validate then return error'() {
        when:
        def errors = migrationCompanyValidator.validate(company)

        then:
        company.getApprovalStatus() >> UNAPPROVED
        errors == of(COMPANY_IS_UNAPPROVED.name())
    }

    void 'given company has no employee accounts when validate then return error'() {
        when:
        def errors = migrationCompanyValidator.validate(company)

        then:
        company.getEmployees() >> []
        errors == of(COMPANY_HAS_NO_EMPLOYEE_ACCOUNTS.name())
    }

    void 'given company with no bpmd id when validate then return error'() {
        when:
        def errors = migrationCompanyValidator.validate(company)

        then:
        company.getBpmdId() >> ''
        errors == of(COMPANY_HAS_NO_BPMD_ID.name())
    }

    void 'given company with multiple validation issues when validate then return all errors'() {
        when:
        def errors = migrationCompanyValidator.validate(company)

        then:
        company.getOperationalStage() >> RESTRICTED
        company.getEmployees() >> []
        errors == of(COMPANY_IN_UNSUPPORTED_OPERATIONAL_STAGE.name(), COMPANY_HAS_NO_EMPLOYEE_ACCOUNTS.name())
    }

    @Unroll
    void 'given company with approval status #approvalStatus when validate then return no errors'() {
        when:
        def errors = migrationCompanyValidator.validate(company)

        then:
        company.getApprovalStatus() >> approvalStatus
        errors.isEmpty()

        where:
        approvalStatus << of(AWAITING_ACTIVATION, APPROVED_NON_COMMERCIAL, APPROVED_COMMERCIAL)
    }
}
