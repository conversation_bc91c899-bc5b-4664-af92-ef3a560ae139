package com.sast.cis.macmt.service.validator;

import com.sast.cis.aa.core.migration.service.MigrationAppLicenseService;
import com.sast.cis.aa.core.model.MigrationContractGroupModel;
import com.sast.cis.aa.core.model.MigrationContractModel;
import com.sast.cis.aa.core.model.MigrationOrderEntryDraftModel;
import com.sast.cis.core.model.AppLicenseModel;
import de.hybris.bootstrap.annotations.UnitTest;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Date;
import java.util.Set;

import static com.sast.cis.macmt.exception.ValidationErrorCode.INACTIVE_PRODUCT_CONTRACTS_END_DATE_MISMATCH;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
@UnitTest
public class MigrationOrderDraftEntryValidatorUnitTest {

    @Mock
    private MigrationAppLicenseService migrationAppLicenseService;

    @InjectMocks
    private MigrationOrderDraftEntryValidator validator;

    @Mock
    private MigrationOrderEntryDraftModel entryDraft;

    @Mock
    private AppLicenseModel appLicense;

    @Mock
    private MigrationContractGroupModel contractGroup;

    @Mock
    private MigrationContractModel migrationContract;

    private Date endDate;

    @Before
    public void setUp() {
        endDate = new Date();
        when(migrationAppLicenseService.isAppLicenseActive(appLicense)).thenReturn(false);
        when(entryDraft.getAppLicense()).thenReturn(appLicense);
        when(entryDraft.getContractGroups()).thenReturn(Set.of(contractGroup));
        when(contractGroup.getMigrationContracts()).thenReturn(Set.of(migrationContract));
        when(migrationContract.getEndDate()).thenReturn(endDate);
    }

    @Test
    public void shouldReturnEmptySet_WhenAppLicenseIsActive() {
        when(migrationAppLicenseService.isAppLicenseActive(appLicense)).thenReturn(true);

        Set<String> validationResult = validator.validateInactiveProductContractEndDate(entryDraft);

        assertThat(validationResult).isEmpty();
    }

    @Test
    public void shouldReturnValidationError_WhenContractsWithMultipleDistinctEndDatesExist() {
        MigrationContractModel anotherContract = mock(MigrationContractModel.class);
        when(anotherContract.getEndDate()).thenReturn(new Date(endDate.getTime() + 1000));

        when(contractGroup.getMigrationContracts()).thenReturn(Set.of(migrationContract, anotherContract));

        Set<String> validationResult = validator.validateInactiveProductContractEndDate(entryDraft);

        assertThat(validationResult).containsExactly(INACTIVE_PRODUCT_CONTRACTS_END_DATE_MISMATCH.name());
    }

    @Test
    public void shouldReturnEmptySet_WhenContractsEndsOnTheSameDay() {

        Set<String> validationResult = validator.validateInactiveProductContractEndDate(entryDraft);

        assertThat(validationResult).isEmpty();
    }
}
