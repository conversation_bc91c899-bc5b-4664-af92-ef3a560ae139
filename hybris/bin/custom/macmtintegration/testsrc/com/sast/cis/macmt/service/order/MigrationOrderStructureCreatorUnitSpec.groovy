package com.sast.cis.macmt.service.order

import com.sast.cis.aa.core.migration.service.MigrationAppLicenseService
import com.sast.cis.aa.core.model.ContractMigrationProcessModel
import com.sast.cis.aa.core.model.MigrationContractModel
import com.sast.cis.core.model.AppLicenseModel
import com.sast.cis.core.model.IoTCompanyModel
import com.sast.cis.core.service.company.IotCompanyService
import com.sast.cis.macmt.config.ContractMigrationConfig
import com.sast.cis.macmt.service.order.data.MigrationOrderEntryDraftData
import com.sast.cis.macmt.service.order.data.UpmContractGroupData
import de.hybris.bootstrap.annotations.UnitTest
import de.hybris.platform.core.model.c2l.CountryModel
import de.hybris.platform.testframework.JUnitPlatformSpecification

@UnitTest
class MigrationOrderStructureCreatorUnitSpec extends JUnitPlatformSpecification {

    private MigrationAppLicenseService migrationAppLicenseService = Mock()
    private IotCompanyService iotCompanyService = Mock()
    private ContractMigrationConfig contractMigrationConfig = Mock()

    private MigrationOrderStructureCreator migrationOrderDraftCreator

    private ContractMigrationProcessModel migrationProcess = Mock()

    private MigrationContractModel masterContract_1 = Mock()
    private MigrationContractModel masterContract_2 = Mock()
    private MigrationContractModel masterContract_3 = Mock()
    private MigrationContractModel masterMultiContract_1 = Mock()
    private MigrationContractModel masterMultiContract_2 = Mock()
    private MigrationContractModel masterMultiContract_3 = Mock()
    private MigrationContractModel diagnosticContract_1 = Mock()
    private MigrationContractModel diagnosticMultiContract_1 = Mock()

    private AppLicenseModel masterAppLicense = Mock()
    private AppLicenseModel masterMultiAppLicense = Mock()
    private AppLicenseModel diagnosticAppLicense = Mock()
    private AppLicenseModel diagnosticMultiAppLicense = Mock()

    private IoTCompanyModel ownerCompany = Mock()
    private CountryModel country = Mock()

    private final String masterMaterialId = "1987P12910"
    private final String masterMultiMaterialId = "1987P12917"
    private final String masterMultiBundleIdentifierId = "master-001"
    private final String diagnosticMaterialId = "1987P12820"
    private final String diagnosticMultiMaterialId = "1987P12824"
    private final String diagnosticMultiBundleIdentifierId = "diagnostic-001"
    private final String licenseType = "ZKFO"

    private final String distributor = "DIST"
    private final String ownerCompanyId = "31113111"

    private final int maxOrderSize = 3

    void setup() {
        migrationOrderDraftCreator = new MigrationOrderStructureCreator(
                migrationAppLicenseService,
                iotCompanyService,
                contractMigrationConfig
        )

        migrationProcess.getOwnerCompanyId() >> ownerCompanyId

        ownerCompany.getCountry() >> country
        iotCompanyService.getCompanyByAaExternalCompanyIdOrThrow(ownerCompanyId) >> ownerCompany

        migrationAppLicenseService.findAppLicenseOrThrow(masterMaterialId, country) >> masterAppLicense
        migrationAppLicenseService.findAppLicenseOrThrow(masterMultiMaterialId, country) >> masterMultiAppLicense
        migrationAppLicenseService.findAppLicenseOrThrow(diagnosticMaterialId, country) >> diagnosticAppLicense
        migrationAppLicenseService.findAppLicenseOrThrow(diagnosticMultiMaterialId, country) >> diagnosticMultiAppLicense

        migrationAppLicenseService.isAppLicenseActive(masterAppLicense) >> true
        migrationAppLicenseService.isAppLicenseActive(masterMultiAppLicense) >> true
        migrationAppLicenseService.isAppLicenseActive(diagnosticAppLicense) >> true
        migrationAppLicenseService.isAppLicenseActive(diagnosticMultiAppLicense) >> true

        contractMigrationConfig.getMaxOrderSize() >> maxOrderSize

        masterContract_1.getMaterialId() >> masterMaterialId
        masterContract_1.getLicenseType() >> licenseType
        masterContract_1.getDistributor() >> distributor

        masterContract_2.getMaterialId() >> masterMaterialId
        masterContract_2.getLicenseType() >> licenseType
        masterContract_2.getDistributor() >> distributor

        masterContract_3.getMaterialId() >> masterMaterialId
        masterContract_3.getLicenseType() >> licenseType
        masterContract_3.getDistributor() >> distributor

        masterMultiContract_1.getMaterialId() >> masterMultiMaterialId
        masterMultiContract_1.getLicenseType() >> licenseType
        masterMultiContract_1.getDistributor() >> distributor
        masterMultiContract_1.getUniqueBundleIdentifier() >> masterMultiBundleIdentifierId

        masterMultiContract_2.getMaterialId() >> masterMultiMaterialId
        masterMultiContract_2.getLicenseType() >> licenseType
        masterMultiContract_2.getDistributor() >> distributor
        masterMultiContract_2.getUniqueBundleIdentifier() >> masterMultiBundleIdentifierId

        masterMultiContract_3.getMaterialId() >> masterMultiMaterialId
        masterMultiContract_3.getLicenseType() >> licenseType
        masterMultiContract_3.getDistributor() >> distributor
        masterMultiContract_3.getUniqueBundleIdentifier() >> masterMultiBundleIdentifierId

        diagnosticContract_1.getMaterialId() >> diagnosticMaterialId
        diagnosticContract_1.getLicenseType() >> licenseType
        diagnosticContract_1.getDistributor() >> distributor

        diagnosticMultiContract_1.getMaterialId() >> diagnosticMultiMaterialId
        diagnosticMultiContract_1.getLicenseType() >> licenseType
        diagnosticMultiContract_1.getDistributor() >> distributor
        diagnosticMultiContract_1.getUniqueBundleIdentifier() >> diagnosticMultiBundleIdentifierId
    }

    def "given no contracts when create then return empty"() {
        given:
        migrationProcess.getMigrationContracts() >> []

        when:
        def result = migrationOrderDraftCreator.createFor(migrationProcess)

        then:
        result.isEmpty()
    }

    def "given contracts when create then return order with contracts"() {
        when:
        def result = migrationOrderDraftCreator.createFor(migrationProcess)

        then:
        migrationProcess.getMigrationContracts() >> [masterContract_1, masterContract_2, masterContract_3]

        expect:
        result.size() == 1
        def migrationOrder = result.first()
        migrationOrder.distributor() == distributor
        migrationOrder.licenseType() == licenseType
        migrationOrder.migrationOrderEntries().contains(
                new MigrationOrderEntryDraftData(masterMaterialId, 3,
                        [
                                new UpmContractGroupData("", [masterContract_1]),
                                new UpmContractGroupData("", [masterContract_2]),
                                new UpmContractGroupData("", [masterContract_3])
                        ]
                )
        )
    }

    def "given contracts with different license types when create then return one order for each license type"() {
        given:
        def masterContractLicenseType_1 = "ZKFO1"
        def masterContractLicenseType_2 = "ZKFO2"

        when:
        def result = migrationOrderDraftCreator.createFor(migrationProcess)

        then:
        masterContract_1.getLicenseType() >> masterContractLicenseType_1
        masterContract_2.getLicenseType() >> masterContractLicenseType_2
        migrationProcess.getMigrationContracts() >> [masterContract_1, masterContract_2]

        expect:
        result.size() == 2
        result.count { it.licenseType() == masterContractLicenseType_1 } == 1
        result.count { it.licenseType() == masterContractLicenseType_2 } == 1
    }

    def "given contracts with different distributors when create then return one order for each distributor"() {
        given:
        def masterContractDistributor_1 = "DIST1"
        def masterContractDistributor_2 = "DIST2"

        when:
        def result = migrationOrderDraftCreator.createFor(migrationProcess)

        then:
        masterContract_1.getDistributor() >> masterContractDistributor_1
        masterMultiContract_1.getDistributor() >> masterContractDistributor_2
        migrationProcess.getMigrationContracts() >> [masterContract_1, masterMultiContract_1]

        expect:
        result.size() == 2
        result.count { it.distributor() == masterContractDistributor_1 } == 1
        result.count { it.distributor() == masterContractDistributor_2 } == 1
        def dist1Order = result.find { it.distributor() == masterContractDistributor_1 }
        dist1Order.migrationOrderEntries().size() == 1
        dist1Order.migrationOrderEntries().contains(
                new MigrationOrderEntryDraftData(masterMaterialId, 1,
                        [
                                new UpmContractGroupData("", [masterContract_1])
                        ]
                )
        )
        def dist2Order = result.find { it.distributor() == masterContractDistributor_2 }
        dist2Order.migrationOrderEntries().size() == 1
        dist2Order.migrationOrderEntries().contains(
                new MigrationOrderEntryDraftData(masterMultiMaterialId, 1,
                        [
                                new UpmContractGroupData(masterMultiBundleIdentifierId, [masterMultiContract_1])
                        ]
                )
        )
    }

    def "given contracts with no distributor when create then return one order with no distributor assigned"() {
        when:
        def result = migrationOrderDraftCreator.createFor(migrationProcess)

        then:
        masterContract_1.getDistributor() >> null
        masterMultiContract_1.getDistributor() >> null
        migrationProcess.getMigrationContracts() >> [masterContract_1, masterMultiContract_1]

        expect:
        result.size() == 1
        def migrationOrder = result.first()
        migrationOrder.licenseType() == licenseType
        !migrationOrder.distributor()
        !migrationOrder.managedAccount()
        migrationOrder.migrationOrderEntries() == List.of(
                new MigrationOrderEntryDraftData(masterMaterialId, 1,
                        [
                                new UpmContractGroupData("", [masterContract_1])
                        ]
                ),
                new MigrationOrderEntryDraftData(masterMultiMaterialId, 1,
                        [
                                new UpmContractGroupData(masterMultiBundleIdentifierId, [masterMultiContract_1])
                        ]
                )
        )
    }

    def "given process contains contracts with distributors and contracts without when create then split into two orders"() {
        when:
        def result = migrationOrderDraftCreator.createFor(migrationProcess)

        then:
        masterContract_1.getDistributor() >> distributor
        masterMultiContract_1.getDistributor() >> null
        migrationProcess.getMigrationContracts() >> [masterContract_1, masterMultiContract_1]

        expect:
        result.size() == 2
        result.count { it.distributor() == distributor } == 1
        result.count { !it.distributor() } == 1
        def distributorOrder = result.find { it.distributor() == distributor }
        distributorOrder.migrationOrderEntries().size() == 1
        distributorOrder.migrationOrderEntries().contains(
                new MigrationOrderEntryDraftData(masterMaterialId, 1,
                        [
                                new UpmContractGroupData("", [masterContract_1])
                        ]
                )
        )
        def orderWithNoDistributor = result.find { !it.distributor() }
        orderWithNoDistributor.migrationOrderEntries().size() == 1
        orderWithNoDistributor.migrationOrderEntries().contains(
                new MigrationOrderEntryDraftData(masterMultiMaterialId, 1,
                        [
                                new UpmContractGroupData(masterMultiBundleIdentifierId, [masterMultiContract_1])
                        ]
                )
        )
    }

    def "given process contains contracts with managed accounts and contracts without when create then split into two orders"() {
        given:
        def managedAccount = "MA1"

        when:
        def result = migrationOrderDraftCreator.createFor(migrationProcess)

        then:
        masterContract_1.getDistributor() >> null
        masterContract_1.getManagedAccount() >> managedAccount
        masterMultiContract_1.getDistributor() >> null
        masterMultiContract_1.getManagedAccount() >> null
        migrationProcess.getMigrationContracts() >> [masterContract_1, masterMultiContract_1]

        expect:
        result.size() == 2
        result.count { it.managedAccount() == managedAccount } == 1
        result.count { !it.managedAccount() } == 1
        def managedAccountOrder = result.find { it.managedAccount() == managedAccount }
        managedAccountOrder.migrationOrderEntries().size() == 1
        managedAccountOrder.migrationOrderEntries().contains(
                new MigrationOrderEntryDraftData(masterMaterialId, 1,
                        [
                                new UpmContractGroupData("", [masterContract_1])
                        ]
                )
        )
        def orderWithNoManagedAccount = result.find { !it.managedAccount() }
        orderWithNoManagedAccount.migrationOrderEntries().size() == 1
        orderWithNoManagedAccount.migrationOrderEntries().contains(
                new MigrationOrderEntryDraftData(masterMultiMaterialId, 1,
                        [
                                new UpmContractGroupData(masterMultiBundleIdentifierId, [masterMultiContract_1])
                        ]
                )
        )
    }

    def "given contracts for different products when create then return order with one entry for each product"() {
        when:
        def result = migrationOrderDraftCreator.createFor(migrationProcess)

        then:
        migrationProcess.getMigrationContracts() >> [masterContract_1, diagnosticContract_1]

        expect:
        result.size() == 1
        result.first().migrationOrderEntries().containsAll(
                new MigrationOrderEntryDraftData(masterMaterialId, 1,
                        [
                                new UpmContractGroupData("", [masterContract_1])
                        ]
                ),
                new MigrationOrderEntryDraftData(diagnosticMaterialId, 1,
                        [
                                new UpmContractGroupData("", [diagnosticContract_1])
                        ]
                )
        )
    }

    def "given contracts with same bundle id when create then return order with one group of contracts and quantity"() {
        when:
        def result = migrationOrderDraftCreator.createFor(migrationProcess)

        then:
        migrationProcess.getMigrationContracts() >> [masterMultiContract_1, masterMultiContract_2, masterMultiContract_3]

        expect:
        result.size() == 1
        result.first().migrationOrderEntries().containsAll(
                new MigrationOrderEntryDraftData(masterMultiMaterialId, 1,
                        [
                                new UpmContractGroupData(masterMultiBundleIdentifierId,
                                        [
                                                masterMultiContract_1,
                                                masterMultiContract_2,
                                                masterMultiContract_3
                                        ]
                                )
                        ]
                )
        )
    }

    def "given contracts with different bundle ids when create then return order with multiple groups of contracts and quantities"() {
        given:
        def masterMultiBundleIdentifierId_1 = "master-001"
        def masterMultiBundleIdentifierId_2 = "master-002"
        def masterMultiBundleIdentifierId_3 = "master-003"

        when:
        def result = migrationOrderDraftCreator.createFor(migrationProcess)

        then:
        masterMultiContract_1.getUniqueBundleIdentifier() >> masterMultiBundleIdentifierId_1
        masterMultiContract_2.getUniqueBundleIdentifier() >> masterMultiBundleIdentifierId_2
        masterMultiContract_3.getUniqueBundleIdentifier() >> masterMultiBundleIdentifierId_3
        migrationProcess.getMigrationContracts() >> [masterMultiContract_1, masterMultiContract_2, masterMultiContract_3]

        expect:
        result.size() == 1
        result.first().migrationOrderEntries().containsAll(
                new MigrationOrderEntryDraftData(masterMultiMaterialId, 3,
                        [
                                new UpmContractGroupData(masterMultiBundleIdentifierId_1, [masterMultiContract_1]),
                                new UpmContractGroupData(masterMultiBundleIdentifierId_2, [masterMultiContract_2]),
                                new UpmContractGroupData(masterMultiBundleIdentifierId_3, [masterMultiContract_3])
                        ]
                )
        )
    }

    def "given contracts with different bundle products when create then return order with multiple entries"() {
        when:
        def result = migrationOrderDraftCreator.createFor(migrationProcess)

        then:
        migrationProcess.getMigrationContracts() >> [masterMultiContract_1, diagnosticMultiContract_1]

        expect:
        result.size() == 1
        result.first().migrationOrderEntries().containsAll(
                new MigrationOrderEntryDraftData(masterMultiMaterialId, 1,
                        [
                                new UpmContractGroupData(masterMultiBundleIdentifierId, [masterMultiContract_1])
                        ]
                ),
                new MigrationOrderEntryDraftData(diagnosticMultiMaterialId, 1,
                        [
                                new UpmContractGroupData(diagnosticMultiBundleIdentifierId, [diagnosticMultiContract_1])
                        ]
                )
        )
    }

    def "given contracts with simple and bundle products when create then return order with multiple entries"() {
        when:
        def result = migrationOrderDraftCreator.createFor(migrationProcess)

        then:
        migrationProcess.getMigrationContracts() >> [masterContract_1, masterMultiContract_1]

        expect:
        result.size() == 1
        result.first().migrationOrderEntries().containsAll(
                new MigrationOrderEntryDraftData(masterMaterialId, 1,
                        [
                                new UpmContractGroupData("", [masterContract_1])
                        ]
                ),
                new MigrationOrderEntryDraftData(masterMultiMaterialId, 1,
                        [
                                new UpmContractGroupData(masterMultiBundleIdentifierId, [masterMultiContract_1])
                        ]
                )
        )
    }

    def "given max order size is exceeded for different product when create then return multiple orders"() {
        when:
        def result = migrationOrderDraftCreator.createFor(migrationProcess)

        then:
        migrationProcess.getMigrationContracts() >> [masterContract_1, masterMultiContract_1, diagnosticContract_1, diagnosticMultiContract_1]

        expect:
        result.size() == 2

        result.each { order ->
            order.migrationOrderEntries().sum { entry -> entry.quantity() } <= maxOrderSize
        }

        def orderEntries = result.collectMany { order -> order.migrationOrderEntries() }
        orderEntries.containsAll(
                new MigrationOrderEntryDraftData(masterMaterialId, 1,
                        [
                                new UpmContractGroupData("", [masterContract_1])
                        ]
                ),
                new MigrationOrderEntryDraftData(masterMultiMaterialId, 1,
                        [
                                new UpmContractGroupData(masterMultiBundleIdentifierId, [masterMultiContract_1])
                        ]
                ),
                new MigrationOrderEntryDraftData(diagnosticMaterialId, 1,
                        [
                                new UpmContractGroupData("", [diagnosticContract_1])
                        ]
                ),
                new MigrationOrderEntryDraftData(diagnosticMultiMaterialId, 1,
                        [
                                new UpmContractGroupData(diagnosticMultiBundleIdentifierId, [diagnosticMultiContract_1])
                        ]
                )
        )
    }

    def "given max order size is exceeded for same product when create then return multiple orders"() {
        given:
        def masterContract_4 = Mock(MigrationContractModel)
        masterContract_4.getMaterialId() >> masterMaterialId
        masterContract_4.getLicenseType() >> licenseType
        masterContract_4.getDistributor() >> distributor

        when:
        def result = migrationOrderDraftCreator.createFor(migrationProcess)

        then:
        migrationProcess.getMigrationContracts() >> [masterContract_1, masterContract_2, masterContract_3, masterContract_4]

        expect:
        result.size() == 2

        result.each { order ->
            order.migrationOrderEntries().sum { entry -> entry.quantity() } <= maxOrderSize
        }

        def orderEntries = result.collectMany { order -> order.migrationOrderEntries() }
        orderEntries.count { it.quantity() == 1 } == 1
        orderEntries.count { it.quantity() == 3 } == 1
    }

    def "given contracts for active products when create then return order marked for active products migration"() {
        when:
        def result = migrationOrderDraftCreator.createFor(migrationProcess)

        then:
        migrationProcess.getMigrationContracts() >> [masterContract_1, masterContract_2, masterContract_3]

        expect:
        result.size() == 1
        def migrationOrder = result.first()
        !migrationOrder.inactiveProductsMigration()
        migrationOrder.migrationOrderEntries().contains(
                new MigrationOrderEntryDraftData(masterMaterialId, 3,
                        [
                                new UpmContractGroupData("", [masterContract_1]),
                                new UpmContractGroupData("", [masterContract_2]),
                                new UpmContractGroupData("", [masterContract_3])
                        ]
                )
        )
    }

    def "given contracts for inactive products when create then return order marked for discontinued products migration"() {
        when:
        def result = migrationOrderDraftCreator.createFor(migrationProcess)

        then:
        migrationProcess.getMigrationContracts() >> [masterContract_1, masterContract_2, masterContract_3]
        migrationAppLicenseService.isAppLicenseActive(masterAppLicense) >> false

        expect:
        result.size() == 1
        def migrationOrder = result.first()
        migrationOrder.inactiveProductsMigration()
        migrationOrder.migrationOrderEntries().contains(
                new MigrationOrderEntryDraftData(masterMaterialId, 3,
                        [
                                new UpmContractGroupData("", [masterContract_1]),
                                new UpmContractGroupData("", [masterContract_2]),
                                new UpmContractGroupData("", [masterContract_3])
                        ]
                )
        )
    }


    def "given process contains contracts for active and inactive products when create then split to multiple orders"() {
        when:
        def result = migrationOrderDraftCreator.createFor(migrationProcess)

        then:
        migrationProcess.getMigrationContracts() >> [masterContract_1, masterMultiContract_1]
        migrationAppLicenseService.isAppLicenseActive(masterAppLicense) >> true
        migrationAppLicenseService.isAppLicenseActive(masterMultiAppLicense) >> false

        expect:
        result.count { it.inactiveProductsMigration() } == 1
        result.count { !it.inactiveProductsMigration() } == 1
        def activeMigrationOrder = result.find { !it.inactiveProductsMigration() }
        activeMigrationOrder.migrationOrderEntries().size() == 1
        activeMigrationOrder.migrationOrderEntries().contains(
                new MigrationOrderEntryDraftData(masterMaterialId, 1,
                        [
                                new UpmContractGroupData("", [masterContract_1])
                        ]
                )
        )
        def inactiveMigrationOrder = result.find { it.inactiveProductsMigration() }
        inactiveMigrationOrder.migrationOrderEntries().size() == 1
        inactiveMigrationOrder.migrationOrderEntries().contains(
                new MigrationOrderEntryDraftData(masterMultiMaterialId, 1,
                        [
                                new UpmContractGroupData(masterMultiBundleIdentifierId, [masterMultiContract_1])
                        ]
                )
        )
    }
}
