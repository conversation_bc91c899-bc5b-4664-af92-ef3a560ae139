package com.sast.cis.macmt.notification.converter;

import com.sast.cis.aa.core.model.MigrationContractModel;
import com.sast.cis.core.util.Base58UUIDCodeGenerator;
import com.sast.cis.macmt.notification.dto.MigrationContractDto;
import de.hybris.platform.servicelayer.model.ModelService;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
public class MigrationContractDtoConverter {

    private final ModelService modelService;

    public Set<MigrationContractModel> convertAll(final @NonNull List<MigrationContractDto> migrationContracts) {
        return migrationContracts.stream().map(this::convert).collect(Collectors.toSet());
    }

    public MigrationContractModel convert(final @NonNull MigrationContractDto migrationContractDto) {
        final String code = Base58UUIDCodeGenerator.generateCode("MigrationContract");
        final Long cmtId = migrationContractDto.id();
        final String uniqueBundleIdentifier = migrationContractDto.uniqueBundleIdentifier();
        final String upmContractId = migrationContractDto.upmContractId();
        final String licenseType = migrationContractDto.licenseType();
        final String distributor = migrationContractDto.wholesaler();
        final String materialId = migrationContractDto.materialId();
        final Date startDate = migrationContractDto.startDate();
        final Date endDate = migrationContractDto.endDate();
        final String managedAccount = migrationContractDto.managedAccountCustomerId();

        final MigrationContractModel migrationContract = modelService.create(MigrationContractModel.class);
        migrationContract.setCode(code);
        migrationContract.setCmtId(String.valueOf(cmtId));
        migrationContract.setUniqueBundleIdentifier(uniqueBundleIdentifier);
        migrationContract.setUmpContractId(upmContractId);
        migrationContract.setLicenseType(licenseType);
        migrationContract.setDistributor(distributor);
        migrationContract.setMaterialId(materialId);
        migrationContract.setStartDate(startDate);
        migrationContract.setEndDate(endDate);
        migrationContract.setManagedAccount(managedAccount);

        return migrationContract;
    }
}
