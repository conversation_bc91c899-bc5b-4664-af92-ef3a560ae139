package com.sast.cis.macmt.service.order;

import com.google.common.collect.Lists;
import com.sast.cis.aa.core.migration.service.MigrationAppLicenseService;
import com.sast.cis.aa.core.model.ContractMigrationProcessModel;
import com.sast.cis.aa.core.model.MigrationContractModel;
import com.sast.cis.core.model.AppLicenseModel;
import com.sast.cis.core.model.IoTCompanyModel;
import com.sast.cis.core.service.company.IotCompanyService;
import com.sast.cis.macmt.config.ContractMigrationConfig;
import com.sast.cis.macmt.service.order.data.MigrationOrderDraftData;
import com.sast.cis.macmt.service.order.data.MigrationOrderEntryDraftData;
import com.sast.cis.macmt.service.order.data.UpmContractGroupData;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static java.util.Optional.ofNullable;
import static org.apache.commons.lang3.StringUtils.EMPTY;
import static org.apache.commons.lang3.StringUtils.isNotBlank;

/**
 * Given a Contract Migration Process with a set of Contracts, return a collection of Order Drafts.
 * <p>
 * The Migration Contracts are grouped into orders, based on the following criteria:
 * - License Type
 * - Distributor (or Managed Account in Indirect Sales countries)
 * - Active or Discontinued Product
 * <p>
 * One or multiple Order Entries will be created for each Product. The same product might have multiple entries if the quantity of
 * the contracts groups for that product exceeds maxOrderSize. Multiple Entries for the same product will be distributed to different orders.
 * The quantity in the Order Entry is based on the number of contract groups created for the product.
 * <p>
 * Contracts groups are used to group contracts that are part of the same bundle product.
 * For Non-Bundle products, each contract will be in its own Contract Group.
 * For Bundle products, contracts with the same Bundle Identifier id will be grouped in one Contract Group.
 * <p>
 * The sum of all quantities in an Order's entries cannot exceed maxOrderSize, and therefore Orders and Order Entries are partitioned in groups
 * that have a maximum added quantity of maxOrderSize.
 */

@Component
@RequiredArgsConstructor
public class MigrationOrderStructureCreator {

    private final MigrationAppLicenseService migrationAppLicenseService;
    private final IotCompanyService iotCompanyService;
    private final ContractMigrationConfig contractMigrationConfig;

    public List<MigrationOrderDraftData> createFor(final ContractMigrationProcessModel process) {
        final List<MigrationOrderDraftData> migrationOrders = new ArrayList<>();

        final Map<String, List<MigrationContractModel>> byLicenseType = process.getMigrationContracts().stream()
            .collect(Collectors.groupingBy(MigrationContractModel::getLicenseType));

        for (final Map.Entry<String, List<MigrationContractModel>> byLicenseTypeEntry : byLicenseType.entrySet()) {
            final String licenseType = byLicenseTypeEntry.getKey();
            final List<MigrationContractModel> groupedByLicenseType = byLicenseTypeEntry.getValue();

            boolean useDistributor = groupedByLicenseType.stream()
                .anyMatch(c -> isNotBlank(c.getDistributor()));

            boolean useManagedAccount = groupedByLicenseType.stream()
                .anyMatch(c -> isNotBlank(c.getManagedAccount()));

            if (useDistributor && useManagedAccount) {
                throw new IllegalStateException("Contracts cannot have both a distributor and a managedAccount.");
            }

            final Map<String, List<MigrationContractModel>> byPropertyBasedOnSalesType = groupedByLicenseType.stream()
                .collect(Collectors.groupingBy(contract -> {
                    if (useDistributor) {
                        return ofNullable(contract.getDistributor()).orElse(EMPTY);
                    } else {
                        return ofNullable(contract.getManagedAccount()).orElse(EMPTY);
                    }
                }));

            for (final Map.Entry<String, List<MigrationContractModel>> byPropertyBasedOnSalesTypeEntry : byPropertyBasedOnSalesType.entrySet()) {
                final String salesTypeProperty = byPropertyBasedOnSalesTypeEntry.getKey();

                // Split into active and non-active products
                final String ownerCompanyId = process.getOwnerCompanyId();
                final List<MigrationContractModel> contractsForActiveProducts = byPropertyBasedOnSalesTypeEntry.getValue().stream()
                    .filter(contract -> isMaterialIdForActiveAppLicense(contract.getMaterialId(), ownerCompanyId)).toList();
                final List<MigrationContractModel> contractsForInactiveProducts = byPropertyBasedOnSalesTypeEntry.getValue().stream()
                    .filter(contract -> !isMaterialIdForActiveAppLicense(contract.getMaterialId(), ownerCompanyId)).toList();

                final String distributor = useDistributor && isNotBlank(salesTypeProperty) ? salesTypeProperty : null;
                final String managedAccount = useManagedAccount && isNotBlank(salesTypeProperty) ? salesTypeProperty : null;
                migrationOrders.addAll(createOrders(licenseType, distributor, managedAccount, contractsForActiveProducts, false));
                migrationOrders.addAll(createOrders(licenseType, distributor, managedAccount, contractsForInactiveProducts, true));
            }
        }
        return migrationOrders;
    }

    private List<MigrationOrderDraftData> createOrders(
        final String licenseType,
        final String distributor,
        final String managedAccount,
        final List<MigrationContractModel> contracts,
        final boolean inactiveProductsMigration) {

        // Split into bundle and non-bundle products
        final List<MigrationContractModel> simpleProductsContracts = contracts.stream()
            .filter(cm -> StringUtils.isBlank(cm.getUniqueBundleIdentifier())).toList();
        final List<MigrationContractModel> bundleProductsContracts = contracts.stream()
            .filter(cm -> isNotBlank(cm.getUniqueBundleIdentifier())).toList();

        // Create Order Entries for bundle and non-bundle products separately and then combine
        final List<MigrationOrderEntryDraftData> entriesForSimple = createMigrationOrderEntriesForSimpleProducts(simpleProductsContracts);
        final List<MigrationOrderEntryDraftData> entriesForBundles = createMigrationOrderEntriesForBundleProducts(bundleProductsContracts);
        final List<MigrationOrderEntryDraftData> allEntries = Stream.concat(entriesForSimple.stream(), entriesForBundles.stream()).toList();

        return createOrdersFromEntries(licenseType, distributor, managedAccount, allEntries, inactiveProductsMigration);
    }

    private List<MigrationOrderEntryDraftData> createMigrationOrderEntriesForSimpleProducts(final List<MigrationContractModel> contracts) {
        final Map<String, List<MigrationContractModel>> byMaterialId = contracts.stream()
            .collect(Collectors.groupingBy(MigrationContractModel::getMaterialId));

        // For simple products group by material id, and for each group create one or multiple entries.
        // The same material id might result in multiple entries if the number of contracts exceeds maxOrderSize.
        final List<MigrationOrderEntryDraftData> migrationOrderEntries = new ArrayList<>();
        byMaterialId.forEach((materialId, contractsGroup) -> {
            // Each simple contract will end up in its own bundle group
            final List<UpmContractGroupData> contractGroups = contractsGroup.stream()
                .map(contract -> new UpmContractGroupData(StringUtils.EMPTY, List.of(contract)))
                .toList();

            final List<MigrationOrderEntryDraftData> migrationOrderEntriesForMaterial = Lists.partition(contractGroups, getMaxOrderSize())
                .stream()
                .map(partition -> new MigrationOrderEntryDraftData(materialId, partition.size(), new ArrayList<>(partition))).toList();
            migrationOrderEntries.addAll(migrationOrderEntriesForMaterial);
        });

        return migrationOrderEntries;
    }

    private List<MigrationOrderEntryDraftData> createMigrationOrderEntriesForBundleProducts(final List<MigrationContractModel> contracts) {
        final Map<String, List<MigrationContractModel>> byMaterialId = contracts.stream()
            .collect(Collectors.groupingBy(MigrationContractModel::getMaterialId));

        final List<MigrationOrderEntryDraftData> migrationOrderEntries = new ArrayList<>();
        byMaterialId.forEach((materialId, contractsByMaterialId) -> {
            // Contracts are grouped by bundle id. For each group create a Contract Group object
            final Map<String, List<MigrationContractModel>> byUniqueBundleId = contractsByMaterialId.stream()
                .collect(Collectors.groupingBy(MigrationContractModel::getUniqueBundleIdentifier));

            final List<UpmContractGroupData> contractGroups = new ArrayList<>();
            byUniqueBundleId.forEach((uniqueBundleId, contractsByBundleId) -> {
                final UpmContractGroupData upmContractGroupData = new UpmContractGroupData(uniqueBundleId, contractsByBundleId);
                contractGroups.add(upmContractGroupData);
            });

            // Each Contract Group represents a quantity of 1, even if the group contains multiple contracts.
            final List<MigrationOrderEntryDraftData> migrationOrderEntriesForMaterial = Lists.partition(contractGroups, getMaxOrderSize())
                .stream()
                .map(partition -> new MigrationOrderEntryDraftData(materialId, partition.size(), new ArrayList<>(partition))).toList();
            migrationOrderEntries.addAll(migrationOrderEntriesForMaterial);
        });

        return migrationOrderEntries;
    }

    private List<MigrationOrderDraftData> createOrdersFromEntries(
        final String licenseType,
        final String distributor,
        final String managedAccount,
        final List<MigrationOrderEntryDraftData> entries,
        final boolean inactiveProductsMigration) {

        final List<MigrationOrderDraftData> orders = new ArrayList<>();
        final List<MigrationOrderEntryDraftData> currentOrderEntries = new ArrayList<>();
        int currentPartitionQuantity = 0;

        for (MigrationOrderEntryDraftData entry : entries) {
            final int entryQuantity = entry.quantity();
            // If adding the current entry exceeds the maxOrderSize, create a new order
            if (currentPartitionQuantity + entryQuantity > getMaxOrderSize()) {
                orders.add(
                    new MigrationOrderDraftData(
                        licenseType, distributor, managedAccount, new ArrayList<>(currentOrderEntries), inactiveProductsMigration
                    )
                );
                currentOrderEntries.clear();
                currentPartitionQuantity = 0;
            }
            // Add the entry to the current order entries
            currentOrderEntries.add(entry);
            currentPartitionQuantity += entryQuantity;
        }

        // Add the remaining entries in the current partition to a new order
        if (!currentOrderEntries.isEmpty()) {
            orders.add(
                new MigrationOrderDraftData(
                    licenseType, distributor, managedAccount, new ArrayList<>(currentOrderEntries), inactiveProductsMigration
                )
            );
        }

        return orders;
    }

    private boolean isMaterialIdForActiveAppLicense(final String materialId, final String companyId) {
        final IoTCompanyModel company = getCompany(companyId);
        final AppLicenseModel appLicense = migrationAppLicenseService.findAppLicenseOrThrow(materialId, company.getCountry());
        return migrationAppLicenseService.isAppLicenseActive(appLicense);
    }

    private IoTCompanyModel getCompany(final String aaExternalCompanyId) {
        return iotCompanyService.getCompanyByAaExternalCompanyIdOrThrow(aaExternalCompanyId);
    }

    private int getMaxOrderSize() {
        return contractMigrationConfig.getMaxOrderSize();
    }
}
