package com.sast.cis.macmt.service.validator;

import com.sast.cis.aa.core.migration.service.MigrationAppLicenseService;
import com.sast.cis.aa.core.model.MigrationContractModel;
import com.sast.cis.aa.core.model.MigrationOrderEntryDraftModel;
import com.sast.cis.core.model.AppLicenseModel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Set;

import static com.sast.cis.macmt.exception.ValidationErrorCode.INACTIVE_PRODUCT_CONTRACTS_END_DATE_MISMATCH;
import static java.util.Collections.emptySet;
import static java.util.stream.Collectors.toSet;

@Component
@RequiredArgsConstructor
@Slf4j
public class MigrationOrderDraftEntryValidator {
    private final MigrationAppLicenseService migrationAppLicenseService;

    public Set<String> validateInactiveProductContractEndDate(final MigrationOrderEntryDraftModel entry) {
        final AppLicenseModel appLicense = entry.getAppLicense();

        if (migrationAppLicenseService.isAppLicenseActive(appLicense)) {
            return emptySet();
        }
        final Set<Date> endDates = entry.getContractGroups().stream()
            .flatMap(group -> group.getMigrationContracts().stream())
            .map(MigrationContractModel::getEndDate)
            .collect(toSet());

        if (endDates.size() != 1) {
            LOG.error("End dates {} of inactive product {} contracts are not the same", endDates, appLicense.getCode());
            return Set.of(INACTIVE_PRODUCT_CONTRACTS_END_DATE_MISMATCH.name());
        }

        return emptySet();
    }
}
