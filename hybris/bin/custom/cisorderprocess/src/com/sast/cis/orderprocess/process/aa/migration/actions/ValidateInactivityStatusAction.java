package com.sast.cis.orderprocess.process.aa.migration.actions;

import com.sast.cis.aa.core.migration.service.MigrationProductInactivityStatusValidator;
import com.sast.cis.aa.core.model.MigrationOrderDraftModel;
import com.sast.cis.aa.core.model.OrderMigrationBusinessProcessModel;
import de.hybris.platform.processengine.action.AbstractSimpleDecisionAction;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@RequiredArgsConstructor
public class ValidateInactivityStatusAction extends AbstractSimpleDecisionAction<OrderMigrationBusinessProcessModel> {
    private final MigrationProductInactivityStatusValidator productStatusValidator;

    @Override
    public Transition executeAction(final OrderMigrationBusinessProcessModel orderMigrationBusinessProcess) {
        final MigrationOrderDraftModel migrationOrderDraft = orderMigrationBusinessProcess.getMigrationOrderDraft();
        if (migrationOrderDraft == null) {
            LOG.error("Migration order draft is null");
            return Transition.NOK;
        }

        boolean isValid = productStatusValidator.validateConsistentInactivityStatus(migrationOrderDraft);
        return isValid ? Transition.OK : Transition.NOK;
    }
}
