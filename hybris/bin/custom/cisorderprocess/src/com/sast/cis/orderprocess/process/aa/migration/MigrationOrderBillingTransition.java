package com.sast.cis.orderprocess.process.aa.migration;

import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public enum MigrationOrderBillingTransition {
    BILLABLE,    // Active subscription products that need BRIM export
    NON_BILLABLE, // Full licenses or inactive subscriptions that skip BRIM
    ERROR;

    private static final Set<String> VALUES = Stream.of(MigrationOrderBillingTransition.values())
        .map(Enum::name)
        .collect(Collectors.toUnmodifiableSet());

    public static Set<String> getStringValues() {
        return VALUES;
    }
}
