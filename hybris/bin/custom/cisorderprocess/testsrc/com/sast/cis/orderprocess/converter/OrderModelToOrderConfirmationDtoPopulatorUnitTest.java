package com.sast.cis.orderprocess.converter;

import com.sast.cis.core.dto.SellerCurrencyDto;
import com.sast.cis.core.dto.portal.OrderConfirmationDto;
import com.sast.cis.core.dto.portal.OrderEntryDto;
import com.sast.cis.core.dto.portal.SubscriptionDto;
import com.sast.cis.core.enums.LicenseType;
import com.sast.cis.core.model.*;
import com.sast.cis.core.service.company.IotCompanyService;
import com.sast.cis.core.service.customer.InternalCustomerUidTranslationService;
import de.hybris.bootstrap.annotations.UnitTest;
import de.hybris.platform.core.model.c2l.CountryModel;
import de.hybris.platform.core.model.c2l.CurrencyModel;
import de.hybris.platform.core.model.order.AbstractOrderEntryModel;
import de.hybris.platform.core.model.order.OrderModel;
import de.hybris.platform.servicelayer.dto.converter.Converter;
import de.hybris.platform.servicelayer.i18n.CommonI18NService;
import de.hybris.platform.servicelayer.time.TimeService;
import generated.com.sast.cis.core.model.*;
import generated.de.hybris.platform.core.model.c2l.CountryBuilder;
import generated.de.hybris.platform.core.model.c2l.CurrencyBuilder;
import generated.de.hybris.platform.core.model.order.AbstractOrderEntryBuilder;
import generated.de.hybris.platform.core.model.order.OrderBuilder;
import generated.de.hybris.platform.core.model.user.UserBuilder;
import generated.de.hybris.platform.variants.model.VariantProductBuilder;
import jersey.repackaged.com.google.common.collect.ImmutableList;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.LocalDate;
import java.util.Date;
import java.util.UUID;

import static com.sast.cis.core.enums.LicenseType.*;
import static java.time.Month.APRIL;
import static java.time.ZoneOffset.UTC;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.util.Lists.emptyList;
import static org.junit.rules.ExpectedException.none;
import static org.mockito.Matchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@UnitTest
@RunWith(MockitoJUnitRunner.Silent.class)
public class OrderModelToOrderConfirmationDtoPopulatorUnitTest {

    private static final String EVALUATION_LICENSE_TYPE = "EVALUATION";
    private static final String FULL_LICENSE_TYPE = "FULL";
    private static final String SUBSCRIPTION_LICENSE_TYPE = "SUBSCRIPTION";
    private static final Date NOW = Date.from(LocalDate.of(2011, APRIL, 1).atTime(13, 14, 15).toInstant(UTC));
    private static final String FORMATTED_TIMESTAMP = "2011-04-01T13:14:15.000Z";
    private static final String ORDER_CODE = "TestOrderCode";
    private static final String COMPANY_ACCOUNT_ID = "TestRemoteID";
    private static final String DEVELOPER_COMPANY_ID = "DEVELOPER_COMPANY_ID";
    private static final String INTEGRATOR_USER_ID = "TestIntegratorUserID@shop";
    private static final String INTEGRATOR_USER_SSOID = "TestIntegratorUserID";
    private static final Date CREATION_TIMESTAMP = new Date();
    private static final String APP_CODE = "TestAppCode";
    private static final String APP_PACKAGE_NAME = "test.package.name";
    private static final String APP_VERSION = "TestAppVersion0.1.0";
    private static final String INVALID_UID = "InvalidUserUid";
    private static final String INVALID_PRODUCT_CODE = "InvalidProductCode";
    private static final Long APP_VERSION_VERSION_CODE = 42L;
    private static final double BASE_CONVERSION = 1d;
    private static final double EUR_USD_CONVERSION = 1.2d;
    public static final String EUR_ISOCODE = "EUR";
    public static final String USD_ISOCODE = "USD";

    @Rule
    public ExpectedException expectedException = none();

    @Mock
    private InternalCustomerUidTranslationService<IntegratorModel> internalIntegratorUidTranslationService;

    @Mock
    private TimeService timeService;

    @Mock
    private IotCompanyService iotCompanyService;

    @Mock
    private Converter<SubscriptionContractModel, SubscriptionDto> subscriptionConverter;

    @Mock
    private CommonI18NService commonI18NService;

    @InjectMocks
    private OrderModelToOrderConfirmationDtoPopulator orderModelToOrderConfirmationDtoPopulator;

    private AppModel app;
    private AppVersionModel appVersion;
    private IntegratorModel mockIntegrator;
    private IoTCompanyModel developerCompany;
    private CurrencyModel orderCurrency;
    private CountryModel developerCountry;
    private CurrencyModel developerCurrency;

    @Before
    public void setUp() {
        app = AppBuilder.generate()
            .withCode(APP_CODE)
            .withPackageName(APP_PACKAGE_NAME)
            .buildMockInstance();

        appVersion = AppVersionBuilder.generate()
            .withApk(ApkMediaBuilder.generate()
                .withVersionName(APP_VERSION)
                .withVersionCode(APP_VERSION_VERSION_CODE)
                .buildMockInstance())
            .buildMockInstance();

        mockIntegrator = IntegratorBuilder.generate()
            .withUid(INTEGRATOR_USER_ID)
            .withCompany(IoTCompanyBuilder.generate()
                .withUid(COMPANY_ACCOUNT_ID)
                .buildMockInstance())
            .buildMockInstance();

        orderCurrency = CurrencyBuilder.generate().withConversion(BASE_CONVERSION).withIsocode(EUR_ISOCODE).buildMockInstance();
        developerCurrency = CurrencyBuilder.generate().withConversion(EUR_USD_CONVERSION).withIsocode(USD_ISOCODE).buildMockInstance();
        developerCountry = CountryBuilder.generate().buildMockInstance();
        developerCompany = IoTCompanyBuilder
            .generate()
            .withUid(DEVELOPER_COMPANY_ID)
            .withCountry(developerCountry)
            .buildMockInstance();

        when(iotCompanyService.getDeveloperCompanyForOrderItems(any(OrderModel.class))).thenReturn(developerCompany);
        when(internalIntegratorUidTranslationService.translateToSsoId(mockIntegrator)).thenReturn(INTEGRATOR_USER_SSOID);
        when(timeService.getCurrentTime()).thenReturn(NOW);
        when(commonI18NService.convertCurrency(BASE_CONVERSION, EUR_USD_CONVERSION, BASE_CONVERSION)).thenReturn(EUR_USD_CONVERSION);
        when(commonI18NService.convertCurrency(BASE_CONVERSION, BASE_CONVERSION, BASE_CONVERSION)).thenReturn(BASE_CONVERSION);
        when(developerCountry.getCurrency()).thenReturn(developerCurrency);
    }

    @Test
    public void validOrder_returnsFilledOrderConfirmation() {
        OrderModel validOrder = validOrderBuilder()
            .withEntries(ImmutableList.of(validOrderEntry(FULL).buildMockInstance()))
            .buildMockInstance();

        OrderConfirmationDto orderConfirmationDto = new OrderConfirmationDto();
        orderModelToOrderConfirmationDtoPopulator.populate(validOrder, orderConfirmationDto);

        assertThat(orderConfirmationDto).isEqualToComparingFieldByFieldRecursively(validOrderConfirmation(FULL_LICENSE_TYPE));
    }

    private OrderBuilder<?, ?> validOrderBuilder() {
        return OrderBuilder.generate()
            .withUser(mockIntegrator)
            .withCreationtime(CREATION_TIMESTAMP)
            .withCurrency(orderCurrency)
            .withCode(ORDER_CODE);
    }

    private AbstractOrderEntryBuilder<?, ?> validOrderEntry(LicenseType licenseType) {
        AppModel app = AppBuilder.generate()
            .withCode(APP_CODE)
            .withPackageName(APP_PACKAGE_NAME)
            .withLatestVersion(appVersion)
            .buildMockInstance();

        return AbstractOrderEntryBuilder.generate()
            .withProduct(AppLicenseBuilder.generate()
                .withLicenseType(licenseType)
                .withBaseProduct(app)
                .buildMockInstance())
            .withQuantity(1L);
    }

    private OrderConfirmationDto validOrderConfirmation(String licenseType) {
        return new OrderConfirmationDto()
            .withCreationTimestamp(CREATION_TIMESTAMP)
            .withCompanyAccountId(COMPANY_ACCOUNT_ID)
            .withDeveloperCompanyId(DEVELOPER_COMPANY_ID)
            .withOrderId(ORDER_CODE)
            .withTransactionId(ORDER_CODE + "_" + FORMATTED_TIMESTAMP)
            .withUserId(INTEGRATOR_USER_SSOID)
            .withBuyerCurrencyIso(orderCurrency.getIsocode())
            .withSellerCurrency(new SellerCurrencyDto()
                .withConversionWithBase(EUR_USD_CONVERSION)
                .withConversionWithBuyer(EUR_USD_CONVERSION)
                .withIsocode(developerCurrency.getIsocode()))
            .withOrder(ImmutableList.of(new OrderEntryDto()
                .withQuantity(1)
                .withApplicationId(APP_PACKAGE_NAME)
                .withTotalPrice(0d)
                .withVersionName(APP_VERSION)
                .withVersionCode(APP_VERSION_VERSION_CODE)
                .withLicenseType(licenseType)
                .withSubscriptions(emptyList())));
    }

    @Test
    public void validSubscriptionOrder_returnsFilledOrderConfirmation() {

        SubscriptionContractModel subscription = new SubscriptionContractModel();
        subscription.setStartDate(NOW);
        subscription.setLegacySubscriptionId(UUID.randomUUID().toString());

        when(subscriptionConverter.convert(subscription)).thenReturn(new SubscriptionDto());

        final ImmutableList<AbstractOrderEntryModel> entries = ImmutableList.of(
            validOrderEntry(SUBSCRIPTION)
                .withBuyerContracts(ImmutableList.of(subscription))
                .buildMockInstance()
        );

        OrderModel validOrder = validOrderBuilder()
            .withEntries(entries)
            .buildMockInstance();

        OrderConfirmationDto orderConfirmationDto = new OrderConfirmationDto();
        orderModelToOrderConfirmationDtoPopulator.populate(validOrder, orderConfirmationDto);

        final var expectedOrderConfirmation = validOrderConfirmation(SUBSCRIPTION_LICENSE_TYPE);
        expectedOrderConfirmation.getOrder().get(0)
            .setSubscriptions(ImmutableList.of(new SubscriptionDto()));

        assertThat(orderConfirmationDto).isEqualToComparingFieldByFieldRecursively(expectedOrderConfirmation);
        verify(subscriptionConverter).convert(subscription);
    }

    @Test
    public void validOrderOfEvaluationLicense_returnsFilledOrderConfirmation() {
        OrderModel validOrder = validOrderBuilder()
            .withEntries(ImmutableList.of(validOrderEntry(EVALUATION).buildMockInstance()))
            .buildMockInstance();

        OrderConfirmationDto orderConfirmationDto = new OrderConfirmationDto();
        orderModelToOrderConfirmationDtoPopulator.populate(validOrder, orderConfirmationDto);

        assertThat(orderConfirmationDto).isEqualToComparingFieldByFieldRecursively(validOrderConfirmation(EVALUATION_LICENSE_TYPE));
    }

    @Test
    public void emptyOrder_returnsOrderConfirmationWithEmptyOrder() {
        OrderModel emptyOrder = validOrderBuilder().withEntries(emptyList()).buildMockInstance();

        OrderConfirmationDto orderConfirmationDto = new OrderConfirmationDto();
        orderModelToOrderConfirmationDtoPopulator.populate(emptyOrder, orderConfirmationDto);

        assertThat(orderConfirmationDto)
            .isEqualToComparingFieldByFieldRecursively(validOrderConfirmation(FULL_LICENSE_TYPE).withOrder(emptyList()));
    }

    @Test
    public void userOfOrderIsNotAnIntegrator_throws() {
        OrderModel orderWithRegularUser = validOrderBuilder()
            .withUser(UserBuilder.generate().withUid(INVALID_UID).buildMockInstance())
            .buildMockInstance();

        expectedException.expect(IllegalArgumentException.class);
        orderModelToOrderConfirmationDtoPopulator.populate(orderWithRegularUser, new OrderConfirmationDto());
    }

    @Test
    public void orderContainingSomethingOtherThanAppLicense_throws() {
        OrderModel orderWithNonAppVersionProduct = validOrderBuilder().withEntries(ImmutableList.of(
            validOrderEntry(FULL).withProduct(VariantProductBuilder.generate().withCode(INVALID_PRODUCT_CODE).buildMockInstance())
                .buildMockInstance()))
            .buildMockInstance();

        expectedException.expect(IllegalArgumentException.class);
        orderModelToOrderConfirmationDtoPopulator.populate(orderWithNonAppVersionProduct, new OrderConfirmationDto());
    }

    @Test
    public void orderContainsLicenseWithoutCorrespondingVersion_throwsException() {
        OrderModel order = validOrderBuilder().withEntries(ImmutableList.of(
            validOrderEntry(FULL).withProduct(
                AppLicenseBuilder.generate()
                    .withLicenseType(LicenseType.FULL)
                    .withBaseProduct(app)
                    .buildMockInstance())
                .buildMockInstance()))
            .buildMockInstance();

        expectedException.expect(IllegalArgumentException.class);
        orderModelToOrderConfirmationDtoPopulator.populate(order, new OrderConfirmationDto());
    }

    @Test
    public void validOrderAndSameBuyerAndSellerCurrency_returnsFilledOrderConfirmation() {
        developerCurrency = orderCurrency;
        when(developerCountry.getCurrency()).thenReturn(developerCurrency);
        OrderModel validOrder = validOrderBuilder()
            .withEntries(ImmutableList.of(validOrderEntry(FULL).buildMockInstance()))
            .buildMockInstance();

        OrderConfirmationDto orderConfirmationDto = new OrderConfirmationDto();
        orderModelToOrderConfirmationDtoPopulator.populate(validOrder, orderConfirmationDto);

        assertThat(orderConfirmationDto)
            .isEqualToComparingFieldByFieldRecursively(validOrderConfirmation(FULL_LICENSE_TYPE)
            .withSellerCurrency(new SellerCurrencyDto()
                .withIsocode(EUR_ISOCODE)
                .withConversionWithBase(BASE_CONVERSION)
                .withConversionWithBuyer(BASE_CONVERSION)));
    }
}
