package com.sast.cis.orderprocess.converter.aa.migration.actions;

import com.sast.cis.aa.core.model.ContractMigrationProcessModel;
import com.sast.cis.aa.core.model.MigrationContractGroupModel;
import com.sast.cis.aa.core.model.MigrationContractModel;
import com.sast.cis.aa.core.model.MigrationOrderDraftModel;
import com.sast.cis.aa.core.model.MigrationOrderEntryDraftModel;
import com.sast.cis.aa.core.model.OrderMigrationBusinessProcessModel;
import com.sast.cis.core.config.keycloak.SiteUmpAdapterConfigResolutionService;
import com.sast.cis.core.dao.CatalogVersion;
import com.sast.cis.core.enums.PaymentProvider;
import com.sast.cis.core.model.AppLicenseModel;
import com.sast.cis.core.model.AppModel;
import com.sast.cis.core.model.DeveloperModel;
import com.sast.cis.core.model.IntegratorModel;
import com.sast.cis.core.service.customer.developer.DeveloperService;
import com.sast.cis.core.service.customer.integrator.IntegratorService;
import com.sast.cis.orderprocess.process.aa.migration.actions.BrimExportMigrationOrderAction;
import com.sast.cis.test.utils.BrimulatorRule;
import com.sast.cis.test.utils.LoginUtil;
import com.sast.cis.test.utils.SampleDataCreator;
import de.hybris.bootstrap.annotations.IntegrationTest;
import de.hybris.platform.core.enums.CreditCardType;
import de.hybris.platform.core.enums.ExportStatus;
import de.hybris.platform.core.enums.OrderStatus;
import de.hybris.platform.core.model.order.OrderEntryModel;
import de.hybris.platform.core.model.order.OrderModel;
import de.hybris.platform.core.model.order.payment.PaymentInfoModel;
import de.hybris.platform.payment.dto.TransactionStatus;
import de.hybris.platform.payment.enums.PaymentTransactionType;
import de.hybris.platform.payment.model.PaymentTransactionEntryModel;
import de.hybris.platform.payment.model.PaymentTransactionModel;
import de.hybris.platform.processengine.action.AbstractSimpleDecisionAction;
import de.hybris.platform.servicelayer.ServicelayerTransactionalTest;
import de.hybris.platform.servicelayer.model.ModelService;
import de.hybris.platform.store.services.BaseStoreService;
import generated.com.sast.cis.aa.core.model.MigrationContractBuilder;
import generated.com.sast.cis.aa.core.model.MigrationContractGroupBuilder;
import generated.com.sast.cis.aa.core.model.MigrationOrderDraftBuilder;
import generated.com.sast.cis.aa.core.model.MigrationOrderEntryDraftBuilder;
import generated.com.sast.cis.aa.core.model.OrderMigrationBusinessProcessBuilder;
import generated.com.sast.cis.core.model.StripeCreditCardPaymentInfoBuilder;
import generated.de.hybris.platform.payment.model.PaymentTransactionBuilder;
import generated.de.hybris.platform.payment.model.PaymentTransactionEntryBuilder;
import org.assertj.core.util.DateUtil;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;
import java.util.UUID;

import static com.sast.cis.test.utils.TestDataConstants.AA_AUSTRIA1_COMPANY_DEVELOPER_UID;
import static com.sast.cis.test.utils.TestDataConstants.AA_DISTRIBUTOR_COMPANY_UID;
import static com.sast.cis.test.utils.TestDataConstants.AA_MIGRATED_BUYER_COMPANY_INTEGRATOR_UID;
import static de.hybris.platform.catalog.enums.ArticleApprovalStatus.CHECK;
import static de.hybris.platform.processengine.action.AbstractSimpleDecisionAction.Transition.NOK;
import static de.hybris.platform.processengine.action.AbstractSimpleDecisionAction.Transition.OK;
import static org.assertj.core.api.Assertions.assertThat;

@IntegrationTest
public class BrimExportMigrationOrderActionITest extends ServicelayerTransactionalTest {

    private static final String APP_CODE = "testapp";
    private static final String APP_PACKAGE = "com.test.brim.migration.aa";

    @Rule
    public BrimulatorRule brimulatorRule = new BrimulatorRule();

    @Resource
    private ModelService modelService;

    @Resource
    private BrimExportMigrationOrderAction brimExportMigrationOrderAction;

    @Resource
    private SiteUmpAdapterConfigResolutionService siteUmpAdapterConfigResolutionService;

    @Resource
    private RestTemplate cisRestTemplate;

    @Resource
    private DeveloperService developerService;

    @Resource
    private IntegratorService integratorService;

    @Resource
    private BaseStoreService baseStoreService;

    private OrderMigrationBusinessProcessModel orderMigrationBusinessProcess;
    private MigrationOrderDraftModel migrationOrderDraft;
    private OrderModel migrationOrder;
    private final SampleDataCreator sampleDataCreator = new SampleDataCreator();

    @Before
    public void setup() {
        final LoginUtil loginUtil = new LoginUtil(siteUmpAdapterConfigResolutionService, cisRestTemplate);
        SecurityContextHolder.getContext().setAuthentication(loginUtil.generateKeycloakAuthenticationObject());

        final DeveloperModel developer = developerService.getDeveloperByInternalUserId(AA_AUSTRIA1_COMPANY_DEVELOPER_UID);
        final IntegratorModel integrator = integratorService.getIntegratorByInternalUserId(AA_MIGRATED_BUYER_COMPANY_INTEGRATOR_UID);

        final AppModel app = sampleDataCreator.createApp(APP_CODE, APP_PACKAGE, developer, CatalogVersion.ONLINE, CHECK);
        final AppLicenseModel fullLicense = sampleDataCreator.createFullAppLicense(app);

        final PaymentTransactionEntryModel transactionEntry = createTransactionEntry(TransactionStatus.ACCEPTED).buildIntegrationInstance();
        final PaymentTransactionModel paymentTransaction = createPaymentTransaction(List.of(transactionEntry));
        final PaymentInfoModel basicPaymentInfo = createPaymentInfo(integrator);

        migrationOrder = sampleDataCreator.createOrder(integrator, OrderStatus.CREATED);
        migrationOrder.setPaymentInfo(basicPaymentInfo);
        migrationOrder.setPaymentTransactions(List.of(paymentTransaction));
        migrationOrder.setStore(baseStoreService.getBaseStoreForUid("aastore"));
        migrationOrder.setMigrationOrder(true);
        migrationOrder.setTotalPrice(100.0);

        final OrderEntryModel orderEntry = sampleDataCreator.createOrderEntry(migrationOrder, fullLicense, 1L);

        paymentTransaction.setInfo(basicPaymentInfo);

        final ContractMigrationProcessModel migrationProcess = sampleDataCreator.createContractMigrationProcess(integrator.getCompany());

        final MigrationContractModel migrationContract = MigrationContractBuilder.generate()
            .withCode("MIGRATION_CONTRACT_1")
            .withCmtId("100")
            .withLicenseType("AZCU")
            .withUmpContractId("*********")
            .withDistributor(AA_DISTRIBUTOR_COMPANY_UID)
            .withMaterialId("material-id")
            .withUniqueBundleIdentifier("bundle-id")
            .withStartDate(DateUtil.yesterday())
            .withEndDate(DateUtil.tomorrow())
            .withContractMigrationProcess(migrationProcess)
            .buildIntegrationInstance();

        final MigrationContractGroupModel migrationContractGroup = MigrationContractGroupBuilder.generate()
            .withCode("MIGRATION_CONTRACT_GROUP_1")
            .withUniqueBundleIdentifier("bundle-id")
            .withMigrationContracts(Set.of(migrationContract))
            .buildIntegrationInstance();

        final MigrationOrderEntryDraftModel migrationOrderEntryDraft = MigrationOrderEntryDraftBuilder.generate()
            .withAppLicense(fullLicense)
            .withCode("migrationOrderEntryDraftTestCode")
            .withContractGroups(Set.of(migrationContractGroup))
            .withQuantity(1)
            .buildIntegrationInstance();

        migrationOrderDraft = MigrationOrderDraftBuilder.generate()
            .withResultingOrder(migrationOrder)
            .withCode("migrationOrderDraftTestCode")
            .withCompany(integrator.getCompany())
            .withContractMigrationProcess(migrationProcess)
            .withEntries(Set.of(migrationOrderEntryDraft))
            .buildIntegrationInstance();

        orderMigrationBusinessProcess = OrderMigrationBusinessProcessBuilder.generate()
            .withMigrationOrderDraft(migrationOrderDraft)
            .withCode("orderMigrationBusinessProcessTestCode")
            .withProcessDefinitionName("testProcess")
            .buildIntegrationInstance();

        modelService.saveAll(migrationOrder, orderEntry, migrationOrderEntryDraft, paymentTransaction, basicPaymentInfo, migrationOrderDraft, orderMigrationBusinessProcess,
            migrationContract, migrationContractGroup);

        brimulatorRule.prepareOrderCreateSkipResponse(migrationOrder.getCode());
    }

    @Test
    public void export_migrationOrderIsNull_fail() {
        migrationOrderDraft.setResultingOrder(null);
        modelService.save(migrationOrderDraft);

        AbstractSimpleDecisionAction.Transition result = brimExportMigrationOrderAction.executeAction(orderMigrationBusinessProcess);

        assertThat(result).isEqualTo(NOK);
    }

    @Test
    public void export_migrationOrderExists_success() {
        AbstractSimpleDecisionAction.Transition result = brimExportMigrationOrderAction.executeAction(orderMigrationBusinessProcess);

        assertThat(result).isEqualTo(OK);

        modelService.refresh(migrationOrder);

        assertThat(migrationOrder.getExportStatus()).isEqualTo(ExportStatus.EXPORTED);
    }

    private PaymentTransactionModel createPaymentTransaction(final List<PaymentTransactionEntryModel> transactionEntries) {
        return PaymentTransactionBuilder
            .generate()
            .withCode("code-transaction")
            .withType(PaymentTransactionType.AUTHORIZATION)
            .withPaymentProvider(PaymentProvider.STRIPE.getCode())
            .withEntries(transactionEntries)
            .withRequestToken(UUID.randomUUID().toString())
            .buildIntegrationInstance();
    }

    private PaymentTransactionEntryBuilder<?, ?> createTransactionEntry(final TransactionStatus status) {
        return PaymentTransactionEntryBuilder
            .generate()
            .withCode("code-transaction-entry")
            .withTransactionStatus(status.name())
            .withType(PaymentTransactionType.AUTHORIZATION)
            .withRequestToken(UUID.randomUUID().toString());
    }

    private PaymentInfoModel createPaymentInfo(final IntegratorModel integrator) {
        return StripeCreditCardPaymentInfoBuilder.generate()
            .withPaymentProvider(PaymentProvider.STRIPE)
            .withPaymentMethodId("paymentMethodId")
            .withType(CreditCardType.VISA)
            .withCardSummary("1234")
            .withNumber("***1234")
            .withValidToMonth("12")
            .withValidToYear("24")
            .withUser(integrator)
            .withCode("testPaymentInfoCode")
            .buildIntegrationInstance();
    }
}
