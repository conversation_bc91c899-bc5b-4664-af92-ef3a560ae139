package com.sast.cis.orderprocess.process.aa.migration.actions;

import com.sast.cis.aa.core.model.MigrationOrderDraftModel;
import com.sast.cis.aa.core.model.OrderMigrationBusinessProcessModel;
import com.sast.cis.core.enums.LicenseType;
import com.sast.cis.core.model.AppLicenseModel;
import com.sast.cis.orderprocess.process.aa.migration.MigrationOrderBillingTransition;
import de.hybris.bootstrap.annotations.UnitTest;
import de.hybris.platform.core.model.order.AbstractOrderEntryModel;
import de.hybris.platform.core.model.order.OrderModel;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

@UnitTest
@RunWith(MockitoJUnitRunner.class)
public class MigrationOrderBillingRoutingActionUnitTest {

    @InjectMocks
    private MigrationOrderBillingRoutingAction routingAction;

    @Mock
    private OrderMigrationBusinessProcessModel businessProcess;

    @Mock
    private MigrationOrderDraftModel orderDraft;

    @Mock
    private OrderModel order;

    @Mock
    private AbstractOrderEntryModel orderEntry;

    @Mock
    private AppLicenseModel product;

    @Before
    public void setUp() {
        when(businessProcess.getMigrationOrderDraft()).thenReturn(orderDraft);
        when(orderDraft.getResultingOrder()).thenReturn(order);
    }

    @Test
    public void shouldReturnBillable_whenActiveSubscriptionAndNotInactive() {
        when(orderDraft.isInactiveProductMigration()).thenReturn(false);
        when(order.getEntries()).thenReturn(List.of(orderEntry));
        when(orderEntry.getProduct()).thenReturn(product);
        when(product.getLicenseType()).thenReturn(LicenseType.SUBSCRIPTION);

        String result = routingAction.execute(businessProcess);

        assertEquals(MigrationOrderBillingTransition.BILLABLE.name(), result);
    }

    @Test
    public void shouldReturnNonBillable_whenInactiveEvenIfSubscription() {
        when(orderDraft.isInactiveProductMigration()).thenReturn(true);
        when(order.getEntries()).thenReturn(List.of(orderEntry));

        String result = routingAction.execute(businessProcess);

        assertEquals(MigrationOrderBillingTransition.NON_BILLABLE.name(), result);
    }

    @Test
    public void shouldReturnNonBillable_whenNonSubscriptionProduct() {
        when(orderDraft.isInactiveProductMigration()).thenReturn(false);
        when(order.getEntries()).thenReturn(List.of(orderEntry));
        when(orderEntry.getProduct()).thenReturn(product);
        when(product.getLicenseType()).thenReturn(LicenseType.FULL);

        String result = routingAction.execute(businessProcess);

        assertEquals(MigrationOrderBillingTransition.NON_BILLABLE.name(), result);
    }

    @Test
    public void shouldReturnError_whenOrderEntriesNull() {
        when(order.getEntries()).thenReturn(null);

        String result = routingAction.execute(businessProcess);

        assertEquals(MigrationOrderBillingTransition.ERROR.name(), result);
    }

    @Test
    public void shouldReturnError_whenOrderEntriesEmpty() {
        when(order.getEntries()).thenReturn(Collections.emptyList());

        String result = routingAction.execute(businessProcess);

        assertEquals(MigrationOrderBillingTransition.ERROR.name(), result);
    }
}
