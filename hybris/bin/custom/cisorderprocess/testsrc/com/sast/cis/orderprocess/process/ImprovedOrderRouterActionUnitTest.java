package com.sast.cis.orderprocess.process;

import com.sast.cis.core.enums.LicenseType;
import com.sast.cis.core.model.AppLicenseModel;
import com.sast.cis.orderprocess.constants.ImprovedOrderRouterTransition;
import de.hybris.bootstrap.annotations.UnitTest;
import de.hybris.platform.core.model.order.OrderModel;
import de.hybris.platform.orderprocessing.model.OrderProcessModel;
import generated.de.hybris.platform.core.model.order.OrderEntryBuilder;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.Collections;

import static com.sast.cis.orderprocess.constants.ImprovedOrderRouterTransition.*;
import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

@UnitTest
public class ImprovedOrderRouterActionUnitTest {

    @InjectMocks
    private ImprovedOrderRouterAction improvedOrderRouterAction;

    @Mock
    private AppLicenseModel aAppLicenseModel;
    @Mock
    private AppLicenseModel anyAppLicenseModel;
    @Mock
    private OrderModel orderModel;
    @Mock
    private OrderProcessModel orderProcessModel;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        when(orderProcessModel.getOrder()).thenReturn(orderModel);
        when(orderModel.getEntries()).thenReturn(Arrays.asList(
            OrderEntryBuilder.generate().withProduct(aAppLicenseModel).withQuantity(1L).withTotalPrice(10d).buildInstance(),
            OrderEntryBuilder.generate().withProduct(anyAppLicenseModel).withQuantity(1L).withTotalPrice(10d).buildInstance()
        ));

    }

    @Test
    public void orderRoute_paid_orderWithFullLicensedEntries() {
        when(orderModel.getTotalPrice()).thenReturn(20d);
        when(aAppLicenseModel.getLicenseType()).thenReturn(LicenseType.FULL);
        when(anyAppLicenseModel.getLicenseType()).thenReturn(LicenseType.FULL);

        String actualTransition = improvedOrderRouterAction.execute(orderProcessModel);

        assertEquals("expected a transition PAID", PAID.toString(),actualTransition);
    }

    @Test
    public void orderRoute_trial_orderWithEvalLicensedEntries() {
        when(orderModel.getEntries()).thenReturn(Arrays.asList(
            OrderEntryBuilder.generate().withProduct(aAppLicenseModel).withQuantity(1L).withTotalPrice(0d).buildInstance(),
            OrderEntryBuilder.generate().withProduct(anyAppLicenseModel).withQuantity(1L).withTotalPrice(0d).buildInstance()
        ));
        when(aAppLicenseModel.getLicenseType()).thenReturn(LicenseType.EVALUATION);
        when(anyAppLicenseModel.getLicenseType()).thenReturn(LicenseType.EVALUATION);

        String actualTransition = improvedOrderRouterAction.execute(orderProcessModel);

        assertEquals("expected a transition TRIAL", TRIAL.toString(),actualTransition);
    }

    @Test
    public void orderRoute_paidFree_orderWithFreeFullLicensedEntries() {
        when(orderModel.getEntries()).thenReturn(Arrays.asList(
            OrderEntryBuilder.generate().withProduct(aAppLicenseModel).withQuantity(1L).withTotalPrice(0d).buildInstance(),
            OrderEntryBuilder.generate().withProduct(anyAppLicenseModel).withQuantity(1L).withTotalPrice(0d).buildInstance()
        ));
        when(aAppLicenseModel.getLicenseType()).thenReturn(LicenseType.FULL);
        when(anyAppLicenseModel.getLicenseType()).thenReturn(LicenseType.FULL);

        String actualTransition = improvedOrderRouterAction.execute(orderProcessModel);

        assertEquals("order with full license but free should transit to PAID_FREE", PAID_FREE.toString(),actualTransition);
    }

    @Test
    public void orderRoute_noLicense_orderWithNoEntries() {
        when(orderModel.getEntries()).thenReturn(Collections.emptyList());

        String actualTransition = improvedOrderRouterAction.execute(orderProcessModel);

        assertEquals("expected a transition NO_LICENSE", NO_LICENSE.toString(), actualTransition);
    }

    @Test
    public void orderRoute_multi_orderWithMultiLicensedEntries() {
        when(orderModel.getEntries()).thenReturn(Arrays.asList(
            OrderEntryBuilder.generate().withProduct(aAppLicenseModel).withQuantity(1L).withTotalPrice(0d).buildInstance(),
            OrderEntryBuilder.generate().withProduct(anyAppLicenseModel).withQuantity(1L).withTotalPrice(10d).buildInstance()
        ));
        when(aAppLicenseModel.getLicenseType()).thenReturn(LicenseType.EVALUATION);
        when(anyAppLicenseModel.getLicenseType()).thenReturn(LicenseType.FULL);

        String actualTransition = improvedOrderRouterAction.execute(orderProcessModel);

        assertEquals("expected a transition MULTI", MULTI.toString(), actualTransition);
    }
}