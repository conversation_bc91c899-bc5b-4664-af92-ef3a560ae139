#% impex.setLocale( Locale.GERMAN );

# numerical isocode for AT, used as prefix for product code
$atCc = 040
$aaPackageName = com.sast.aa.at.

$productCatalog = aaProductCatalog
$catalogVersion = catalogversion(catalog(id[default=$productCatalog]), version[default='Staged'])
$supercategories = supercategories(code, $catalogVersion)
$baseProduct = baseProduct(code, $catalogVersion)
$approved = approvalstatus(code)[default='approved']
$taxGroup = Europe1PriceFactory_PTG(code)[default=eu-vat-full]
$privacyPolicyUrl = https://legal.boschmarketplace.com/boschaftermarket/at/de/Datenschutzhinweise_ESItronic_AT_2021-01.pdf
$emailAddress = <EMAIL>
$companyId = c8c04cf7-b173-48c3-9127-5917910b4fe6

$addOnLicenseHint = <p><b><em>Bestellbar nur in Verbindung mit der Hauptlizenz.</em></b></p>

$packSdSummary = Der Einstieg in die professionelle Diagnose von Bosch
$packSdDescription = Das <b>Paket ESI[tronic] SD für KTS 250<b> ist der Einstieg in die professionelle Diagnose von Bosch. Es ermöglicht eine kompetente elektrische Diagnose und bietet eine Vielzahl weiterer Funktionen für alle abgedeckten Fahrzeuge
$packSdRestrictedGroupDescription = Das <b>Paket ESI[tronic] SD für KTS 251<b> ist der Einstieg in die professionelle Diagnose von Bosch. Es ermöglicht eine kompetente elektrische Diagnose und bietet eine Vielzahl weiterer Funktionen für alle abgedeckten Fahrzeuge

$packDiagnosticSummary = Der Einstieg in die professionelle Diagnose, Reparatur und Wartung von Bosch
$packDiagnosticDescription = Das Paket <b>ESI[tronic] Diagnostic</b> ist der Einstieg in die professionelle Diagnose, Reparatur und Wartung von Bosch. Es ermöglicht eine kompetente elektrische Diagnose und bietet eine Vielzahl weiterer Funktionen für alle abgedeckten Fahrzeuge. Bekannte Fehler eines Fahrzeugs können schnell und umfassend erkannt und gelöst werden.

$packAdvancedSummary = Die nächste Stufe der professionellen Werkstattausrüstung von Bosch
$packAdvancedDescription = Das Paket <b>ESI[tronic] Advanced</b> ist die nächste Stufe der professionellen Werkstattausrüstung von Bosch. Zusätzlich zum Paket ESI[tronic] Diagnostic sind Anleitungen und Handbücher aus Bosch- und Herstellerquellen enthalten. Connected Repair liefert die Service- und Wartungshistorie sowie Reparaturinformationen und kann jeden Besuch per Smartphone-Kamera dokumentieren.

$packAdvancedMSummary = Beinhaltet das Paket Advanced und zusätzlich die Wartungspläne von Bosch
$packAdvancedMDescription = Das Paket <b>ESI[tronic] Advanced M</b> ist die nächste Stufe der professionellen Werkstattausrüstung von Bosch. Zusätzlich zum Paket ESI[tronic] Diagnostic sind Anleitungen und Handbücher aus Bosch- und Herstellerquellen enthalten. Connected Repair liefert die Service- und Wartungshistorie sowie Reparaturinformationen und kann jeden Besuch per Smartphone-Kamera dokumentieren. Des Weitern sind in diesem Paket die Wartungspläne und ergänzende Informationen integriert.

$packAdvancedPSummary = Beinhaltet das Paket Advanced und zusätzlich die Schaltpläne für Komfortsysteme von Bosch
$packAdvancedPDescription = Das Paket <b>ESI[tronic] Advanced P</b> ist die nächste Stufe der professionellen Werkstattausrüstung von Bosch. Zusätzlich zum Paket ESI[tronic] Diagnostic sind Anleitungen und Handbücher aus Bosch- und Herstellerquellen enthalten. Connected Repair liefert die Service- und Wartungshistorie sowie Reparaturinformationen und kann jeden Besuch per Smartphone-Kamera dokumentieren. Des Weitern sind in diesem Paket die Schaltpläne für Komfortsysteme integriert.

$packMasterSummary = Das vollumfängliche Paket in der professionellen Fahrzeugdiagnose von Bosch
$packMasterDescription = <b>ESI[tronic] 2.0 Master</b> ist das vollumfängliche Paket in der professionellen Fahrzeugdiagnose von Bosch. Es unterstützt bei allen Diagnose-, Reparatur- und Wartungsaufgaben und stellt alle nötigen Informationen und Funktionen für Diagnose, Reparatur, Wartung, Ersatzteile, Dokumentation und Datenmanagement zur Verfügung. Der Technische Support (THL) unterstützt Sie bei der Lösungsfindung zu jeglichen Fahrzeugreparatur-Themen.

$packRepairDieselSummary = Umfassende Informationen zu Ersatzteilen und Anleitungen von Diesel- und Elektrik-Komponenten von Bosch
$packRepairDieselDescription = Das <b>ESI[tronic] 2.0 Komponente Repair Diesel-Paket</b> bietet umfassende Informationen zu Ersatzteilen und Reparatur von Diesel- und Elektrik-Komponenten. Es ermöglicht die richtige Identifikation des Fahrzeugs und der gesamten Bosch-Kfz-Ausrüstung und beinhaltet Reparaturanleitungen sowie Serviceinformationen und -telegramme für Komponenten. Dank der Schritt-für-Schritt Anleitungen und der einfachen und klaren Struktur ist eine professionelle Komponentenreparatur möglich.

$packRepairElektrikSummary = Ersatzteildaten zur Autoelektrik in übersichtlicher Form von Bosch
$packRepairElektrikDescription = Der ständig steigende Umfang an Fahrzeugmodellen erschwert es Werkstätten, laufend aktuelle Informationen für Aggregate der Fahrzeugelektrik parat zu haben. Die <b>ESI[tronic]-E</b> von Bosch unterstützt dabei: Auf ihr finden sich Ersatzteildaten zur Autoelektrik in übersichtlicher Form systematisch erfasst.

$packErsatzteileSummary = Umfassende Informationen zu Ersatzteilen von Diesel und Elektrik von Bosch
$packErsatzteileDescription = Das <b>ESI[tronic] 2.0 Ersatzteile-Katalog-Paket</b> beinhaltet die Anwendungen, Funktionen und Kfz-Ausrüstung sowie die Diesel Ersatzteile und Elektrik Ersatzteile inkl. Archiv und Elektrik Ersatzteil ESI[tronic]-F.

$infoartWSummary = Beinhaltet Informationen zu Diesel-Einspritzpumpen und Prüfwert von Bosch
$infoartWDescription = Die <b>ESI[tronic] 2.0-Infoart W</b> beinhaltet Informationen zu Diesel Prüfwerten für Reihenpumpen-Kombinationen sowie für VE-Pumpen, den kompletten Prüfvorgang von der Messwerteermittlung bis zum Protokollausdruck und die Anzeige der Prüfschritte in optimaler Reihenfolge.

$infoartASummary = Allgemeine Fahrzeuginformationen - umfasst die Anwendungen, Funktionen, sowie die Kfz-Ausrüstung.
$infoartADescription = <b>Die ESI[tronic] Infoart A</b> wichtige Basisinformationen zu allen abgedeckten Fahrzeugen wie Modellreihe, Leistung, Motorkennzeichnung und Antriebsart sowie verbaute Systeme und zugeordnete Informationen. Zugriff auf den kompletten Ersatzteilkatalog von Bosch inklusive Ersatz- und Exchange-Nummern, Serviceinformationen und Montagehinweisen.
$infoartAOnetimeDescription = $infoartADescription <p><em>Hinweis:</em> Bei dieser Version handelt es sich um den einmaligen Kauf der ESI[tronic] Infoart A. Sie erhalten <em>keine</em> Updates.</p>

$infoartTestdataSummary = Umfasst Prüfwerte zu Diesel-Common-Rail-Einspritzpumpen und Injektoren von Bosch
$infoartTestdataDescription = Die <b>ESI[tronic] 2.0-Infoart Testdata (CD)</b> enthält Prüfwerte für Bosch Common Rail Hochdruckpumpen, Common Rail Injektoren und VP 29 / 30 / 44 Verteilereinspritzpumpen.

$infoartZDSummary = Beinhaltet Zexel-Diesel-Ersatzteile
$infoartZDDescription = Die <b>ESI[tronic] 2.0-Infoart ZD Archiv</b> beinhaltet die Zexel-Programm Software, Katalog für Zexel-Dieselerzeugnisse, detaillierte Explosionszeichnungen und Ersatzteil-Listen.

$infoartZWSummary = Beinhaltet Zexel-Diesel-Prüfwerte
$infoartZWDescription = Die <b>ESI[tronic] 2.0-Infoart ZW Archiv</b> beinhaltet die Zexel-Diesel-Aggregate und den kompletten Prüfvorgang von der Messwertermittlung bis zum Protokollausdruck.

$infoartSDSummary = Die professionelle Diagnose von Bosch
$infoartSDDescription = Die Diagnose zeigt verbaute Systeme sowie gespeicherte Fehlercodes und Kilometerstände in einer Übersicht, löscht Fehlercodes, zeigt Ist-Werte an und bereitet diese grafisch auf, aktiviert Stellglieder, setzt Service-Intervalle zurück, führt automatisierte Tests durch, kalibriert Sensoren und Systeme und lernt Komponenten an.
$infoartSDOnetimeDecription = $infoartSDDescription <p><em>Hinweis:</em> Bei dieser Version handelt es sich um den einmaligen Kauf der ESI[tronic] Infoart SD. Sie erhalten <em>keine</em> Updates.</p>

$infoartSDKTSSummary = Die professionelle Diagnose von Bosch
$infoartSDKTSDescription = Die Diagnose mit der <b>ESI[tronic] Infoart SD für KTS 250</b> zeigt verbaute Systeme sowie gespeicherte Fehlercodes und Kilometerstände in einer Übersicht, löscht Fehlercodes, zeigt Ist-Werte an und bereitet diese grafisch auf, aktiviert Stellglieder, setzt Service-Intervalle zurück, führt automatisierte Tests durch, kalibriert Sensoren und Systeme und lernt Komponenten an.
$infoartSDKTSOnetimeSummary = $infoartSDKTSDescription <p><em>Hinweis:</em> Bei dieser Version handelt es sich um den einmaligen Kauf der ESI[tronic] Infoart SD für KTS 250. Sie erhalten <em>keine</em> Updates.</p>

$compacFsa7xxSummary = Die perfekte Lösung für alle Messaufgaben am Fahrzeug
$compacFsa7xxDescription = Mit <b>CompacSoft[plus] für FSA 7xx</b> wird der Komfort für alle Messaufgaben am Fahrzeug durch die menügeführten Prüfschritte, den optionalen fahrzeugspezifischen Sollwerten, sowie der Anzeige der Ist-Werte noch weiter erhöht.

$compacFsa500Summary = Der perfekte Einstieg in die wirtschaftliche Fahrzeugsystem-Analyse und ideal für alle Werkstätten von Bosch
$compacFsa500Description = Die <b>CompacSoft[plus] für FSA 500</b> ist mit voreingestellten Komponententests ausgestattet und kann an vorhandene Systeme angeschlossen, sowie für den schrittweisen Ausbau Ihres Werkstatt-Testsystems genutzt werden.

$thlPkwSummary = Unterstützt Sie bei der Lösungsfindung zu jeglichen Fahrzeugreparatur-Themen.
$thlPkwDescription = Sie benötigen technische Unterstützung beim Warten oder Reparieren eines Pkw oder einfach nur eine zuverlässige zweite Meinung? Dann wenden Sie sich an unser Support-Team der <b>Technischen Hotline (THL)</b>. Es stellt Ihnen schnelle und fundierte Lösungen für fast alle Fahrzeugmarken und -systeme bereit.

$thlTruckSummary = Unterstützt Sie bei der Lösungsfindung an leichten und schweren Nutzfahrzeugen
$thlTruckDescription = Mit einer umfangreichen Abdeckung von Marken, Modellen und Systemen kann das Support-Team der <b>Technischen Hotline (THL)</b> vollständige technische Unterstützung und Unterlagen für unterschiedliche Reparaturen an leichten und schweren Nutzfahrzeugen anbieten.

$thlPkwTruckSummary = Unterstützt Sie bei der Lösungsfindung zu jeglichen Fahrzeugreparatur-Themen. Im Service inbegriffen sind PKW sowie leichten und schweren Nutzfahrzeuge.
$thlPkwTruckDescription = Sie benötigen technische Unterstützung beim Warten oder Reparieren eines Pkw und/oder Truck oder einfach nur eine zuverlässige zweite Meinung? Dann wenden Sie sich an unser Support-Team der <b>Technischen Hotline (THL)</b>. Es stellt Ihnen schnelle und fundierte Lösungen für fast alle Fahrzeugmarken und -systeme bereit. Mit einer umfangreichen Abdeckung von Marken, Modellen und Systemen kann das Support-Team vollständige technische Unterstützung und Unterlagen für unterschiedliche Reparaturen an leichten und schweren Nutzfahrzeugen anbieten

$coreServerSummary = Verbindet die Testgeräte mit der Fahrzeugannahme für schnellere Prozesse in der Werkstatt.
$coreServerDescription = <b>Bosch Connected Repair</b> verbindet die Testgeräte mit der Fahrzeugannahme in der Werkstatt. Service-Aufgaben werden mit dem Kunden direkt am Empfangsschalter definiert und sofort an die Geräte in der Werkstatt übertragen. Die Fahrzeugidentifikation erfolgt nur einmal und die Techniker können sich auf ihre Aufgaben konzentrieren. Alle Testergebnisse und Fotos werden elektronisch dokumentiert und auf den lokal installierten Server zurückgesendet und gespeichert. Alle Mitarbeiter können über die vernetzten Geräte nahtlos und gleichzeitig den Serviceplan live einsehen und den Status jeder Aufgabe unmittelbar abfragen.

$coreOnlineSummary = Profitieren Sie von allen vorhandenen Funktionen von Bosch Connected Repair
$coreOnlineDescription = Rüsten Sie auf <b>Bosch Connected Repair Online</b> auf – Profitieren Sie von allen vorhandenen Funktionen von Bosch Connected Repair sowie der zusätzlichen Möglichkeit, sich mit allen künftigen cloudbasierten Dealer-Management-Systemen zu vernetzen (Zusätzliche Lizenz erforderlich).

$truckSummary = Unterstützt Werkstätten bei der zuverlässigen Diagnose, kompletten Wartung und effizienten Reparatur von allen gängigen leichten und schweren Nutzfahrzeugen, Anhängern, Transportern und Bussen
$truckDescription = Das <b>ESI[tronic] Paket Truck</b> unterstützt Werkstätten bei der zuverlässigen Diagnose, kompletten Wartung und effizienten Reparatur von allen gängigen leichten und schweren Nutzfahrzeugen, Anhängern, Transportern und Bussen. Mit der Steuergeräte-Diagnose können Werkstätten folgende Funktionen verwenden: Fehlerspeicher Lesen/Löschen, Auswahl von Ist-Werten, Betätigen von Stellgliedern, Anlernen von Komponenten, Zurücksetzen von Wartungsintervallen, und technische Informationen über Komponenten. Neben der Steuergeräte-Diagnose enthält die Software auch technische Informationen wie Schaltpläne, Service- und Reparaturanleitungen sowie Systeminformationen verbreiteter Nutzfahrzeughersteller.
$truckOnetimeDescription = $truckDescription <p><em>Hinweis:</em> Bei dieser Version handelt es sich um den einmaligen Kauf der ESI[tronic] Truck. Sie erhalten <em>keine</em> Updates.</p>

$truckUpgradeSummary = Unterstützt Werkstätten bei der zuverlässigen Diagnose, kompletten Wartung und effizienten Reparatur von allen gängigen leichten und schweren Nutzfahrzeugen, Anhängern, Transportern und Bussen
$truckUpgradeDescription = <p><em>Das Paket <b>ESI[tronic] Truck-Upgrade</b> setzt ein ESI[tronic] 2.0 PKW Paket voraus.</em></p> Es unterstützt Werkstätten bei der zuverlässigen Diagnose, kompletten Wartung und effizienten Reparatur von allen gängigen leichten und schweren Nutzfahrzeugen, Anhängern, Transportern und Bussen. Mit der Steuergeräte-Diagnose können Werkstätten folgende Funktionen verwenden: Fehlerspeicher Lesen/Löschen, Auswahl von Ist-Werten, Betätigen von Stellgliedern, Anlernen von Komponenten, Zurücksetzen von Wartungsintervallen, und technische Informationen über Komponenten. Neben der Steuergeräte-Diagnose enthält die Software auch technische Informationen wie Schaltpläne, Service- und Reparaturanleitungen sowie Systeminformationen verbreiteter Nutzfahrzeughersteller.

$ohw1Summary = Informationen zur Diagnose, Wartung und Reparatur von landwirtschaftlich genutzten Fahrzeugen von Bosch
$ohw1Description = In der Diagnose-Software ESI[tronic] 2.0 mit dem <b>Paket Landmaschinen (OHW 1)</b> stehen Werkstätten Informationen zur Diagnose, Wartung und Reparatur von landwirtschaftlich genutzten Fahrzeugen zur Verfügung. Diese Lizenz kann zusammen mit dem Paket Truck oder/und dem Paket Baumaschinen und Motoren (OHW 2), aber auch unabhängig davon verwendet werden. Zusätzlich bietet das Paket Landmaschinen (OHW 1) Einstell- und Parametrierfunktionen sowie Diagnose an Hydrauliksystemen. Außerdem stehen technische Informationen zum Ein- und Ausbau von Komponenten zur Verfügung.

$ohw2Summary = Informationen zur Diagnose, Wartung und Reparatur von Baumaschinen sowie Motoren von Bosch
$ohw2Description = In der Diagnose-Software ESI[tronic] 2.0 mit dem Paket <b>Baumaschinen und Motoren (OHW 2)</b> stehen Werkstätten Informationen zur Diagnose, Wartung und Reparatur von Baumaschinen sowie Motoren zur Verfügung. Diese Lizenz kann zusammen mit dem Paket Truck oder/und dem Paket Landmaschinen (OHW 1), aber auch unabhängig davon verwendet werden. Zusätzlich bietet das Paket Baumaschinen und Motoren (OHW 2) Einstell- und Parametrierfunktionen sowie Diagnose an Hydrauliksystemen. Außerdem stehen technische Informationen zum Ein- und Ausbau von Komponenten zur Verfügung.

$criSummary = Umfasst die Prüfung von Piezo-Injektoren für Common Rail Systeme
$criDescription = Die <b>ESI[tronic] CRI für DCI 700</b> Software liefert aktuelle Daten, sorgt für reibungslose Abläufe und umfasst die Prüfung von Piezo-Injektoren für Common Rail Systeme.

$crinSummary = Umfasst die Prüfung von Magnetventil-Injektoren für Common Rail Systeme
$crinDescription = Die <b>ESI[tronic] CRIN für DCI 700</b> Software liefert aktuelle Daten, sorgt für reibungslose Abläufe und umfasst die Prüfung von Magnetventil-Injektoren für Common Rail Systeme.

$alltrucksSummary = Beinhaltet die ESI[tronic] Truck sowie die Software von NEO | orange und ZF-TESTMAN. Nur für Alltrucks-Konzeptwerkstätten.
$alltrucksDescription = Die <b>Alltrucks Diagnose-Software</b> beinhaltet wichtige Informationen zu den Nutzfahrzeugen wie Modellreihe, Leistung, Motorkennzeichnung sowie Achskonfiguration. Die integrierte Diagnose-Software bestehend aus: ESI[tronic] Truck, NEO | orange von Knorr-Bremse. Die Benutzerführung beinhaltet eine dynamische Komponentenbeschreibun, halbjährliche und jährliche Inspektionen mit zusätzlichen Kontrollpunkten, Komponenten-Informationen mit Sollwerten, Ausgangsspannung, Kennlinien, etc. sowie Zugriff auf den kompletten Ersatzteilkatalog von Bosch und ZF Services inklusive Serviceinformationen und Montagehinweisen.

$esiEula = legal.boschmarketplace.com/boschaftermarket/at/de/EULA_ESItronic_AT_2021-06.pdf
$esiTruckEula = legal.boschmarketplace.com/boschaftermarket/at/de/EULA_ESItronic_AT_Truck_2021-06.pdf
$esiDci700Eula = legal.boschmarketplace.com/boschaftermarket/at/de/EULA_DCI700_AT_2021-06.pdf
$esiAlltrucksEula = legal.boschmarketplace.com/boschaftermarket/at/de/EULA_ESItronic_AT_Alltrucks_2021-06.pdf

# these values will be replaced in migration release_1_22/20-aa-set-brim-name-to-products-in-v1-catalog.impex
$defaultBrimNameDe = Default BRIM Name DE
$defaultBrimNameEn = Default BRIM Name EN

INSERT_UPDATE App; code[unique = true]; packageName         ; name[lang = de]                ; name[lang = en]                ; summary[lang = de]         ; summary[lang = en]         ; description[lang = de]                           ; description[lang = en]                           ; emailAddress[default = $emailAddress]; submittedBy(uid); storeAvailabilityMode(code)[default = PUBLIC]; unit(code)[default = pieces]; variantType(code)[default = AppLicense]; $catalogVersion[unique = true]; $approved; $taxGroup; privacyPolicyUrl[default = $privacyPolicyUrl]; termsOfUseUrl; supportPageUrl; productWebsiteUrl; company(uid)[default = $companyId]
#esi general
                 ; AA_$atCc12398      ; $aaPackageName12398 ; KTS 251 SD Hauptlizenz         ; KTS 251 SD Hauptlizenz         ; $packSdSummary             ; $packSdSummary             ; $packSdRestrictedGroupDescription                ; $packSdRestrictedGroupDescription                ;                                      ;                 ; RESTRICTED_BUYER_GROUP
                 ; AA_$atCc12385      ; $aaPackageName12385 ; KTS 250 SD Hauptlizenz         ; KTS 250 SD Hauptlizenz         ; $packSdSummary             ; $packSdSummary             ; $packSdDescription                               ; $packSdDescription                               ;
                 ; AA_$atCc12395      ; $aaPackageName12395 ; KTS 250 SD Zusatzlizenz        ; KTS 250 SD Zusatzlizenz        ; $packSdSummary             ; $packSdSummary             ; $addOnLicenseHint $packSdDescription             ; $addOnLicenseHint $packSdDescription             ;
                 ; AA_$atCc12820      ; $aaPackageName12820 ; Paket Diagnostic Hauptlizenz   ; Paket Diagnostic Hauptlizenz   ; $packDiagnosticSummary     ; $packDiagnosticSummary     ; $packDiagnosticDescription                       ; $packDiagnosticDescription                       ;
                 ; AA_$atCc12830      ; $aaPackageName12830 ; Paket Diagnostic Zusatzlizenz  ; Paket Diagnostic Zusatzlizenz  ; $packDiagnosticSummary     ; $packDiagnosticSummary     ; $addOnLicenseHint $packDiagnosticDescription     ; $addOnLicenseHint $packDiagnosticDescription     ;
                 ; AA_$atCc12840      ; $aaPackageName12840 ; Paket Advanced Hauptlizenz     ; Paket Advanced Hauptlizenz     ; $packAdvancedSummary       ; $packAdvancedSummary       ; $packAdvancedDescription                         ; $packAdvancedDescription                         ;
                 ; AA_$atCc12860      ; $aaPackageName12860 ; Paket Advanced Zusatzlizenz    ; Paket Advanced Zusatzlizenz    ; $packAdvancedSummary       ; $packAdvancedSummary       ; $addOnLicenseHint $packAdvancedDescription       ; $addOnLicenseHint $packAdvancedDescription       ;
                 ; AA_$atCc12870      ; $aaPackageName12870 ; Paket Advanced M Hauptlizenz   ; Paket Advanced M Hauptlizenz   ; $packAdvancedMSummary      ; $packAdvancedMSummary      ; $packAdvancedMDescription                        ; $packAdvancedMDescription                        ;
                 ; AA_$atCc12880      ; $aaPackageName12880 ; Paket Advanced M Zusatzlizenz  ; Paket Advanced M Zusatzlizenz  ; $packAdvancedMSummary      ; $packAdvancedMSummary      ; $addOnLicenseHint $packAdvancedMDescription      ; $addOnLicenseHint $packAdvancedMDescription      ;
                 ; AA_$atCc12890      ; $aaPackageName12890 ; Paket Advanced P Hauptlizenz   ; Paket Advanced P Hauptlizenz   ; $packAdvancedPSummary      ; $packAdvancedPSummary      ; $packAdvancedPDescription                        ; $packAdvancedPSummary                            ;
                 ; AA_$atCc12900      ; $aaPackageName12900 ; Paket Advanced P Zusatzlizenz  ; Paket Advanced P Zusatzlizenz  ; $packAdvancedPSummary      ; $packAdvancedPSummary      ; $addOnLicenseHint $packAdvancedPDescription      ; $addOnLicenseHint $packAdvancedPSummary          ;
                 ; AA_$atCc12910      ; $aaPackageName12910 ; Paket Master Hauptlizenz       ; Paket Master Hauptlizenz       ; $packMasterSummary         ; $packMasterSummary         ; $packMasterDescription                           ; $packMasterDescription                           ;
                 ; AA_$atCc12920      ; $aaPackageName12920 ; Paket Master Zusatzlizenz      ; Paket Master Zusatzlizenz      ; $packMasterSummary         ; $packMasterSummary         ; $addOnLicenseHint $packMasterDescription         ; $addOnLicenseHint $packMasterDescription         ;
                 ; AA_$atCc12970      ; $aaPackageName12970 ; Paket Repair Diesel Haupt      ; Paket Repair Diesel Haupt      ; $packRepairDieselSummary   ; $packRepairDieselSummary   ; $packRepairDieselDescription                     ; $packRepairDieselDescription                     ;
                 ; AA_$atCc12980      ; $aaPackageName12980 ; Paket Repair Diesel Zusatz     ; Paket Repair Diesel Zusatz     ; $packRepairDieselSummary   ; $packRepairDieselSummary   ; $addOnLicenseHint $packRepairDieselDescription   ; $addOnLicenseHint $packRepairDieselDescription   ;
                 ; AA_$atCc12990      ; $aaPackageName12990 ; Paket Repair Elektrik Haupt    ; Paket Repair Elektrik Haupt    ; $packRepairElektrikSummary ; $packRepairElektrikSummary ; $packRepairElektrikDescription                   ; $packRepairElektrikDescription                   ;
                 ; AA_$atCc12996      ; $aaPackageName12996 ; Paket Repair Elektrik Zusatz   ; Paket Repair Elektrik Zusatz   ; $packRepairElektrikSummary ; $packRepairElektrikSummary ; $addOnLicenseHint $packRepairElektrikDescription ; $addOnLicenseHint $packRepairElektrikDescription ;
                 ; AA_$atCc12998      ; $aaPackageName12998 ; Paket Ersatzteile-Kat. Haupt   ; Paket Ersatzteile-Kat. Haupt   ; $packErsatzteileSummary    ; $packErsatzteileSummary    ; $packErsatzteileDescription                      ; $packErsatzteileDescription                      ;
                 ; AA_$atCc12999      ; $aaPackageName12999 ; Paket Ersatzteile-Kat. Zusatz  ; Paket Ersatzteile-Kat. Zusatz  ; $packErsatzteileSummary    ; $packErsatzteileSummary    ; $addOnLicenseHint $packErsatzteileDescription    ; $addOnLicenseHint $packErsatzteileDescription    ;
                 ; AA_$atCc12410      ; $aaPackageName12410 ; Infoart A Hauptlizenz          ; Infoart A Hauptlizenz          ; $infoartASummary           ; $infoartASummary           ; $infoartADescription                             ; $infoartADescription                             ;
                 ; AA_$atCc12555      ; $aaPackageName12555 ; Infoart A Zusatzlizenz         ; Infoart A Zusatzlizenz         ; $infoartASummary           ; $infoartASummary           ; $addOnLicenseHint $infoartADescription           ; $addOnLicenseHint $infoartADescription           ;
                 ; AA_$atCc12500      ; $aaPackageName12500 ; Infoart W Hauptlizenz          ; Infoart W Hauptlizenz          ; $infoartWSummary           ; $infoartWSummary           ; $infoartWDescription                             ; $infoartWDescription                             ;
                 ; AA_$atCc12640      ; $aaPackageName12640 ; Infoart W Zusatzlizenz         ; Infoart W Zusatzlizenz         ; $infoartWSummary           ; $infoartWSummary           ; $addOnLicenseHint $infoartWDescription           ; $addOnLicenseHint $infoartWDescription           ;
                 ; AA_$atCc15015      ; $aaPackageName15015 ; Infoart Testdata               ; Infoart Testdata               ; $infoartTestdataSummary    ; $infoartTestdataSummary    ; $infoartTestdataDescription                      ; $infoartTestdataDescription                      ;
                 ; AA_$atCc10436      ; $aaPackageName10436 ; Infoart ZD-Archive             ; Infoart ZD-Archive             ; $infoartZDSummary          ; $infoartZDSummary          ; $infoartZDDescription                            ; $infoartZDDescription                            ;
                 ; AA_$atCc10437      ; $aaPackageName10437 ; Infoart ZW-Archive             ; Infoart ZW-Archive             ; $infoartZWSummary          ; $infoartZWSummary          ; $infoartZWDescription                            ; $infoartZWDescription                            ;
                 ; AA_$atCc15045      ; $aaPackageName15045 ; CompacSoft[plus] FSA 7xx       ; CompacSoft[plus] FSA 7xx       ; $compacFsa7xxSummary       ; $compacFsa7xxSummary       ; $compacFsa7xxDescription                         ; $compacFsa7xxDescription                         ;
                 ; AA_$atCc15060      ; $aaPackageName15060 ; CompacSoft[plus] FSA 500       ; CompacSoft[plus] FSA 500       ; $compacFsa500Summary       ; $compacFsa500Summary       ; $compacFsa500Description                         ; $compacFsa500Description                         ;
                 ; AA_$atCc29271      ; $aaPackageName29271 ; Technische Hotline PKW         ; Technische Hotline PKW         ; $thlPkwSummary             ; $thlPkwSummary             ; $thlPkwDescription                               ; $thlPkwDescription                               ;
                 ; AA_$atCc13515      ; $aaPackageName13515 ; Technische Hotline Truck       ; Technische Hotline Truck       ; $thlTruckSummary           ; $thlTruckSummary           ; $thlTruckDescription                             ; $thlTruckDescription                             ;
                 ; AA_$atCc13516      ; $aaPackageName13516 ; Technische Hotline PKW&Truck   ; Technische Hotline PKW&Truck   ; $thlPkwTruckSummary        ; $thlPkwTruckSummary        ; $thlPkwTruckDescription                          ; $thlPkwTruckDescription                          ;
                 ; AA_$atCc15076      ; $aaPackageName15076 ; Bosch Connected Repair Server  ; Bosch Connected Repair Server  ; $coreServerSummary         ; $coreServerSummary         ; $coreServerDescription                           ; $coreServerDescription                           ;
                 ; AA_$atCc15083      ; $aaPackageName15083 ; Bosch Connected Repair Online  ; Bosch Connected Repair Online  ; $coreOnlineSummary         ; $coreOnlineSummary         ; $coreOnlineDescription                           ; $coreOnlineDescription                           ;
                 ; AA_$atCc12050      ; $aaPackageName12050 ; Infoart A Einmalkauf           ; Infoart A Einmalkauf           ; $infoartASummary           ; $infoartASummary           ; $infoartAOnetimeDescription                      ; $infoartAOnetimeDescription                      ;
                 ; AA_$atCc12051      ; $aaPackageName12051 ; Infoart SD Einmalkauf          ; Infoart SD Einmalkauf          ; $infoartSDSummary          ; $infoartSDSummary          ; $infoartSDOnetimeDecription                      ; $infoartSDOnetimeDecription                      ;
                 ; AA_$atCc12389      ; $aaPackageName12389 ; nfoart SD KTS 250 Einmalkauf   ; Infoart SD KTS 250 Einmalkauf  ; $infoartSDKTSSummary       ; $infoartSDKTSSummary       ; $infoartSDKTSOnetimeSummary                      ; $infoartSDKTSOnetimeSummary                      ;
                 ; AA_$atCc12400      ; $aaPackageName12400 ; Paket Truck Hauptlizenz        ; Paket Truck Hauptlizenz        ; $truckSummary              ; $truckSummary              ; $truckDescription                                ; $truckDescription                                ;
                 ; AA_$atCc12405      ; $aaPackageName12405 ; Paket Truck Zusatzlizenz       ; Paket Truck Zusatzlizenz       ; $truckSummary              ; $truckSummary              ; $addOnLicenseHint $truckDescription              ; $addOnLicenseHint $truckDescription              ;
                 ; AA_$atCc12404      ; $aaPackageName12404 ; Paket Truck Upgrade Haupt      ; Paket Truck Upgrade Haupt      ; $truckUpgradeSummary       ; $truckUpgradeSummary       ; $truckUpgradeDescription                         ; $truckUpgradeDescription                         ;
                 ; AA_$atCc12406      ; $aaPackageName12406 ; Paket Truck Upgrade Zusatz     ; Paket Truck Upgrade Zusatz     ; $truckUpgradeSummary       ; $truckUpgradeSummary       ; $addOnLicenseHint $truckUpgradeDescription       ; $addOnLicenseHint $truckUpgradeDescription       ;
                 ; AA_$atCc12260      ; $aaPackageName12260 ; Off Highway I (OHW I) Haupt    ; Off Highway I (OHW I) Haupt    ; $ohw1Summary               ; $ohw1Summary               ; $ohw1Description                                 ; $ohw1Description                                 ;
                 ; AA_$atCc12266      ; $aaPackageName12266 ; Off Highway I (OHW I) Zusatz   ; Off Highway I (OHW I) Zusatz   ; $ohw1Summary               ; $ohw1Summary               ; $addOnLicenseHint $ohw1Description               ; $addOnLicenseHint $ohw1Description               ;
                 ; AA_$atCc12278      ; $aaPackageName12278 ; Off Highway II (OHW II) Haupt  ; Off Highway II (OHW II) Haupt  ; $ohw2Summary               ; $ohw2Summary               ; $ohw2Description                                 ; $ohw2Description                                 ;
                 ; AA_$atCc12284      ; $aaPackageName12284 ; Off Highway II (OHW II) Zusatz ; Off Highway II (OHW II) Zusatz ; $ohw2Summary               ; $ohw2Summary               ; $addOnLicenseHint $ohw2Description               ; $addOnLicenseHint $ohw2Description               ;
                 ; AA_$atCc12412      ; $aaPackageName12412 ; Paket Truck Einmalkauf         ; Paket Truck Einmalkauf         ; $truckSummary              ; $truckSummary              ; $truckOnetimeDescription                         ; $truckOnetimeDescription                         ;
                 ; AA_$atCc12760      ; $aaPackageName12760 ; Alltrucks Hauptlizenz          ; Alltrucks Hauptlizenz          ; $alltrucksSummary          ; $alltrucksSummary          ; $alltrucksDescription                            ; $alltrucksDescription                            ;
                 ; AA_$atCc12765      ; $aaPackageName12765 ; Alltrucks Zusatzlizenz         ; Alltrucks Zusatzlizenz         ; $alltrucksSummary          ; $alltrucksSummary          ; $addOnLicenseHint $alltrucksDescription          ; $addOnLicenseHint $alltrucksDescription          ;
                 ; AA_$atCc15090      ; $aaPackageName15090 ; CRI Bosch Hauptlizenz          ; CRI Bosch Hauptlizenz          ; $criSummary                ; $criSummary                ; $criDescription                                  ; $criDescription                                  ;
                 ; AA_$atCc15095      ; $aaPackageName15095 ; CRI Bosch Zusatzlizenz         ; CRI Bosch Zusatzlizenz         ; $criSummary                ; $criSummary                ; $addOnLicenseHint $criDescription                ; $addOnLicenseHint $criDescription                ;
                 ; AA_$atCc15100      ; $aaPackageName15100 ; CRIN Bosch Hauptlizenz         ; CRIN Bosch Hauptlizenz         ; $crinSummary               ; $crinSummary               ; $crinDescription                                 ; $crinDescription                                 ;
                 ; AA_$atCc15105      ; $aaPackageName15105 ; CRIN Bosch Zusatzlizenz        ; CRIN Bosch Zusatzlizenz        ; $crinSummary               ; $crinSummary               ; $addOnLicenseHint $crinDescription               ; $addOnLicenseHint $crinDescription               ;


INSERT_UPDATE ProductContainer; code[unique = true]; app(code, $catalogVersion); company(uid)[unique = true][default = $companyId];
                              ; pcaa_$atCc12398    ; AA_$atCc12398
                              ; pcaa_$atCc12385    ; AA_$atCc12385
                              ; pcaa_$atCc12395    ; AA_$atCc12395
                              ; pcaa_$atCc12820    ; AA_$atCc12820
                              ; pcaa_$atCc12830    ; AA_$atCc12830
                              ; pcaa_$atCc12840    ; AA_$atCc12840
                              ; pcaa_$atCc12860    ; AA_$atCc12860
                              ; pcaa_$atCc12870    ; AA_$atCc12870
                              ; pcaa_$atCc12880    ; AA_$atCc12880
                              ; pcaa_$atCc12890    ; AA_$atCc12890
                              ; pcaa_$atCc12900    ; AA_$atCc12900
                              ; pcaa_$atCc12910    ; AA_$atCc12910
                              ; pcaa_$atCc12920    ; AA_$atCc12920
                              ; pcaa_$atCc12970    ; AA_$atCc12970
                              ; pcaa_$atCc12980    ; AA_$atCc12980
                              ; pcaa_$atCc12990    ; AA_$atCc12990
                              ; pcaa_$atCc12996    ; AA_$atCc12996
                              ; pcaa_$atCc12998    ; AA_$atCc12998
                              ; pcaa_$atCc12999    ; AA_$atCc12999
                              ; pcaa_$atCc12410    ; AA_$atCc12410
                              ; pcaa_$atCc12555    ; AA_$atCc12555
                              ; pcaa_$atCc12500    ; AA_$atCc12500
                              ; pcaa_$atCc12640    ; AA_$atCc12640
                              ; pcaa_$atCc15015    ; AA_$atCc15015
                              ; pcaa_$atCc10436    ; AA_$atCc10436
                              ; pcaa_$atCc10437    ; AA_$atCc10437
                              ; pcaa_$atCc15045    ; AA_$atCc15045
                              ; pcaa_$atCc15060    ; AA_$atCc15060
                              ; pcaa_$atCc29271    ; AA_$atCc29271
                              ; pcaa_$atCc13515    ; AA_$atCc13515
                              ; pcaa_$atCc13516    ; AA_$atCc13516
                              ; pcaa_$atCc15076    ; AA_$atCc15076
                              ; pcaa_$atCc15083    ; AA_$atCc15083
                              ; pcaa_$atCc12050    ; AA_$atCc12050
                              ; pcaa_$atCc12051    ; AA_$atCc12051
                              ; pcaa_$atCc12389    ; AA_$atCc12389
                              ; pcaa_$atCc12400    ; AA_$atCc12400
                              ; pcaa_$atCc12405    ; AA_$atCc12405
                              ; pcaa_$atCc12404    ; AA_$atCc12404
                              ; pcaa_$atCc12406    ; AA_$atCc12406
                              ; pcaa_$atCc12260    ; AA_$atCc12260
                              ; pcaa_$atCc12266    ; AA_$atCc12266
                              ; pcaa_$atCc12278    ; AA_$atCc12278
                              ; pcaa_$atCc12284    ; AA_$atCc12284
                              ; pcaa_$atCc12412    ; AA_$atCc12412
                              ; pcaa_$atCc12760    ; AA_$atCc12760
                              ; pcaa_$atCc12765    ; AA_$atCc12765
                              ; pcaa_$atCc15090    ; AA_$atCc15090
                              ; pcaa_$atCc15095    ; AA_$atCc15095
                              ; pcaa_$atCc15100    ; AA_$atCc15100
                              ; pcaa_$atCc15105    ; AA_$atCc15105


# licenses
INSERT_UPDATE AppLicense; code[unique = true]; $baseProduct  ; sellerProductId; specifiedPrice; licenseType(code)[default = SUBSCRIPTION]; billingSystemStatus(code)[default = IN_SYNC]; unit(code)[default = pieces]; $catalogVersion[unique = true]; $approved; $taxGroup; enabledCountries(isocode)[default = 'AT']; availabilityStatus(code)[default = PUBLISHED]; brimName[lang = de, default = $defaultBrimNameDe]; brimName[lang = en, default = $defaultBrimNameEn]; userGroups(uid);
                        ; AA_$atCc12398_sub  ; AA_$atCc12398 ; 1 987 P12 398  ; 888           ;
                        ; AA_$atCc12385_sub  ; AA_$atCc12385 ; 1 987 P12 385  ; 645           ;
                        ; AA_$atCc12395_sub  ; AA_$atCc12395 ; 1 987 P12 395  ; 50            ;
                        ; AA_$atCc12820_sub  ; AA_$atCc12820 ; 1 987 P12 820  ; 705           ;
                        ; AA_$atCc12830_sub  ; AA_$atCc12830 ; 1 987 P12 830  ; 50            ;
                        ; AA_$atCc12840_sub  ; AA_$atCc12840 ; 1 987 P12 840  ; 1280          ;
                        ; AA_$atCc12860_sub  ; AA_$atCc12860 ; 1 987 P12 860  ; 50            ;
                        ; AA_$atCc12870_sub  ; AA_$atCc12870 ; 1 987 P12 870  ; 1580          ;
                        ; AA_$atCc12880_sub  ; AA_$atCc12880 ; 1 987 P12 880  ; 50            ;
                        ; AA_$atCc12890_sub  ; AA_$atCc12890 ; 1 987 P12 890  ; 1580          ;
                        ; AA_$atCc12900_sub  ; AA_$atCc12900 ; 1 987 P12 900  ; 50            ;
                        ; AA_$atCc12910_sub  ; AA_$atCc12910 ; 1 987 P12 910  ; 1885          ;
                        ; AA_$atCc12920_sub  ; AA_$atCc12920 ; 1 987 P12 920  ; 50            ;
                        ; AA_$atCc12970_sub  ; AA_$atCc12970 ; 1 987 P12 970  ; 664           ;
                        ; AA_$atCc12980_sub  ; AA_$atCc12980 ; 1 987 P12 980  ; 50            ;
                        ; AA_$atCc12990_sub  ; AA_$atCc12990 ; 1 987 P12 990  ; 220           ;
                        ; AA_$atCc12996_sub  ; AA_$atCc12996 ; 1 987 P12 996  ; 50            ;
                        ; AA_$atCc12998_sub  ; AA_$atCc12998 ; 1 987 P12 998  ; 360           ;
                        ; AA_$atCc12999_sub  ; AA_$atCc12999 ; 1 987 P12 999  ; 50            ;
                        ; AA_$atCc12410_sub  ; AA_$atCc12410 ; 1 987 P12 410  ; 1             ;
                        ; AA_$atCc12555_sub  ; AA_$atCc12555 ; 1 987 P12 555  ; 1             ;
                        ; AA_$atCc12500_sub  ; AA_$atCc12500 ; 1 987 P12 500  ; 679           ;
                        ; AA_$atCc12640_sub  ; AA_$atCc12640 ; 1 987 P12 640  ; 50            ;
                        ; AA_$atCc15015_sub  ; AA_$atCc15015 ; 1 687 P15 015  ; 304           ;
                        ; AA_$atCc10436_sub  ; AA_$atCc10436 ; 1 987 P10 436  ; 90            ;
                        ; AA_$atCc10437_sub  ; AA_$atCc10437 ; 1 987 P10 437  ; 200           ;
                        ; AA_$atCc15045_sub  ; AA_$atCc15045 ; 1 687 P15 045  ; 310           ;
                        ; AA_$atCc15060_sub  ; AA_$atCc15060 ; 1 687 P15 060  ; 1             ;
                        ; AA_$atCc29271_sub  ; AA_$atCc29271 ; 1 987 729 271  ; 410           ;
                        ; AA_$atCc13515_sub  ; AA_$atCc13515 ; 1 987 P13 515  ; 605           ;
                        ; AA_$atCc13516_sub  ; AA_$atCc13516 ; 1 987 P13 516  ; 815           ;
                        ; AA_$atCc15076_sub  ; AA_$atCc15076 ; 1 687 P15 076  ; 255           ;
                        ; AA_$atCc15083_sub  ; AA_$atCc15083 ; 1 687 P15 083  ; 100           ;
                        ; AA_$atCc12050_full ; AA_$atCc12050 ; 1 987 P12 050  ; 1             ; "FULL"
                        ; AA_$atCc12051_full ; AA_$atCc12051 ; 1 987 P12 051  ; 1360          ; "FULL"
                        ; AA_$atCc12389_full ; AA_$atCc12389 ; 1 987 P12 389  ; 1360          ; "FULL"
                        ; AA_$atCc12400_sub  ; AA_$atCc12400 ; 1 987 P12 400  ; 998           ;
                        ; AA_$atCc12405_sub  ; AA_$atCc12405 ; 1 987 P12 405  ; 50            ;
                        ; AA_$atCc12404_sub  ; AA_$atCc12404 ; 1 987 P12 404  ; 950           ;
                        ; AA_$atCc12406_sub  ; AA_$atCc12406 ; 1 987 P12 406  ; 50            ;
                        ; AA_$atCc12260_sub  ; AA_$atCc12260 ; 1 987 P12 260  ; 385           ;
                        ; AA_$atCc12266_sub  ; AA_$atCc12266 ; 1 987 P12 266  ; 50            ;
                        ; AA_$atCc12278_sub  ; AA_$atCc12278 ; 1 987 P12 278  ; 770           ;
                        ; AA_$atCc12284_sub  ; AA_$atCc12284 ; 1 987 P12 284  ; 50            ;
                        ; AA_$atCc12412_full ; AA_$atCc12412 ; 1 987 P12 412  ; 2890          ; "FULL"
                        ; AA_$atCc12760_sub  ; AA_$atCc12760 ; 1 987 P12 760  ; 2150          ;
                        ; AA_$atCc12765_sub  ; AA_$atCc12765 ; 1 987 P12 765  ; 20            ;
                        ; AA_$atCc15090_sub  ; AA_$atCc15090 ; 1 687 P15 090  ; 300           ;
                        ; AA_$atCc15095_sub  ; AA_$atCc15095 ; 1 687 P15 095  ; 50            ;
                        ; AA_$atCc15100_sub  ; AA_$atCc15100 ; 1 687 P15 100  ; 300           ;
                        ; AA_$atCc15105_sub  ; AA_$atCc15105 ; 1 687 P15 105  ; 50            ;


# prices EUR
INSERT_UPDATE PriceRow; product(code, $catalogVersion)[unique = true]; price; currency(isocode)[unique = true, default = EUR]; unit(code[unique = true, default = pieces]); minqtd[default = 1]; unitFactor[default = 1]; net[default = true]
                      ; AA_$atCc12398_sub                            ; 888
                      ; AA_$atCc12385_sub                            ; 645
                      ; AA_$atCc12395_sub                            ; 50
                      ; AA_$atCc12820_sub                            ; 705
                      ; AA_$atCc12830_sub                            ; 50
                      ; AA_$atCc12840_sub                            ; 1280
                      ; AA_$atCc12860_sub                            ; 50
                      ; AA_$atCc12870_sub                            ; 1580
                      ; AA_$atCc12880_sub                            ; 50
                      ; AA_$atCc12890_sub                            ; 1580
                      ; AA_$atCc12900_sub                            ; 50
                      ; AA_$atCc12910_sub                            ; 1885
                      ; AA_$atCc12920_sub                            ; 50
                      ; AA_$atCc12970_sub                            ; 664
                      ; AA_$atCc12980_sub                            ; 50
                      ; AA_$atCc12990_sub                            ; 220
                      ; AA_$atCc12996_sub                            ; 50
                      ; AA_$atCc12998_sub                            ; 360
                      ; AA_$atCc12999_sub                            ; 50
                      ; AA_$atCc12410_sub                            ; 1
                      ; AA_$atCc12555_sub                            ; 1
                      ; AA_$atCc12500_sub                            ; 679
                      ; AA_$atCc12640_sub                            ; 50
                      ; AA_$atCc15015_sub                            ; 304
                      ; AA_$atCc10436_sub                            ; 90
                      ; AA_$atCc10437_sub                            ; 200
                      ; AA_$atCc15045_sub                            ; 310
                      ; AA_$atCc15060_sub                            ; 1
                      ; AA_$atCc29271_sub                            ; 410
                      ; AA_$atCc13515_sub                            ; 605
                      ; AA_$atCc13516_sub                            ; 815
                      ; AA_$atCc15076_sub                            ; 255
                      ; AA_$atCc15083_sub                            ; 100
                      ; AA_$atCc12050_full                           ; 1
                      ; AA_$atCc12051_full                           ; 1360
                      ; AA_$atCc12389_full                           ; 1360
                      ; AA_$atCc12400_sub                            ; 998
                      ; AA_$atCc12405_sub                            ; 50
                      ; AA_$atCc12404_sub                            ; 950
                      ; AA_$atCc12406_sub                            ; 50
                      ; AA_$atCc12260_sub                            ; 385
                      ; AA_$atCc12266_sub                            ; 50
                      ; AA_$atCc12278_sub                            ; 770
                      ; AA_$atCc12284_sub                            ; 50
                      ; AA_$atCc12412_full                           ; 2890
                      ; AA_$atCc12760_sub                            ; 2150
                      ; AA_$atCc12765_sub                            ; 20
                      ; AA_$atCc15090_sub                            ; 300
                      ; AA_$atCc15095_sub                            ; 50
                      ; AA_$atCc15100_sub                            ; 300
                      ; AA_$atCc15105_sub                            ; 50


# prices USD
INSERT_UPDATE PriceRow; product(code, $catalogVersion)[unique = true]; price; currency(isocode)[unique = true, default = USD]; unit(code[unique = true, default = pieces]); minqtd[default = 1]; unitFactor[default = 1]; net[default = true]
                      ; AA_$atCc12398_sub                            ; 888
                      ; AA_$atCc12385_sub                            ; 645
                      ; AA_$atCc12395_sub                            ; 50
                      ; AA_$atCc12820_sub                            ; 705
                      ; AA_$atCc12830_sub                            ; 50
                      ; AA_$atCc12840_sub                            ; 1280
                      ; AA_$atCc12860_sub                            ; 50
                      ; AA_$atCc12870_sub                            ; 1580
                      ; AA_$atCc12880_sub                            ; 50
                      ; AA_$atCc12890_sub                            ; 1580
                      ; AA_$atCc12900_sub                            ; 50
                      ; AA_$atCc12910_sub                            ; 1885
                      ; AA_$atCc12920_sub                            ; 50
                      ; AA_$atCc12970_sub                            ; 664
                      ; AA_$atCc12980_sub                            ; 50
                      ; AA_$atCc12990_sub                            ; 220
                      ; AA_$atCc12996_sub                            ; 50
                      ; AA_$atCc12998_sub                            ; 360
                      ; AA_$atCc12999_sub                            ; 50
                      ; AA_$atCc12410_sub                            ; 1
                      ; AA_$atCc12555_sub                            ; 1
                      ; AA_$atCc12500_sub                            ; 679
                      ; AA_$atCc12640_sub                            ; 50
                      ; AA_$atCc15015_sub                            ; 304
                      ; AA_$atCc10436_sub                            ; 90
                      ; AA_$atCc10437_sub                            ; 200
                      ; AA_$atCc15045_sub                            ; 310
                      ; AA_$atCc15060_sub                            ; 1
                      ; AA_$atCc29271_sub                            ; 410
                      ; AA_$atCc13515_sub                            ; 605
                      ; AA_$atCc13516_sub                            ; 815
                      ; AA_$atCc15076_sub                            ; 255
                      ; AA_$atCc15083_sub                            ; 100
                      ; AA_$atCc12050_full                           ; 1
                      ; AA_$atCc12051_full                           ; 1360
                      ; AA_$atCc12389_full                           ; 1360
                      ; AA_$atCc12400_sub                            ; 998
                      ; AA_$atCc12405_sub                            ; 50
                      ; AA_$atCc12404_sub                            ; 950
                      ; AA_$atCc12406_sub                            ; 50
                      ; AA_$atCc12260_sub                            ; 385
                      ; AA_$atCc12266_sub                            ; 50
                      ; AA_$atCc12278_sub                            ; 770
                      ; AA_$atCc12284_sub                            ; 50
                      ; AA_$atCc12412_full                           ; 2890
                      ; AA_$atCc12760_sub                            ; 2150
                      ; AA_$atCc12765_sub                            ; 20
                      ; AA_$atCc15090_sub                            ; 300
                      ; AA_$atCc15095_sub                            ; 50
                      ; AA_$atCc15100_sub                            ; 300
                      ; AA_$atCc15105_sub                            ; 50

$matSDSDADescription = Zeigt verbaute Systeme sowie gespeicherte Fehlercodes incl SDA und Kilometerstände in einer Übersicht, löscht Fehlercodes, zeigt Ist-Werte an und bereitet diese grafisch auf, aktiviert Stellglieder, setzt Service-Intervalle zurück, führt automatisierte Tests durch, kalibriert Sensoren und Systeme und lernt Komponenten an.
$matSDDescription = Zeigt verbaute Systeme sowie gespeicherte Fehlercodes und Kilometerstände in einer Übersicht, löscht Fehlercodes, zeigt Ist-Werte an und bereitet diese grafisch auf, aktiviert Stellglieder, setzt Service-Intervalle zurück, führt automatisierte Tests durch, kalibriert Sensoren und Systeme und lernt Komponenten an.
$matADescription = Umfasst die Anwendungen, Funktionen, sowie die KfZ-Ausrüstung.
$matEBRDescription = Enthält eine Beschreibung bekannter Fehler am individuellen Fahrzeug, sortiert nach System (ABS, Airbag, Motorsteuerung usw.) und nach Symptomen. Jeder Fehler wird mit seiner Ursache beschrieben, und es wird angegeben, welche Maßnahmen für eine Reparatur empfohlen werden.
$matCoreServerDescription = Eine Software, die Werkstattausrüstung, Fahrzeugdaten und  Reparaturdaten verbindet. Ob Fehlerfälle oder die Speicherung von Daten und Bildern.
$matCoreDescription = Eine Lizenz zur Anbindung an Online Syteme.
$matSisDescription = Enthält Informationen und Fehlersuchanleitungen des SIS / CAS, sowohl fahrzeugspezifische als auch allgemeine Informationen sowie technische Service-Bulletins des Herstellers.
$matMDescription = Enthält Servicepläne, Serviceabbildungen, manueller Service-Reset, Reset der Servicelampe, technische Daten, Wechsel des Zahnriemens und Programmierung der Schlüssel.
$matPDescription = Enthält Systeme wie Klimaanlage, Alarmanlage, Zentralverriegelung, Sitzheizung, Beleuchtung und Lampen, Schiebedach, elektrische Fensterheber, Radio, manuelle Service-Rückstellung usw. Für Systeme, die nicht im SIS enthalten sind.
$matTHLDescription = Unterstützt Sie bei der Lösungsfindung zu jeglichen Fahrzeugreparatur-Themen. Unsere Support Mitarbeiter verfügen über umfassende praktische Erfahrung in der Fahrzeugtechnologie, somit werden bei komplexen Systemen Probleme schnell und kompetent gelöst.
$matEDescription = Umfassende Informationen für Bosch-Elektrikerzeugnisse, incl. Ersatzteillisten, Einzelteilen, sowie Explosionszeichnungen.
$matK2Description = Reparaturanleitungen, Serviceinformationen für Komponenten aus dem Bereich Elektrik.
$matK3Description = Reparaturanleitungen, Serviceinformationen für Komponenten aus den Bereichen Diesel und Elektrik.
$matDDescription = Umfassende Informationen für Bosch-Dieselerzeugnisse, incl. Ersatzteillisten, Einzelteilen, sowie Explosionszeichnungen.
$matWDescription = Beinhaltet Prüfwerte von Reihenpumpen und VE-Pumpen, komplette Prüfvorgänge, Anzeige der Prüfschritte.
$matTrFZDescription = Wichtige Basisinformationen zu den Nutzfahrzeugen wie Modellreihe, Leistung, Motorkennzeichnung sowie Achskonfiguration bilden die Grundlage für eine fundierte Diagnose in der Nutzfahrzeug-Werkstatt
$matTrDDescription = Die Software liest Fehlercodes und kann diese löschen, wählt Ist-Werte aus, aktiviert Stellglieder, setzt Service-Intervalle zurück und lernt Komponenten an
$matTrSpDescription = Die intuitive Benutzerführung mit dynamischer Komponentenbeschreibung sorgt für eine schnelle und einfache Nutzung
$matTrWtDescription = Halbjährliche und jährliche Inspektionen mit zusätzlichen Kontrollpunkten sind jederzeit abrufbar
$matTrTDDescription = Komponenten-Informationen mit Sollwerten, Ausgangsspannung, Kennlinien, etc. unterstützen bei der Fehlersuche
$matTrETKDescription = Zugriff auf den kompletten Ersatzteilkatalog von Bosch und ZF Services inklusive Serviceinformationen und Montagehinweisen der Marken Sachs, Lemförder, Boge und ZF Parts
$matTrAtDDescription = Die integrierte Diagnose-Software bestehend aus: ESI[tronic] Truck, NEO I orange von Knorr-Bremse.
$matOHW1Description = Diagnosesoftware für Landmaschinen
$matOHW2Description = Diagnosesoftware für Baumaschinen und Sondermotoren
$matCRIDescription = Prüfung von Piezo-Injektoren für Common Rail Systeme
$matCRINDescription = Prüfung von Magnetventil-Injektoren für Common Rail Systeme

$matTestdataDescription = Umfasst Diesel-Einspritzpumpen, sowie Prüfwerte, VP-M, CP
$matZDDescription = Katalog für Zexel-Dieselerzeugnisse, Explosionszeichnungen und Ersatzteil-Listen
$matZWDescription = Prüfwerte von Zexel-Diesel-Aggregate, kompletter Prüfvorgang
$matCSFSA7Description = Fahrzeugsystem-Analyse für FSA 7xx
$matCSFSA5Description = Fahrzeugsystem-Analyse für FSA 500

INSERT_UPDATE Material; code[unique = true]  ; name[lang = de]                                       ; name[lang = en]                                            ; description[lang = de]    ; videoUrl[lang = de]
                      ; MAT_$atCcSDSDA       ; SD incl. SDA (Diagnose)                               ; SD incl. SDA (Diagnosis)                                   ; $matSDSDADescription      ; https://www.youtube.com/watch?v=uWV9dySPUcs
                      ; MAT_$atCcA           ; A (Allgemeine Fahrzeuginformationen)                  ; A (General vehicle information)                            ; $matADescription          ;
                      ; MAT_$atCcSD          ; SD (Diagnose)                                         ; SD (Diagnosis)                                             ; $matSDDescription         ; https://www.youtube.com/watch?v=uWV9dySPUcs
                      ; MAT_$atCcEBR         ; EBR (Erfahrungsbasierte Reparatur)                    ; EBR (experience-based-repair)                              ; $matEBRDescription        ; https://www.youtube.com/watch?v=mWKh2gnCGH0
                      ; MAT_$atCcCoRe        ; CoRe (Bosch Connected Repair)                         ; CoRe (Bosch Connected Repair)                              ; $matCoreDescription       ;
                      ; MAT_$atCcCoReS       ; CoRe-Server (Bosch Connected Repair Serverlizenz)     ; CoRe-Server (Bosch Connected Repair Server)                ; $matCoreServerDescription ;
                      ; MAT_$atCcSIS         ; SIS (Technische Handbücher)                           ; SIS                                                        ; $matSisDescription        ; https://www.youtube.com/watch?v=wfIn3N9Ktk4
                      ; MAT_$atCcM           ; M (Wartungs- und Serviceinformationen)                ; M (Maintenance Schedules and Information)                  ; $matMDescription          ; https://www.youtube.com/watch?v=mvZK6wnsM4Q
                      ; MAT_$atCcP           ; P (Fahrzeugspezifische Schaltpläne - Zusatzsysteme)   ; P (Vehicle-specific circuit diagrams - additional systems) ; $matPDescription          ; https://www.youtube.com/watch?v=SkNHNA5hlRc
                      ; MAT_$atCcTHLPKW      ; PKW-THL (Technischer Support PKW)                     ; THL-PKW (Technical Hotline Cars)                           ; $matTHLDescription        ;
                      ; MAT_$atCcTHLTruck    ; THL-Truck (Technischer Support Truck)                 ; THL-Truck (Technical Hotline Truck)                        ; $matTHLDescription        ;
                      ; MAT_$atCcTHLPKWTruck ; THL-PKW und Truck (Technischer Support PKW und Truck) ; THL-PKW and Truck (Technical Hotline Car + Truck)          ; $matTHLDescription        ;
                      ; MAT_$atCcE           ; E (Elektrik-Ersatzteile)                              ; E (Electrical Spare Parts)                                 ; $matEDescription          ;
                      ; MAT_$atCcK2          ; K2 (Komponenten-Reparaturanleitungen K2)              ; K2 (Components repair instructions K3)                     ; $matK2Description         ;
                      ; MAT_$atCcK3          ; K3 (Komponenten-Reparaturanleitungen K3)              ; K3 (Components repair instructions K3)                     ; $matK3Description         ;
                      ; MAT_$atCcD           ; D (Diesel Ersatzteile)                                ; D (Diesel Spare Parts)                                     ; $matDDescription          ;
                      ; MAT_$atCcW           ; W (Diesel Einspritzpumpen Prüfwerte)                  ; W (Diesel Einspritzpumpen Prüfwerte)                       ; $matWDescription          ;
                      ; MAT_$atCcTrFZ        ; Fahrzeuginformation                                   ; Vehicle Information                                        ; $matTrFZDescription       ;
                      ; MAT_$atCcTrD         ; Diagnose                                              ; Diagnosis                                                  ; $matTrDDescription        ;
                      ; MAT_$atCcTrSp        ; Schaltpläne                                           ; Circuit diagrams                                           ; $matTrSpDescription       ;
                      ; MAT_$atCcTrWt        ; Wartung                                               ; Maintenance                                                ; $matTrWtDescription       ;
                      ; MAT_$atCcTrTD        ; Technische Daten                                      ; Technical data                                             ; $matTrTDDescription       ;
                      ; MAT_$atCcTrETK       ; Ersatzteilkatalog                                     ; Spare parts catalog                                        ; $matTrETKDescription      ;
                      ; MAT_$atCcTrAtD       ; Alltrucks Diagnose                                    ; Alltrucks Diagnosis                                        ; $matTrAtDDescription      ;
                      ; MAT_$atCcOHW1        ; OHW I (Off Highway I)                                 ; OHW I (Off Highway I)                                      ; $matOHW1Description       ;
                      ; MAT_$atCcOHW2        ; OHW II (Off Highway II)                               ; OHW II (Off Highway II)                                    ; $matOHW2Description       ;
                      ; MAT_$atCcCRI         ; CRI                                                   ; CRI                                                        ; $matCRIDescription        ;
                      ; MAT_$atCcCRIN        ; CRIN                                                  ; CRIN                                                       ; $matCRINDescription       ;
                      ; MAT_$atCcTestdata    ; Testdata                                              ; Testdata                                                   ; $matTestdataDescription   ;
                      ; MAT_$atCcZD          ; ZD Archiv                                             ; ZD Archiv                                                  ; $matZDDescription         ;
                      ; MAT_$atCcZW          ; ZW Archiv                                             ; ZW Archiv                                                  ; $matZWDescription         ;
                      ; MAT_$atCcCSFSA7      ; CompacSoft[plus] für FSA 7xx                          ; CompacSoft[plus] FSA 7xx                                   ; $matCSFSA7Description     ;
                      ; MAT_$atCcCSFSA5      ; CompacSoft[plus] für FSA 500                          ; CompacSoft[plus] FSA 500                                   ; $matCSFSA5Description     ;
                      ; MAT_$atCcTSB         ; Technische Service Info                               ; Technical Service Info                                     ;

$matADescription_en = Includes the applications, functions, as well as the motor vehicle equipment.
$matSDSDADescription_en = Displays installed systems as well as stored fault codes incl. SDA and mileage in an overview, deletes fault codes, displays actual values and prepares them graphically, activates actuators, resets service intervals, performs automated tests, calibrates sensors and systems and learns components.
$matSDDescription_en = Displays installed systems as well as stored fault codes incl. SDA and mileage in an overview, deletes fault codes, displays actual values and prepares them graphically, activates actuators, resets service intervals, performs automated tests, calibrates sensors and systems and learns components.
$matEBRDescription_en = Contains a description of known faults on the individual vehicle, sorted by system (ABS, airbag, engine management, etc.) and by symptoms. Each fault is described with its cause and it is indicated which measures are recommended for a repair.

$matSisDescription_en = Contains SIS / CAS information and troubleshooting guides, both vehicle-specific and general information, as well as manufacturer's technical service bulletins.

$matCoreDescription_en = A licence for connection to online systems.
$matMDescription_en = Contains service schedules, service figures, manual service reset, service lamp reset, specifications, timing belt replacement and key programming.
$matPDescription_en = Includes systems such as air conditioning, alarm system, central locking, seat heating, lighting and lamps, sun roof, electric window lifters, radio, manual service reset, etc. For systems not included in the SIS.
$matTHLDescription_en = Supports you in finding solutions for any vehicle repair topic. Our support staff have extensive practical experience in vehicle technology, which means that problems with complex systems are solved quickly and competently.
$matEDescription_en = Comprehensive information for Bosch electrical products, incl. Spare parts lists, individual parts and exploded views.
$matK2Description_en = Repair instructions, service information for electrical components.
$matK3Description_en = Repair instructions, service information for diesel and electrical components.
$matDDescription_en = Comprehensive information for Bosch diesel products, incl. Spare parts lists, individual parts and exploded views.

$matWDescription_en = Beinhaltet Prüfwerte von Reihenpumpen und VE-Pumpen, komplette Prüfvorgänge, Anzeige der Prüfschritte.
$matTrFZDescription_en = Important basic information about the commercial vehicles, such as model series, performance, engine identification and axle configuration, form the basis for a sound diagnosis in the commercial vehicle workshop
$matTrDDescription_en = The software reads fault codes and can delete them, selects actual values, activates actuators, resets service intervals and learns components
$matTrSpDescription_en = The intuitive user guidance with dynamic component description ensures quick and easy use
$matTrWtDescription_en = Semi-annual and annual inspections with additional control points can be called up at any time
$matTrTDDescription_en = Component information with set values, output voltage, characteristic curves, etc. support troubleshooting
$matTrETKDescription_en = Access to the complete spare parts catalog from Bosch and ZF Services including service information and installation instructions for the Sachs, Lemförder, Boge and ZF Parts brands
$matTrAtDDescription_en = Die integrierte Diagnose-Software bestehend aus: ESI[tronic] Truck, NEO I orange von Knorr-Bremse.

UPDATE Material; code[unique = true]  ; description[lang = en]  ; videoUrl[lang = en]
               ; MAT_$atCcSDSDA       ; $matSDSDADescription_en ;
               ; MAT_$atCcA           ; $matADescription_en     ;
               ; MAT_$atCcSD          ; $matSDDescription_en    ;
               ; MAT_$atCcEBR         ; $matEBRDescription_en   ; https://www.youtube.com/watch?v=UL4heA174N0
               ; MAT_$atCcCoRe        ; $matCoreDescription_en  ;
               ; MAT_$atCcSIS         ; $matSisDescription_en   ;
               ; MAT_$atCcM           ; $matMDescription_en     ; https://www.youtube.com/watch?v=QxRfNpT_psw
               ; MAT_$atCcP           ; $matPDescription_en     ;
               ; MAT_$atCcTHLPKW      ; $matTHLDescription_en   ;
               ; MAT_$atCcTHLTruck    ; $matTHLDescription_en   ;
               ; MAT_$atCcTHLPKWTruck ; $matTHLDescription_en   ;
               ; MAT_$atCcE           ; $matEDescription_en     ;
               ; MAT_$atCcK2          ; $matK2Description_en    ;
               ; MAT_$atCcK3          ; $matK3Description_en    ;
               ; MAT_$atCcD           ; $matDDescription_en     ;
               ; MAT_$atCcW           ; $matWDescription_en     ;
               ; MAT_$atCcTrFZ        ; $matTrFZDescription_en  ;
               ; MAT_$atCcTrD         ; $matTrDDescription_en   ;
               ; MAT_$atCcTrSp        ; $matTrSpDescription_en  ;
               ; MAT_$atCcTrWt        ; $matTrWtDescription_en  ;
               ; MAT_$atCcTrTD        ; $matTrTDDescription_en  ;
               ; MAT_$atCcTrETK       ; $matTrETKDescription_en ;
               ; MAT_$atCcTrAtD       ; $matTrAtDDescription_en ;


INSERT_UPDATE App; code[unique = true]; boms(code)                                                                                              ; $catalogVersion[unique = true];
                 ; AA_$atCc12398      ; MAT_$atCcSDSDA
                 ; AA_$atCc12385      ; MAT_$atCcSDSDA
                 ; AA_$atCc12395      ; MAT_$atCcSDSDA
                 ; AA_$atCc12820      ; MAT_$atCcA,MAT_$atCcSDSDA,MAT_$atCcEBR                                                                  ;
                 ; AA_$atCc12830      ; MAT_$atCcA,MAT_$atCcSDSDA,MAT_$atCcEBR                                                                  ;
                 ; AA_$atCc12840      ; MAT_$atCcA,MAT_$atCcSDSDA,MAT_$atCcEBR,MAT_$atCcSIS,MAT_$atCcCoRe                                       ;
                 ; AA_$atCc12860      ; MAT_$atCcA,MAT_$atCcSDSDA,MAT_$atCcEBR,MAT_$atCcSIS,MAT_$atCcCoRe                                       ;
                 ; AA_$atCc12870      ; MAT_$atCcA,MAT_$atCcSDSDA,MAT_$atCcEBR,MAT_$atCcSIS,MAT_$atCcCoRe,MAT_$atCcM                            ;
                 ; AA_$atCc12880      ; MAT_$atCcA,MAT_$atCcSDSDA,MAT_$atCcEBR,MAT_$atCcSIS,MAT_$atCcCoRe,MAT_$atCcM                            ;
                 ; AA_$atCc12890      ; MAT_$atCcA,MAT_$atCcSDSDA,MAT_$atCcEBR,MAT_$atCcSIS,MAT_$atCcCoRe,MAT_$atCcP                            ;
                 ; AA_$atCc12900      ; MAT_$atCcA,MAT_$atCcSDSDA,MAT_$atCcEBR,MAT_$atCcSIS,MAT_$atCcCoRe,MAT_$atCcP                            ;
                 ; AA_$atCc12910      ; MAT_$atCcA,MAT_$atCcSDSDA,MAT_$atCcEBR,MAT_$atCcSIS,MAT_$atCcCoRe,MAT_$atCcM,MAT_$atCcP,MAT_$atCcTHLPKW ;
                 ; AA_$atCc12920      ; MAT_$atCcA,MAT_$atCcSDSDA,MAT_$atCcEBR,MAT_$atCcSIS,MAT_$atCcCoRe,MAT_$atCcM,MAT_$atCcP,MAT_$atCcTHLPKW ;
                 ; AA_$atCc12970      ; MAT_$atCcA,MAT_$atCcD,MAT_$atCcE,MAT_$atCcK3                                                            ;
                 ; AA_$atCc12980      ; MAT_$atCcA,MAT_$atCcD,MAT_$atCcE,MAT_$atCcK3                                                            ;
                 ; AA_$atCc12990      ; MAT_$atCcA,MAT_$atCcE,MAT_$atCcK2                                                                       ;
                 ; AA_$atCc12996      ; MAT_$atCcA,MAT_$atCcE,MAT_$atCcK2                                                                       ;
                 ; AA_$atCc12998      ; MAT_$atCcA,MAT_$atCcD,MAT_$atCcE                                                                        ;
                 ; AA_$atCc12999      ; MAT_$atCcA,MAT_$atCcD,MAT_$atCcE                                                                        ;
                 ; AA_$atCc12410      ; MAT_$atCcA                                                                                              ;
                 ; AA_$atCc12555      ; MAT_$atCcA                                                                                              ;
                 ; AA_$atCc12500      ; MAT_$atCcW                                                                                              ;
                 ; AA_$atCc12640      ; MAT_$atCcW                                                                                              ;
                 ; AA_$atCc15015      ; MAT_$atCcTestdata                                                                                       ;
                 ; AA_$atCc10436      ; MAT_$atCcZD                                                                                             ;
                 ; AA_$atCc10437      ; MAT_$atCcZW                                                                                             ;
                 ; AA_$atCc15045      ; MAT_$atCcCSFSA7                                                                                         ;
                 ; AA_$atCc15060      ; MAT_$atCcCSFSA5                                                                                         ;
                 ; AA_$atCc29271      ; MAT_$atCcTHLPKW                                                                                         ;
                 ; AA_$atCc13515      ; MAT_$atCcTHLTruck                                                                                       ;
                 ; AA_$atCc13516      ; MAT_$atCcTHLPKWTruck                                                                                    ;
                 ; AA_$atCc15076      ; MAT_$atCcCoReS                                                                                          ;
                 ; AA_$atCc15083      ; MAT_$atCcCoRe                                                                                           ;
                 ; AA_$atCc12050      ; MAT_$atCcA                                                                                              ;
                 ; AA_$atCc12051      ; MAT_$atCcSD                                                                                             ;
                 ; AA_$atCc12389      ; MAT_$atCcSDSDA                                                                                          ;
                 ; AA_$atCc12400      ; MAT_$atCcTrFZ,MAT_$atCcTrD,MAT_$atCcTrSp,MAT_$atCcTrWt,MAT_$atCcTrTD,MAT_$atCcTrETK                     ;
                 ; AA_$atCc12405      ; MAT_$atCcTrFZ,MAT_$atCcTrD,MAT_$atCcTrSp,MAT_$atCcTrWt,MAT_$atCcTrTD,MAT_$atCcTrETK                     ;
                 ; AA_$atCc12404      ; MAT_$atCcTrFZ,MAT_$atCcTrD,MAT_$atCcTrSp,MAT_$atCcTrWt,MAT_$atCcTrTD,MAT_$atCcTrETK                     ;
                 ; AA_$atCc12406      ; MAT_$atCcTrFZ,MAT_$atCcTrD,MAT_$atCcTrSp,MAT_$atCcTrWt,MAT_$atCcTrTD,MAT_$atCcTrETK                     ;
                 ; AA_$atCc12260      ; MAT_$atCcOHW1                                                                                           ;
                 ; AA_$atCc12266      ; MAT_$atCcOHW1                                                                                           ;
                 ; AA_$atCc12278      ; MAT_$atCcOHW2                                                                                           ;
                 ; AA_$atCc12284      ; MAT_$atCcOHW2                                                                                           ;
                 ; AA_$atCc12412      ; MAT_$atCcTrFZ,MAT_$atCcTrD,MAT_$atCcTrSp,MAT_$atCcTrWt,MAT_$atCcTrTD,MAT_$atCcTrETK                     ;
                 ; AA_$atCc12760      ; MAT_$atCcTrFZ,MAT_$atCcTrAtD,MAT_$atCcTrSp,MAT_$atCcTrWt,MAT_$atCcTrTD,MAT_$atCcTrETK                   ;
                 ; AA_$atCc12765      ; MAT_$atCcTrFZ,MAT_$atCcTrAtD,MAT_$atCcTrSp,MAT_$atCcTrWt,MAT_$atCcTrTD,MAT_$atCcTrETK                   ;
                 ; AA_$atCc15090      ; MAT_$atCcCRI                                                                                            ;
                 ; AA_$atCc15095      ; MAT_$atCcCRI                                                                                            ;
                 ; AA_$atCc15100      ; MAT_$atCcCRIN                                                                                           ;
                 ; AA_$atCc15105      ; MAT_$atCcCRIN                                                                                           ;


INSERT_UPDATE App; code[unique = true]; boltons(code, $catalogVersion); $catalogVersion[unique = true];
                 ; AA_$atCc12398      ; AA_$atCc12398                 ;
                 ; AA_$atCc12385      ; AA_$atCc12395                 ;
                 ; AA_$atCc12820      ; AA_$atCc12830                 ;
                 ; AA_$atCc12840      ; AA_$atCc12860                 ;
                 ; AA_$atCc12870      ; AA_$atCc12880                 ;
                 ; AA_$atCc12890      ; AA_$atCc12900                 ;
                 ; AA_$atCc12910      ; AA_$atCc12920                 ;
                 ; AA_$atCc12970      ; AA_$atCc12980                 ;
                 ; AA_$atCc12990      ; AA_$atCc12996                 ;
                 ; AA_$atCc12998      ; AA_$atCc12999                 ;
                 ; AA_$atCc12410      ; AA_$atCc12555                 ;
                 ; AA_$atCc12500      ; AA_$atCc12640                 ;
                 ; AA_$atCc15076      ; AA_$atCc15083                 ;
                 ; AA_$atCc12400      ; AA_$atCc12405                 ;
                 ; AA_$atCc12404      ; AA_$atCc12406                 ;
                 ; AA_$atCc12260      ; AA_$atCc12266                 ;
                 ; AA_$atCc12278      ; AA_$atCc12284                 ;
                 ; AA_$atCc12760      ; AA_$atCc12765                 ;
                 ; AA_$atCc15090      ; AA_$atCc15095                 ;
                 ; AA_$atCc15100      ; AA_$atCc15105                 ;

INSERT_UPDATE Eula; customUrl[unique = true]  ; type(code)[default = CUSTOM]
                  ; https://$esiEula          ;
                  ; https://$esiTruckEula     ;
                  ; https://$esiDci700Eula    ;
                  ; https://$esiAlltrucksEula ;

UPDATE App; code[unique = true]; eula(customUrl)            ; $catalogVersion[unique = true]
          ; AA_$atCc12398      ; https\://$esiEula          ;
          ; AA_$atCc12385      ; https\://$esiEula          ;
          ; AA_$atCc12395      ; https\://$esiEula          ;
          ; AA_$atCc12820      ; https\://$esiEula          ;
          ; AA_$atCc12830      ; https\://$esiEula          ;
          ; AA_$atCc12840      ; https\://$esiEula          ;
          ; AA_$atCc12860      ; https\://$esiEula          ;
          ; AA_$atCc12870      ; https\://$esiEula          ;
          ; AA_$atCc12880      ; https\://$esiEula          ;
          ; AA_$atCc12890      ; https\://$esiEula          ;
          ; AA_$atCc12900      ; https\://$esiEula          ;
          ; AA_$atCc12910      ; https\://$esiEula          ;
          ; AA_$atCc12920      ; https\://$esiEula          ;
          ; AA_$atCc12970      ; https\://$esiEula          ;
          ; AA_$atCc12980      ; https\://$esiEula          ;
          ; AA_$atCc12990      ; https\://$esiEula          ;
          ; AA_$atCc12996      ; https\://$esiEula          ;
          ; AA_$atCc12998      ; https\://$esiEula          ;
          ; AA_$atCc12999      ; https\://$esiEula          ;
          ; AA_$atCc12410      ; https\://$esiEula          ;
          ; AA_$atCc12555      ; https\://$esiEula          ;
          ; AA_$atCc12500      ; https\://$esiEula          ;
          ; AA_$atCc12640      ; https\://$esiEula          ;
          ; AA_$atCc15015      ; https\://$esiEula          ;
          ; AA_$atCc10436      ; https\://$esiEula          ;
          ; AA_$atCc10437      ; https\://$esiEula          ;
          ; AA_$atCc15045      ; https\://$esiEula          ;
          ; AA_$atCc15060      ; https\://$esiEula          ;
          ; AA_$atCc29271      ; https\://$esiEula          ;
          ; AA_$atCc13515      ; https\://$esiEula          ;
          ; AA_$atCc13516      ; https\://$esiEula          ;
          ; AA_$atCc15076      ; https\://$esiEula          ;
          ; AA_$atCc15083      ; https\://$esiEula          ;
          ; AA_$atCc12050      ; https\://$esiEula          ;
          ; AA_$atCc12051      ; https\://$esiEula          ;
          ; AA_$atCc12389      ; https\://$esiEula          ;
          ; AA_$atCc12400      ; https\://$esiTruckEula     ;
          ; AA_$atCc12405      ; https\://$esiTruckEula     ;
          ; AA_$atCc12404      ; https\://$esiTruckEula     ;
          ; AA_$atCc12406      ; https\://$esiTruckEula     ;
          ; AA_$atCc12260      ; https\://$esiTruckEula     ;
          ; AA_$atCc12266      ; https\://$esiTruckEula     ;
          ; AA_$atCc12278      ; https\://$esiTruckEula     ;
          ; AA_$atCc12284      ; https\://$esiTruckEula     ;
          ; AA_$atCc12412      ; https\://$esiTruckEula     ;
          ; AA_$atCc12760      ; https\://$esiAlltrucksEula ;
          ; AA_$atCc12765      ; https\://$esiAlltrucksEula ;
          ; AA_$atCc15090      ; https\://$esiDci700Eula    ;
          ; AA_$atCc15095      ; https\://$esiDci700Eula    ;
          ; AA_$atCc15100      ; https\://$esiDci700Eula    ;
          ; AA_$atCc15105      ; https\://$esiDci700Eula    ;

UPDATE App; code[unique = true]; $supercategories; $catalogVersion[unique = true];
          ; AA_$atCc12398      ; cat_10101
          ; AA_$atCc12385      ; cat_10101
          ; AA_$atCc12395      ; cat_10101
          ; AA_$atCc12820      ; cat_10101
          ; AA_$atCc12830      ; cat_10101
          ; AA_$atCc12840      ; cat_10101
          ; AA_$atCc12860      ; cat_10101
          ; AA_$atCc12870      ; cat_10101
          ; AA_$atCc12880      ; cat_10101
          ; AA_$atCc12890      ; cat_10101
          ; AA_$atCc12900      ; cat_10101
          ; AA_$atCc12910      ; cat_10101
          ; AA_$atCc12920      ; cat_10101
          ; AA_$atCc12970      ; cat_1010101
          ; AA_$atCc12980      ; cat_1010101
          ; AA_$atCc12990      ; cat_1010101
          ; AA_$atCc12996      ; cat_1010101
          ; AA_$atCc12998      ; cat_1010101
          ; AA_$atCc12999      ; cat_1010101
          ; AA_$atCc12410      ; cat_1010101
          ; AA_$atCc12555      ; cat_1010101
          ; AA_$atCc12500      ; cat_40101
          ; AA_$atCc12640      ; cat_40101
          ; AA_$atCc15015      ; cat_40101
          ; AA_$atCc10436      ; cat_1010101
          ; AA_$atCc10437      ; cat_1010101
          ; AA_$atCc15045      ; cat_201
          ; AA_$atCc15060      ; cat_201
          ; AA_$atCc29271      ; cat_1010102
          ; AA_$atCc13515      ; cat_1010202
          ; AA_$atCc13516      ; cat_1010102,cat_1010202
          ; AA_$atCc15076      ; cat_1010101
          ; AA_$atCc15083      ; cat_1010101
          ; AA_$atCc12050      ; cat_1010101
          ; AA_$atCc12051      ; cat_1010101
          ; AA_$atCc12389      ; cat_1010101
          ; AA_$atCc12400      ; cat_10102
          ; AA_$atCc12405      ; cat_10102
          ; AA_$atCc12404      ; cat_10102
          ; AA_$atCc12406      ; cat_10102
          ; AA_$atCc12260      ; cat_10102
          ; AA_$atCc12266      ; cat_10102
          ; AA_$atCc12278      ; cat_10102
          ; AA_$atCc12284      ; cat_10102
          ; AA_$atCc12412      ; cat_10102
          ; AA_$atCc12760      ; cat_1010201
          ; AA_$atCc12765      ; cat_1010201
          ; AA_$atCc15090      ; cat_401
          ; AA_$atCc15095      ; cat_401
          ; AA_$atCc15100      ; cat_401
          ; AA_$atCc15105      ; cat_401

