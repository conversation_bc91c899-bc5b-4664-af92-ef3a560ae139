package com.sast.cis.payment.zero.service;

import com.google.common.base.Preconditions;
import com.sast.cis.core.enums.PaymentProvider;
import com.sast.cis.core.service.AbstractOrderHashService;
import de.hybris.platform.core.model.order.AbstractOrderModel;
import de.hybris.platform.payment.dto.TransactionStatus;
import de.hybris.platform.payment.dto.TransactionStatusDetails;
import de.hybris.platform.payment.enums.PaymentTransactionType;
import de.hybris.platform.payment.model.PaymentTransactionEntryModel;
import de.hybris.platform.payment.model.PaymentTransactionModel;
import de.hybris.platform.servicelayer.keygenerator.KeyGenerator;
import de.hybris.platform.servicelayer.model.ModelService;
import de.hybris.platform.servicelayer.time.TimeService;
import lombok.NonNull;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

@Service
public class ZeroPaymentTransactionFactory {
    private final ModelService modelService;
    private final KeyGenerator paymentTransactionCodeGenerator;
    private final TimeService timeService;
    private final AbstractOrderHashService abstractOrderHashService;


    public ZeroPaymentTransactionFactory(ModelService modelService,
        KeyGenerator paymentTransactionCodeGenerator, TimeService timeService,
        AbstractOrderHashService abstractOrderHashService) {
        this.modelService = modelService;
        this.paymentTransactionCodeGenerator = paymentTransactionCodeGenerator;
        this.timeService = timeService;
        this.abstractOrderHashService = abstractOrderHashService;
    }

    public PaymentTransactionEntryModel persistAuthorization(@NonNull AbstractOrderModel abstractOrder) {
        Preconditions.checkArgument(abstractOrder.getPaymentInfo() != null, "Given cart {} has no payment info", abstractOrder.getCode());
        Preconditions.checkArgument(PaymentProvider.ZERO.equals(abstractOrder.getPaymentInfo().getPaymentProvider()),
            "Given cart payment info {} for cart {} is not for payment provider ZERO.",
            abstractOrder.getPaymentInfo().getCode(), abstractOrder.getCode());

        PaymentTransactionModel paymentTransactionModel = createAuthorizationTransaction(abstractOrder);
        return createAuthorizationEntry(paymentTransactionModel);
    }


    private PaymentTransactionModel createAuthorizationTransaction(AbstractOrderModel cart) {
        PaymentTransactionModel transaction = modelService.create(PaymentTransactionModel.class);

        transaction.setCode(createTransactionCode());
        transaction.setType(PaymentTransactionType.AUTHORIZATION);
        transaction.setPaymentProvider(PaymentProvider.ZERO.getCode());
        transaction.setInfo(cart.getPaymentInfo());
        transaction.setCurrency(cart.getCurrency());
        transaction.setPlannedAmount(BigDecimal.valueOf(cart.getTotalPrice()));
        transaction.setOrder(cart);

        modelService.save(transaction);
        return transaction;
    }

    private PaymentTransactionEntryModel createAuthorizationEntry(PaymentTransactionModel transaction) {
        PaymentTransactionEntryModel transactionEntry = modelService.create(PaymentTransactionEntryModel.class);

        transactionEntry.setType(PaymentTransactionType.AUTHORIZATION);
        transactionEntry.setCode(createTransactionEntryCode(transaction));
        transactionEntry.setPaymentTransaction(transaction);
        transactionEntry.setTransactionStatus(TransactionStatus.ACCEPTED.name());
        transactionEntry.setTransactionStatusDetails(TransactionStatusDetails.SUCCESFULL.name());
        transactionEntry.setCartHash(abstractOrderHashService.calculateHash(transaction.getOrder()));
        transactionEntry.setTime(timeService.getCurrentTime());

        modelService.save(transactionEntry);
        modelService.refresh(transaction);
        return transactionEntry;
    }


    private String createTransactionCode() {
        return String.format("%s_%s",
            PaymentTransactionType.AUTHORIZATION.getCode(), paymentTransactionCodeGenerator.generate().toString());
    }

    private String createTransactionEntryCode(PaymentTransactionModel paymentTransaction) {
        return String.format("%s-%s-%03d", paymentTransaction.getCode(),
            PaymentTransactionType.AUTHORIZATION.getCode(), CollectionUtils.emptyIfNull(paymentTransaction.getEntries()).size());
    }
}
