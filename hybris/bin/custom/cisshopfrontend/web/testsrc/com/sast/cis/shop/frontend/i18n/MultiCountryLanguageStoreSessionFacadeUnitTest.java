package com.sast.cis.shop.frontend.i18n;

import com.sast.cis.core.exceptions.web.BadRequestException;
import com.sast.cis.core.model.CountryStoreConfigurationModel;
import com.sast.cis.core.model.IoTCompanyModel;
import com.sast.cis.core.model.IoTCustomerModel;
import com.sast.cis.core.service.country.CountryStoreConfigurationService;
import com.sast.cis.shop.frontend.countrystore.CustomerPreferredLanguage;
import com.sast.cis.shop.frontend.validator.SessionCountryLanguageValidator;
import de.hybris.bootstrap.annotations.UnitTest;
import de.hybris.platform.commercefacades.storesession.data.LanguageData;
import de.hybris.platform.core.model.c2l.CountryModel;
import de.hybris.platform.core.model.c2l.LanguageModel;
import de.hybris.platform.servicelayer.user.UserService;
import de.hybris.platform.store.BaseStoreModel;
import de.hybris.platform.store.services.BaseStoreService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.junit.Assert.assertThrows;
import static org.mockito.Mockito.*;

@UnitTest
@RunWith(MockitoJUnitRunner.class)
public class MultiCountryLanguageStoreSessionFacadeUnitTest {

    private static final String ERROR_CODE_FAILED_TO_SWITCH_TO_DEFAULT = "api.error.countryStore.failedToSwitchToDefault";
    private final String countryIsoCode = "ch";
    private final String languageIsoCode = "it";
    @Mock
    private SessionCountryLanguageValidator sessionCountryLanguageValidator;
    @Mock
    private UserService userService;
    @Mock
    private BaseStoreService baseStoreService;
    @Mock
    private CustomerPreferredLanguage customerPreferredLanguage;
    @Mock
    private UserLanguageSelectionManager userLanguageSelectionManager;
    @Mock
    private UserCountrySelectionManager userCountrySelectionManager;
    @Mock
    private CountryStoreConfigurationService countryStoreConfigurationService;
    @InjectMocks
    private MultiCountryLanguageStoreSessionFacade multiCountryLanguageStoreSessionFacade;
    @Mock
    private CountryStoreConfigurationModel countryStoreConfiguration;
    @Mock
    private BaseStoreModel baseStore;
    @Mock
    private LanguageModel language;
    @Mock
    private LanguageData languageData;
    @Mock
    private CountryModel country;
    @Mock
    private IoTCustomerModel ioTCustomer;

    @Before
    public void setUp() {
        when(baseStoreService.getCurrentBaseStore()).thenReturn(baseStore);
        IoTCompanyModel company = mock(IoTCompanyModel.class);
        when(country.getIsocode()).thenReturn(countryIsoCode);
        when(ioTCustomer.getCompany()).thenReturn(company);
        when(company.getCountry()).thenReturn(country);
        when(languageData.getIsocode()).thenReturn(languageIsoCode);
        when(language.getIsocode()).thenReturn(languageIsoCode);
        when(userCountrySelectionManager.getUserSelectedCountry()).thenReturn(countryIsoCode);
        when(countryStoreConfiguration.getDefaultLanguage()).thenReturn(language);
        when(countryStoreConfigurationService.getCountryStoreConfiguration(country, baseStore)).thenReturn(
            Optional.of(countryStoreConfiguration));
        when(sessionCountryLanguageValidator.isValidCountryLanguage(countryIsoCode, languageIsoCode, baseStore)).thenReturn(true);
        when(userService.getCurrentUser()).thenReturn(ioTCustomer);
        when(customerPreferredLanguage.get()).thenReturn(Optional.of(languageIsoCode));
        when(userLanguageSelectionManager.getCurrentLanguage()).thenReturn(languageData);

    }

    @Test
    public void givenUserCountrySelectionManager_whenSetUserSelectedCountry_thenDelegatesToCountrySelectionManager() {

        multiCountryLanguageStoreSessionFacade.setUserSelectedCountry(countryIsoCode);

        verify(userCountrySelectionManager).setUserSelectedCountry(countryIsoCode);
    }

    @Test
    public void whenGetUserSelectedCountry_thenGetSelectedCountrySessionAttribute() {
        final String selectedCountry = multiCountryLanguageStoreSessionFacade.getUserSelectedCountry();

        assertThat(selectedCountry).isEqualTo(countryIsoCode);
    }

    @Test
    public void givenLanguageIsValid_whenSetCurrentLanguage_thenInvokeStoreSessionFacade() {
        multiCountryLanguageStoreSessionFacade.setCurrentLanguage(languageIsoCode);

        verify(userLanguageSelectionManager).setCurrentLanguage(languageIsoCode);
    }

    @Test
    public void givenInvalidCountry_whenSelectCountryAndLanguage_thenThrowsException() {
        when(sessionCountryLanguageValidator.isValidCountryLanguage(countryIsoCode, languageIsoCode,
            baseStoreService.getCurrentBaseStore())).thenReturn(false);

        assertThatThrownBy(() -> multiCountryLanguageStoreSessionFacade.selectCountryAndLanguage(countryIsoCode, languageIsoCode))
            .isInstanceOf(BadRequestException.class);

        verifyNoInteractions(userCountrySelectionManager);
        verifyNoInteractions(userLanguageSelectionManager);
    }

    @Test
    public void givenValidCountry_whenSelectCountryAndLanguage_thenSessionValuesSet() {
        when(sessionCountryLanguageValidator.isValidCountryLanguage(countryIsoCode, languageIsoCode,
            baseStoreService.getCurrentBaseStore())).thenReturn(true);

        multiCountryLanguageStoreSessionFacade.selectCountryAndLanguage(countryIsoCode, languageIsoCode);

        verify(userCountrySelectionManager).setUserSelectedCountry(countryIsoCode);
        verify(userLanguageSelectionManager).setCurrentLanguage(languageIsoCode);
    }

    @Test
    public void givenCustomerCountryUnknown_whenMakeCustomerDefaultCountryAsCurrent_thenThrowsIllegalStateException() {
        // Set up the mock behavior for the supplier methods
        when(userService.getCurrentUser()).thenReturn(null);

        BadRequestException exception = assertThrows(BadRequestException.class,
            () -> multiCountryLanguageStoreSessionFacade.makeCustomerDefaultCountryStoreAsCurrent());

        assertThat(exception.getMessage()).isEqualTo(ERROR_CODE_FAILED_TO_SWITCH_TO_DEFAULT);
    }

    @Test
    public void givenCustomerCountryAndLanguage_whenMakeCustomerDefaultCountryAsCurrent_thenSelectedCountryAndLanguageSetInSession() {

        // Call the method under test
        multiCountryLanguageStoreSessionFacade.makeCustomerDefaultCountryStoreAsCurrent();

        // Verify that the selectCountryAndLanguage method was called with the correct arguments
        verify(userCountrySelectionManager).setUserSelectedCountry(countryIsoCode);
    }

    @Test
    public void givenNoPreferredLanguageAndWithCountryDefaultLanguage_whenMakeCustomerDefaultCountryAsCurrent_thenUpdatesSession() {

        when(customerPreferredLanguage.get()).thenReturn(Optional.empty());
        when(countryStoreConfiguration.getDefaultLanguage()).thenReturn(language);

        // Call the method under test
        multiCountryLanguageStoreSessionFacade.makeCustomerDefaultCountryStoreAsCurrent();

        // Verify that the selectCountryAndLanguage method was called with the correct arguments
        verify(userCountrySelectionManager).setUserSelectedCountry(countryIsoCode);
    }

    @Test
    public void givenNoPreferredLanguageNorCountryDefaultLanguage_whenMakeCustomerDefaultCountryAsCurrent_testThrowsBadRequestException() {

        when(countryStoreConfiguration.getDefaultLanguage()).thenReturn(null);
        when(customerPreferredLanguage.get()).thenReturn(Optional.empty());

        BadRequestException exception = assertThrows(BadRequestException.class,
            () -> multiCountryLanguageStoreSessionFacade.makeCustomerDefaultCountryStoreAsCurrent());

        assertThat(exception.getMessage()).isEqualTo(ERROR_CODE_FAILED_TO_SWITCH_TO_DEFAULT);
    }

    @Test
    public void givenNoChangeInCountryOrLanguage_whenUpdateCustomerCountryAndLanguageIfChanged_thenSessionValueNotChanged() {

        multiCountryLanguageStoreSessionFacade.updateCustomerSessionCountryAndLanguage();

        verify(userCountrySelectionManager, times(0)).setUserSelectedCountry(countryIsoCode);
        verify(userLanguageSelectionManager).getCurrentLanguage();
        verifyNoMoreInteractions(userLanguageSelectionManager);
    }

    @Test
    public void givenChangeInCountry_whenUpdateCustomerCountryAndLanguageIfChanged_thenSessionValueChanged() {
        when(userCountrySelectionManager.getUserSelectedCountry()).thenReturn("pt");

        multiCountryLanguageStoreSessionFacade.updateCustomerSessionCountryAndLanguage();

        verify(userCountrySelectionManager).setUserSelectedCountry(countryIsoCode);
        verify(userLanguageSelectionManager).getCurrentLanguage();
        verifyNoMoreInteractions(userLanguageSelectionManager);
    }

    @Test
    public void givenNoCountryOrLanguage_whenUpdateCustomerCountryAndLanguageIfChanged_thenSessionDataIsNotAltered() {
        when(userCountrySelectionManager.getUserSelectedCountry()).thenReturn(countryIsoCode);
        when(languageData.getIsocode()).thenReturn(languageIsoCode);

        multiCountryLanguageStoreSessionFacade.updateCustomerSessionCountryAndLanguage();

        verify(userCountrySelectionManager).getUserSelectedCountry();
        verifyNoMoreInteractions(userCountrySelectionManager);
        verify(userLanguageSelectionManager).getCurrentLanguage();
        verifyNoMoreInteractions(userLanguageSelectionManager);
    }

}
