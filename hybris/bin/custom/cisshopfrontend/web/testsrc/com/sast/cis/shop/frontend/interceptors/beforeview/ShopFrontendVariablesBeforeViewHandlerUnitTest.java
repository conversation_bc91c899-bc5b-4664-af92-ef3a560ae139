package com.sast.cis.shop.frontend.interceptors.beforeview;

import com.google.common.collect.Lists;
import com.sast.cis.core.config.TranslationConfigResolver;
import com.sast.cis.core.constants.UserPermissionShop;
import com.sast.cis.core.data.ImportedCompanyPurchaseEligibilityInfoDto;
import com.sast.cis.core.data.MiniCartData;
import com.sast.cis.core.data.NavigationItemData;
import com.sast.cis.core.data.ShopCoreData;
import com.sast.cis.core.data.TranslationConfig;
import com.sast.cis.core.enums.NavigationItemGroup;
import com.sast.cis.core.enums.NavigationItemType;
import com.sast.cis.core.facade.CisCartFacade;
import com.sast.cis.core.facade.NavigationItemFacade;
import com.sast.cis.core.facade.payment.PaymentSettingsFacade;
import com.sast.cis.core.i18n.FallbackCountryProvider;
import com.sast.cis.core.model.IoTCompanyModel;
import com.sast.cis.core.service.company.IotCompanyService;
import com.sast.cis.core.service.license.ImportedCompanyPurchaseEligibilityService;
import com.sast.cis.core.service.security.UserPermissionService;
import com.sast.cis.shop.frontend.navitem.access.NavigationItemAccessEvaluator;
import de.hybris.bootstrap.annotations.UnitTest;
import de.hybris.platform.servicelayer.config.ConfigurationService;
import de.hybris.platform.store.BaseStoreModel;
import de.hybris.platform.store.services.BaseStoreService;
import org.apache.commons.configuration.Configuration;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.web.servlet.ModelAndView;

import java.util.Collections;
import java.util.Date;
import java.util.Optional;

import static com.sast.cis.core.constants.shop.ShopConfig.ALLOWED_MAX_QUANTITY_LINEITEM_FULL_LICENSE;
import static com.sast.cis.core.constants.shop.ShopConfig.ALLOWED_MAX_QUANTITY_LINEITEM_SUBS_LICENSE;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Matchers.any;
import static org.mockito.Mockito.when;

@UnitTest
@RunWith(MockitoJUnitRunner.Silent.class)
public class ShopFrontendVariablesBeforeViewHandlerUnitTest {
    private static final Date DATE = new Date();
    @Mock
    private ConfigurationService configurationService;

    @Mock
    private Configuration configuration;

    @Mock
    private ModelAndView model;

    @Mock
    private UserPermissionService userPermissionService;

    @Mock
    private NavigationItemFacade navigationItemFacade;

    @Mock
    private IotCompanyService iotCompanyService;

    @Mock
    private CisCartFacade cisCartFacade;

    @Mock
    private BaseStoreService baseStoreService;

    @Mock
    private NavigationItemAccessEvaluator navigationItemAccessEvaluator;

    @Mock
    private TranslationConfigResolver translationConfigResolver;

    @Mock
    private FallbackCountryProvider fallbackCountryProvider;

    @Mock
    private PaymentSettingsFacade paymentSettingsFacade;
    @Mock
    private ImportedCompanyPurchaseEligibilityService importedCompanyPurchaseEligibilityService;

    @InjectMocks
    private ShopFrontendVariablesBeforeViewHandler handler;

    @Mock
    private BaseStoreModel baseStore;
    @Mock
    private IoTCompanyModel company;

    private final long allowedMaxQuantityFullLicense = 100;
    private final long allowedMaxQuantitySubsLicense = 200;
    private final String globalFallbackCountry = "AT";
    private ImportedCompanyPurchaseEligibilityInfoDto importedCompanyPurchaseEligibilityInfo;

    @Before
    public void setUp() {
        importedCompanyPurchaseEligibilityInfo = new ImportedCompanyPurchaseEligibilityInfoDto()
            .withShowPurchaseDisabledBanner(false)
            .withDate(DATE);
        when(configuration.getLong(ALLOWED_MAX_QUANTITY_LINEITEM_FULL_LICENSE.getValue())).thenReturn(allowedMaxQuantityFullLicense);
        when(configuration.getLong(ALLOWED_MAX_QUANTITY_LINEITEM_SUBS_LICENSE.getValue())).thenReturn(allowedMaxQuantitySubsLicense);
        when(configurationService.getConfiguration()).thenReturn(configuration);

        when(baseStoreService.getCurrentBaseStore()).thenReturn(baseStore);

        when(navigationItemFacade.getAllNavigationItems(NavigationItemType.STORE, baseStore)).thenReturn(Collections.emptyList());
        when(userPermissionService.hasShopPermission(any())).thenReturn(false);
        when(iotCompanyService.getCurrentCompany()).thenReturn(Optional.of(company));
        when(cisCartFacade.getMiniCartData()).thenReturn(new MiniCartData());
        when(translationConfigResolver.resolve(baseStore)).thenReturn(getTranslationConfig());
        when(fallbackCountryProvider.getCountry()).thenReturn(globalFallbackCountry);
        when(paymentSettingsFacade.shouldGuideCustomerToProvideDirectDebitMandate()).thenReturn(false);
        when(importedCompanyPurchaseEligibilityService.importedCompanyCanBuy(any())).thenReturn(false);
    }

    @Test
    public void beforeView_shouldPopulateAllowedToBuy() {
        when(userPermissionService.hasShopPermission(UserPermissionShop.PLACE_ORDER)).thenReturn(true);
        ShopCoreData data = handler.getAndPopulateShopCoreData(model);
        assertThat(data.isAllowedToBuy()).isTrue();
    }

    @Test
    public void beforeView_shouldPopulateNumberOfCartItems() {
        when(cisCartFacade.getMiniCartData()).thenReturn(new MiniCartData().withTotalQuantity(5));
        ShopCoreData data = handler.getAndPopulateShopCoreData(model);
        assertThat(data.getNumberOfCartItems()).isEqualTo(5);
    }

    @Test
    public void givenNonAccessibleNavigationItem_whenPopulated_thenDoNotToCoreDate() {
        final NavigationItemData nonAccessibleNavItem = getNavigationItemData(NavigationItemGroup.HEADER, "storeLogin", 0);
        when(navigationItemFacade.getAllNavigationItems(NavigationItemType.STORE, baseStore))
            .thenReturn(Lists.newArrayList(nonAccessibleNavItem));
        when(navigationItemAccessEvaluator.isItemAccessible(nonAccessibleNavItem)).thenReturn(false);

        final ShopCoreData data = handler.getAndPopulateShopCoreData(model);

        assertThat(data.getNavigationItems()).isEmpty();
    }

    @Test
    public void givenAccessibleNavigationItem_whenPopulated_thenAddToCoreDate() {
        final NavigationItemData accessibleNavItem = getNavigationItemData(NavigationItemGroup.HEADER, "storeLogin", 0);
        when(navigationItemFacade.getAllNavigationItems(NavigationItemType.STORE, baseStore))
            .thenReturn(Lists.newArrayList(accessibleNavItem));
        when(navigationItemAccessEvaluator.isItemAccessible(accessibleNavItem)).thenReturn(true);

        final ShopCoreData data = handler.getAndPopulateShopCoreData(model);

        assertThat(data.getNavigationItems()).hasSize(1);
        assertThat(data.getNavigationItems()).containsOnly(accessibleNavItem);
    }

    @Test
    public void shouldPopulateGlobalConfigData() {
        final ShopCoreData data = handler.getAndPopulateShopCoreData(model);

        assertThat(data.getAllowedMaxQuantityLineItemFullLicense()).isEqualTo(allowedMaxQuantityFullLicense);
        assertThat(data.getAllowedMaxQuantityLineItemSubsLicense()).isEqualTo(allowedMaxQuantitySubsLicense);
    }

    @Test
    public void shouldPopulateRegionAndLanguage() {
        final ShopCoreData data = handler.getAndPopulateShopCoreData(model);

        assertThat(data.getGlobalFallbackCountry()).isEqualTo(globalFallbackCountry);
        assertThat(data.getGlobalDefaultLanguage()).isEqualTo("en");
        assertThat(data.getTranslationConfig()).isNotNull();
    }

    @Test
    public void shouldPopulateGuideCustomerToProvideDirectDebitMandateData() {
        when(userPermissionService.isAuthenticated()).thenReturn(true);
        when(paymentSettingsFacade.shouldGuideCustomerToProvideDirectDebitMandate()).thenReturn(true);
        final ShopCoreData data = handler.getAndPopulateShopCoreData(model);

        assertThat(data.isGuideCustomerToProvideDirectDebitMandate()).isTrue();
    }

    @Test
    public void unAuthenticatedUserShouldPopulateGuideCustomerToProvideDirectDebitMandateDataFalse() {
        when(userPermissionService.isAuthenticated()).thenReturn(false);
        when(paymentSettingsFacade.shouldGuideCustomerToProvideDirectDebitMandate()).thenReturn(true);
        final ShopCoreData data = handler.getAndPopulateShopCoreData(model);

        assertThat(data.isGuideCustomerToProvideDirectDebitMandate()).isFalse();
    }

    @Test
    public void givenAuthenticatedUserOfImportedCompanyWithPurchaseDisabledThenShowPurchaseDisabledBannerIsTrue() {
        when(userPermissionService.isAuthenticated()).thenReturn(true);
        importedCompanyPurchaseEligibilityInfo.setShowPurchaseDisabledBanner(true);
        when(importedCompanyPurchaseEligibilityService.getImportedCompanyPurchaseEligibilityInfo(any()))
            .thenReturn(Optional.of(importedCompanyPurchaseEligibilityInfo));
        final ShopCoreData data = handler.getAndPopulateShopCoreData(model);

        assertThat(data.getPurchaseDisabledBannerInfo().isShowPurchaseDisabledBanner()).isTrue();
    }

    @Test
    public void givenUnAuthenticatedUserOfImportedCompanyWithPurchaseEnabledThenShowPurchaseDisabledBannerIsFalse() {
        when(userPermissionService.isAuthenticated()).thenReturn(false);
        when(importedCompanyPurchaseEligibilityService.getImportedCompanyPurchaseEligibilityInfo(any()))
            .thenReturn(Optional.of(importedCompanyPurchaseEligibilityInfo));
        final ShopCoreData data = handler.getAndPopulateShopCoreData(model);

        assertThat(data.getPurchaseDisabledBannerInfo()).isNull();
    }

    private NavigationItemData getNavigationItemData(NavigationItemGroup group, String identifier, int index) {
        return new NavigationItemData().withGroup(group.getCode())
            .withItemCode(identifier)
            .withIndex(index);
    }

    private TranslationConfig getTranslationConfig() {
        return new TranslationConfig().withRemoteTranslationsEnabled(false).withBaseUrl("").withProjectId("");
    }
}
