package com.sast.cis.shop.frontend.validator;

import com.sast.cis.core.model.CountryStoreConfigurationModel;
import com.sast.cis.core.service.country.CountryStoreConfigurationService;
import de.hybris.platform.core.model.c2l.CountryModel;
import de.hybris.platform.core.model.c2l.LanguageModel;
import de.hybris.platform.servicelayer.exceptions.UnknownIdentifierException;
import de.hybris.platform.servicelayer.i18n.CommonI18NService;
import de.hybris.platform.store.BaseStoreModel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@Slf4j
public class SessionCountryLanguageValidator {

    private final CommonI18NService commonI18NService;

    private final CountryStoreConfigurationService countryStoreConfigurationService;

    public boolean isValidCountry(final String countryIsoCode, BaseStoreModel baseStore) {
        if (countryIsoCode == null) {
            return false;
        }

        final var countryModel = getCountry(countryIsoCode);
        if (countryModel == null || !countryModel.getActive()) {
            return false;
        }

        final var countryStoreConfiguration = countryStoreConfigurationService.getCountryStoreConfiguration(countryModel, baseStore)
            .filter(CountryStoreConfigurationModel::getStorefrontEnabled);

        if (countryStoreConfiguration.isEmpty()) {
            LOG.info("Country '{}' is not enabled for base store '{}'", countryIsoCode, baseStore.getName());
            return false;
        }

        return true;
    }

    public boolean isValidCountryLanguage(final String countryIsoCode, final String languageIsoCode, BaseStoreModel baseStore) {
        if (countryIsoCode == null || languageIsoCode == null) {
            return false;
        }

        final var languageModel = getLanguage(languageIsoCode);
        final var countryModel = getCountry(countryIsoCode);
        if (languageModel == null || countryModel == null) {
            return false;
        }

        final var countryStoreConfiguration = countryStoreConfigurationService.getCountryStoreConfiguration(countryModel, baseStore);

        if (countryStoreConfiguration.filter(c -> c.getLanguages().contains(languageModel)).isEmpty()) {
            LOG.info("Language '{}' in country '{}' is not active for base store '{}'", languageIsoCode, countryIsoCode,
                baseStore.getName());
            return false;
        }

        return true;
    }

    public boolean isValidStoreLanguage(final String languageIsoCode, BaseStoreModel baseStore) {
        if (languageIsoCode == null) {
            return false;
        }

        final var languageModel = getLanguage(languageIsoCode);
        if (languageModel == null) {
            return false;
        }

        if (!baseStore.getLanguages().contains(languageModel)) {
            LOG.info("Language '{}' is not active for base store '{}'", languageIsoCode, baseStore.getName());
            return false;
        }

        return true;
    }

    private CountryModel getCountry(final String countryIsoCode) {
        try {
            return commonI18NService.getCountry(countryIsoCode);
        } catch (UnknownIdentifierException e) {
            LOG.info("Country with isocode '{}' was not found", countryIsoCode);
            return null;
        }
    }

    private LanguageModel getLanguage(final String languageIsoCode) {
        try {
            return commonI18NService.getLanguage(languageIsoCode);
        } catch (UnknownIdentifierException e) {
            LOG.info("Language with isocode '{}' was not found", languageIsoCode);
            return null;
        }
    }
}
