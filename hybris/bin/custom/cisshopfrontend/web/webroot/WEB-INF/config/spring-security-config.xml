<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	   xmlns:security="http://www.springframework.org/schema/security"
	   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	   xmlns:context="http://www.springframework.org/schema/context"
	   xmlns:util="http://www.springframework.org/schema/util"
	   xsi:schemaLocation="http://www.springframework.org/schema/beans
		http://www.springframework.org/schema/beans/spring-beans.xsd
		http://www.springframework.org/schema/security
		http://www.springframework.org/schema/security/spring-security.xsd
		http://www.springframework.org/schema/context
        http://www.springframework.org/schema/context/spring-context.xsd
        http://www.springframework.org/schema/util
		http://www.springframework.org/schema/util/spring-util.xsd">

	<context:annotation-config/>

	<security:http pattern="/_ui/**" security="none" />

	<!-- Ignores url(s) that match paths specified in the set -->
	<bean id="excludeUrlRequestMatcher" class="com.sast.cis.shop.frontend.security.ExcludeUrlRequestMatcher">
		<property name="excludeUrlSet" ref="excludeUrlSet"/>
		<property name="pathMatcher" ref="defaultPathMatcher"/>
	</bean>

	<!-- The set of url(s) to exclude from the 'Default security config'-->
	<bean id="excludeUrlSet" class="java.util.HashSet" />

	<!-- Path matcher to use, AntPathMatcher is the default -->
	<alias name="antPathMatcher" alias="defaultPathMatcher" />
	<bean id="antPathMatcher" class="org.springframework.util.AntPathMatcher" />

	<security:global-method-security pre-post-annotations="enabled" proxy-target-class="true" secured-annotations="enabled" />

	<bean id="cisRedirectHandler" class="com.sast.cis.shop.frontend.security.CisRedirectHandler">
		<property name="configurationService" ref="configurationService"/>
	</bean>

	<!-- Default security config -->
	<security:http use-expressions="true" request-matcher-ref="excludeUrlRequestMatcher" auto-config="false" entry-point-ref="keycloakAuthenticationEntryPoint" >
		<security:expression-handler ref="cisRedirectHandler"/>
		<security:anonymous username="anonymous" granted-authority="ROLE_ANONYMOUS" />
		<security:access-denied-handler ref="ssoAccessDeniedHandler"/>
		<!-- <security:csrf token-repository-ref="csrfTokenRepository" request-matcher-ref="csrfProtectionMatcher" /> DISABLED FOR DEVELOPMENT -->
		<security:custom-filter before="CSRF_FILTER" ref="logoutFilter" />
		<security:custom-filter ref="keycloakPreAuthActionsFilter" before="LOGOUT_FILTER"/>
		<security:custom-filter ref="keycloakAuthenticationProcessingFilter" before="FORM_LOGIN_FILTER"/>
		<security:custom-filter ref="logoutFilter" position="LOGOUT_FILTER"/>
		<security:custom-filter ref="cisKeycloakSecurityContextRequestFilter" after="ANONYMOUS_FILTER"/>

		<!-- SSL / AUTHENTICATED pages -->
		<security:intercept-url pattern="/robots.txt" access="permitAll" requires-channel="https" /> <!-- Robots.txt should be available for everybody -->
		<security:intercept-url pattern="/_s/**" access="permitAll" requires-channel="https"/> <!-- Store info page should be available for everybody  (e.g to change language on login page)-->
		<security:intercept-url pattern="/images/**" access="permitAll" requires-channel="https"/>
		<security:intercept-url pattern="/media/images/**" access="permitAll" requires-channel="https"/>
		<security:intercept-url pattern="/favicon.ico" access="permitAll" requires-channel="https"/>

		<security:intercept-url pattern="/" access="isFullyAuthenticated() or requestNotOriginatingInKnownReferrer()" requires-channel="https"/>
		<security:intercept-url pattern="/tools" access="isFullyAuthenticated() or requestNotOriginatingInKnownReferrer()" requires-channel="https"/>
		<security:intercept-url pattern="/catalog/c/main" access="isFullyAuthenticated() or requestNotOriginatingInKnownReferrer()" requires-channel="https"/>
		<security:intercept-url pattern="/api/coredata" access="isFullyAuthenticated() or requestNotOriginatingInKnownReferrer()" requires-channel="https"/>
		<security:intercept-url pattern="/api/products" access="isFullyAuthenticated() or requestNotOriginatingInKnownReferrer()" requires-channel="https"/>
		<security:intercept-url pattern="/api/promotion/consent/*" access="isFullyAuthenticated() or requestNotOriginatingInKnownReferrer()" requires-channel="https"/>
		<security:intercept-url pattern="/api/products/prices" access="isFullyAuthenticated()" requires-channel="https"/>
		<security:intercept-url pattern="/api/products/*" access="isFullyAuthenticated() or requestNotOriginatingInKnownReferrer()" requires-channel="https"/>
		<security:intercept-url pattern="/api/categories/**" access="isFullyAuthenticated() or requestNotOriginatingInKnownReferrer()" requires-channel="https"/>
		<security:intercept-url pattern="/api/tools" access="isFullyAuthenticated() or requestNotOriginatingInKnownReferrer()" requires-channel="https"/>
		<security:intercept-url pattern="/api/app/*/company-profile" access="isFullyAuthenticated() or requestNotOriginatingInKnownReferrer()" requires-channel="https"/>
		<security:intercept-url pattern="/api/session/country-store" access="isFullyAuthenticated() or requestNotOriginatingInKnownReferrer()" requires-channel="https"/>
		<security:intercept-url pattern="/p/**" access="isFullyAuthenticated() or requestNotOriginatingInKnownReferrer()" requires-channel="https"/>
		<security:intercept-url pattern="/company-profile/**" access="isFullyAuthenticated() or requestNotOriginatingInKnownReferrer()" requires-channel="https"/>
		<security:intercept-url pattern="/help-resources" access="isFullyAuthenticated() or requestNotOriginatingInKnownReferrer()" requires-channel="https"/>
		<security:intercept-url pattern="/products-overview" access="isFullyAuthenticated() or requestNotOriginatingInKnownReferrer()" requires-channel="https"/>
		<security:intercept-url pattern="/products-search" access="isFullyAuthenticated() or requestNotOriginatingInKnownReferrer()" requires-channel="https"/>
		<security:intercept-url pattern="/product-selection/*" access="isFullyAuthenticated() or requestNotOriginatingInKnownReferrer()" requires-channel="https"/>

		<security:intercept-url pattern="/**" access="isFullyAuthenticated()" requires-channel="https"/> <!-- Everything should be secure -->

		<security:port-mappings>
			<security:port-mapping http="#{configurationService.configuration.getProperty('tomcat.http.port')}"
								   https="#{configurationService.configuration.getProperty('tomcat.ssl.port')}" />
			<security:port-mapping http="80" https="443" />
			<!--security:port-mapping http="#{configurationService.configuration.getProperty('proxy.http.port')}"
				https="#{configurationService.configuration.getProperty('proxy.ssl.port')}" /-->
		</security:port-mappings>

		<security:request-cache ref="httpSessionRequestCache" />

		<security:headers>
			<security:frame-options disabled="true"/>
		</security:headers>
	</security:http>

	<!-- Security configuration for swagger resources -->
	<security:http pattern="/#{configurationService.configuration.getProperty('springfox.documentation.swagger.v2.path')}" security="none"/>
	<security:http pattern="/swagger-resources/**" security="none"/>
	<security:http pattern="/swagger-ui.html" security="none"/>
	<security:http pattern="/webjars/*swagger*/**" security="none"/>

	<bean id="ssoAccessDeniedHandler" class="com.sast.cis.web.security.sso.SsoAccessDeniedHandler"/>

	<alias name="defaultGuestCheckoutCartCleanStrategy" alias="guestCheckoutCartCleanStrategy"/>
	<bean id="defaultGuestCheckoutCartCleanStrategy" class="com.sast.cis.shop.frontend.security.impl.DefaultGuestCheckoutCartCleanStrategy">
		<property name="skipPatterns">
			<list>
				<value>#{configurationService.configuration.getProperty('cisshopfrontend.checkout.url.pattern')}</value>
				<value>#{configurationService.configuration.getProperty('cisshopfrontend.favicon.pattern')}</value>
			</list>
		</property>
		<property name="checkoutCustomerStrategy"  ref="checkoutCustomerStrategy" />
		<property name="cartService" ref="cartService" />
		<property name="sessionService" ref="sessionService"/>
		<property name="userService" ref="userService"/>
	</bean>

	<bean id="httpSessionRequestCache" class="com.sast.cis.shop.frontend.security.impl.WebHttpSessionRequestCache">
		<property name="requestMatcher" ref="excludeRedirectUrlRequestMatcher" />
		<property name="sessionService" ref="sessionService"/>
		<property name="configurationService" ref="configurationService"/>
		<property name="forceSaveUrls">
			<set>
				<value>/login</value>
			</set>
		</property>
	</bean>

	<!-- Ignores url(s) that match paths specified in the set -->
	<bean id="excludeRedirectUrlRequestMatcher" class="com.sast.cis.shop.frontend.security.ExcludeUrlRequestMatcher">
		<property name="excludeUrlSet">
			<set>
				<value>/order-confirmation/*</value>
				<value>/login/checkout</value>
				<value>/sso**</value>
				<value>/_s/**</value>
				<value>/images/**</value>
			</set>
		</property>
		<property name="pathMatcher" ref="defaultPathMatcher" />
	</bean>

	<bean id="redirectStrategy" class="org.springframework.security.web.DefaultRedirectStrategy"  />

	<!-- Login Success Handlers -->

	<alias name="defaultLoginAuthenticationSuccessHandler" alias="loginAuthenticationSuccessHandler"/>
	<bean id="defaultLoginAuthenticationSuccessHandler" class="com.sast.cis.web.security.KeycloakAuthenticationSuccessHandler" >
		<property name="customerFacade" ref="customerFacade" />
		<property name="defaultTargetUrl" value="${shop.landingpage}"/>
		<property name="useReferer" value="false"/>
		<property name="requestCache" ref="httpSessionRequestCache" />
		<property name="uiExperienceService" ref="uiExperienceService"/>
		<property name="cartFacade" ref="cartFacade"/>
		<property name="customerConsentDataStrategy" ref="customerConsentDataStrategy"/>
		<property name="cartRestorationStrategy" ref="cartRestorationStrategy"/>
		<property name="forceDefaultTargetForUiExperienceLevel">
			<map key-type="de.hybris.platform.commerceservices.enums.UiExperienceLevel" value-type="java.lang.Boolean">
				<entry key="DESKTOP" value="false"/>
				<entry key="MOBILE" value="false"/>
			</map>
		</property>
		<property name="bruteForceAttackCounter" ref="bruteForceAttackCounter" />
		<property name="listRedirectUrlsForceDefaultTarget">
			<list></list>
		</property>
		<property name="ssoUserService" ref="shopSsoUserService"/>
		<property name="userAuthorizationStrategy" ref="shopUserAuthorizationStrategy"/>
		<property name="restrictedPages">
			<util:list>
				<value>${sso.keycloak.configuration.auth-server-url}</value>
			</util:list>
		</property>
	</bean>

	<!-- CSRF -->
	<bean id="csrfTokenRepository" class="org.springframework.security.web.csrf.HttpSessionCsrfTokenRepository">
		<property name="headerName" value="CSRFToken" />
		<property name="parameterName" value="CSRFToken" />
	</bean>

	<util:list id="csrfAllowedUrlPatternsList" />

	<alias name="defaultCsrfProtectionMatcher" alias="csrfProtectionMatcher"/>
	<bean id="defaultCsrfProtectionMatcher" class="com.sast.cis.shop.frontend.security.CsrfProtectionMatcher">
		<property name="csrfAllowedUrlPatterns" ref="csrfAllowedUrlPatternsList"/>
	</bean>

	<!-- Keycloak configuration -->
	<context:component-scan base-package="org.keycloak.adapters.springsecurity" />

	<security:authentication-manager alias="authenticationManager">
		<security:authentication-provider ref="keycloakAuthenticationProvider"/>
	</security:authentication-manager>

	<bean id="siteKeycloakConfigResolver" class="com.sast.cis.web.security.sso.SiteKeycloakConfigResolver">
		<constructor-arg name="baseStoreService" ref="baseStoreService"/>
		<constructor-arg name="siteUmpAdapterConfigResolutionService" ref="siteUmpAdapterConfigResolutionService"/>
	</bean>

	<bean id="adapterDeploymentContext" class="org.keycloak.adapters.AdapterDeploymentContext">
		<constructor-arg name="configResolver" ref="siteKeycloakConfigResolver"/>
	</bean>

	<bean id="cisKeycloakSecurityContextRequestFilter"
		  class="com.sast.cis.web.security.sso.CisKeycloakSecurityContextRequestFilter"/>

	<bean id="keycloakAuthenticationEntryPoint"
		  class="org.keycloak.adapters.springsecurity.authentication.KeycloakAuthenticationEntryPoint">
		<constructor-arg ref="adapterDeploymentContext"/>
	</bean>

	<bean id="keycloakAuthenticationProvider"
		  class="org.keycloak.adapters.springsecurity.authentication.KeycloakAuthenticationProvider"/>

	<bean id="keycloakPreAuthActionsFilter"
		  class="org.keycloak.adapters.springsecurity.filter.KeycloakPreAuthActionsFilter">
		<constructor-arg ref="cisHttpSessionManager"/>
	</bean>

	<bean id="keycloakAuthenticationProcessingFilter"
		  class="org.keycloak.adapters.springsecurity.filter.KeycloakAuthenticationProcessingFilter">
		<constructor-arg name="authenticationManager" ref="authenticationManager"/>
		<property name="authenticationSuccessHandler" ref="loginAuthenticationSuccessHandler"/>
	</bean>

	<bean id="keycloakLogoutHandler" class="org.keycloak.adapters.springsecurity.authentication.KeycloakLogoutHandler">
		<constructor-arg ref="adapterDeploymentContext"/>
	</bean>

	<bean id="logoutFilter" class="org.springframework.security.web.authentication.logout.LogoutFilter">
		<constructor-arg name="logoutSuccessUrl" value="/"/>
		<constructor-arg name="handlers">
			<list>
				<ref bean="keycloakLogoutHandler"/>
				<bean class="org.springframework.security.web.authentication.logout.SecurityContextLogoutHandler"/>
			</list>
		</constructor-arg>
		<property name="logoutRequestMatcher">
			<bean class="org.springframework.security.web.util.matcher.AntPathRequestMatcher">
				<constructor-arg name="pattern" value="/logout**"/>
				<constructor-arg name="httpMethod" value="GET"/>
			</bean>
		</property>
	</bean>

	<bean id="keycloakRestTemplate" class="org.keycloak.adapters.springsecurity.client.KeycloakRestTemplate"
		  scope="prototype">
		<constructor-arg name="factory" ref="keycloakClientRequestFactory"/>
	</bean>
</beans>
