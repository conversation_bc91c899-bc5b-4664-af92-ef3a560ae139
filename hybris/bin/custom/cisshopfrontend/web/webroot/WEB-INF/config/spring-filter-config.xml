<?xml version="1.0" encoding="UTF-8"?>
<!--
 [y] hybris Platform

 Copyright (c) 2017 SAP SE or an SAP affiliate company.  All rights reserved.

 This software is the confidential and proprietary information of SAP
 ("Confidential Information"). You shall not disclose such Confidential
 Information and shall use it only in accordance with the terms of the
 license agreement you entered into with SAP.
-->
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:util="http://www.springframework.org/schema/util"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
		http://www.springframework.org/schema/beans/spring-beans.xsd
		http://www.springframework.org/schema/util
		http://www.springframework.org/schema/util/spring-util.xsd
		http://www.springframework.org/schema/context
        http://www.springframework.org/schema/context/spring-context.xsd">

	<context:annotation-config/>



	<!-- tenant scoped filter -->

	<bean id="storefrontTenantFilterChain" class="com.sast.cis.shop.frontend.filters.UrlPathFilter" >
		<property name="defaultFilter" ref="storefrontTenantDefaultFilterChain"/>
		<property name="urlPathHelper">
			<bean class="org.springframework.web.util.UrlPathHelper"/>
		</property>
		<property name="urlPathMapping">
			<map>
				<entry key="/integration/" value-ref="integrationTenantFilterChain"/>
				<entry key="/sso/k_" value-ref="keycloakAdminRequestPlatformFilterChain"/>
			</map>
		</property>
	</bean>

	<alias name="defaultStorefrontTenantDefaultFilterChainList" alias="storefrontTenantDefaultFilterChainList" />
	<util:list id="defaultStorefrontTenantDefaultFilterChainList">
		<!-- filter for handling session failover -->
    	<ref bean="hybrisSpringSessionFilter"/>


		<!-- generic platform filters -->
		<!-- <ref bean="corsFilter"/> DISABLED FOR DEVELOPMENT -->
		<ref bean="log4jFilter"/>
		<ref bean="mediaSessionFilter"/>
		<ref bean="storefrontSessionFilter"/>
		<ref bean="logMessageFilter"/>
		<ref bean="cisshopfrontendMediaFilter"/>
		<ref bean="addOnDevelopmentFilter"/>

		<!-- filter to log the current request -->
		<ref bean="requestLoggerFilter"/>

		<!-- filter to setup the cms integration -->
		<ref bean="cmsSiteFilter"/>

		<!-- filter to initialize the storefront -->
		<ref bean="storefrontFilter"/>

		<!-- filter to handle url encoding attributes -->
		<ref bean="urlEncoderFilter"/>

		<!-- filter to handle multipart file upload -->
		<ref bean="fileUploadFilter"/>

		<!-- Security -->
		<ref bean="springSecurityFilterChain"/>

		<!-- filter to log out guest user if he/she attempts to access a page outside of checkout flow -->
		<ref bean="anonymousCheckoutFilter"/>

		<!-- filter to restore items in cart -->
		<ref bean="cartRestorationFilter"/>

		<!-- filter to restore customer preferred location -->
		<ref bean="customerLocationRestorationFilter"/>
	</util:list>

	<bean id="mediaSessionFilter" class="com.sast.cis.web.filter.MediaSessionFilter">
		<property name="userService" ref="userService"/>
	</bean>

	<bean id="storefrontTenantDefaultFilterChain" class="de.hybris.platform.servicelayer.web.PlatformFilterChain" >
		<constructor-arg>
			<ref bean="storefrontTenantDefaultFilterChainList"/>
		</constructor-arg>
	</bean>

	<alias name="defaultIntegrationTenantFilterChainList" alias="integrationTenantFilterChainList" />
	<util:list id="defaultIntegrationTenantFilterChainList">
		<!-- generic platform filters -->
		<ref bean="storefrontSessionFilter"/>
		<ref bean="addOnDevelopmentFilter"/>
		<!-- filter to log the current request -->
		<ref bean="requestLoggerFilter"/>
	</util:list>

	<bean id="integrationTenantFilterChain" class="de.hybris.platform.servicelayer.web.PlatformFilterChain" >
		<constructor-arg>
			<ref bean="integrationTenantFilterChainList" />
		</constructor-arg>
	</bean>


	<bean id="keycloakAdminRequestPlatformFilterChain" class="de.hybris.platform.servicelayer.web.PlatformFilterChain">
		<constructor-arg>
			<list>
				<ref bean="log4jFilter"/>
				<ref bean="restSessionFilter"/>
				<ref bean="sessionCatalogFilter"/>
				<ref bean="springSecurityFilterChain"/>
			</list>
		</constructor-arg>
	</bean>

	<bean id="sessionCatalogFilter" class="com.sast.cis.core.filter.SessionCatalogFilter">
		<constructor-arg name="baseStoreId" value="iotstore"/>
	</bean>

	<bean id="restSessionFilter" class="de.hybris.platform.webservicescommons.filter.RestSessionFilter">
		<property name="sessionService" ref="sessionService"/>
	</bean>

	<bean id="cisshopfrontendMediaFilter" class="com.sast.cis.web.filter.CisWebAppMediaFilter">
		<property name="mediaPermissionService" ref="mediaPermissionService"/>
		<property name="modelService" ref="modelService"/>
		<property name="userService" ref="userService"/>
		<property name="mediaService" ref="mediaService"/>
		<property name="addContextPath" value="true"/>
	</bean>

	<bean id="urlEncoderFilter" class="com.sast.cis.shop.frontend.filters.UrlEncoderFilter" >
		<property name="urlEncoderFacade" ref="urlEncoderFacade"/>
		<property name="sessionService" ref="sessionService"/>
	</bean>

	<bean id="storefrontSessionFilter" class="de.hybris.platform.servicelayer.web.SessionFilter" >
		<property name="sessionService" ref="sessionService"/>
	</bean>

	<bean id="logMessageFilter" class="com.sast.cis.web.filter.LogMessageFilter"/>

	<bean id="addOnDevelopmentFilter" class="com.sast.cis.shop.frontend.filters.AcceleratorAddOnFilter" >
		<property name="configurationService" ref="configurationService"/>
	</bean>

	<bean id="requestLoggerFilter" class="com.sast.cis.shop.frontend.filters.RequestLoggerFilter"/>

	<bean id="cmsSiteFilter" class="com.sast.cis.shop.frontend.filters.cms.CMSSiteFilter" >
		<property name="previewDataModelUrlResolver" ref="previewDataModelUrlResolver"/>
		<property name="cmsSiteService" ref="cmsSiteService"/>
		<property name="cmsPreviewService" ref="cmsPreviewService"/>
		<property name="baseSiteService" ref="baseSiteService"/>
		<property name="commerceCommonI18NService" ref="commerceCommonI18NService"/>
		<property name="sessionService" ref="sessionService"/>
		<property name="contextInformationLoader" ref="contextInformationLoader"/>
		<property name="cmsPageContextService" ref="cmsPageContextService"/>
		<property name="siteChannelValidationStrategy" ref="acceleratorSiteChannelValidationStrategy"/>
	</bean>

	<bean id="acceleratorSiteChannelValidationStrategy" class="de.hybris.platform.acceleratorservices.site.strategies.impl.DefaultSiteChannelValidationStrategy">
		<property name="supportedSiteChannels" ref="acceleratorSiteChannels" />
	</bean>

	<alias name="b2cAcceleratorSiteChannels" alias="acceleratorSiteChannels"/>
	<util:set id="b2cAcceleratorSiteChannels" value-type="de.hybris.platform.commerceservices.enums.SiteChannel">
		<ref bean="SiteChannel.B2C"/>
	</util:set>

	<alias name="defaultRefererExcludeUrlSet" alias="refererExcludeUrlSet"/>
	<util:set id="defaultRefererExcludeUrlSet" value-type="java.lang.String">
	</util:set>

	<bean id="storefrontFilter" class="com.sast.cis.shop.frontend.filters.StorefrontFilter" >
		<property name="storeSessionFacade" ref="storeSessionFacade"/>
		<property name="browseHistory" ref="browseHistory"/>
		<property name="refererExcludeUrlSet" ref="refererExcludeUrlSet"/>
		<property name="pathMatcher" ref="defaultPathMatcher"/>
		<property name="i18nSessionInitializer" ref="i18nSessionInitializer"/>
	</bean>

	<bean id="cartRestorationFilter" class="com.sast.cis.shop.frontend.filters.CartRestorationFilter" >
		<property name="baseSiteService" ref="baseSiteService"/>
		<property name="cartService" ref="cartService"/>
		<property name="cartFacade" ref="cartFacade"/>
		<property name="cartRestoreCookieGenerator" ref="cartRestoreCookieGenerator"/>
		<property name="userService" ref="userService"/>
		<property name="sessionService" ref="sessionService"/>
	</bean>

	<bean id="anonymousCheckoutFilter" class="com.sast.cis.shop.frontend.filters.AnonymousCheckoutFilter" >
		<property name="guestCheckoutCartCleanStrategy"  ref="guestCheckoutCartCleanStrategy" />
	</bean>

	<bean id="customerLocationRestorationFilter" class="com.sast.cis.shop.frontend.filters.CustomerLocationRestorationFilter" >
		<property name="customerLocationFacade" ref="customerLocationFacade"/>
		<property name="customerLocationCookieGenerator" ref="customerLocationCookieGenerator"/>
	</bean>

	<alias name="defaultFileUploadUrlFilterMappings" alias="fileUploadUrlFilterMappings" />
	<util:map id="defaultFileUploadUrlFilterMappings" key-type="java.lang.String" value-type="org.springframework.web.multipart.support.MultipartFilter">
		<entry key="/import/csv/*" value-ref="importCSVMultipartFilter"/>
	</util:map>

	<bean id="fileUploadFilter" class="com.sast.cis.shop.frontend.filters.FileUploadFilter" >
		<property name="urlFilterMapping">
			<ref bean="fileUploadUrlFilterMappings" />
		</property>
		<property name="pathMatcher" ref="defaultPathMatcher"/>
	</bean>

	<alias name="defaultSessionCookieGenerator" alias="sessionCookieGenerator"/>
	<bean id="defaultSessionCookieGenerator" class="com.sast.cis.shop.frontend.security.cookie.EnhancedCookieGenerator" >
		<property name="cookieSecure" value="true"/>
		<property name="cookieName" value="JSESSIONID"/>
		<property name="cookieMaxAge" value="-1"/>
		<property name="useDefaultPath" value="false"/>
		<property name="httpOnly" value="true"/>
	</bean>

	<alias name="defaultCartRestoreCookieGenerator" alias="cartRestoreCookieGenerator"/>
	<bean id="defaultCartRestoreCookieGenerator" class="com.sast.cis.shop.frontend.security.cookie.CartRestoreCookieGenerator" >
		<property name="cookieSecure" value="true"/>
		<property name="cookieMaxAge" value="360000000"/>
		<property name="useDefaultPath" value="false"/>
		<property name="httpOnly" value="true"/>
		<property name="baseSiteService" ref="baseSiteService"/>
	</bean>

	<alias name="defaultCustomerLocationCookieGenerator" alias="customerLocationCookieGenerator"/>
	<bean id="defaultCustomerLocationCookieGenerator" class="com.sast.cis.shop.frontend.security.cookie.CustomerLocationCookieGenerator" >
		<property name="cookieSecure" value="true"/>
		<property name="cookieMaxAge" value="360000000"/>
		<property name="useDefaultPath" value="false"/>
		<property name="httpOnly" value="true"/>
		<property name="baseSiteService" ref="baseSiteService"/>
	</bean>

	<bean id="importCSVMultipartFilter" class="org.springframework.web.multipart.support.MultipartFilter">
		<property name="multipartResolverBeanName" value="importCSVMultipartResolver"/>
	</bean>

	<bean id="importCSVMultipartResolver" class="org.springframework.web.multipart.commons.CommonsMultipartResolver">
		<property name="maxUploadSize" value="#{configurationService.configuration.getProperty('import.csv.max.upload.size.bytes')}" />
	</bean>
</beans>
