# -----------------------------------------------------------------------
# [y] hybris Platform
#
# Copyright (c) 2017 SAP SE or an SAP affiliate company.  All rights reserved.
#
# This software is the confidential and proprietary information of SAP
# ("Confidential Information"). You shall not disclose such Confidential
# Information and shall use it only in accordance with the terms of the
# license agreement you entered into with SAP.
# -----------------------------------------------------------------------
# you can put key/value pairs here.
# Use Config.getParameter(..) to retrieve the values during runtime.

# Specifies the location of the spring context file putted automatically to the global platform application context.
cisshopfrontend.application-context=cisshopfrontend-spring.xml

# Specifies the location of the acceleratorstorefrontcommons spring context file
cisshopfrontend.additionalWebSpringConfigs.acceleratorstorefrontcommons=classpath:/acceleratorstorefrontcommons/web/spring/acceleratorstorefrontcommons-spring.xml

# Specifies the location of the addonsupport web spring context file
cisshopfrontend.additionalWebSpringConfigs.addonsupport=classpath:/addonsupport/web/spring/addonsupport-b2c-web-spring.xml

# Specify how long resource messages should be cached.
# Set to -1 to never expire the cache.
# Suggested values:
#   Development: 1
#   Production: -1
storefront.resourceBundle.cacheSeconds=1

# Turn on test IDs for selenium and smoke tests (not for production)
#cisshopfrontend.testIds.enable=true

# Show debug info in the frontend pages
# Suggested values:
#   Development: true
#   Production:  false
storefront.show.debug.info=false

# Flag the tomcat JSessionID cookie as httpOnly
cisshopfrontend.tomcat60.context.useHttpOnly=true

# URLs that do not need CSRF verification
csrf.allowed.url.patterns=/[^/]+(/[^?]*)+(merchant_callback)$,/[^/]+(/[^?]*)+(language)$,/[^/]+(/[^?]*)+(currency)$,.*/sso/.*
#csrf.allowed.url.patterns=^https?://orderpagetest.ic3.com+(/[^?]*)$

# Enable storefront static resources client side caching.
# Suggested values:
#   Development: no-cache,must-revalidate
#   Production: public,max-age=600
# In development this will prevent these resources from being cached.
# In production this will cache these static resources for only 10 minutes
# after which the client will check if the resource has changed using the
# resource's ETAG. This is required as the resource file contents could change
# without the URL changing.
#storefront.staticResourceFilter.response.header.Cache-Control=public,max-age=600
storefront.staticResourceFilter.response.header.Cache-Control=no-cache,must-revalidate


# Enable media client side caching for 1 year. This is fine because the media
# URLs change each time there is a data change within the media
mediafilter.response.header.Cache-Control=public,max-age=31536000



###########
# Context root for the storefront.
# By default this is set to the extension name.
# The XXX.webroot controls the actual context path for the extension's web module.
# The storefrontContextRoot is used to set the context path in the impexed data.
# Both of these values should be changed together.
# If you want to change these values then it is suggested that you override both of
# them in your config/local.properties file rather than changing the value here.
# If you want to remap the the context path to the root site on the web container
# then you must set these values to be blank, do not set them to be '/'
cisshopfrontend.webroot=/cisshopfrontend
storefrontContextRoot=/cisshopfrontend

# Google API key
# For information on generating your API Key: https://developers.google.com/maps/documentation/javascript/tutorial#api_key
#googleApiKey=

#version of the google map api
googleApiVersion=3.7

# This is the (XML format) reverse geocoding URL for version 3 of the
# Google Maps JavaScript API
google.geocoding.reverse.url=https://maps.googleapis.com/maps/api/geocode/xml

# Google Analytics Tracking IDs. Can be host specific, as listed below.
#google.analytics.tracking.id=your_google_analytics_tracking_id

# Jirafe properties
#jirafe.api.url=your_url
#jirafe.api.token=your_api_token
#jirafe.app.id=your_api_id
#jirafe.version=your_version
#jirafe.data.url=your_data_url

# Default site ID
#jirafe.site.id=your_site_id

###########
# Search results page size configuration per store:
# Set to zero to load the default page size
storefront.search.pageSize.Desktop=0

###########
# Configuration for showing checkout flow options on the cart page
# The accelerator has a strategy to select the checkout flow for a site. This strategy
# can be overridden by selecting a different strategy on the cart page. This may be useful
# during development while the desired strategy is being developed.
# For production this should be disabled. If no configuration is specified then FALSE is assumed.
# The configuration below is set to enable checkout flow selection for all sites.
# The configuration can also be overridden per site.
storefront.show.checkout.flows=true
# Specify configuration for a single site
#storefront.show.checkout.flows.electronics=true

###########
#Definition of which countries regions should be attached to the address
resolve.country.regions=US,CA

#Fallback taxcode is returned by DefaultTaxCodeStrategy when it cannot find taxCode for product and taxArea
#Different value can be configure for different base store by adding base store name at the end of property name
externaltax.fallbacktaxcode=PC040100


#Number of pagination results to display
pagination.number.results.count=5

############XSS FILTER SECURITY SETTINGS###############
xss.filter.enabled=true
cisshopfrontend.xss.filter.rule.src1=(?ims)[\\s\r\n]+src[\\s\r\n]*=[\\s\r\n]*\\\'(.*?)\\\'
cisshopfrontend.xss.filter.rule.src2=(?ims)[\\s\r\n]+src[\\s\r\n]*=[\\s\r\n]*\\\"(.*?)\\\"
cisshopfrontend.xss.filter.rule.src3=(?ims)[\\s\r\n]+src[\\s\r\n]*=*(.*?)>
cisshopfrontend.xss.filter.rule.javascript2=(?i)\\u0023
cisshopfrontend.xss.filter.rule.braces=(?i)<(.*?)>
#######################################################

#https://jira.spring.io/browse/SPR-9014
spring.beaninfo.ignore=true

# Checkout URL pattern
cisshopfrontend.checkout.url.pattern=(^https://.*/checkout/.*)
cisshopfrontend.favicon.pattern=(^https://.*/favicon.ico.*)

#############################
### Weblogic specific properties
## Define the tld libs required to be copied to the WEB-INF of the generated war
## when packaging into an ear
#############################
cisshopfrontend.weblogic.copy-tld-files=spring.tld,spring-form.tld,security.tld

##########
# Add mime support for svg format
mediatype.by.fileextension.svg=image/svg+xml

#########
# Defines the minimum and maximum number of rows for quick order
cisshopfrontend.quick.order.rows.min=3
cisshopfrontend.quick.order.rows.max=25

##########
# Defines maximum size in bytes of the upload request that takes into account additional parameters,
# such as CSRF token (please note that the size of the upload request seems to vary between browsers)
import.csv.max.upload.size.bytes=11240
# Defines maximum size in bytes of the uploaded CSV file
import.csv.file.max.size.bytes=10240

##########
# When restoring a saved cart, one copy of the restored saved cart can be kept. The name of this copied/(cloned) cart is
# the original saved cart name + copy#. This property set the regex for the name suffix of #.
commerceservices.clone.savecart.name.regex=(\\s+\\d*)$

##########
# Defines whether we want to update the uploading saved carts, which show the message of "CSV file is importing..."
refresh.uploading.saved.cart=true
# Defines the interval of the requests for refreshing uploading saved cart
refresh.uploading.saved.cart.interval=5000

##########
# Quotes #
##########
# number of comments to show initially (integer)
quote.pagination.numberofcomments=4

#################################
### Jar scanning setup for Tomcat
#################################
cisshopfrontend.tomcat.tld.scan=json-taglib*.jar,*jstl-1*.jar
cisshopfrontend.tomcat.tld.default.scan.enabled=false
cisshopfrontend.tomcat.pluggability.scan=json-taglib*.jar,*jstl-1*.jar
cisshopfrontend.tomcat.pluggability.default.scan.enabled=false

#######################################
### Setup for Swagger API documentation
#######################################
cisshopfrontend.documentation.desc=cisshopfrontend Documentation
cisshopfrontend.documentation.title=cisshopfrontend API

#enables static documentation generation by wsStaticDoc ant task
cisshopfrontend.documentation.static.generate=true

#CORS filter configuration (required for PGW hosted page) - ALLOW ALL FOR DEVELOPMENT
corsfilter.allowedOrigins=*
corsfilter.allowedMethods=GET,POST,PUT,DELETE,OPTIONS,HEAD,PATCH
corsfilter.allowedHeaders=*
corsfilter.allowCredentials=true
corsfilter.maxAge=3600