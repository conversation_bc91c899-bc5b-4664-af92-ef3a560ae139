package com.sast.cis.payment.pgw.service.client

import com.sast.cis.core.model.SepaMandatePaymentInfoModel
import com.sast.cis.core.paymentintegration.PaymentRedirectTargetProvider
import de.hybris.bootstrap.annotations.UnitTest
import de.hybris.platform.core.model.c2l.CurrencyModel
import de.hybris.platform.core.model.order.AbstractOrderModel
import de.hybris.platform.payment.model.PaymentTransactionModel
import de.hybris.platform.testframework.JUnitPlatformSpecification
import generated.com.sast.cis.core.model.SepaCreditTransferPaymentInfoBuilder
import org.junit.Test

@UnitTest
class CreateHppRequestDtoFactoryForTransactionUnitSpec extends JUnitPlatformSpecification {

    private PaymentRedirectTargetProvider paymentRedirectTargetProvider = Mock()

    private CreateHppRequestDtoFactoryForTransaction factoryForTransaction

    private PaymentTransactionModel paymentTransaction = Mock()
    private SepaMandatePaymentInfoModel paymentInfo = Mock()
    private AbstractOrderModel order = Mock()
    private CurrencyModel eur = Mock()

    private final URI successRedirectTarget = new URI('https://aa.store.dev.local:9002/shop/checkout/multi/payment/12345/successful')
    private final URI failureRedirectTarget = new URI('https://aa.store.dev.local:9002/shop/checkout/multi/payment/12345/failed')
    private final URI cancelRedirectTarget = new URI('https://aa.store.dev.local:9002/shop/checkout/multi/payment/12345/cancelled')

    private final String transactionCode = 'tx_code'
    private final String language = 'de'
    private final String mandateReference = 'mandate'
    private final BigDecimal amount = new BigDecimal("5.75")

    void setup() {
        factoryForTransaction = new CreateHppRequestDtoFactoryForTransaction(paymentRedirectTargetProvider)

        paymentTransaction.getOrder() >> order
        paymentTransaction.getInfo() >> paymentInfo
        paymentTransaction.getCode() >> transactionCode
        paymentTransaction.getPlannedAmount() >> amount
        paymentTransaction.getCurrency() >> eur

        paymentInfo.getMandateReference() >> mandateReference

        eur.getIsocode() >> 'EUR'

        paymentRedirectTargetProvider.getSuccessUrl(order) >> successRedirectTarget
        paymentRedirectTargetProvider.getFailureUrl(order) >> failureRedirectTarget
        paymentRedirectTargetProvider.getCancelUrl(order) >> cancelRedirectTarget
    }

    @Test
    void "should create request dto with correct values"() {
        when:
        def result = factoryForTransaction.createForTransaction(paymentTransaction, language)

        then:
        result
        result.language() == language
        result.transactionId() == transactionCode
        result.referenceNumber() == mandateReference
        result.amount().amount() == amount
        result.amount().currency() == Currency.getInstance('EUR')
        result.storeRedirectTargets().successUri() == successRedirectTarget
        result.storeRedirectTargets().failureUri() == failureRedirectTarget
        result.storeRedirectTargets().cancelUri() == cancelRedirectTarget
    }


    @Test
    void 'createForTransaction throws IllegalStateException if tx has no info'() {
        when:
        factoryForTransaction.createForTransaction(paymentTransaction, language)

        then:
        paymentTransaction.getInfo() >> null
        thrown(IllegalStateException)
    }

    @Test
    void 'createForTransaction throws IllegalStateException if tx has info of wrong type'() {
        when:
        factoryForTransaction.createForTransaction(paymentTransaction, language)

        then:
        paymentTransaction.getInfo() >> SepaCreditTransferPaymentInfoBuilder.generate().buildInstance()
        thrown(IllegalStateException)
    }

    @Test
    void 'createForTransaction throws IllegalStateException if tx has no planned amount'() {
        when:
        factoryForTransaction.createForTransaction(paymentTransaction, language)

        then:
        paymentTransaction.getPlannedAmount() >> null
        thrown(IllegalStateException)
    }

    @Test
    void 'createForTransaction throws IllegalStateException if tx has no currency'() {
        when:
        factoryForTransaction.createForTransaction(paymentTransaction, language)

        then:
        paymentTransaction.getCurrency() >> null
        thrown(IllegalStateException)
    }

    @Test
    void 'createForTransaction throws IllegalStateException if tx has no order'() {
        when:
        factoryForTransaction.createForTransaction(paymentTransaction, language)

        then:
        paymentTransaction.getOrder() >> null
        thrown(IllegalStateException)
    }
}
