package com.sast.cis.payment.pgw.service.directdebit

import com.sast.cis.core.data.SepaMandatePaymentInfoData
import com.sast.cis.core.model.IntegratorModel
import com.sast.cis.core.model.SepaMandatePaymentInfoModel
import com.sast.cis.core.paymentintegration.paymentinfo.IntegratorPaymentInfoService
import com.sast.cis.payment.pgw.exception.PgwIbanException
import com.sast.pgw.proxyclient.model.rest.payment.details.SepaDirectDebitDetailsDto
import de.hybris.bootstrap.annotations.UnitTest
import de.hybris.platform.core.model.order.AbstractOrderModel
import de.hybris.platform.core.model.user.UserModel
import de.hybris.platform.payment.model.PaymentTransactionModel
import de.hybris.platform.servicelayer.model.ModelService
import de.hybris.platform.testframework.JUnitPlatformSpecification
import generated.com.sast.cis.core.model.SepaMandatePaymentInfoBuilder
import generated.com.sast.cis.payment.pgw.model.DirectDebitPaymentInfoDraftBuilder
import org.junit.Test

import java.time.Instant
import java.time.LocalDate

import static com.sast.cis.core.enums.PaymentProvider.DPG
import static com.sast.cis.core.enums.PaymentProvider.PGW

@UnitTest
class PgwSepaMandatePaymentInfoServiceUnitSpec extends JUnitPlatformSpecification {
    private static final String MERCHANT_ID = 'merch12345'
    private static final String WRONG_MERCHANT_ID = 'lkasdasd'
    private static final LocalDate SIGNATURE_LOCAL_DATE = LocalDate.of(2024, 02, 14)
    private static final Date SIGNATURE_DATE = Date.from(Instant.parse('2024-02-14T00:00:00Z'))
    private static final String ACCOUNT_OWNER = 'Hans Dampf'
    private static final String EXPECTED_IBAN = '**********************'
    private static final String INVALID_IBAN = '******************'
    private static final String MANDATE_REFERENCE = 'mandate982734'
    private IntegratorPaymentInfoService integratorPaymentInfoService = Mock()
    private ModelService modelService = Mock()

    private PgwSepaMandatePaymentInfoService pgwSepaMandatePaymentInfoService

    private SepaMandatePaymentInfoModel firstStoredInfo = Mock()
    private SepaMandatePaymentInfoModel secondStoredInfo = Mock()
    private SepaMandatePaymentInfoModel paymentInfoWithWrongMerchant = Mock()
    private SepaMandatePaymentInfoModel newInfo = Mock()
    private IntegratorModel integrator = Mock()
    private AbstractOrderModel order = Mock()
    private PaymentTransactionModel mockTx = Mock()

    def setup() {
        pgwSepaMandatePaymentInfoService = new PgwSepaMandatePaymentInfoService(integratorPaymentInfoService, modelService)
        firstStoredInfo.getUser() >> integrator
        firstStoredInfo.getPgwMerchantId() >> MERCHANT_ID
        secondStoredInfo.getPgwMerchantId() >> MERCHANT_ID
        secondStoredInfo.getUser() >> integrator
        newInfo.getPgwMerchantId() >> MERCHANT_ID
        newInfo.getUser() >> integrator
        paymentInfoWithWrongMerchant.getPgwMerchantId() >> WRONG_MERCHANT_ID
        paymentInfoWithWrongMerchant.getUser() >> integrator
        mockTx.getOrder() >> order
    }

    @Test
    void 'getPaymentProvider returns PGW'() {
        when:
        def actualPaymentProvider = pgwSepaMandatePaymentInfoService.getPaymentProvider()

        then:
        actualPaymentProvider == PGW
    }

    @Test
    void 'getPaymentInfos retrieves appropriate infos for the given integrator'() {
        when:
        def actualPaymentInfos = pgwSepaMandatePaymentInfoService.getPaymentInfos(integrator, MERCHANT_ID)

        then:
        1 * integratorPaymentInfoService.getPaymentInfos(integrator, PGW, SepaMandatePaymentInfoModel.class) >> ([firstStoredInfo, secondStoredInfo, paymentInfoWithWrongMerchant] as Set)
        actualPaymentInfos == [firstStoredInfo, secondStoredInfo] as Set
    }

    @Test
    void 'getDefaultPaymentInfo retrieves matching default payment info'() {
        when:
        def actualPaymentInfo = pgwSepaMandatePaymentInfoService.getDefaultPaymentInfo(integrator, MERCHANT_ID)

        then:
        1 * integratorPaymentInfoService.getDefaultPaymentInfo(integrator, PGW, SepaMandatePaymentInfoModel.class) >> Optional.of(secondStoredInfo)
        actualPaymentInfo == Optional.of(secondStoredInfo)
    }

    @Test
    void 'getDefaultPaymentInfo passes along empty optional if user has no matching default payment info'() {
        when:
        def actualPaymentInfo = pgwSepaMandatePaymentInfoService.getDefaultPaymentInfo(integrator, MERCHANT_ID)

        then:
        1 * integratorPaymentInfoService.getDefaultPaymentInfo(integrator, PGW, SepaMandatePaymentInfoModel.class) >> Optional.empty()
        actualPaymentInfo == Optional.empty()
    }

    @Test
    void 'getDefaultPaymentInfo passes along empty optional if user has no default payment info with matching merchant id'() {
        when:
        def actualPaymentInfo = pgwSepaMandatePaymentInfoService.getDefaultPaymentInfo(integrator, MERCHANT_ID)

        then:
        1 * integratorPaymentInfoService.getDefaultPaymentInfo(integrator, PGW, SepaMandatePaymentInfoModel.class) >> Optional.of(paymentInfoWithWrongMerchant)
        actualPaymentInfo == Optional.empty()
    }

    @Test
    void 'getCartPaymentInfo retrieves matching order payment info'() {
        when:
        def actualPaymentInfo = pgwSepaMandatePaymentInfoService.getCartPaymentInfo(order, MERCHANT_ID)

        then:
        1 * integratorPaymentInfoService.getCartPaymentInfo(order, PGW, SepaMandatePaymentInfoModel.class) >> Optional.of(firstStoredInfo)
        actualPaymentInfo == Optional.of(firstStoredInfo)
    }

    @Test
    void 'getCartPaymentInfo passes along empty optional if order has no matching default payment info'() {
        when:
        def actualPaymentInfo = pgwSepaMandatePaymentInfoService.getCartPaymentInfo(order, MERCHANT_ID)

        then:
        1 * integratorPaymentInfoService.getCartPaymentInfo(order, PGW, SepaMandatePaymentInfoModel.class) >> Optional.empty()
        actualPaymentInfo == Optional.empty()
    }

    @Test
    void 'getCartPaymentInfo passes along empty optional if order has no payment info with matching merchant'() {
        when:
        def actualPaymentInfo = pgwSepaMandatePaymentInfoService.getCartPaymentInfo(order, MERCHANT_ID)

        then:
        1 * integratorPaymentInfoService.getCartPaymentInfo(order, PGW, SepaMandatePaymentInfoModel.class) >> Optional.of(paymentInfoWithWrongMerchant)
        actualPaymentInfo == Optional.empty()
    }

    @Test
    void 'createPaymentInfo creates payment info with the given information as unsaved info'() {
        given:
        def givenAccountHolderName = 'Herbert'
        def givenDateOfSignature = new Date()
        def givenIban = '**********************'
        def givenMandateReference = 'ABCD12345FOOBARBAZ'
        def newPaymentInfo = SepaMandatePaymentInfoBuilder.generate().buildInstance()
        def givenPaymentInfoData = new SepaMandatePaymentInfoData()
                .withAccountHolderName(givenAccountHolderName)
                .withDateOfSignature(givenDateOfSignature)
                .withIban(givenIban)
                .withMandateReference(givenMandateReference)

        when:
        def actualPaymentInfo = pgwSepaMandatePaymentInfoService.createPaymentInfo(integrator, givenPaymentInfoData)

        then:
        1 * modelService.create(SepaMandatePaymentInfoModel.class) >> newPaymentInfo
        1 * modelService.save(newPaymentInfo)
        1 * modelService.refresh(integrator)
        actualPaymentInfo == newPaymentInfo
        verifyAll(actualPaymentInfo) {
            user == integrator
            !saved
            paymentProvider == PGW
            !code.blank
            code.startsWith('PgwSepaMandateInfo')
            mandateReference.startsWith('PGWMD')
            mandateReference.size() <= 30
        }
    }

    @Test
    void 'createPaymentInfoFromDraft creates payment info from the given payment info draft'() {
        given:
        def draftCode = 'draft_code'
        def draftMandateReference = 'draft_mandate_ref'
        def pgwMerchantId = 'pgw_merchant_id'
        def infoDraft = DirectDebitPaymentInfoDraftBuilder.generate()
                .withCode(draftCode)
                .withMandateReference(draftMandateReference)
                .withIntegrator(integrator)
                .withPgwMerchantId(pgwMerchantId)
                .buildInstance()
        def newPaymentInfo = SepaMandatePaymentInfoBuilder.generate().buildInstance()

        when:
        def actualPaymentInfo = pgwSepaMandatePaymentInfoService.createPaymentInfoFromDraft(infoDraft)

        then:
        1 * modelService.create(SepaMandatePaymentInfoModel.class) >> newPaymentInfo
        1 * modelService.saveAll(newPaymentInfo, infoDraft)
        1 * modelService.refresh(integrator)

        actualPaymentInfo == newPaymentInfo
        infoDraft.resultingPaymentInfo == actualPaymentInfo
        verifyAll(actualPaymentInfo) {
            user == integrator
            code == draftCode
            paymentProvider == PGW
            mandateReference == draftMandateReference
            !saved
            companyScope
        }
    }

    @Test
    void 'removePaymentInfo deletes the payment info and refreshes its integrator'() {
        when:
        pgwSepaMandatePaymentInfoService.removePaymentInfo(firstStoredInfo)

        then:
        1 * modelService.remove(firstStoredInfo)
        1 * modelService.refresh(integrator)
    }

    @Test
    void 'removePaymentInfo deletes the payment info and handles absence of integrator gracefully'() {
        when:
        pgwSepaMandatePaymentInfoService.removePaymentInfo(firstStoredInfo)

        then:
        firstStoredInfo.getUser() >> null
        1 * modelService.remove(firstStoredInfo)
    }

    @Test
    void 'cleanupForOrder removes saved payment info from order and refreshes the order'() {
        when:
        pgwSepaMandatePaymentInfoService.cleanupForOrder(firstStoredInfo, order)

        then:
        1 * order.setPaymentInfo(null)
        1 * modelService.save(order)
        1 * modelService.refresh(order)
    }

    @Test
    void 'cleanupForOrder throws for null payment info'() {
        when:
        pgwSepaMandatePaymentInfoService.cleanupForOrder(null, order)

        then:
        thrown(IllegalArgumentException)
        0 * modelService._
    }

    @Test
    void 'cleanupForOrder throws for null order'() {
        when:
        pgwSepaMandatePaymentInfoService.cleanupForOrder(firstStoredInfo, null)

        then:
        thrown(IllegalArgumentException)
        0 * modelService._
    }

    @Test
    void 'updateAfterSuccessfulAuthorization throws exception for invalid iban'() {
        given:
        def givenInfo = SepaMandatePaymentInfoBuilder.generate()
                .withPgwMerchantId(MERCHANT_ID)
                .buildInstance()
        def givenDirectDebitDetails = SepaDirectDebitDetailsDto.builder()
                .accountOwnerName(ACCOUNT_OWNER).dateOfSignature(SIGNATURE_LOCAL_DATE).iban(INVALID_IBAN).mandateId(MANDATE_REFERENCE)
                .build()

        when:
        pgwSepaMandatePaymentInfoService.updateAfterAuthorization(givenInfo, givenDirectDebitDetails)

        then:
        thrown(PgwIbanException.class)
    }

    @Test
    void 'updateAfterSuccessfulAuthorization updates the payment given payment info with the given data from PGW'() {
        given:
        def givenInfo = SepaMandatePaymentInfoBuilder.generate()
                .withPgwMerchantId(MERCHANT_ID)
                .buildInstance()
        def givenDirectDebitDetails = SepaDirectDebitDetailsDto.builder()
                .accountOwnerName(ACCOUNT_OWNER).dateOfSignature(SIGNATURE_LOCAL_DATE).iban(EXPECTED_IBAN).mandateId(MANDATE_REFERENCE)
                .build()

        when:
        pgwSepaMandatePaymentInfoService.updateAfterAuthorization(givenInfo, givenDirectDebitDetails)

        then:
        1 * modelService.save(givenInfo)
        verifyAll(givenInfo) {
            saved
            accountHolderName == ACCOUNT_OWNER
            dateOfSignature == SIGNATURE_DATE
            getIBAN() == EXPECTED_IBAN
            mandateReference == MANDATE_REFERENCE
        }
    }

    @Test
    void 'updateAfterSuccessfulAuthorization updates the payment given duplicate payment info and its original with the given data from PGW'() {
        given:
        def givenInfo = SepaMandatePaymentInfoBuilder.generate()
                .withPgwMerchantId(MERCHANT_ID).withDuplicate(true).buildInstance()
        def givenOriginal = SepaMandatePaymentInfoBuilder.generate()
                .withPgwMerchantId(MERCHANT_ID).buildInstance()
        def givenDirectDebitDetails = SepaDirectDebitDetailsDto.builder()
                .accountOwnerName(ACCOUNT_OWNER).dateOfSignature(SIGNATURE_LOCAL_DATE).iban(EXPECTED_IBAN).mandateId(MANDATE_REFERENCE)
                .build()

        when:
        pgwSepaMandatePaymentInfoService.updateAfterAuthorization(givenInfo, givenDirectDebitDetails)

        then:
        integratorPaymentInfoService.getNonDuplicateOriginalInfo(givenInfo, SepaMandatePaymentInfoModel.class) >> Optional.of(givenOriginal)
        1 * modelService.save(givenInfo)
        verifyAll(givenInfo) {
            saved
            accountHolderName == ACCOUNT_OWNER
            dateOfSignature == SIGNATURE_DATE
            getIBAN() == EXPECTED_IBAN
            mandateReference == MANDATE_REFERENCE
        }
        1 * modelService.save(givenOriginal)
        verifyAll(givenOriginal) {
            saved
            accountHolderName == ACCOUNT_OWNER
            dateOfSignature == SIGNATURE_DATE
            getIBAN() == EXPECTED_IBAN
            mandateReference == MANDATE_REFERENCE
        }
    }

    @Test
    void 'updateAfterSuccessfulAuthorization throws for a given duplicate info without original'() {
        given:
        def givenInfo = SepaMandatePaymentInfoBuilder.generate()
                .withPgwMerchantId(MERCHANT_ID).withDuplicate(true)
                .buildInstance()
        def givenDirectDebitDetails = SepaDirectDebitDetailsDto.builder()
                .accountOwnerName(ACCOUNT_OWNER).dateOfSignature(SIGNATURE_LOCAL_DATE).iban(EXPECTED_IBAN).mandateId(MANDATE_REFERENCE)
                .build()

        when:
        pgwSepaMandatePaymentInfoService.updateAfterAuthorization(givenInfo, givenDirectDebitDetails)

        then:
        integratorPaymentInfoService.getNonDuplicateOriginalInfo(givenInfo, SepaMandatePaymentInfoModel.class) >> Optional.empty()

        0 * modelService._
        thrown(IllegalStateException)
    }

    @Test
    void 'removePaymentInfo throws if given paymentInfo is null'() {
        when:
        pgwSepaMandatePaymentInfoService.removePaymentInfo(null)

        then:
        thrown(IllegalArgumentException)
    }

    @Test
    void 'getPaymentInfos throws IllegalArgumentException when null integrator is given'() {
        when:
        pgwSepaMandatePaymentInfoService.getPaymentInfos(null, MERCHANT_ID)

        then:
        thrown(IllegalArgumentException)
    }

    @Test
    void 'getDefaultPaymentInfo throws IllegalArgumentException when null integrator is given'() {
        when:
        pgwSepaMandatePaymentInfoService.getDefaultPaymentInfo(null, MERCHANT_ID)

        then:
        thrown(IllegalArgumentException)
    }

    @Test
    void 'getCartPaymentInfo throws IllegalArgumentException when null order is given'() {
        when:
        pgwSepaMandatePaymentInfoService.getCartPaymentInfo(null, MERCHANT_ID)

        then:
        thrown(IllegalArgumentException)
    }

    @Test
    void 'getPaymentInfos throws IllegalArgumentException when null merchantId is given'() {
        when:
        pgwSepaMandatePaymentInfoService.getPaymentInfos(integrator, null)

        then:
        thrown(IllegalArgumentException)
    }

    @Test
    void 'getDefaultPaymentInfo throws IllegalArgumentException when null merchantId is given'() {
        when:
        pgwSepaMandatePaymentInfoService.getDefaultPaymentInfo(integrator, null)

        then:
        thrown(IllegalArgumentException)
    }

    @Test
    void 'getCartPaymentInfo throws IllegalArgumentException when null merchantId is given'() {
        when:
        pgwSepaMandatePaymentInfoService.getCartPaymentInfo(order, null)

        then:
        thrown(IllegalArgumentException)
    }

    @Test
    void 'createPaymentInfo throws IllegalArgumentException when null integrator is given'() {
        when:
        pgwSepaMandatePaymentInfoService.createPaymentInfo(null, new SepaMandatePaymentInfoData())

        then:
        thrown(IllegalArgumentException)
        0 * modelService._
    }

    @Test
    void 'createPaymentInfo throws IllegalArgumentException when null paymentInfoData is given'() {
        when:
        pgwSepaMandatePaymentInfoService.createPaymentInfo(integrator, null)

        then:
        thrown(IllegalArgumentException)
        0 * modelService._
    }

    @Test
    void 'setMerchantId updates merchant ID on the given payment info if it does not have one yet'() {
        given:
        def givenInfo = SepaMandatePaymentInfoBuilder.generate().buildInstance()

        when:
        pgwSepaMandatePaymentInfoService.setMerchantId(givenInfo, MERCHANT_ID)

        then:
        givenInfo.pgwMerchantId == MERCHANT_ID
        1 * modelService.save(givenInfo)
    }

    @Test
    void 'setMerchantId updates merchant ID on the given duplicate payment info and its original if it does not have one yet'() {
        given:
        def givenInfo = SepaMandatePaymentInfoBuilder.generate().withDuplicate(true).buildInstance()
        def originalInfo = SepaMandatePaymentInfoBuilder.generate().buildInstance()

        when:
        pgwSepaMandatePaymentInfoService.setMerchantId(givenInfo, MERCHANT_ID)

        then:
        1 * integratorPaymentInfoService.getNonDuplicateOriginalInfo(givenInfo, SepaMandatePaymentInfoModel.class) >> Optional.of(originalInfo)
        givenInfo.pgwMerchantId == MERCHANT_ID
        originalInfo.pgwMerchantId == MERCHANT_ID
        1 * modelService.save(givenInfo)
        1 * modelService.save(originalInfo)
    }

    @Test
    void 'setMerchantId throws for a given duplicate payment info without original info'() {
        given:
        def givenInfo = SepaMandatePaymentInfoBuilder.generate().withDuplicate(true).buildInstance()

        when:
        pgwSepaMandatePaymentInfoService.setMerchantId(givenInfo, MERCHANT_ID)

        then:
        1 * integratorPaymentInfoService.getNonDuplicateOriginalInfo(givenInfo, SepaMandatePaymentInfoModel.class) >> Optional.empty()
        thrown(IllegalStateException)
        0 * modelService._
    }

    @Test
    void 'setMerchantId throws if given merchantId is null'() {
        when:
        pgwSepaMandatePaymentInfoService.setMerchantId(firstStoredInfo, null)

        then:
        thrown(IllegalArgumentException)
    }

    @Test
    void 'setMerchantId throws if given merchantId is blank'() {
        when:
        pgwSepaMandatePaymentInfoService.setMerchantId(firstStoredInfo, '')

        then:
        thrown(IllegalArgumentException)
    }

    @Test
    void 'setMerchantId throws if given payment info already has a different merchant ID'() {
        given:
        def givenInfo = SepaMandatePaymentInfoBuilder.generate().withPgwMerchantId('foo').buildInstance()

        when:
        pgwSepaMandatePaymentInfoService.setMerchantId(givenInfo, MERCHANT_ID)

        then:
        thrown(IllegalStateException)
    }

    @Test
    void 'setMerchantId returns early and does nothing if payment info already has the same merchant ID'() {
        given:
        def givenInfo = SepaMandatePaymentInfoBuilder.generate().withPgwMerchantId(MERCHANT_ID).buildInstance()

        when:
        pgwSepaMandatePaymentInfoService.setMerchantId(givenInfo, MERCHANT_ID)

        then:
        0 * modelService._
    }

    @Test
    void 'findMatchingExistingInfo returns an existing info with the same mandate reference and merchant id'() {
        when:
        def actualPaymentInfos = pgwSepaMandatePaymentInfoService
                .findMatchingExistingInfo(newInfo, MANDATE_REFERENCE)

        then:
        secondStoredInfo.getMandateReference() >> MANDATE_REFERENCE
        integratorPaymentInfoService.getPaymentInfos(integrator, PGW, SepaMandatePaymentInfoModel.class) >> ([firstStoredInfo, secondStoredInfo, paymentInfoWithWrongMerchant] as Set)
        actualPaymentInfos == Optional.of(secondStoredInfo)
    }

    @Test
    void 'findMatchingExistingInfo returns empty if none of the stored infos has a matching mandate reference'() {
        when:
        def actualPaymentInfos = pgwSepaMandatePaymentInfoService
                .findMatchingExistingInfo(newInfo, MANDATE_REFERENCE)

        then:
        secondStoredInfo.getMandateReference() >> 'something else'
        integratorPaymentInfoService.getPaymentInfos(integrator, PGW, SepaMandatePaymentInfoModel.class) >> ([firstStoredInfo, secondStoredInfo, paymentInfoWithWrongMerchant] as Set)
        actualPaymentInfos == Optional.empty()
    }

    @Test
    void 'findMatchingExistingInfo returns returns empty if the mandate reference matches but the merchant id doesnt'() {
        when:
        def actualPaymentInfos = pgwSepaMandatePaymentInfoService
                .findMatchingExistingInfo(newInfo, MANDATE_REFERENCE)

        then:
        secondStoredInfo.getMandateReference() >> MANDATE_REFERENCE
        secondStoredInfo.getPgwMerchantId() >> WRONG_MERCHANT_ID
        integratorPaymentInfoService.getPaymentInfos(integrator, PGW, SepaMandatePaymentInfoModel.class) >> ([firstStoredInfo, secondStoredInfo, paymentInfoWithWrongMerchant] as Set)
        actualPaymentInfos == Optional.empty()
    }

    @Test
    void 'findMatchingExistingInfo throws if given info user is not of type integrator'() {
        given:
        def wrongUserType = Mock(UserModel)

        when:
        pgwSepaMandatePaymentInfoService.findMatchingExistingInfo(newInfo, MANDATE_REFERENCE)

        then:
        newInfo.getUser() >> wrongUserType
        thrown(IllegalStateException)
    }

    @Test
    void 'findMatchingExistingInfo throws if given info is null'() {
        when:
        pgwSepaMandatePaymentInfoService.findMatchingExistingInfo(null, MANDATE_REFERENCE)

        then:
        thrown(IllegalArgumentException)
    }

    @Test
    void 'findMatchingExistingInfo throws if given mandate reference is null'() {
        when:
        pgwSepaMandatePaymentInfoService.findMatchingExistingInfo(newInfo, null)

        then:
        thrown(IllegalArgumentException)
    }

    @Test
    void 'setPaymentInfoForTransactionAndOrder updates both the given transaction and the associated order with the given info'() {
        when:
        pgwSepaMandatePaymentInfoService.setPaymentInfoForTransactionAndOrder(secondStoredInfo, mockTx)

        then:
        secondStoredInfo.getPaymentProvider() >> PGW
        secondStoredInfo.getPgwMerchantId() >> MERCHANT_ID
        mockTx.getPgwMerchantId() >> MERCHANT_ID
        1 * mockTx.setInfo(secondStoredInfo)
        1 * order.setPaymentInfo(secondStoredInfo)
        1 * modelService.saveAll(mockTx, order)
        1 * modelService.refresh(order)
    }

    @Test
    void 'setPaymentInfoForTransactionAndOrder throws if given info does not have PGW as payment provider'() {
        when:
        pgwSepaMandatePaymentInfoService.setPaymentInfoForTransactionAndOrder(secondStoredInfo, mockTx)

        then:
        secondStoredInfo.getPaymentProvider() >> DPG
        secondStoredInfo.getPgwMerchantId() >> MERCHANT_ID
        mockTx.getPgwMerchantId() >> MERCHANT_ID
        0 * mockTx.setInfo(_)
        0 * order.setPaymentInfo(_)
        0 * modelService._
        thrown(IllegalArgumentException)
    }

    @Test
    void 'setPaymentInfoForTransactionAndOrder throws if given info does not have the same merchat id as the given transaction'() {
        when:
        pgwSepaMandatePaymentInfoService.setPaymentInfoForTransactionAndOrder(secondStoredInfo, mockTx)

        then:
        secondStoredInfo.getPaymentProvider() >> PGW
        secondStoredInfo.getPgwMerchantId() >> WRONG_MERCHANT_ID
        mockTx.getPgwMerchantId() >> MERCHANT_ID
        0 * mockTx.setInfo(_)
        0 * order.setPaymentInfo(_)
        0 * modelService._
        thrown(IllegalArgumentException)
    }

    @Test
    void 'setPaymentInfoForTransactionAndOrder throws if given info is null'() {
        when:
        pgwSepaMandatePaymentInfoService.setPaymentInfoForTransactionAndOrder(null, mockTx)

        then:
        secondStoredInfo.getPaymentProvider() >> PGW
        secondStoredInfo.getPgwMerchantId() >> MERCHANT_ID
        mockTx.getPgwMerchantId() >> MERCHANT_ID
        0 * mockTx.setInfo(_)
        0 * order.setPaymentInfo(_)
        0 * modelService._
        thrown(IllegalArgumentException)
    }

    @Test
    void 'setPaymentInfoForTransactionAndOrder throws if given transaction is null'() {
        when:
        pgwSepaMandatePaymentInfoService.setPaymentInfoForTransactionAndOrder(secondStoredInfo, null)

        then:
        secondStoredInfo.getPaymentProvider() >> PGW
        secondStoredInfo.getPgwMerchantId() >> MERCHANT_ID
        mockTx.getPgwMerchantId() >> MERCHANT_ID
        0 * mockTx.setInfo(_)
        0 * order.setPaymentInfo(_)
        0 * modelService._
        thrown(IllegalArgumentException)
    }
}
