package com.sast.cis.payment.pgw.service

import com.fasterxml.jackson.databind.ObjectMapper
import com.sast.cis.core.constants.BaseStoreEnum
import com.sast.cis.core.data.SepaMandatePaymentInfoData
import com.sast.cis.core.enums.BillingSystemStatus
import com.sast.cis.core.enums.PaymentMethodType
import com.sast.cis.core.enums.PaymentProvider
import com.sast.cis.core.enums.PspSellerAccountStatus
import com.sast.cis.core.model.*
import com.sast.cis.core.paymentintegration.data.*
import com.sast.cis.core.service.customer.developer.DeveloperService
import com.sast.cis.core.service.customer.integrator.IntegratorService
import com.sast.cis.core.util.Base58UUIDCodeGenerator
import com.sast.cis.payment.pgw.model.PgwSellerAccountModel
import com.sast.cis.payment.pgw.service.client.PgwRestServiceFactory
import com.sast.cis.payment.pgw.util.PgwWiremockRule
import com.sast.cis.test.utils.Country
import com.sast.cis.test.utils.SampleDataCreator
import com.sast.cis.test.utils.TestDataConstants
import com.sast.pgw.proxyclient.model.rest.payment.CreateHppResponseDto
import com.sast.pgw.proxyclient.model.rest.payment.GetPaymentResponseDto
import com.sast.pgw.proxyclient.model.rest.payment.PaymentStatus
import com.sast.pgw.proxyclient.model.rest.payment.details.SepaDirectDebitDetailsDto
import de.hybris.bootstrap.annotations.IntegrationTest
import de.hybris.platform.core.model.c2l.CountryModel
import de.hybris.platform.core.model.order.CartModel
import de.hybris.platform.core.model.order.payment.PaymentInfoModel
import de.hybris.platform.order.CartService
import de.hybris.platform.payment.dto.TransactionStatus
import de.hybris.platform.payment.enums.PaymentTransactionType
import de.hybris.platform.payment.model.PaymentTransactionModel
import de.hybris.platform.product.UnitService
import de.hybris.platform.servicelayer.ServicelayerTransactionalSpockSpecification
import de.hybris.platform.servicelayer.config.ConfigurationService
import de.hybris.platform.servicelayer.model.ModelService
import de.hybris.platform.servicelayer.user.UserService
import de.hybris.platform.site.BaseSiteService
import de.hybris.platform.store.BaseStoreModel
import de.hybris.platform.store.services.BaseStoreService
import generated.com.sast.cis.core.model.SepaMandatePaymentInfoBuilder
import generated.com.sast.cis.payment.pgw.model.PgwSellerAccountBuilder
import org.junit.Rule
import org.junit.Test

import javax.annotation.Resource
import java.time.Instant
import java.time.LocalDate

import static com.sast.cis.core.dao.CatalogVersion.ONLINE
import static com.sast.cis.core.enums.LicenseType.FULL
import static de.hybris.platform.catalog.enums.ArticleApprovalStatus.APPROVED

@IntegrationTest
class PgwPaymentServiceISpec extends ServicelayerTransactionalSpockSpecification {
    private static final BigDecimal AUTH_AMOUNT = BigDecimal.valueOf(14999.00)
    private static final String MATCHING_MERCHANT_ID = 'merch123'
    private static final String OTHER_MERCHANT_ID = 'merch456'
    private static final LocalDate DATE_OF_SIGNATURE = LocalDate.of(2024, 02, 10)
    private static final Date DATE_OF_SIGNATURE_AS_DATE = Date.from(Instant.parse("2024-02-10T00:00:00Z"))
    private static final String ACCOUNT_OWNER = 'Hans Dampf'
    private static final String MATCHING_INFO_CODE = 'sepaMandateInfo123'
    private static final String OTHER_INFO_CODE = 'sepaMandateInfo456'
    private static final String DUMMY_IBAN = '**********************'
    private static final URI HPP_REDIRECT_URI = new URI("https://foobarbaz.local:9231/hpp?something=other")
    private static final String EXPECTED_HPP_URI = 'https://foobarbaz.local:9231/hpp?something=other&language=en'

    private SampleDataCreator sampleDataCreator = new SampleDataCreator()

    @Rule
    public PgwWiremockRule pgwWiremockRule = new PgwWiremockRule();

    @Resource
    public ObjectMapper pgwProxyClientMapper

    @Resource
    private IntegratorService integratorService

    @Resource
    private CartService cartService

    @Resource
    private ModelService modelService

    @Resource
    private UserService userService

    @Resource
    private ConfigurationService configurationService

    @Resource
    private DeveloperService developerService

    @Resource
    private UnitService unitService

    @Resource
    private BaseSiteService baseSiteService

    @Resource
    private BaseStoreService baseStoreService

    @Resource
    private PgwPaymentService pgwPaymentService

    private BaseStoreModel aaStore
    private CountryModel germany
    private IntegratorModel buyerUser
    private AppLicenseModel license
    private PgwSellerAccountModel pgwSellerAccount
    private IoTCompanyModel sellerCompany
    private SepaMandatePaymentInfoModel savedMatchingInfo
    private SepaMandatePaymentInfoModel savedNonMatchingInfo

    def setup() {
        configurationService.getConfiguration().setProperty(PgwRestServiceFactory.PGW_PROXY_URL, pgwWiremockRule.getPgwUrl().toString())
        configurationService.getConfiguration().setProperty(PgwRestServiceFactory.API_KEY, pgwWiremockRule.getApiKey())
        prepareCreateHppResponse()


        aaStore = baseStoreService.getBaseStoreForUid(BaseStoreEnum.AA.getBaseStoreUid())
        buyerUser = integratorService.getIntegratorByInternalUserId(TestDataConstants.SAMPLE_DATA_INTEGRATOR_UID)
        savedMatchingInfo = createSepaMandateInfo(MATCHING_INFO_CODE, buyerUser, MATCHING_MERCHANT_ID)
        savedNonMatchingInfo = createSepaMandateInfo(OTHER_INFO_CODE, buyerUser, OTHER_MERCHANT_ID)
        buyerUser.setDefaultPaymentInfo(savedMatchingInfo)
        modelService.save(buyerUser)
        modelService.refresh(buyerUser)
        userService.setCurrentUser(buyerUser)
        cartService.removeSessionCart()
        germany = sampleDataCreator.getCountry(Country.GERMANY)
        germany.setSupportedPaymentProviders(Set.of(PaymentProvider.PGW, PaymentProvider.BOSCH_TRANSFER))
        germany.setSupportedPaymentMethods(Set.of(PaymentMethodType.SEPA_DIRECTDEBIT, PaymentMethodType.SEPA_CREDIT))
        modelService.save(germany)

        license = createLicense()
        sellerCompany = ((AppModel) license.getBaseProduct()).getCompany()
        pgwSellerAccount = createSellerAccount(sellerCompany)
        setCurrentBaseStore(aaStore)
    }

    @Test
    void 'perform successful checkout with stored payment info for SEPA_DIRECTDEBIT'() {
        given:
        def givenAuthParameter = createAuthorizationParameter()
        def sessionCart = givenAuthParameter.abstractOrder() as CartModel
        sampleDataCreator.createCartEntry(sessionCart, license, 2)

        when:
        def actualCheckout = pgwPaymentService.prepareCheckout(givenAuthParameter, PaymentMethodType.SEPA_DIRECTDEBIT)

        then:
        verifyRegularCheckout(actualCheckout)

        when:
        sessionCart.setPaymentInfo(actualCheckout.getDefaultPaymentInfo())
        modelService.save(sessionCart)
        pgwPaymentService.confirmSelectedPaymentInfo(givenAuthParameter)
        def authTxEntry = pgwPaymentService.authorize(givenAuthParameter)
        def authResult = pgwPaymentService.getAuthorizationResult(authTxEntry)

        then:
        verifyAuthorization(authResult, AuthorizationStatus.SUCCESS, PaymentTransactionType.AUTHORIZATION, TransactionStatus.ACCEPTED, savedMatchingInfo)
    }

    @Test
    void 'perform checkout with SEPA_DIRECTDEBIT and new payment info'() {
        given:
        def givenInfoData = new SepaMandatePaymentInfoData()
        givenInfoData.setPaymentProvider(PaymentProvider.PGW)
        givenInfoData.setPaymentMethod(PaymentMethodType.SEPA_DIRECTDEBIT)
        def givenAuthParameter = createAuthorizationParameter()
        def sessionCart = givenAuthParameter.abstractOrder() as CartModel
        sampleDataCreator.createCartEntry(sessionCart, license, 2)

        when:
        def actualCheckout = pgwPaymentService.prepareCheckout(givenAuthParameter, PaymentMethodType.SEPA_DIRECTDEBIT)

        then:
        verifyRegularCheckout(actualCheckout)

        when:
        def newPaymentInfo = pgwPaymentService.createPaymentInfo(buyerUser, givenInfoData) as SepaMandatePaymentInfoModel
        sessionCart.setPaymentInfo(newPaymentInfo)
        modelService.save(sessionCart)

        then:
        verifyAll(newPaymentInfo) {
            paymentProvider == PaymentProvider.PGW
            !saved
            it instanceof SepaMandatePaymentInfoModel
        }

        when:
        pgwPaymentService.confirmSelectedPaymentInfo(givenAuthParameter)
        def firstAuthTxEntry = pgwPaymentService.authorize(givenAuthParameter)
        def firstAuthResult = pgwPaymentService.getAuthorizationResult(firstAuthTxEntry)

        then:
        verifyAuthorization(firstAuthResult, AuthorizationStatus.REQUIRE_USER, PaymentTransactionType.CHECKOUT, TransactionStatus.PENDING, newPaymentInfo)
        verifyAll(firstAuthResult) {
            userActionParameters.containsKey('url')
            userActionParameters.get('url').toString() == EXPECTED_HPP_URI
        }

        when:
        preparePaymentResponse(firstAuthTxEntry.paymentTransaction.code, 'somePayId', newPaymentInfo.mandateReference, PaymentStatus.OK)
        pgwPaymentService.notifyRedirectResult(PaymentRedirectResult.builder()
                .abstractOrder(sessionCart).status(PaymentRedirectStatus.SUCCESS).build())
        def secondAuthTxEntry = pgwPaymentService.authorize(givenAuthParameter)
        def secondAuthResult = pgwPaymentService.getAuthorizationResult(secondAuthTxEntry)

        then:
        verifyAuthorization(secondAuthResult, AuthorizationStatus.SUCCESS, PaymentTransactionType.AUTHORIZATION, TransactionStatus.ACCEPTED, newPaymentInfo)
        verifyRequiredInfoAttributes(newPaymentInfo)
    }

    @Test
    void 'perform checkout with SEPA_DIRECTDEBIT and new payment info but status query returns a known mandate reference'() {
        given:
        def givenInfoData = new SepaMandatePaymentInfoData()
        givenInfoData.setPaymentProvider(PaymentProvider.PGW)
        givenInfoData.setPaymentMethod(PaymentMethodType.SEPA_DIRECTDEBIT)
        def givenAuthParameter = createAuthorizationParameter()
        def sessionCart = givenAuthParameter.abstractOrder() as CartModel
        sampleDataCreator.createCartEntry(sessionCart, license, 2)

        when:
        def actualCheckout = pgwPaymentService.prepareCheckout(givenAuthParameter, PaymentMethodType.SEPA_DIRECTDEBIT)

        then:
        verifyRegularCheckout(actualCheckout)

        when:
        def newPaymentInfo = pgwPaymentService.createPaymentInfo(buyerUser, givenInfoData) as SepaMandatePaymentInfoModel
        sessionCart.setPaymentInfo(newPaymentInfo)
        modelService.save(sessionCart)

        then:
        verifyAll(newPaymentInfo) {
            paymentProvider == PaymentProvider.PGW
            !saved
            it instanceof SepaMandatePaymentInfoModel
        }

        when:
        pgwPaymentService.confirmSelectedPaymentInfo(givenAuthParameter)
        def firstAuthTxEntry = pgwPaymentService.authorize(givenAuthParameter)
        def firstAuthResult = pgwPaymentService.getAuthorizationResult(firstAuthTxEntry)

        then:
        verifyAuthorization(firstAuthResult, AuthorizationStatus.REQUIRE_USER, PaymentTransactionType.CHECKOUT, TransactionStatus.PENDING, newPaymentInfo)
        verifyAll(firstAuthResult) {
            userActionParameters.containsKey('url')
            userActionParameters.get('url').toString() == EXPECTED_HPP_URI
        }

        when:
        preparePaymentResponse(firstAuthTxEntry.paymentTransaction.code, 'somePayId', savedMatchingInfo.mandateReference, PaymentStatus.OK)
        pgwPaymentService.notifyRedirectResult(PaymentRedirectResult.builder()
                .abstractOrder(sessionCart).status(PaymentRedirectStatus.SUCCESS).build())
        def secondAuthTxEntry = pgwPaymentService.authorize(givenAuthParameter)
        def secondAuthResult = pgwPaymentService.getAuthorizationResult(secondAuthTxEntry)

        then:
        verifyAuthorization(secondAuthResult, AuthorizationStatus.SUCCESS, PaymentTransactionType.AUTHORIZATION, TransactionStatus.ACCEPTED, savedMatchingInfo)
        sessionCart.paymentInfo == savedMatchingInfo
        firstAuthTxEntry.paymentTransaction.info == savedMatchingInfo
        modelService.isRemoved(newPaymentInfo)
    }

    @Test
    void 'perform checkout with SEPA_DIRECTDEBIT and new payment info but status query returns failed'() {
        given:
        def givenInfoData = new SepaMandatePaymentInfoData()
        givenInfoData.setPaymentProvider(PaymentProvider.PGW)
        givenInfoData.setPaymentMethod(PaymentMethodType.SEPA_DIRECTDEBIT)
        def givenAuthParameter = createAuthorizationParameter()
        def sessionCart = givenAuthParameter.abstractOrder() as CartModel
        sampleDataCreator.createCartEntry(sessionCart, license, 2)

        when:
        def actualCheckout = pgwPaymentService.prepareCheckout(givenAuthParameter, PaymentMethodType.SEPA_DIRECTDEBIT)

        then:
        verifyRegularCheckout(actualCheckout)

        when:
        def newPaymentInfo = pgwPaymentService.createPaymentInfo(buyerUser, givenInfoData) as SepaMandatePaymentInfoModel
        sessionCart.setPaymentInfo(newPaymentInfo)
        modelService.save(sessionCart)

        then:
        verifyAll(newPaymentInfo) {
            paymentProvider == PaymentProvider.PGW
            !saved
            it instanceof SepaMandatePaymentInfoModel
        }

        when:
        pgwPaymentService.confirmSelectedPaymentInfo(givenAuthParameter)
        def firstAuthTxEntry = pgwPaymentService.authorize(givenAuthParameter)
        def firstAuthResult = pgwPaymentService.getAuthorizationResult(firstAuthTxEntry)

        then:
        verifyAuthorization(firstAuthResult, AuthorizationStatus.REQUIRE_USER, PaymentTransactionType.CHECKOUT, TransactionStatus.PENDING, newPaymentInfo)
        verifyAll(firstAuthResult) {
            userActionParameters.containsKey('url')
            userActionParameters.get('url').toString() == EXPECTED_HPP_URI
        }

        when:
        preparePaymentResponse(firstAuthTxEntry.paymentTransaction.code, 'somePayId',
                newPaymentInfo.mandateReference, PaymentStatus.FAILED)
        pgwPaymentService.notifyRedirectResult(PaymentRedirectResult.builder()
                .abstractOrder(sessionCart).status(PaymentRedirectStatus.SUCCESS).build())
        def secondAuthTxEntry = pgwPaymentService.authorize(givenAuthParameter)
        def secondAuthResult = pgwPaymentService.getAuthorizationResult(secondAuthTxEntry)

        then:
        verifyAuthorization(secondAuthResult, AuthorizationStatus.ERROR, PaymentTransactionType.AUTHORIZATION, TransactionStatus.ERROR, newPaymentInfo)
        isCancelled(secondAuthTxEntry.paymentTransaction)
    }

    @Test
    void 'perform checkout with SEPA_DIRECTDEBIT and new payment info but status query initially returns http error and status is repolled until successful'() {
        given:
        def givenInfoData = new SepaMandatePaymentInfoData()
        givenInfoData.setPaymentProvider(PaymentProvider.PGW)
        givenInfoData.setPaymentMethod(PaymentMethodType.SEPA_DIRECTDEBIT)
        def givenAuthParameter = createAuthorizationParameter()
        def sessionCart = givenAuthParameter.abstractOrder() as CartModel
        sampleDataCreator.createCartEntry(sessionCart, license, 2)

        when:
        pgwPaymentService.prepareCheckout(givenAuthParameter, PaymentMethodType.SEPA_DIRECTDEBIT)
        def newPaymentInfo = pgwPaymentService.createPaymentInfo(buyerUser, givenInfoData) as SepaMandatePaymentInfoModel
        sessionCart.setPaymentInfo(newPaymentInfo)
        modelService.save(sessionCart)
        pgwPaymentService.confirmSelectedPaymentInfo(givenAuthParameter)
        def firstAuthTxEntry = pgwPaymentService.authorize(givenAuthParameter)
        def firstAuthResult = pgwPaymentService.getAuthorizationResult(firstAuthTxEntry)

        then:
        verifyAuthorization(firstAuthResult, AuthorizationStatus.REQUIRE_USER, PaymentTransactionType.CHECKOUT, TransactionStatus.PENDING, newPaymentInfo)
        verifyAll(firstAuthResult) {
            userActionParameters.containsKey('url')
            userActionParameters.get('url').toString() == EXPECTED_HPP_URI
        }

        when:
        preparePaymentQueryError(firstAuthTxEntry.paymentTransaction.code, 500)
        pgwPaymentService.notifyRedirectResult(PaymentRedirectResult.builder()
                .abstractOrder(sessionCart).status(PaymentRedirectStatus.SUCCESS).build())
        def pollingTxEntry = pgwPaymentService.authorize(givenAuthParameter)
        def pollingAuthResult = pgwPaymentService.getAuthorizationResult(pollingTxEntry)

        then:
        verifyAuthorization(pollingAuthResult, AuthorizationStatus.PENDING, PaymentTransactionType.AUTHORIZATION, TransactionStatus.PENDING, newPaymentInfo)

        when:
        preparePaymentResponse(firstAuthTxEntry.paymentTransaction.code, 'somePayId',
                newPaymentInfo.mandateReference, PaymentStatus.OK)
        def secondAuthTxEntry = pgwPaymentService.authorize(givenAuthParameter)
        def secondAuthResult = pgwPaymentService.getAuthorizationResult(secondAuthTxEntry)

        then:
        verifyAuthorization(secondAuthResult, AuthorizationStatus.SUCCESS, PaymentTransactionType.AUTHORIZATION, TransactionStatus.ACCEPTED, newPaymentInfo)
        verifyRequiredInfoAttributes(newPaymentInfo)
    }

    @Test
    void 'confirmSelectedPaymentInfo can be called multiple times on an in-progress checkout for SEPA_DIRECTDEBIT does modify it'() {
        given:
        def givenInfoData = new SepaMandatePaymentInfoData()
        givenInfoData.setPaymentProvider(PaymentProvider.PGW)
        givenInfoData.setPaymentMethod(PaymentMethodType.SEPA_DIRECTDEBIT)
        def givenAuthParameter = createAuthorizationParameter()
        def sessionCart = givenAuthParameter.abstractOrder() as CartModel
        sampleDataCreator.createCartEntry(sessionCart, license, 2)

        when:
        def actualCheckout = pgwPaymentService.prepareCheckout(givenAuthParameter, PaymentMethodType.SEPA_DIRECTDEBIT)

        then:
        verifyRegularCheckout(actualCheckout)

        when:
        def newPaymentInfo = pgwPaymentService.createPaymentInfo(buyerUser, givenInfoData) as SepaMandatePaymentInfoModel
        sessionCart.setPaymentInfo(newPaymentInfo)
        modelService.save(sessionCart)

        pgwPaymentService.confirmSelectedPaymentInfo(givenAuthParameter)
        def firstAuthTxEntry = pgwPaymentService.authorize(givenAuthParameter)
        pgwPaymentService.confirmSelectedPaymentInfo(givenAuthParameter)
        pgwPaymentService.getAuthorizationResult(firstAuthTxEntry)
        preparePaymentResponse(firstAuthTxEntry.paymentTransaction.code, 'somePayId', newPaymentInfo.mandateReference, PaymentStatus.OK)

        pgwPaymentService.notifyRedirectResult(PaymentRedirectResult.builder()
                .abstractOrder(sessionCart).status(PaymentRedirectStatus.SUCCESS).build())
        def secondAuthTxEntry = pgwPaymentService.authorize(givenAuthParameter)
        pgwPaymentService.confirmSelectedPaymentInfo(givenAuthParameter)
        def secondAuthResult = pgwPaymentService.getAuthorizationResult(secondAuthTxEntry)

        then:
        verifyAuthorization(secondAuthResult, AuthorizationStatus.SUCCESS, PaymentTransactionType.AUTHORIZATION, TransactionStatus.ACCEPTED, newPaymentInfo)
        verifyRequiredInfoAttributes(newPaymentInfo)
    }

    @Test
    void 'authorization for SEPA_DIRECTDEBIT is set to failed if redirect notifies FAILURE'() {
        given:
        def givenInfoData = new SepaMandatePaymentInfoData()
        givenInfoData.setPaymentProvider(PaymentProvider.PGW)
        givenInfoData.setPaymentMethod(PaymentMethodType.SEPA_DIRECTDEBIT)
        def givenAuthParameter = createAuthorizationParameter()
        def sessionCart = givenAuthParameter.abstractOrder() as CartModel
        sampleDataCreator.createCartEntry(sessionCart, license, 2)

        when:
        def actualCheckout = pgwPaymentService.prepareCheckout(givenAuthParameter, PaymentMethodType.SEPA_DIRECTDEBIT)

        then:
        verifyRegularCheckout(actualCheckout)

        when:
        def newPaymentInfo = pgwPaymentService.createPaymentInfo(buyerUser, givenInfoData)
        sessionCart.setPaymentInfo(newPaymentInfo)
        modelService.save(sessionCart)
        pgwPaymentService.confirmSelectedPaymentInfo(givenAuthParameter)
        def authTxEntry = pgwPaymentService.authorize(givenAuthParameter)
        pgwPaymentService.getAuthorizationResult(authTxEntry)
        pgwPaymentService.notifyRedirectResult(PaymentRedirectResult.builder()
                .abstractOrder(sessionCart).status(PaymentRedirectStatus.FAILURE).build())

        then:
        sessionCart.paymentInfo == null
        isCancelled(authTxEntry.paymentTransaction)
    }

    @Test
    void 'authorization for SEPA_DIRECTDEBIT is set to cancelled if redirect notifies CANCEL'() {
        given:
        def givenInfoData = new SepaMandatePaymentInfoData()
        givenInfoData.setPaymentProvider(PaymentProvider.PGW)
        givenInfoData.setPaymentMethod(PaymentMethodType.SEPA_DIRECTDEBIT)
        def givenAuthParameter = createAuthorizationParameter()
        def sessionCart = givenAuthParameter.abstractOrder() as CartModel
        sampleDataCreator.createCartEntry(sessionCart, license, 2)

        when:
        def actualCheckout = pgwPaymentService.prepareCheckout(givenAuthParameter, PaymentMethodType.SEPA_DIRECTDEBIT)

        then:
        verifyRegularCheckout(actualCheckout)

        when:
        def newPaymentInfo = pgwPaymentService.createPaymentInfo(buyerUser, givenInfoData)
        sessionCart.setPaymentInfo(newPaymentInfo)
        modelService.save(sessionCart)
        pgwPaymentService.confirmSelectedPaymentInfo(givenAuthParameter)
        def authTxEntry = pgwPaymentService.authorize(givenAuthParameter)
        pgwPaymentService.getAuthorizationResult(authTxEntry)
        pgwPaymentService.notifyRedirectResult(PaymentRedirectResult.builder()
                .abstractOrder(sessionCart).status(PaymentRedirectStatus.CANCEL).build())

        then:
        sessionCart.paymentInfo == null
        isCancelled(authTxEntry.paymentTransaction)
    }

    private void verifyRegularCheckout(CheckoutInfo checkout) {
        verifyAll(checkout) {
            storedPaymentInfos == [savedMatchingInfo] as Set
            defaultPaymentInfo == savedMatchingInfo
            paymentProvider == PaymentProvider.PGW
            cartPaymentInfo == savedMatchingInfo
        }
    }

    private void verifyRequiredInfoAttributes(SepaMandatePaymentInfoModel paymentInfo) {
        verifyAll(paymentInfo) {
            getIBAN() == DUMMY_IBAN
            dateOfSignature == DATE_OF_SIGNATURE_AS_DATE
            !mandateReference.blank
            accountHolderName == ACCOUNT_OWNER
            saved
            pgwMerchantId == MATCHING_MERCHANT_ID
        }
    }

    private void verifyAuthorization(AuthorizationResult result, AuthorizationStatus expectedAuthStatus,
                                     PaymentTransactionType expectedTxEntryType, TransactionStatus expectedTxEntryStatus,
                                     PaymentInfoModel expectedInfo) {
        verifyAll(result) {
            authorizationStatus == expectedAuthStatus
            verifyAll(paymentTransactionEntry) {
                transactionStatus == expectedTxEntryStatus.toString()
                type == expectedTxEntryType
                verifyAll(paymentTransaction) {
                    paymentProvider == PaymentProvider.PGW.toString()
                    type == PaymentTransactionType.AUTHORIZATION
                    info == expectedInfo
                    pgwMerchantId == MATCHING_MERCHANT_ID
                }
            }
        }
    }

    private AuthorizationParameter createAuthorizationParameter() {
        def cart = cartService.getSessionCart()
        cart.setStore(aaStore)
        AuthorizationParameter.builder()
                .abstractOrder(cart)
                .plannedAmount(AUTH_AMOUNT)
                .build()
    }

    private AppLicenseModel createLicense() {
        AppModel app = sampleDataCreator.createApp("app", "sample.package.name", ONLINE)
        AppLicenseModel appLicense = sampleDataCreator.createAppLicense("app license", app, APPROVED, FULL)
        appLicense.setPriceQuantity(1.0)
        modelService.save(appLicense)
        return appLicense
    }

    private PgwSellerAccountModel createSellerAccount(IoTCompanyModel company) {
        PgwSellerAccountModel account = PgwSellerAccountBuilder.generate()
                .withCompany(sellerCompany)
                .withPaymentProvider(PaymentProvider.PGW)
                .withBillingSystemStatus(BillingSystemStatus.IN_SYNC)
                .withStatus(PspSellerAccountStatus.ACTIVE)
                .withAccountId(MATCHING_MERCHANT_ID)
                .buildIntegrationInstance()
        modelService.save(account)
        modelService.refresh(company)
        return account
    }

    private SepaMandatePaymentInfoModel createSepaMandateInfo(String code, IntegratorModel integrator, String merchantId) {
        def newInfo = SepaMandatePaymentInfoBuilder.generate()
                .withCode(code)
                .withPaymentProvider(PaymentProvider.PGW)
                .withSaved(true)
                .withDuplicate(false)
                .withUser(integrator)
                .withAccountHolderName(Base58UUIDCodeGenerator.generateCode('testAccountHolder'))
                .withDateOfSignature(new Date())
                .withIBAN(Base58UUIDCodeGenerator.generateCode('testIBAN'))
                .withMandateReference(Base58UUIDCodeGenerator.generateCode('testMandateReference'))
                .withPgwMerchantId(merchantId)
                .buildIntegrationInstance()
        modelService.save(newInfo)
        modelService.refresh(integrator)
        return newInfo
    }

    private boolean isCancelled(PaymentTransactionModel transaction) {
        transaction.entries.find(entry -> entry.getType() == PaymentTransactionType.CANCEL)
    }

    private void preparePaymentResponse(String transactionId, String paymentId, String mandateReference, PaymentStatus status) {
        def paymentResponse = GetPaymentResponseDto.builder()
                .transactionId(transactionId)
                .paymentId(paymentId)
                .status(status)
                .paymentDetails(SepaDirectDebitDetailsDto.builder()
                        .mandateId(mandateReference)
                        .iban(DUMMY_IBAN)
                        .accountOwnerName(ACCOUNT_OWNER)
                        .dateOfSignature(DATE_OF_SIGNATURE)
                        .build())
                .build()
        def responseString = pgwProxyClientMapper.writeValueAsString(paymentResponse)
        pgwWiremockRule.prepareGetPaymentResponse(MATCHING_MERCHANT_ID, transactionId, responseString)
    }

    private void preparePaymentQueryError(String transactionId, int statusCode) {
        pgwWiremockRule.prepareGetPaymentHttpError(MATCHING_MERCHANT_ID, transactionId, '', statusCode)

    }

    private void prepareCreateHppResponse() {
        def hppResponse = CreateHppResponseDto.builder()
                .paymentId('***************').redirectUri(HPP_REDIRECT_URI)
                .build()
        def hppResponseString = pgwProxyClientMapper.writeValueAsString(hppResponse)
        pgwWiremockRule.prepareCreateHppResponse(MATCHING_MERCHANT_ID, hppResponseString)
    }

    private void setCurrentBaseStore(BaseStoreModel store) {
        baseSiteService.setCurrentBaseSite(store.getCmsSites().stream()
                .filter(site -> site.getUid().equals(store.getUid())).findFirst()
                .orElseThrow(() -> new IllegalStateException(String.format("No site found for %s", store.getUid()))), false)
    }
}
