package com.sast.cis.payment.pgw.service.directdebit.paymentinfo;

import com.sast.cis.core.enums.PaymentInfoDraftCreationStatus;
import com.sast.cis.core.enums.PaymentMethodType;
import com.sast.cis.core.model.IntegratorModel;
import com.sast.cis.core.model.IoTCompanyModel;
import com.sast.cis.core.model.PaymentInfoDraftModel;
import com.sast.cis.core.model.SepaMandatePaymentInfoModel;
import com.sast.cis.core.paymentintegration.data.PaymentInfoCreationRequest;
import com.sast.cis.core.paymentintegration.paymentinfo.data.PaymentInfoCreationNotificationStatus;
import com.sast.cis.core.paymentintegration.paymentinfo.data.PaymentInfoCreationResult;
import com.sast.cis.core.paymentintegration.paymentinfo.exception.PaymentInfoCreationException;
import com.sast.cis.core.service.company.AaCompanyService;
import com.sast.cis.core.service.company.IotCompanyService;
import com.sast.cis.payment.pgw.model.DirectDebitPaymentInfoDraftModel;
import com.sast.cis.payment.pgw.model.PgwSellerAccountModel;
import com.sast.cis.payment.pgw.service.PgwPaymentInfoCreationMethodStrategy;
import com.sast.cis.payment.pgw.service.client.PgwProxyCommunicator;
import com.sast.cis.payment.pgw.service.directdebit.PgwSepaMandatePaymentInfoService;
import com.sast.cis.payment.pgw.service.selleraccount.PgwSellerAccountProvider;
import com.sast.cis.payment.pgw.util.PgwCodeGenerator;
import com.sast.pgw.proxyclient.model.rest.payment.CreateHppResponseDto;
import com.sast.pgw.proxyclient.model.rest.payment.GetPaymentResponseDto;
import com.sast.pgw.proxyclient.model.rest.payment.details.SepaDirectDebitDetailsDto;
import de.hybris.platform.core.model.c2l.CountryModel;
import de.hybris.platform.core.model.c2l.CurrencyModel;
import de.hybris.platform.core.model.c2l.LanguageModel;
import de.hybris.platform.servicelayer.model.ModelService;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Map;

import static com.google.common.base.Preconditions.checkState;
import static com.sast.cis.core.enums.PaymentInfoDraftCreationStatus.*;
import static com.sast.cis.core.enums.PaymentMethodType.SEPA_DIRECTDEBIT;
import static com.sast.cis.core.enums.PaymentProvider.PGW;
import static java.math.BigDecimal.ZERO;
import static org.springframework.web.util.UriComponentsBuilder.fromUri;

@Service
@Slf4j
@RequiredArgsConstructor
public class PgwSepaDirectDebitPaymentInfoCreationStrategy implements PgwPaymentInfoCreationMethodStrategy {
    private static final String HPP_URI_PARAMETER_NAME = "url";
    private static final String FALLBACK_LANGUAGE = "en";
    private static final String HPP_LANGUAGE_PARAMETER = "language";

    private final AaCompanyService aaCompanyService;
    private final PgwSellerAccountProvider pgwSellerAccountProvider;
    private final IotCompanyService iotCompanyService;
    private final PgwProxyCommunicator pgwProxyCommunicator;
    private final ModelService modelService;
    private final PgwSepaMandatePaymentInfoService pgwSepaMandatePaymentInfoService;

    /**
     * Initiates the creation of a new payment info for the SEPA Direct Debit payment method.
     * <br/>
     * It creates a new payment info draft, creates an HPP in the pgw-proxy and instructs the browser to redirect the user to the HPP.
     * <br/>
     * Note that this method will not create a Payment Info. The payment info will be created only after the HPP redirects the user back to the Store.
     *
     * @param request the payment info creation request
     * @return the payment info creation result
     */
    @Override
    public PaymentInfoCreationResult initiatePaymentInfoCreation(final PaymentInfoCreationRequest request) {
        final IntegratorModel integrator = request.integrator();
        final IoTCompanyModel sellerCompany = findSellerCompanyForIntegratorCountry(integrator);
        final DirectDebitPaymentInfoDraftModel paymentInfoDraft = createPaymentInfoDraft(request, sellerCompany);

        final IoTCompanyModel buyerCompany = integrator.getCompany();
        final String language = determineLanguage(buyerCompany);

        final CreateHppResponseDto createHppResponseDto = createHpp(paymentInfoDraft, language);

        final String redirectUrl = fromUri(createHppResponseDto.redirectUri())
            .queryParam(HPP_LANGUAGE_PARAMETER, language)
            .build()
            .toUriString();

        return new PaymentInfoCreationResult(paymentInfoDraft, Map.of(HPP_URI_PARAMETER_NAME, redirectUrl));
    }

    /**
     * Handle the redirect from the HPP.
     * <br/>
     * Based on the notification status, the behavior is as follows:
     * <ul>
     *     <li>SUCCESS: Retrieve the Payment resource. Create a Payment Info if the Payment has an OK status, otherwise set the draft's status to WAITING_FOR_CONFIRMATION</li>
     *     <li>FAILURE: Set the draft's status to FAILED</li>
     *     <li>CANCEL: Set the draft's status to CANCELED</li>
     * </ul>
     *
     * @param paymentInfoDraft the payment info draft
     */
    @Override
    public void handlePaymentInfoCreationNotification(
        @NonNull final PaymentInfoDraftModel paymentInfoDraft, @NonNull final PaymentInfoCreationNotificationStatus notificationStatus) {

        LOG.info("Handle payment info creation notification for payment info draft {}", paymentInfoDraft.getCode());
        checkState(IN_PROGRESS.equals(paymentInfoDraft.getCreationStatus()),
            "Given Payment Info Draft %s is not IN_PROGRESS", paymentInfoDraft.getCode());

        switch (notificationStatus) {
            case SUCCESS -> handlePaymentInfoCreationSuccessNotification(paymentInfoDraft);
            case FAILURE -> handlePaymentInfoCreationFailureNotification(paymentInfoDraft);
            case CANCEL -> handlePaymentInfoCreationCancellationNotification(paymentInfoDraft);
        }
    }

    /**
     * Finalizes the creation of a pending payment info.
     * <br/>
     * This method will fetch the Payment Resource from the pgw-proxy and create a Payment Info if the Payment has an OK status.
     *
     * @param paymentInfoDraft - the payment info draft
     */
    @Override
    public void finalizePendingPaymentInfoCreation(final PaymentInfoDraftModel paymentInfoDraft) {
        LOG.info("Finalize creation of pending payment info {}", paymentInfoDraft.getCode());
        checkState(WAITING_FOR_CONFIRMATION.equals(paymentInfoDraft.getCreationStatus()),
            "Given Payment Info Draft %s is not WAITING_FOR_CONFIRMATION", paymentInfoDraft.getCode());
        finalizePaymentInfoCreation(paymentInfoDraft);
    }

    @Override
    public PaymentMethodType getPaymentMethod() {
        return SEPA_DIRECTDEBIT;
    }

    private DirectDebitPaymentInfoDraftModel createPaymentInfoDraft(
        final PaymentInfoCreationRequest request, final IoTCompanyModel sellerCompany) {

        final String code = PgwCodeGenerator.forSepaMandateInfo();
        final String mandateReference = PgwCodeGenerator.forSepaMandateReference();
        final IntegratorModel integrator = request.integrator();
        final CurrencyModel currency = sellerCompany.getCountry().getCurrency();
        final String pgwMerchantId = getPgwMerchantId(sellerCompany);

        final DirectDebitPaymentInfoDraftModel newPaymentInfoDraft = modelService.create(DirectDebitPaymentInfoDraftModel.class);
        newPaymentInfoDraft.setCode(code);
        newPaymentInfoDraft.setMandateReference(mandateReference);
        newPaymentInfoDraft.setIntegrator(integrator);
        newPaymentInfoDraft.setPlannedAmount(ZERO);
        newPaymentInfoDraft.setCurrency(currency);
        newPaymentInfoDraft.setPgwMerchantId(pgwMerchantId);
        newPaymentInfoDraft.setPaymentProvider(PGW);
        newPaymentInfoDraft.setPaymentMethodType(SEPA_DIRECTDEBIT);
        newPaymentInfoDraft.setCreationStatus(PaymentInfoDraftCreationStatus.IN_PROGRESS);

        modelService.save(newPaymentInfoDraft);
        return newPaymentInfoDraft;
    }

    private CreateHppResponseDto createHpp(final DirectDebitPaymentInfoDraftModel paymentInfoDraft, final String language) {
        try {
            LOG.info("Create HPP for payment info draft {}", paymentInfoDraft.getCode());
            final CreateHppResponseDto createHppResponseDto = pgwProxyCommunicator.createHpp(paymentInfoDraft, language);
            updatePaymentInfoDraft(paymentInfoDraft, createHppResponseDto);
            return createHppResponseDto;
        } catch (final Exception e) {
            LOG.error("Failed to create HPP for payment info draft {}", paymentInfoDraft.getCode(), e);
            updatePaymentInfoDraftStatus(paymentInfoDraft, FAILED);
            throw e;
        }
    }

    private void handlePaymentInfoCreationSuccessNotification(final PaymentInfoDraftModel paymentInfoDraft) {
        finalizePaymentInfoCreation(paymentInfoDraft);
    }

    private void finalizePaymentInfoCreation(final PaymentInfoDraftModel paymentInfoDraft) {
        if (paymentInfoDraft instanceof DirectDebitPaymentInfoDraftModel directDebitPaymentInfoDraft) {
            try {
                LOG.info("Retrieve the PGW Payment for payment info draft {}", paymentInfoDraft.getCode());
                final GetPaymentResponseDto paymentResponse = pgwProxyCommunicator.getPayment(directDebitPaymentInfoDraft);
                LOG.info("PGW Payment has status {}", paymentResponse.status());
                switch (paymentResponse.status()) {
                    case UNKNOWN -> {
                        LOG.error("ALERT: SEPA Mandate creation failed for payment info draft {}: status is UNKNOWN", paymentInfoDraft.getCode());
                        updatePaymentInfoDraftStatus(paymentInfoDraft, WAITING_FOR_CONFIRMATION);
                    }
                    case FAILED -> {
                        LOG.error("ALERT: SEPA Mandate creation failed for payment info draft {}: status is FAILED", paymentInfoDraft.getCode());
                        updatePaymentInfoDraftStatus(paymentInfoDraft, FAILED);
                    }
                    case OK -> handleSuccessfulPaymentResponse(directDebitPaymentInfoDraft, paymentResponse);
                }
            } catch (final Exception e) {
                LOG.error("ALERT: Get Payment query failed for payment info draft {}: {} caused by {}", paymentInfoDraft.getCode(), e.getMessage(), e.getCause() != null ? e.getCause().getMessage() : "No cause");
                updatePaymentInfoDraftStatus(paymentInfoDraft, WAITING_FOR_CONFIRMATION);
            }
        } else {
            throw PaymentInfoCreationException.withMessage(
                "Payment info draft with code %s is of the wrong type".formatted(paymentInfoDraft.getCode())
            );
        }
    }

    private void handlePaymentInfoCreationFailureNotification(final PaymentInfoDraftModel paymentInfoDraft) {
        updatePaymentInfoDraftStatus(paymentInfoDraft, FAILED);
    }

    private void handlePaymentInfoCreationCancellationNotification(final PaymentInfoDraftModel paymentInfoDraft) {
        updatePaymentInfoDraftStatus(paymentInfoDraft, CANCELED);
    }

    private void updatePaymentInfoDraft(final DirectDebitPaymentInfoDraftModel paymentInfoDraft, final CreateHppResponseDto responseDto) {
        final String paymentId = responseDto.paymentId();
        final String redirectUrl = responseDto.redirectUri().toString();
        paymentInfoDraft.setPgwPaymentId(paymentId);
        paymentInfoDraft.setPgwHppRedirectUri(redirectUrl);
        modelService.save(paymentInfoDraft);
    }

    private void updatePaymentInfoDraftStatus(final PaymentInfoDraftModel paymentInfoDraft, final PaymentInfoDraftCreationStatus status) {
        LOG.info("Update payment info draft {} status to {}", paymentInfoDraft.getCode(), status);
        paymentInfoDraft.setCreationStatus(status);
        modelService.save(paymentInfoDraft);
    }

    private IoTCompanyModel findSellerCompanyForIntegratorCountry(final IntegratorModel integrator) {
        final IoTCompanyModel buyerCompany = integrator.getCompany();
        final CountryModel country = buyerCompany.getCountry();
        return aaCompanyService.findSellerForCountry(country).orElseThrow(
            () -> PaymentInfoCreationException.withMessage("No seller company found for country %s".formatted(country.getIsocode()))
        );
    }

    private void handleSuccessfulPaymentResponse(final DirectDebitPaymentInfoDraftModel draft, final GetPaymentResponseDto response) {
        LOG.info("Handle successful payment response for payment info draft {}", draft.getCode());
        if (response.paymentDetails() instanceof SepaDirectDebitDetailsDto sepaDirectDebitDetailsDto) {
            final SepaMandatePaymentInfoModel paymentInfo = pgwSepaMandatePaymentInfoService.createPaymentInfoFromDraft(draft);
            pgwSepaMandatePaymentInfoService.updateAfterAuthorization(paymentInfo, sepaDirectDebitDetailsDto);
            updatePaymentInfoDraftStatus(draft, SUCCEEDED);
        } else {
            LOG.error(
                "Received unexpected response type {} for paymentId={}", response.paymentDetails().getClass(), response.paymentId()
            );
            updatePaymentInfoDraftStatus(draft, FAILED);
        }
    }

    private String getPgwMerchantId(@NonNull final IoTCompanyModel company) {
        final PgwSellerAccountModel pgwSellerAccount = pgwSellerAccountProvider.getActiveSellerAccountForCompany(company)
            .orElseThrow(
                () -> PaymentInfoCreationException.withMessage(
                    "ALERT: No active PGW seller account for company %s".formatted(company.getUid())
                )
            );
        return pgwSellerAccount.getAccountId();
    }

    private String determineLanguage(@NonNull final IoTCompanyModel company) {
        return iotCompanyService.determineLanguageForCompany(company).map(LanguageModel::getIsocode).orElse(FALLBACK_LANGUAGE);
    }
}
