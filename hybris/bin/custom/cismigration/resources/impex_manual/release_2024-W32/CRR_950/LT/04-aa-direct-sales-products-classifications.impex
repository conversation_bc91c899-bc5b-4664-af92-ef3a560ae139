#% impex.setLocale( Locale.GERMAN );
#% setAccessibility(true)
#% impex.definitions.put("$currentDateTime", new java.text.SimpleDateFormat("dd.MM.yyyy HH:mm:ss").format(new Date()))

# numerical code for LT, used as prefix for product code
$ltCc = 370

$productCatalog = aav2ProductCatalog
$classificationCatalog = aaClassificationCatalog
$catalogVersion = catalogversion(catalog(id[default = $productCatalog]), version[default = 'Staged'])[unique = true, default = $productCatalog:Staged]
$classCatalogVersion = catalogversion(catalog(id[default = 'aaClassificationCatalog']), version[default = '1.0'])[unique = true, default = 'aaClassificationCatalog:1.0']
$supercategories = source(code, $classCatalogVersion)[unique = true]
$categories = target(code, $catalogVersion)[unique = true]

$clAttrModifiersVariants = system = '$classificationCatalog', version = '1.0', translator = de.hybris.platform.catalog.jalo.classification.impex.ClassificationAttributeTranslator;

# @formatter:off
$featureHR = @hardware-requirements,100[$clAttrModifiersVariants]; # hardware needed
$featureVT = @vehicle-types,100[$clAttrModifiersVariants]; # vehicle type
# @formatter:on

# Links ClassificationClasses to Categories
INSERT_UPDATE CategoryCategoryRelation; $categories; $supercategories
                                      ; main       ; 100

# when using relation, hybris does not change the modified time stamp of the main category.
# as a result the relation is not synchroized without force.
# this work around will help synchronize supercategories of the main category.
UPDATE Category; code[unique = true]; modifiedtime; $catalogVersion
               ; main               ; "$currentDateTime"

INSERT_UPDATE App; code[unique = true]       ; $featureHR; $catalogVersion;
                 ; AA2_$ltCc1687_CRR950      ;


INSERT_UPDATE App; code[unique = true]       ; $featureVT; $catalogVersion;
                 ; AA2_$ltCc1687_CRR950      ; vt_pkw

