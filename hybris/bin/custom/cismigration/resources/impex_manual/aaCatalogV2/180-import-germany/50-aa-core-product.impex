# This file is used to import the CoRe product for Germany.
# It is created only for the contract migration, and is disabled by default.

#% impex.setLocale( Locale.ENGLISH );

# numerical code for DE, used as prefix for product code
$deCc = 049
$aaCc = 040
$aaPackageName = com.sast.aa.de.

$productCatalog = aav2ProductCatalog
$catalogVersion = catalogversion(catalog(id[default = $productCatalog]), version[default = 'Staged'])
$supercategories = supercategories(code, $catalogVersion)
$baseProduct = baseProduct(code, $catalogVersion)
$approved = approvalstatus(code)[default = 'approved']
$taxGroup = Europe1PriceFactory_PTG(code)[default = eu-vat-full]
$emailAddress = <EMAIL>

UPDATE GenericItem[processor = de.hybris.platform.commerceservices.impex.impl.ConfigPropertyImportProcessor]; pk[unique = true]
$staticLegalResourcesUrl = $config-aa.corporate.home

$privacyPolicyUrl = $staticLegalResourcesUrl/Datenschutzhinweise_ESItronic_AT_2021-01.pdf

# please make sure, to use right DELEVELOPER COMPANY for Germany in the right environment.
# live
# $companyId =
# demo
# $companyId = 774ae2d7-2ca2-42f7-b7bd-eba0128a666f
# dev
# $companyId = c7cc0be2-8851-4947-b9b7-51a8bb1261fd
$companyId = 91ae83b9-302b-4da2-8b15-814675361228

$coReName = CoRe Server + Online
$coReName_en = CoRe Server + Online
$coReDescription = Bosch Connected Repair ist eine Software, die Werkstattausrüstung, Fahrzeug- und Reparaturdaten miteinander verbindet. Ob bei Störungen oder bei der Speicherung von Daten und Bildern gemäß der Datenschutzgrundverordnung - CoRe wurde an die Bedürfnisse der Kunden angepasst.
$coReDescription_en = Bosch Connected Repair is software that connects workshop equipment, vehicle and repair data. Whether in the event of malfunctions or the storage of data and images in accordance with the Basic Data Protection Regulation - CoRe has been adapted to the needs of customers.

$coreS1BrimName = CoRe for ESI 2.0 packages

$countryRestricted = true
$enabledIn = DE


INSERT_UPDATE App; code[unique = true]       ; packageName                    ; emailAddress[default = $emailAddress]; submittedBy(uid); storeAvailabilityMode(code)[default = RESTRICTED_BUYER_GROUP]; unit(code)[default = pieces]; variantType(code)[default = AppLicense]; $catalogVersion[unique = true]; $approved; $taxGroup; privacyPolicyUrl[default = $privacyPolicyUrl]; termsOfUseUrl; supportPageUrl; productWebsiteUrl; company(uid)[default = $companyId]; countryRestricted[default = $countryRestricted]
                 ; AA2_$deCc1687_CORE_ESIPKG ; $aaPackageName1687_CORE_ESIPKG ;

INSERT_UPDATE App; code[unique = true]       ; name[lang = de]; summary[lang = de]; description[lang = de]; $catalogVersion[unique = true];
                 ; AA2_$deCc1687_CORE_ESIPKG ; $coReName      ; $coReDescription  ; $coReDescription      ;

INSERT_UPDATE App; code[unique = true]       ; name[lang = en]; summary[lang = en]  ; description[lang = en]; $catalogVersion[unique = true];
                 ; AA2_$deCc1687_CORE_ESIPKG ; $coReName_en   ; $coReDescription_en ; $coReDescription_en   ;

INSERT_UPDATE App; code[unique = true]       ; masterEnabled[default = false]; $catalogVersion[unique = true];
                 ; AA2_$deCc1687_CORE_ESIPKG ;

INSERT_UPDATE ProductContainer; code[unique = true]        ; app(code, $catalogVersion); company(uid)[unique = true][default = $companyId];
                              ; pcaa_$deCc1687_CORE_ESIPKG ; AA2_$deCc1687_CORE_ESIPKG

INSERT_UPDATE AppLicense; code[unique = true] ; $baseProduct              ; sellerProductId; brimName[lang = en]; licenseType(code)[default = SUBSCRIPTION]; runtime(code)          ; bundleInfo(code); specifiedPrice; billingSystemStatus(code)[default = NEW]; unit(code)[default = pieces]; $catalogVersion[unique = true]; $approved; $taxGroup; enabledCountries(isocode)[default = $enabledIn]; availabilityStatus(code)[default = UNPUBLISHED]
                        ; AA2_$deCc1687P15130 ; AA2_$deCc1687_CORE_ESIPKG ; 1687P15130     ; $coreS1BrimName    ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 1             ;

UPDATE AppLicense; code[unique = true] ; userGroups(uid)                                                                                                                                                         ; $catalogVersion[unique = true];
                 ; AA2_$deCc1687P15130 ; IDW000,BCS000,BDS000,BDC000,BM0000,AC0000,AT0000,WD0000,KA0011,KA0012,KA0013,KA0014,KA0015,KA0016,KA0017,KA0018,KA0019,KA0020,KA0021,KA0022,KA0023,KA0024,KA0025,KA0026 ;


INSERT_UPDATE PriceRow; product(code, $catalogVersion)[unique = true]; price; currency(isocode)[unique = true, default = EUR]; unit(code[unique = true, default = pieces]); minqtd[default = 1]; unitFactor[default = 1]; net[default = true]; ug[unique = true, default = '']
                      ; AA2_$deCc1687P15130                          ; 1


INSERT_UPDATE App; code[unique = true]       ; contentModules(code); $catalogVersion[unique = true];
                 ; AA2_$deCc1687_CORE_ESIPKG ; CM_$aaCcCoReESIPKG

INSERT_UPDATE App; code[unique = true]       ; $supercategories; $catalogVersion[unique = true];
                 ; AA2_$deCc1687_CORE_ESIPKG ; cat_1010103     ;

INSERT_UPDATE App; code[unique = true]       ; eulaContainers(code); $catalogVersion[unique = true]
                 ; AA2_$deCc1687_CORE_ESIPKG ; AA2_CoRe
