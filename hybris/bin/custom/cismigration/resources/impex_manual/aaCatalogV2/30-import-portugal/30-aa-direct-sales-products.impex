#% impex.setLocale( Locale.ENGLISH );

# numerical isocode for PT, used as prefix for product code
$ptCc = 351
$aaCc = 040
$aaPackageName = com.sast.aa.pt.

$productCatalog = aav2ProductCatalog
$catalogVersion = catalogversion(catalog(id[default = $productCatalog]), version[default = 'Staged'])
$supercategories = supercategories(code, $catalogVersion)
$baseProduct = baseProduct(code, $catalogVersion)
$approved = approvalstatus(code)[default = 'approved']
$taxGroup = Europe1PriceFactory_PTG(code)[default = eu-vat-full]
$emailAddress = <EMAIL>

UPDATE GenericItem[processor = de.hybris.platform.commerceservices.impex.impl.ConfigPropertyImportProcessor]; pk[unique = true]
$staticLegalResourcesUrl = $config-aa.corporate.home

$privacyPolicyUrl = $staticLegalResourcesUrl/Datenschutzhinweise_ESItronic_AT_2021-01.pdf

# please make sure, to use right DELEVELOPER COMPANY for portugal in the right environment.
# demo
#$companyId = 5ec78d86-78cf-4269-bddf-112f5c72e671
# dev
#$companyId = d5b1e612-4646-4267-a4b3-7d49d14325b0
# live
# $companyId = 03cb8bd0-f982-4bb8-8ece-e5528b3dc795
# local
$companyId = 34079b51-379b-46dd-934e-57e6bf5c4cd1

$alltrucksDescription = O software Alltrucks contém informações importantes sobre veículos comerciais, como a série do modelo, o desempenho, a identificação do motor e a configuração dos eixos. Inclui ESI[tronic] Truck, NEO | orange da Knorr-Bremse e ZF-TESTMAN da ZF.
$alltrucksDescription_en = The Alltrucks software contains important information about commercial vehicles such as model series, performance, engine identification as well as axle configuration. Includes ESI[tronic] Truck, NEO | orange from Knorr-Bremse and ZF-TESTMAN from ZF.
$compacFsa500Description = O CompacSoft[plus] para FSA 500 está equipado com testes de componentes predefinidos e pode ser ligado a sistemas existentes, bem como utilizado para expandir gradualmente o seu sistema de testes de oficina.
$compacFsa500Description_en = The CompacSoft[plus] for FSA 500 is equipped with preset component tests and can be connected to existing systems, as well as used to gradually expand your workshop test system.
$compacFsa7xxDescription = Com o CompacSoft[plus] para FSA 7xx, o conforto para todas as tarefas de medição no veículo é aumentado ainda mais pelos passos de teste guiados pelo menu, os valores de ajuste opcionais específicos do veículo, bem como a exibição dos valores reais.
$compacFsa7xxDescription_en = With CompacSoft[plus] for FSA 7xx, the comfort for all measuring tasks on the vehicle is further increased by the menu-guided test steps, the optional vehicle-specific set values, as well as the display of the actual values.

$criDescription = O software CRI para DCI 200 e DCI 700 fornece dados atualizados, assegura processos simples e inclui o teste de injectores piezoelétricos para sistemas common rail.
$criDescription_en = CRI for DCI 700 software provides up-to-date data, ensures smooth processes and includes testing of piezo injectors for common rail systems.
$crinDescription = O software CRIN para DCI 200 e DCI 700 fornece dados atualizados, assegura processos simples e inclui o teste de injectores de válvulas solenóides para sistemas common rail.
$crinDescription_en = CRIN for DCI 700 software provides up-to-date data, ensures smooth processes and includes the testing of solenoid valve injectors for common rail systems.

$packAdvancedDescription = O nível seguinte de equipamento oficinal profissional. Além do pacote ESI[tronic] 2.0 Diagnóstico, estão incluídas instruções e manuais de reparação.  Connected Repair fornece um histórico de serviço e manutenção assim como informações de reparações.
$packAdvancedDescription_en = The next level of professional workshop equipment. In addition to the Diagnostic package, instructions and manuals are included. Connected Repair provides service and maintenance history and repair information.
$packComponentCatDescription = O catálogo de peças de reposição inclui as aplicações, as funções e o equipamento automóvel, bem como as peças de reposição diesel e as peças de reposição eléctricas, incluindo o arquivo e as peças de reposição eléctricas ESI[tronic]-F.
$packComponentCatDescription_en = The spare parts catalog package includes the applications, functions and automotive equipment as well as the diesel spare parts and electrical spare parts incl. archive and electrical spare part ESI[tronic]-F.
$packComponentRepairDieselDescription = O pacote Reparação de componentes diesel e elétricos contém informação completa sobre peças de reposição e a reparação de componentes diesel e elétricos. O que permite que o veículo e toda a gama de equipamentos Bosch para veículos seja identificada corretamente e ainda inclui instruções de reparação e informações de serviço.
$packComponentRepairDieselDescription_en = The Repair Diesel package provides information on spare parts and repair of diesel and electrical components. It allows identification of the vehicle, Bosch automotive equipment and includes repair instructions & service information.
$packComponentRepairElectricDescription = O número crescente de modelos de veículos faz com que seja difícil para as oficinas disporem de informações atualizadas sobre os sistemas eléctricos dos veículos. O pacote Reparação de componentes elétricos pode ajudar, fornecendo dados de peças de reposição para os sistemas elétricos dos veículos num formato claro e sistemático.
$packComponentRepairElectricDescription_en = The increasing number of vehicle models makes it difficult for workshops to have up-to-date information on vehicle electrical systems at their fingertips. The Repair Electrics Package provides support with spare parts data on car electrics in a clear format.
$packDiagnosticDescription = Pacote inicial para diagnóstico profissional, reparação e manutenção. Permite a execução de diagnósticos elétricos a um nível profissional e oferece uma vasta gama de outras funções para todos os veículos abrangidos.
$packDiagnosticDescription_en = The entry into professional diagnosis, repair and maintenance. It enables competent electrical diagnosis and offers a wide range of other functions for all covered vehicles.
$packMasterDescription = Pacote de toda a informação para diagnósticos profissionais de veículos. Fornece todas as informações e funções necessárias para diagnóstico, reparação, manutenção, peças de reposição, documentação e gestão de dados. O suporte técnico ajuda-o a encontrar soluções.
$packMasterDescription_en = The fully comprehensive package for professional vehicle diagnostics. It provides all the necessary information for diagnosis, repair, maintenance, spare parts, documentation and data management. Technical Support assists you in finding solutions.
$ohw1Description = O Pacote Truck OHW I apoia as oficinas com diagnósticos fiáveis, manutenção completa e reparações eficientes de máquinas agrícolas. Inclui, entre outras coisas, funções de ajuste e parametrização para sistemas hidráulicos.
$ohw1Description_en = The Agricultural Machinery Diagnostic Package provides information on the diagnosis, maintenance and repair of agricultural vehicles. Included are, among other things, adjustment and parameterisation functions for hydraulic systems.
$ohw2Description = O Pacote Truck OHW II apoia as oficinas com diagnósticos fiáveis, manutenção completa e reparações eficientes de máquinas e motores para a construção. Inclui, entre outras coisas, funções de ajuste e parametrização para sistemas hidráulicos.
$ohw2Description_en = The diagnosis package for construction machinery and engines provides information on the diagnosis, maintenance and repair of agricultural vehicles. Included are, among other things, adjustment and parameterisation functions for hydraulic systems.
$ohw3Description_en = The diagnosis package for construction machinery and engines provides information on the diagnosis, maintenance and repair of agricultural vehicles. Included are, among other things, adjustment and parameterisation functions for hydraulic systems.
$packTruckDescription = O pacote Truck apoia as oficinas com diagnósticos fiáveis, manutenção completa e reparações eficientes de todos os veículos comerciais ligeiros e pesados, reboques, furgonetas e autocarros.
$packTruckDescription_en = The Truck package supports workshops in the reliable diagnosis, complete maintenance and efficient repair of all common light and heavy commercial vehicles, trailers, vans and buses.
$infoartWDescription = O ESI[tronic] W contém informações sobre valores de teste de diesel para combinações de bombas em linha, bem como para bombas VE, o procedimento completo de teste, desde a determinação dos valores medidos até a impressão do relatório e a exibição das etapas de teste na sequência ideal.
$infoartWDescription_en = The ESI[tronic] 2.0 Infoart W contains information about diesel test values for in-line pump combinations as well as for VE pumps, the complete test process test procedure from the determination of the measured values to the printout of the report and the display of the test steps in the optimum sequence.
$thlPkwDescription = Você necessita de suporte técnico para a manutenção ou reparação de um carro ou simplesmente de uma segunda opinião? Então, entre em contato com a nossa equipa de suporte técnico e obtenha soluções rápidas e comprovadas.
$thlPkwDescription_en = Do you need technical support to maintain or repair a car, or simply a reliable second opinion? Then contact our support team and get fast and sound solutions.
$thlTruckDescription = Você necessita de suporte técnico para a manutenção ou reparação de um camião ou simplesmente de uma segunda opinião? Então, entre em contato com a nossa equipa de suporte técnico e obtenha soluções rápidas e comprovadas.
$thlTruckDescription_en = Do you need technical support to maintain or repair a truck, or just a reliable second opinion? Then contact our support team and get fast and sound solutions.
$thlPkwTruckDescription = Com uma vasta cobertura de marcas, modelos e sistemas, a equipa de suporte técnico pode fornecer assistência técnica e documentação para várias reparações de veículos comerciais ligeiros e pesados.
$thlPkwTruckDescription_en = With extensive coverage of makes, models and systems, the support team can provide technical assistance and documentation for various light and heavy commercial vehicle repairs.
$infoartTestdataDescription = O ESI[tronic] Testdata (CD) contém valores de teste para as bombas de alta pressão Common Rail da Bosch, injetores Common Rail e bombas de injecção tipo distribuidor VP 29 / 30 / 44.
$infoartTestdataDescription_en = The ESI[tronic] 2.0-Infotype Testdata (CD) contains test values for Bosch Common Rail high pressure pumps, Common Rail injectors and VP 29 / 30 / 44 distributor injection pumps.
$truckUpgradeDescription = O pacote Ampliação Truck é indicado para clientes existentes de pacotes ESI[tronic] para veículos ligeiros e que adquirem um KTS Truck. Este pacote apoia a oficina com diagnósticos fiáveis, manutenção completa e reparações eficientes de todos os veículos comerciais ligeiros e pesados, reboques, furgonetas e autocarros.
$truckUpgradeDescription_en = The Truck Upgrade package is dedicated to ESI[tronic] Car users and supports workshops in the reliable diagnosis, complete maintenance and efficient repair of all common light and heavy commercial vehicles, trailers, vans and buses.

$coReName = CoRe Server + Online
$coReName_en = CoRe Server + Online
$coReDescription = O Bosch Connected Repair é um software que conecta equipamentos de oficina, dados de veículos e informações de reparações. Seja para solucionar problemas ou para armazenar dados e imagens de acordo com o Regulamento Geral de Proteção de Dados, o CoRe foi adaptado às necessidades dos clientes.
$coReDescription_en = Bosch Connected Repair is software that connects workshop equipment, vehicle and repair data. Whether in the event of malfunctions or the storage of data and images in accordance with the Basic Data Protection Regulation - CoRe has been adapted to the needs of customers.

$kts250SDDescription = A entrada no diagnóstico, reparação e manutenção profissionais especificamente para o KTS 250. Permite a execução de diagnósticos eletrónicos a um nível profissional e oferece uma vasta gama de outras funções para todos os veículos abrangidos.
$kts250SDDescription_en = The entry into professional diagnosis, repair and maintenance specifically for KTS 250. It enables competent electrical diagnosis and offers a wide range of other functions for all covered vehicles.

$crr950DieselInjectorsRepairSoftDescription = CRR950 - Software para Reparação de Injectores Diesel Bosch
$crr950DieselInjectorsRepairSoftDescription_en = The CRR 950 guides and support the professional repairs of magnetic solenoid common-rail injectors from Bosch

# TODO: UPDATE TRANSLATION
$adasDescription = Fully integrated into the ESI[tronic] or as stand-alone application: The ADAS One Solution provides up-to-date data and user guidance to ensures precise calibration and adjustment of driver assistance systems. Significantly shortened and optimized workflows despite the increasing number of sensor technologies and calibration routines.
$adasDescription_en = Fully integrated into the ESI[tronic] or as stand-alone application: The ADAS One Solution provides up-to-date data and user guidance to ensures precise calibration and adjustment of driver assistance systems. Significantly shortened and optimized workflows despite the increasing number of sensor technologies and calibration routines.


$coreS1BrimName = CoRe for ESI 2.0 packages
$alltrucksSubsS1BrimName = Alltrucks Diagnose-Paket
$alltrucksSubsM3BrimName = ESI[tronic] 2.0 All Trucks Unlim Multi
$compacFsa500Full3YBrimName = CompacSoft[plus] FSA 500 (3 years)
$compacFsa500SubsBrimName = CompacSoft[plus] FSA 500
$compacFsa7xxFull3YBrimName = CompacSoft[plus] FSA 7xx (3 years)
$compacFsa7xxSubsBrimName = CompacSoft[plus] FSA 7xx
$criSubsS1BrimName = Component DCI-CRI
$criSubsM3BrimName = Component DCI-CRI_Multi-users_3
$crinSubsS1BrimName = Component DCI-CRIN
$crinSubsM3BrimName = Component DCI-CRIN_Multi-users_3
$packAdvancedSubsBrimName = ESI 2.0 Advanced Unlimited
$packAdvancedFull3YBrimName = ESI 2.0 Advanced 3 years
$packComponentCatSubsS1BrimName = ESI 2.0 ComponentCatalog D+E Unlimited
$packComponentCatSubsM3BrimName = ESI 2.0 ComponentCatalog D+E Unlim Multi
$packComponentCatFull3YS1BrimName = ESI 2.0 ComponentCatalog D+E 3 years
$packComponentCatFull3YM3BrimName = ESI 2.0 ComponentCatalog D+E 3y Multi
$packComponentRepairDieselSubsS1BrimName = ESI 2.0 ComponentRepair D+E Unlimited
$packComponentRepairDieselSubsM3BrimName = ESI 2.0 ComponentRepair D+E Unlim Multi
$packComponentRepairDieselFull3YS1BrimName = ESI 2.0 ComponentRepair D+E 3 years
$packComponentRepairDieselFull3YM3BrimName = ESI 2.0 ComponentRepair D+E 3y Multi
$packComponentRepairElectricSubsS1BrimName = ESI 2.0 ComponentRepair E Unlimited
$packComponentRepairElectricSubsM3BrimName = ESI 2.0 ComponentRepair E Unlim Multi
$packComponentRepairElectricFull3YS1BrimName = ESI 2.0 ComponentRepair E 3 years
$packComponentRepairElectricFull3YM3BrimName = ESI 2.0 ComponentRepair E 3 years Multi
$packDiagnosticFull3YBrimName = ESI 2.0 Diagnostic 3 years
$packDiagnosticSubsBrimName = ESI 2.0 Diagnostic Unlimited
$packDiagnosticFullOneTimeBrimName = ESI[tronic] 2.0 SD (OTP one time purchase)
$packMasterFull3YBrimName = ESI 2.0 Master 3 years
$packMasterSubsS1BrimName = ESI 2.0 Master Unlimited
$packMasterSubsM3BrimName = ESI 2.0 Master Unlimited Multi
$ohw1SubsS1BrimName = ESI[tronic] 2.0 Truck OHW I
$ohw1SubsM3BrimName = ESI[tronic] 2.0 Truck OHW I Multi
$ohw1Full3YS1BrimName = ESI[tronic] 2.0 Truck OHW I (3 years)
$ohw1Full3YM3BrimName = ESI[tronic] 2.0 Truck OHW I (3y) Multi
$ohw2SubsS1BrimName = ESI[tronic] 2.0 Truck OHW II
$ohw2SubsM3BrimName = ESI[tronic] 2.0 Truck OHW II Multi
$ohw2Full3YS1BrimName = ESI[tronic] 2.0 Truck OHW II (3 years)
$ohw2Full3YM3BrimName = ESI[tronic] 2.0 Truck OHW II (3y) Multi
$ohw3S1BrimName = ESI[tronic] 2.0 Truck OHW III Unlimited
$ohw3M3BrimName = ESI[tronic] 2.0 Truck OHW III Unlimited Multi
$ohw3Single3YearBrimName = ESI[tronic] Truck OHW III (3y)
$ohw3Multi3YearBrimName = ESI[tronic] Truck OHW III (3y) Multi.
$packTruckSubsS1BrimName = ESI[tronic] 2.0 Truck
$packTruckSubsM3BrimName = ESI[tronic] 2.0 Truck Multi
$packTruckFull3YS1BrimName = ESI[tronic] 2.0 Truck (3 years)
$packTruckFull3YM3BrimName = ESI[tronic] 2.0 Truck (3 years) Multi
$packTruckFullBrimName = ESI[tronic] 2.0 Truck (Einmalkauf)
$infoartWSubsBrimName = ESI[tronic] W Diesel Test Data (WP)
$infoartWFull3YBrimName = ESI[tronic] W (3 Jahre)
$thlPkwSubsBrimName = THL use technical hotline
$thlPkwFull3YBrimName = Technische Hotline LKW (3 Jahre)
$thlTrkSubsBrimName = Technische Hotline ESI[tronic] for Truck
$thlPkwTrkSubsBrimName = Technische Hotline fuer LKWs und PKWs
$infoartTestdataSubsBrimName = Testdata VP-M/CP
$truckUpgradeSubsS1BrimName = ESI[tronic] 2.0 Truck Upgrade
$truckUpgradeSubsM3BrimName = ESI[tronic] 2.0 Truck Upgrade Multi
$truckUpgradeFull3YS1BrimName = ESI[tronic] 2.0 Truck Upgrade (3 years)
$truckUpgradeFull3YM3BrimName = ESI[tronic] 2.0 Truck Upgrade (3y) Multi
$kts250SDFullBrimName = KTS 250 SD (OTP one time purchase)
$kts250SDSubsBrimName = KTS 250 SD ECU Diagnosis
$crr950DieselInjectorsRepairSoftwareBrimName1L = CRR 950 Diesel Injectors (1 License)
$crr950DieselInjectorsRepairSoftwareBrimName3L = CRR 950 Diesel Injectors (3 Licenses)
$fsa7xxMulti = CompacSoft[plus] FSA 7xx Multi
$fsa7xx3YearsMulti = CompacSoft[plus] FSA 7xx (3 years) Multi
$fsa5xxMulti = CompacSoft[plus] FSA 500 Multi
$fsa5xx3YearsMulti = CompacSoft[plus] FSA 500 (3 years)Multi

$adasSubsS1BrimName = ADAS ONE - DATA
$adasFull3YS1BrimName = ADAS ONE - DATA (3 years)

$countryRestricted = true

INSERT_UPDATE App; code[unique = true]       ; packageName                    ; name[lang = pt]                                ; summary[lang = pt]                          ; description[lang = pt]                      ; emailAddress[default = $emailAddress]; submittedBy(uid); storeAvailabilityMode(code)[default = RESTRICTED_BUYER_GROUP]; unit(code)[default = pieces]; variantType(code)[default = AppLicense]; $catalogVersion[unique = true]; $approved; $taxGroup; privacyPolicyUrl[default = $privacyPolicyUrl]; termsOfUseUrl; supportPageUrl; productWebsiteUrl; company(uid)[default = $companyId]; countryRestricted[default = $countryRestricted]
                 ; AA2_$ptCc1987_ALLTRUCKS   ; $aaPackageName1987_ALLTRUCKS   ; Software Diagnóstico Alltrucks                 ; $alltrucksDescription                       ; $alltrucksDescription                       ;
                 ; AA2_$ptCc1687_CSFSA500    ; $aaPackageName1687_CSFSA500    ; CompacSoft[plus] FSA 500                       ; $compacFsa500Description                    ; $compacFsa500Description                    ;
                 ; AA2_$ptCc1687_CSFSA7XX    ; $aaPackageName1687_CSFSA7XX    ; CompacSoft[plus] FSA 7xx                       ; $compacFsa7xxDescription                    ; $compacFsa7xxDescription                    ;
                 ; AA2_$ptCc1687_DCICRI      ; $aaPackageName1687_DCICRI      ; Componentes DCI-CRI                            ; $criDescription                             ; $criDescription                             ;
                 ; AA2_$ptCc1687_DCICRIN     ; $aaPackageName1687_DCICRIN     ; Componentes DCI-CRIN                           ; $crinDescription                            ; $crinDescription                            ;
                 ; AA2_$ptCc1987_ESIADV      ; $aaPackageName1987_ESIADV      ; ESI[tronic] 2.0 Avançado                       ; $packAdvancedDescription                    ; $packAdvancedDescription                    ;
                 ; AA2_$ptCc1987_ESIREPCAT   ; $aaPackageName1987_ESIREPCAT   ; Catálogo de peças de reposição                 ; $packComponentCatDescription                ; $packComponentCatDescription                ;
                 ; AA2_$ptCc1987_ESIREPD     ; $aaPackageName1987_ESIREPD     ; Reparação de componentes Diesel e elétricos    ; $packComponentRepairDieselDescription       ; $packComponentRepairDieselDescription       ;
                 ; AA2_$ptCc1987_ESIREPE     ; $aaPackageName1987_ESIREPE     ; Reparação de componentes elétricos             ; $packComponentRepairElectricDescription     ; $packComponentRepairElectricDescription     ;
                 ; AA2_$ptCc1987_ESIDIAG     ; $aaPackageName1987_ESIDIAG     ; ESI[tronic] 2.0 Diagnóstico                    ; $packDiagnosticDescription                  ; $packDiagnosticDescription                  ;
                 ; AA2_$ptCc1987_ESIMASTER   ; $aaPackageName1987_ESIMASTER   ; ESI[tronic] 2.0 Master                         ; $packMasterDescription                      ; $packMasterDescription                      ;
                 ; AA2_$ptCc1987_TRKOHW1     ; $aaPackageName1987_TRKOHW1     ; ESI[tronic] 2.0 Truck OHW I                    ; $ohw1Description                            ; $ohw1Description                            ;
                 ; AA2_$ptCc1987_TRKOHW2     ; $aaPackageName1987_TRKOHW2     ; ESI[tronic] 2.0 Truck OHW II                   ; $ohw2Description                            ; $ohw2Description                            ;
                 ; AA2_$ptCc1987_TRKTRUCK    ; $aaPackageName1987_TRKTRUCK    ; ESI[tronic] 2.0 Truck                          ; $packTruckDescription                       ; $packTruckDescription                       ;
                 ; AA2_$ptCc1987_TSTINFOAW   ; $aaPackageName1987_TSTINFOAW   ; ESI[tronic] W                                  ; $infoartWDescription                        ; $infoartWDescription                        ;
                 ; AA2_$ptCc1987_THLPKW      ; $aaPackageName1987_THLPKW      ; Suporte técnico de veículos ligeiros           ; $thlPkwDescription                          ; $thlPkwDescription                          ;
                 ; AA2_$ptCc1987_THLTRK      ; $aaPackageName1987_THLTRK      ; Suporte técnico de veículos pesados            ; $thlTruckDescription                        ; $thlTruckDescription                        ;
                 ; AA2_$ptCc1987_THLPKWTRK   ; $aaPackageName1987_THLPKWTRK   ; Suporte técnico de veículos ligeiros e pesados ; $thlPkwTruckDescription                     ; $thlPkwTruckDescription                     ;
                 ; AA2_$ptCc1687_TSTINFODAT  ; $aaPackageName1687_TSTINFODAT  ; Testdata VP-M/CP                               ; $infoartTestdataDescription                 ; $infoartTestdataDescription                 ;
                 ; AA2_$ptCc1987_TRKUPG      ; $aaPackageName1987_TRUCKUPG    ; ESI[tronic] 2.0 Truck Upgrade                  ; $truckUpgradeDescription                    ; $truckUpgradeDescription                    ;
                 ; AA2_$ptCc1687_CORE_ESIPKG ; $aaPackageName1687_CORE_ESIPKG ; $coReName                                      ; $coReDescription                            ; $coReDescription                            ;
                 ; AA2_$ptCc1987_KTS250SD    ; $aaPackageName1987_KTS250      ; KTS 250 SD ECU Diagnosis                       ; $kts250SDDescription                        ; $kts250SDDescription                        ;
                 ; AA2_$ptCc1687_CRR950      ; $aaPackageName1687_CRR950      ; CRR 950 Diesel Injectors Repair Software       ; $crr950DieselInjectorsRepairSoftDescription ; $crr950DieselInjectorsRepairSoftDescription ;
                 ; AA2_$ptCc1987_TRKOHW3     ; $aaPackageName1987_TRKOHW3     ; Off Highway III (OHW III)                      ; $ohw3Description_en                         ; $ohw3Description_en                         ;
                 ; AA2_$ptCc1987_ADAS_ONE    ; $aaPackageName1987_ADAS_ONE    ; ADAS One Solution                              ; $adasDescription                            ; $adasDescription                            ;


INSERT_UPDATE App; code[unique = true]       ; name[lang = en]                          ; summary[lang = en]                             ; description[lang = en]; $catalogVersion[unique = true];
                 ; AA2_$ptCc1987_ALLTRUCKS   ; Alltrucks Diagnosis                      ; $alltrucksDescription_en                       ; $alltrucksDescription_en
                 ; AA2_$ptCc1687_CSFSA500    ; CompacSoft[plus] FSA 500                 ; $compacFsa500Description_en                    ; $compacFsa500Description_en
                 ; AA2_$ptCc1687_CSFSA7XX    ; CompacSoft[plus] FSA 7xx                 ; $compacFsa7xxDescription_en                    ; $compacFsa7xxDescription_en
                 ; AA2_$ptCc1687_DCICRI      ; Component DCI-CRI                        ; $criDescription_en                             ; $criDescription_en
                 ; AA2_$ptCc1687_DCICRIN     ; Component DCI-CRIN                       ; $crinDescription_en                            ; $crinDescription_en
                 ; AA2_$ptCc1987_ESIADV      ; ESI[tronic] 2.0 Advanced                 ; $packAdvancedDescription_en                    ; $packAdvancedDescription_en
                 ; AA2_$ptCc1987_ESIREPCAT   ; ESI[tronic] 2.0 ComponentCat D+E         ; $packComponentCatDescription_en                ; $packComponentCatDescription_en
                 ; AA2_$ptCc1987_ESIREPD     ; ESI[tronic] 2.0 ComponentRepair D+E      ; $packComponentRepairDieselDescription_en       ; $packComponentRepairDieselDescription_en
                 ; AA2_$ptCc1987_ESIREPE     ; ESI[tronic] 2.0 ComponentRepair E        ; $packComponentRepairElectricDescription_en     ; $packComponentRepairElectricDescription_en
                 ; AA2_$ptCc1987_ESIDIAG     ; ESI[tronic] 2.0 Diagnostic               ; $packDiagnosticDescription_en                  ; $packDiagnosticDescription_en
                 ; AA2_$ptCc1987_ESIMASTER   ; ESI[tronic] 2.0 Master                   ; $packMasterDescription_en                      ; $packMasterDescription_en
                 ; AA2_$ptCc1987_TRKOHW1     ; ESI[tronic] 2.0 Truck OHW I              ; $ohw1Description_en                            ; $ohw1Description_en
                 ; AA2_$ptCc1987_TRKOHW2     ; ESI[tronic] 2.0 Truck OHW II             ; $ohw2Description_en                            ; $ohw2Description_en
                 ; AA2_$ptCc1987_TRKTRUCK    ; ESI[tronic] 2.0 Truck                    ; $packTruckDescription_en                       ; $packTruckDescription_en
                 ; AA2_$ptCc1987_TSTINFOAW   ; ESI[tronic] W                            ; $infoartWDescription_en                        ; $infoartWDescription_en
                 ; AA2_$ptCc1987_THLPKW      ; Technical Hotline Cars                   ; $thlPkwDescription_en                          ; $thlPkwDescription_en
                 ; AA2_$ptCc1987_THLTRK      ; Technical Hotline Truck                  ; $thlTruckDescription_en                        ; $thlTruckDescription_en
                 ; AA2_$ptCc1987_THLPKWTRK   ; Technical Hotline Car + Truck            ; $thlPkwTruckDescription_en                     ; $thlPkwTruckDescription_en
                 ; AA2_$ptCc1687_TSTINFODAT  ; Testdata VP-M/CP                         ; $infoartTestdataDescription_en                 ; $infoartTestdataDescription_en
                 ; AA2_$ptCc1987_TRKUPG      ; ESI[tronic] 2.0 Truck Upgrade            ; $truckUpgradeDescription_en                    ; $truckUpgradeDescription_en
                 ; AA2_$ptCc1687_CORE_ESIPKG ; $coReName_en                             ; $coReDescription_en                            ; $coReDescription_en
                 ; AA2_$ptCc1987_KTS250SD    ; KTS 250 SD ECU Diagnosis                 ; $kts250SDDescription_en                        ; $kts250SDDescription_en
                 ; AA2_$ptCc1687_CRR950      ; CRR 950 Diesel Injectors Repair Software ; $crr950DieselInjectorsRepairSoftDescription_en ; $crr950DieselInjectorsRepairSoftDescription_en
                 ; AA2_$ptCc1987_TRKOHW3     ; Off Highway III (OHW III)                ; $ohw3Description_en                            ; $ohw3Description_en
                 ; AA2_$ptCc1987_ADAS_ONE    ; ADAS One Solution                        ; $adasDescription_en                            l $adasDescription_en

INSERT_UPDATE ProductContainer; code[unique = true]        ; app(code, $catalogVersion); company(uid)[unique = true][default = $companyId];
                              ; pcaa_$ptCc1987_ALLTRUCKS   ; AA2_$ptCc1987_ALLTRUCKS
                              ; pcaa_$ptCc1687_CSFSA500    ; AA2_$ptCc1687_CSFSA500
                              ; pcaa_$ptCc1687_CSFSA7XX    ; AA2_$ptCc1687_CSFSA7XX
                              ; pcaa_$ptCc1687_DCICRI      ; AA2_$ptCc1687_DCICRI
                              ; pcaa_$ptCc1687_DCICRIN     ; AA2_$ptCc1687_DCICRIN
                              ; pcaa_$ptCc1987_ESIADV      ; AA2_$ptCc1987_ESIADV
                              ; pcaa_$ptCc1987_ESIREPCAT   ; AA2_$ptCc1987_ESIREPCAT
                              ; pcaa_$ptCc1987_ESIREPD     ; AA2_$ptCc1987_ESIREPD
                              ; pcaa_$ptCc1987_ESIREPE     ; AA2_$ptCc1987_ESIREPE
                              ; pcaa_$ptCc1987_ESIDIAG     ; AA2_$ptCc1987_ESIDIAG
                              ; pcaa_$ptCc1987_ESIMASTER   ; AA2_$ptCc1987_ESIMASTER
                              ; pcaa_$ptCc1987_TRKOHW1     ; AA2_$ptCc1987_TRKOHW1
                              ; pcaa_$ptCc1987_TRKOHW2     ; AA2_$ptCc1987_TRKOHW2
                              ; pcaa_$ptCc1987_TRKTRUCK    ; AA2_$ptCc1987_TRKTRUCK
                              ; pcaa_$ptCc1987_TSTINFOAW   ; AA2_$ptCc1987_TSTINFOAW
                              ; pcaa_$ptCc1987_THLPKW      ; AA2_$ptCc1987_THLPKW
                              ; pcaa_$ptCc1987_THLTRK      ; AA2_$ptCc1987_THLTRK
                              ; pcaa_$ptCc1987_THLPKWTRK   ; AA2_$ptCc1987_THLPKWTRK
                              ; pcaa_$ptCc1687_TSTINFODAT  ; AA2_$ptCc1687_TSTINFODAT
                              ; pcaa_$ptCc1987_TRKUPG      ; AA2_$ptCc1987_TRKUPG
                              ; pcaa_$ptCc1687_CORE_ESIPKG ; AA2_$ptCc1687_CORE_ESIPKG
                              ; pcaa_$ptCc1987_KTS250SD    ; AA2_$ptCc1987_KTS250SD
                              ; pcaa_$ptCc1687_CRR950      ; AA2_$ptCc1687_CRR950
                              ; pcaa_$ptCc1987_TRKOHW3     ; AA2_$ptCc1987_TRKOHW3
                              ; pcaa_$ptCc1987_ADAS_ONE    ; AA2_$ptCc1987_ADAS_ONE

INSERT_UPDATE AppLicense; code[unique = true] ; $baseProduct              ; sellerProductId; brimName[lang = en]                            ; licenseType(code)[default = SUBSCRIPTION]; runtime(code)          ; bundleInfo(code); specifiedPrice; billingSystemStatus(code)[default = NEW]; unit(code)[default = pieces]; $catalogVersion[unique = true]; $approved; $taxGroup; enabledCountries(isocode)[default = 'PT']; availabilityStatus(code)[default = PUBLISHED]
                        ; AA2_$ptCc1987P12760 ; AA2_$ptCc1987_ALLTRUCKS   ; 1987P12760     ; $alltrucksSubsS1BrimName                       ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 1000000       ;
                        ; AA2_$ptCc1987P12949 ; AA2_$ptCc1987_ALLTRUCKS   ; 1987P12949     ; $alltrucksSubsM3BrimName                       ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 1000000       ;
                        ; AA2_$ptCc1687P15063 ; AA2_$ptCc1687_CSFSA500    ; 1687P15063     ; $compacFsa500Full3YBrimName                    ; "FULL"                                   ; runtime_full_3y        ; <ignore>        ; 210           ;
                        ; AA2_$ptCc1687P15060 ; AA2_$ptCc1687_CSFSA500    ; 1687P15060     ; $compacFsa500SubsBrimName                      ;                                          ; runtime_subs_unlimited ; <ignore>        ; 80            ;
                        ; AA2_$ptCc1687P15048 ; AA2_$ptCc1687_CSFSA7XX    ; 1687P15048     ; $compacFsa7xxFull3YBrimName                    ; "FULL"                                   ; runtime_full_3y        ; <ignore>        ; 755           ;
                        ; AA2_$ptCc1687P15045 ; AA2_$ptCc1687_CSFSA7XX    ; 1687P15045     ; $compacFsa7xxSubsBrimName                      ;                                          ; runtime_subs_unlimited ; <ignore>        ; 295           ;
                        ; AA2_$ptCc1687P15090 ; AA2_$ptCc1687_DCICRI      ; 1687P15090     ; $criSubsS1BrimName                             ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 440           ;
                        ; AA2_$ptCc1687P15102 ; AA2_$ptCc1687_DCICRI      ; 1687P15102     ; $criSubsM3BrimName                             ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 580           ;
                        ; AA2_$ptCc1687P15100 ; AA2_$ptCc1687_DCICRIN     ; 1687P15100     ; $crinSubsS1BrimName                            ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 440           ;
                        ; AA2_$ptCc1687P15107 ; AA2_$ptCc1687_DCICRIN     ; 1687P15107     ; $crinSubsM3BrimName                            ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 580           ;
                        ; AA2_$ptCc1987P12843 ; AA2_$ptCc1987_ESIADV      ; 1987P12843     ; $packAdvancedFull3YBrimName                    ; "FULL"                                   ; runtime_full_3y        ; <ignore>        ; 3540          ;
                        ; AA2_$ptCc1987P12840 ; AA2_$ptCc1987_ESIADV      ; 1987P12840     ; $packAdvancedSubsBrimName                      ;                                          ; runtime_subs_unlimited ; <ignore>        ; 1390          ;
                        ; AA2_$ptCc1987P12988 ; AA2_$ptCc1987_ESIREPCAT   ; 1987P12988     ; $packComponentCatFull3YS1BrimName              ; "FULL"                                   ; runtime_full_3y        ; BI_S_1          ; 945           ;
                        ; AA2_$ptCc1987P12783 ; AA2_$ptCc1987_ESIREPCAT   ; 1987P12783     ; $packComponentCatFull3YM3BrimName              ; "FULL"                                   ; runtime_full_3y        ; BI_M_3          ; 10000000      ;
                        ; AA2_$ptCc1987P12998 ; AA2_$ptCc1987_ESIREPCAT   ; 1987P12998     ; $packComponentCatSubsS1BrimName                ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 370           ;
                        ; AA2_$ptCc1987P12784 ; AA2_$ptCc1987_ESIREPCAT   ; 1987P12784     ; $packComponentCatSubsM3BrimName                ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 1000000       ;
                        ; AA2_$ptCc1987P12973 ; AA2_$ptCc1987_ESIREPD     ; 1987P12973     ; $packComponentRepairDieselFull3YS1BrimName     ; "FULL"                                   ; runtime_full_3y        ; BI_S_1          ; 1695          ;
                        ; AA2_$ptCc1987P12296 ; AA2_$ptCc1987_ESIREPD     ; 1987P12296     ; $packComponentRepairDieselFull3YM3BrimName     ; "FULL"                                   ; runtime_full_3y        ; BI_M_3          ; 1000000       ;
                        ; AA2_$ptCc1987P12970 ; AA2_$ptCc1987_ESIREPD     ; 1987P12970     ; $packComponentRepairDieselSubsS1BrimName       ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 665           ;
                        ; AA2_$ptCc1987P12297 ; AA2_$ptCc1987_ESIREPD     ; 1987P12297     ; $packComponentRepairDieselSubsM3BrimName       ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 1000000       ;
                        ; AA2_$ptCc1987P12993 ; AA2_$ptCc1987_ESIREPE     ; 1987P12993     ; $packComponentRepairElectricFull3YS1BrimName   ; "FULL"                                   ; runtime_full_3y        ; BI_S_1          ; 815           ;
                        ; AA2_$ptCc1987P12294 ; AA2_$ptCc1987_ESIREPE     ; 1987P12294     ; $packComponentRepairElectricFull3YM3BrimName   ; "FULL"                                   ; runtime_full_3y        ; BI_M_3          ; 1000000       ;
                        ; AA2_$ptCc1987P12990 ; AA2_$ptCc1987_ESIREPE     ; 1987P12990     ; $packComponentRepairElectricSubsS1BrimName     ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 320           ;
                        ; AA2_$ptCc1987P12295 ; AA2_$ptCc1987_ESIREPE     ; 1987P12295     ; $packComponentRepairElectricSubsM3BrimName     ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 1000000       ;
                        ; AA2_$ptCc1987P12823 ; AA2_$ptCc1987_ESIDIAG     ; 1987P12823     ; $packDiagnosticFull3YBrimName                  ; "FULL"                                   ; runtime_full_3y        ; <ignore>        ; 2050          ;
                        ; AA2_$ptCc1987P12820 ; AA2_$ptCc1987_ESIDIAG     ; 1987P12820     ; $packDiagnosticSubsBrimName                    ;                                          ; runtime_subs_unlimited ; <ignore>        ; 805           ;
                        ; AA2_$ptCc1987P12051 ; AA2_$ptCc1987_ESIDIAG     ; 1987P12051     ; $packDiagnosticFullOneTimeBrimName             ; "FULL"                                   ; runtime_full_unlimited ; <ignore>        ; 1000000       ;
                        ; AA2_$ptCc1987P12913 ; AA2_$ptCc1987_ESIMASTER   ; 1987P12913     ; $packMasterFull3YBrimName                      ; "FULL"                                   ; runtime_full_3y        ; <ignore>        ; 4905          ;
                        ; AA2_$ptCc1987P12910 ; AA2_$ptCc1987_ESIMASTER   ; 1987P12910     ; $packMasterSubsS1BrimName                      ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 1925          ;
                        ; AA2_$ptCc1987P12917 ; AA2_$ptCc1987_ESIMASTER   ; 1987P12917     ; $packMasterSubsM3BrimName                      ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 1000000       ;
                        ; AA2_$ptCc1987P12263 ; AA2_$ptCc1987_TRKOHW1     ; 1987P12263     ; $ohw1Full3YS1BrimName                          ; "FULL"                                   ; runtime_full_3y        ; BI_S_1          ; 1265          ;
                        ; AA2_$ptCc1987P12265 ; AA2_$ptCc1987_TRKOHW1     ; 1987P12265     ; $ohw1Full3YM3BrimName                          ; "FULL"                                   ; runtime_full_3y        ; BI_M_3          ; 1625          ;
                        ; AA2_$ptCc1987P12260 ; AA2_$ptCc1987_TRKOHW1     ; 1987P12260     ; $ohw1SubsS1BrimName                            ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 520           ;
                        ; AA2_$ptCc1987P12262 ; AA2_$ptCc1987_TRKOHW1     ; 1987P12262     ; $ohw1SubsM3BrimName                            ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 640           ;
                        ; AA2_$ptCc1987P12280 ; AA2_$ptCc1987_TRKOHW2     ; 1987P12280     ; $ohw2Full3YS1BrimName                          ; "FULL"                                   ; runtime_full_3y        ; BI_S_1          ; 2290          ;
                        ; AA2_$ptCc1987P12276 ; AA2_$ptCc1987_TRKOHW2     ; 1987P12276     ; $ohw2Full3YM3BrimName                          ; "FULL"                                   ; runtime_full_3y        ; BI_M_3          ; 2650          ;
                        ; AA2_$ptCc1987P12278 ; AA2_$ptCc1987_TRKOHW2     ; 1987P12278     ; $ohw2SubsS1BrimName                            ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 930           ;
                        ; AA2_$ptCc1987P12275 ; AA2_$ptCc1987_TRKOHW2     ; 1987P12275     ; $ohw2SubsM3BrimName                            ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 1050          ;
                        ; AA2_$ptCc1987P12402 ; AA2_$ptCc1987_TRKTRUCK    ; 1987P12402     ; $packTruckFull3YS1BrimName                     ; "FULL"                                   ; runtime_full_3y        ; BI_S_1          ; 3010          ;
                        ; AA2_$ptCc1987P12937 ; AA2_$ptCc1987_TRKTRUCK    ; 1987P12937     ; $packTruckFull3YM3BrimName                     ; "FULL"                                   ; runtime_full_3y        ; BI_M_3          ; 3370          ;
                        ; AA2_$ptCc1987P12412 ; AA2_$ptCc1987_TRKTRUCK    ; 1987P12412     ; $packTruckFullBrimName                         ; "FULL"                                   ; runtime_full_unlimited ; <ignore>        ; 1785          ;
                        ; AA2_$ptCc1987P12400 ; AA2_$ptCc1987_TRKTRUCK    ; 1987P12400     ; $packTruckSubsS1BrimName                       ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 1230          ;
                        ; AA2_$ptCc1987P12936 ; AA2_$ptCc1987_TRKTRUCK    ; 1987P12936     ; $packTruckSubsM3BrimName                       ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 1350          ;
                        ; AA2_$ptCc1987P12503 ; AA2_$ptCc1987_TSTINFOAW   ; 1987P12503     ; $infoartWFull3YBrimName                        ; "FULL"                                   ; runtime_full_3y        ; <ignore>        ; 1610          ;
                        ; AA2_$ptCc1987P12500 ; AA2_$ptCc1987_TSTINFOAW   ; 1987P12500     ; $infoartWSubsBrimName                          ;                                          ; runtime_subs_unlimited ; <ignore>        ; 630           ;
                        ; AA2_$ptCc1987P13523 ; AA2_$ptCc1987_THLPKW      ; 1987P13523     ; $thlPkwFull3YBrimName                          ; "FULL"                                   ; runtime_full_3y        ; <ignore>        ; 875           ;
                        ; AA2_$ptCc1987729274 ; AA2_$ptCc1987_THLPKW      ; 1987729274     ; $thlPkwSubsBrimName                            ;                                          ; runtime_subs_unlimited ; <ignore>        ; 330           ;
                        ; AA2_$ptCc1987P13515 ; AA2_$ptCc1987_THLTRK      ; 1987P13515     ; $thlTrkSubsBrimName                            ;                                          ; runtime_subs_unlimited ; <ignore>        ; 1000000       ;
                        ; AA2_$ptCc1987P13516 ; AA2_$ptCc1987_THLPKWTRK   ; 1987P13516     ; $thlPkwTrkSubsBrimName                         ;                                          ; runtime_subs_unlimited ; <ignore>        ; 1000000       ;
                        ; AA2_$ptCc1687P15015 ; AA2_$ptCc1687_TSTINFODAT  ; 1687P15015     ; $infoartTestdataSubsBrimName                   ;                                          ; runtime_subs_unlimited ; <ignore>        ; 315           ;
                        ; AA2_$ptCc1987P12140 ; AA2_$ptCc1987_TRKUPG      ; 1987P12140     ; $truckUpgradeFull3YS1BrimName                  ; "FULL"                                   ; runtime_full_3y        ; BI_S_1          ; 2550          ;
                        ; AA2_$ptCc1987P12364 ; AA2_$ptCc1987_TRKUPG      ; 1987P12364     ; $truckUpgradeFull3YM3BrimName                  ; "FULL"                                   ; runtime_full_3y        ; BI_M_3          ; 2910          ;
                        ; AA2_$ptCc1987P12404 ; AA2_$ptCc1987_TRKUPG      ; 1987P12404     ; $truckUpgradeSubsS1BrimName                    ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 1045          ;
                        ; AA2_$ptCc1987P12359 ; AA2_$ptCc1987_TRKUPG      ; 1987P12359     ; $truckUpgradeSubsM3BrimName                    ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 1165          ;
                        ; AA2_$ptCc1687P15130 ; AA2_$ptCc1687_CORE_ESIPKG ; 1687P15130     ; $coreS1BrimName                                ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 1000000       ; NOT_READY_FOR_EXPORT
                        ; AA2_$ptCc1987P12389 ; AA2_$ptCc1987_KTS250SD    ; 1987P12389     ; $kts250SDFullBrimName                          ; "FULL"                                   ; runtime_full_unlimited ; <ignore>        ; 1000000       ;
                        ; AA2_$ptCc1987P12385 ; AA2_$ptCc1987_KTS250SD    ; 1987P12385     ; $kts250SDSubsBrimName                          ;                                          ; runtime_subs_unlimited ; <ignore>        ; 710           ;
                        ; AA2_$ptCc1687P15137 ; AA2_$ptCc1687_CRR950      ; 1687P15137     ; $crr950DieselInjectorsRepairSoftwareBrimName1L ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 100           ;
                        ; AA2_$ptCc1687P15139 ; AA2_$ptCc1687_CRR950      ; 1687P15139     ; $crr950DieselInjectorsRepairSoftwareBrimName3L ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 200           ;
                        ; AA2_$ptCc1987P12254 ; AA2_$ptCc1987_TRKOHW3     ; 1987P12254     ; $ohw3S1BrimName                                ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 337.50        ;
                        ; AA2_$ptCc1987P12257 ; AA2_$ptCc1987_TRKOHW3     ; 1987P12257     ; $ohw3M3BrimName                                ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 427.50        ;
                        ; AA2_$ptCc1987P12255 ; AA2_$ptCc1987_TRKOHW3     ; 1987P12255     ; $ohw3Single3YearBrimName                       ; "FULL"                                   ; runtime_full_3y        ; BI_S_1          ; 832.50        ;
                        ; AA2_$ptCc1987P12256 ; AA2_$ptCc1987_TRKOHW3     ; 1987P12256     ; $ohw3Multi3YearBrimName                        ; "FULL"                                   ; runtime_full_3y        ; BI_M_3          ; 1102.50       ;
                        ; AA2_$ptCc1987P12515 ; AA2_$ptCc1987_ADAS_ONE    ; 1987P12515     ; $adasSubsS1BrimName                            ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 0.01
                        ; AA2_$ptCc1987P12517 ; AA2_$ptCc1987_ADAS_ONE    ; 1987P12517     ; $adasFull3YS1BrimName                          ; "FULL"                                   ; runtime_full_3y        ; BI_M_3          ; 0.01
                        ; AA2_$ptCc1687P15170 ; AA2_$ptCc1687_CSFSA500    ; 1687P15170     ; $fsa5xxMulti                                   ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 0.01          ;
                        ; AA2_$ptCc1687P15173 ; AA2_$ptCc1687_CSFSA500    ; 1687P15173     ; $fsa5xx3YearsMulti                             ; "FULL"                                   ; runtime_full_3y        ; <ignore>        ; 0.01          ;
                        ; AA2_$ptCc1687P15160 ; AA2_$ptCc1687_CSFSA7XX    ; 1687P15160     ; $fsa7xxMulti                                   ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 0.01          ;
                        ; AA2_$ptCc1687P15163 ; AA2_$ptCc1687_CSFSA7XX    ; 1687P15163     ; $fsa7xx3YearsMulti                             ; "FULL"                                   ; runtime_full_3y        ; <ignore>        ; 0.01          ;


UPDATE AppLicense; code[unique = true] ; userGroups(uid)                        ; $catalogVersion[unique = true];
                 ; AA2_$ptCc1987P12760 ;                                        ;
                 ; AA2_$ptCc1987P12949 ;                                        ;
                 ; AA2_$ptCc1687P15063 ; IDW000,WD0001,WD0002                   ;
                 ; AA2_$ptCc1687P15060 ; IDW000,WD0001,WD0002                   ;
                 ; AA2_$ptCc1687P15048 ; IDW000,WD0001,WD0002                   ;
                 ; AA2_$ptCc1687P15045 ; IDW000,WD0001,WD0002                   ;
                 ; AA2_$ptCc1687P15090 ; IDW000,WD0001,WD0002                   ;
                 ; AA2_$ptCc1687P15102 ; IDW000,WD0001,WD0002                   ;
                 ; AA2_$ptCc1687P15100 ; IDW000,WD0001,WD0002                   ;
                 ; AA2_$ptCc1687P15107 ; IDW000,WD0001,WD0002                   ;
                 ; AA2_$ptCc1987P12843 ; IDW000,WD0001,WD0002,KA0003            ;
                 ; AA2_$ptCc1987P12840 ; IDW000,WD0001,WD0002                   ;
                 ; AA2_$ptCc1987P12988 ; IDW000,WD0001,WD0002                   ;
                 ; AA2_$ptCc1987P12783 ;                                        ;
                 ; AA2_$ptCc1987P12998 ; IDW000,WD0001,WD0002                   ;
                 ; AA2_$ptCc1987P12784 ;                                        ;
                 ; AA2_$ptCc1987P12973 ; IDW000,WD0001,WD0002                   ;
                 ; AA2_$ptCc1987P12296 ;                                        ;
                 ; AA2_$ptCc1987P12970 ; IDW000,WD0001,WD0002                   ;
                 ; AA2_$ptCc1987P12297 ;                                        ;
                 ; AA2_$ptCc1987P12993 ; IDW000,WD0001,WD0002                   ;
                 ; AA2_$ptCc1987P12294 ;                                        ;
                 ; AA2_$ptCc1987P12990 ; IDW000,WD0001,WD0002                   ;
                 ; AA2_$ptCc1987P12295 ;                                        ;
                 ; AA2_$ptCc1987P12823 ; IDW000,WD0001,WD0002,KA0003            ;
                 ; AA2_$ptCc1987P12820 ; IDW000,WD0001,WD0002,KA0002            ;
                 ; AA2_$ptCc1987P12051 ; IDW000,WD0001,WD0002                   ;
                 ; AA2_$ptCc1987P12913 ; IDW000,WD0001,WD0002,KA0003            ;
                 ; AA2_$ptCc1987P12910 ; IDW000,WD0001,WD0002,KA0002            ;
                 ; AA2_$ptCc1987P12917 ;                                        ;
                 ; AA2_$ptCc1987P12263 ; IDW000,WD0001,WD0002                   ;
                 ; AA2_$ptCc1987P12265 ; IDW000,WD0001,WD0002                   ;
                 ; AA2_$ptCc1987P12260 ; IDW000,WD0001,WD0002                   ;
                 ; AA2_$ptCc1987P12262 ; IDW000,WD0001,WD0002                   ;
                 ; AA2_$ptCc1987P12280 ; IDW000,WD0001,WD0002                   ;
                 ; AA2_$ptCc1987P12276 ; IDW000,WD0001,WD0002                   ;
                 ; AA2_$ptCc1987P12278 ; IDW000,WD0001,WD0002                   ;
                 ; AA2_$ptCc1987P12275 ; IDW000,WD0001,WD0002                   ;
                 ; AA2_$ptCc1987P12402 ; IDW000,WD0001,WD0002                   ;
                 ; AA2_$ptCc1987P12937 ; IDW000,WD0001,WD0002                   ;
                 ; AA2_$ptCc1987P12412 ; IDW000,WD0001,WD0002                   ;
                 ; AA2_$ptCc1987P12400 ; IDW000,WD0001,WD0002                   ;
                 ; AA2_$ptCc1987P12936 ; IDW000,WD0001,WD0002                   ;
                 ; AA2_$ptCc1987P12503 ; IDW000,WD0001,WD0002                   ;
                 ; AA2_$ptCc1987P12500 ; IDW000,WD0001,WD0002                   ;
                 ; AA2_$ptCc1987P13523 ; IDW000,WD0001,WD0002                   ;
                 ; AA2_$ptCc1987729274 ; IDW000,WD0001,WD0002,KA0003            ;
                 ; AA2_$ptCc1987P13515 ;                                        ;
                 ; AA2_$ptCc1987P13516 ;                                        ;
                 ; AA2_$ptCc1687P15015 ; IDW000,WD0001,WD0002                   ;
                 ; AA2_$ptCc1987P12140 ; IDW000,WD0001,WD0002                   ;
                 ; AA2_$ptCc1987P12364 ; IDW000,WD0001,WD0002                   ;
                 ; AA2_$ptCc1987P12404 ; IDW000,WD0001,WD0002                   ;
                 ; AA2_$ptCc1987P12359 ; IDW000,WD0001,WD0002                   ;
                 ; AA2_$ptCc1687P15130 ;                                        ;
                 ; AA2_$ptCc1987P12389 ; IDW000,WD0001,WD0002                   ;
                 ; AA2_$ptCc1987P12385 ; IDW000,WD0001,WD0002                   ;
                 ; AA2_$ptCc1687P15137 ; IDW000,WD0001,WD0002                   ;
                 ; AA2_$ptCc1687P15139 ; IDW000,WD0001,WD0002                   ;
                 ; AA2_$ptCc1987P12254 ; IDW000, WD0001, WD0002, KA0002, KA0003 ;
                 ; AA2_$ptCc1987P12257 ; IDW000, WD0001, WD0002, KA0002, KA0003 ;
                 ; AA2_$ptCc1987P12255 ; IDW000, WD0001, WD0002, KA0002, KA0003 ;
                 ; AA2_$ptCc1987P12256 ; IDW000, WD0001, WD0002, KA0002, KA0003 ;
# The group assignment for the ADAS ONE is most probably not final. we're missing the prices for now
                 ; AA2_$ptCc1987P12515 ; IDW000                                 ;
                 ; AA2_$ptCc1987P12517 ; IDW000                                 ;
                 ; AA2_$ptCc1687P15163 ; IDW000                                 ;
                 ; AA2_$ptCc1687P15170 ; IDW000                                 ;
                 ; AA2_$ptCc1687P15160 ; IDW000                                 ;
                 ; AA2_$ptCc1687P15173 ; IDW000                                 ;


INSERT_UPDATE PriceRow; product(code, $catalogVersion)[unique = true]; price; currency(isocode)[unique = true, default = EUR]; unit(code[unique = true, default = pieces]); minqtd[default = 1]; unitFactor[default = 1]; net[default = true]; ug[unique = true, default = '']
                      ; AA2_$ptCc1687P15063                          ; 210  ;
                      ; AA2_$ptCc1687P15060                          ; 80   ;
                      ; AA2_$ptCc1687P15048                          ; 755  ;
                      ; AA2_$ptCc1687P15045                          ; 295  ;
                      ; AA2_$ptCc1687P15090                          ; 440  ;
                      ; AA2_$ptCc1687P15102                          ; 580  ;
                      ; AA2_$ptCc1687P15100                          ; 440  ;
                      ; AA2_$ptCc1687P15107                          ; 580  ;
                      ; AA2_$ptCc1987P12843                          ; 3540 ;
                      ; AA2_$ptCc1987P12840                          ; 1390 ;
                      ; AA2_$ptCc1987P12988                          ; 945  ;
                      ; AA2_$ptCc1987P12998                          ; 370  ;
                      ; AA2_$ptCc1987P12973                          ; 1695 ;
                      ; AA2_$ptCc1987P12970                          ; 665  ;
                      ; AA2_$ptCc1987P12993                          ; 815  ;
                      ; AA2_$ptCc1987P12990                          ; 320  ;
                      ; AA2_$ptCc1987P12823                          ; 2050 ;
                      ; AA2_$ptCc1987P12820                          ; 805  ;
                      ; AA2_$ptCc1987P12913                          ; 4905 ;
                      ; AA2_$ptCc1987P12910                          ; 1925 ;
                      ; AA2_$ptCc1987P12263                          ; 1265 ;
                      ; AA2_$ptCc1987P12265                          ; 1625 ;
                      ; AA2_$ptCc1987P12260                          ; 520  ;
                      ; AA2_$ptCc1987P12262                          ; 640  ;
                      ; AA2_$ptCc1987P12280                          ; 2290 ;
                      ; AA2_$ptCc1987P12276                          ; 2650 ;
                      ; AA2_$ptCc1987P12278                          ; 930  ;
                      ; AA2_$ptCc1987P12275                          ; 1050 ;
                      ; AA2_$ptCc1987P12402                          ; 3010 ;
                      ; AA2_$ptCc1987P12937                          ; 3370 ;
                      ; AA2_$ptCc1987P12412                          ; 1785 ;
                      ; AA2_$ptCc1987P12400                          ; 1230 ;
                      ; AA2_$ptCc1987P12936                          ; 1350 ;
                      ; AA2_$ptCc1987P12503                          ; 1610 ;
                      ; AA2_$ptCc1987P12500                          ; 630  ;
                      ; AA2_$ptCc1987P13523                          ; 875  ;
                      ; AA2_$ptCc1987729274                          ; 330  ;
                      ; AA2_$ptCc1687P15015                          ; 315  ;
                      ; AA2_$ptCc1987P12140                          ; 2550 ;
                      ; AA2_$ptCc1987P12364                          ; 2910 ;
                      ; AA2_$ptCc1987P12404                          ; 1045 ;
                      ; AA2_$ptCc1987P12359                          ; 1165 ;
                      ; AA2_$ptCc1987P12385                          ; 710  ;
                      ; AA2_$ptCc1687P15137                          ; 100  ;
                      ; AA2_$ptCc1687P15139                          ; 200  ;
                      ; AA2_$ptCc1987P12254                          ; 450
                      ; AA2_$ptCc1987P12257                          ; 570
                      ; AA2_$ptCc1987P12255                          ; 1100
                      ; AA2_$ptCc1987P12256                          ; 1470
                      ; AA2_$ptCc1987P12515                          ; 0.01 ;
                      ; AA2_$ptCc1987P12517                          ; 0.01 ;
                      ; AA2_$ptCc1687P15163                          ; 0.01 ;
                      ; AA2_$ptCc1687P15170                          ; 0.01 ;
                      ; AA2_$ptCc1687P15160                          ; 0.01 ;
                      ; AA2_$ptCc1687P15173                          ; 0.01 ;

INSERT_UPDATE App; code[unique = true]       ; contentModules(code); $catalogVersion[unique = true];
                 ; AA2_$ptCc1987_ALLTRUCKS   ; CM_$aaCcNEOOrangeATruck, CM_$aaCcETruck
                 ; AA2_$ptCc1687_CSFSA500    ; CM_$aaCcCSFSA5
                 ; AA2_$ptCc1687_CSFSA7XX    ; CM_$aaCcCSS,CM_$aaCcCSK
                 ; AA2_$ptCc1687_DCICRI      ; CM_$aaCcDCICRI
                 ; AA2_$ptCc1687_DCICRIN     ; CM_$aaCcDCICRIN
                 ; AA2_$ptCc1987_ESIADV      ; CM_$aaCcA,CM_$aaCcSDSDA,CM_$aaCcEBR,CM_$aaCcTSB,CM_$aaCcSIS,CM_$aaCcCoRe
                 ; AA2_$ptCc1987_ESIREPCAT   ; CM_$aaCcA,CM_$aaCcD,CM_$aaCcE
                 ; AA2_$ptCc1987_ESIREPD     ; CM_$aaCcA,CM_$aaCcD,CM_$aaCcE,CM_$aaCcK3
                 ; AA2_$ptCc1987_ESIREPE     ; CM_$aaCcA,CM_$aaCcE,CM_$aaCcK2
                 ; AA2_$ptCc1987_ESIDIAG     ; CM_$aaCcA,CM_$aaCcSDSDA,CM_$aaCcEBR,CM_$aaCcTSB
                 ; AA2_$ptCc1987_ESIMASTER   ; CM_$aaCcA,CM_$aaCcSDSDA,CM_$aaCcEBR,CM_$aaCcTSB,CM_$aaCcSIS,CM_$aaCcCoRe,CM_$aaCcM,CM_$aaCcP
                 ; AA2_$ptCc1987_TRKOHW1     ; CM_$aaCcETOHW1
                 ; AA2_$ptCc1987_TRKOHW2     ; CM_$aaCcETOHW2
                 ; AA2_$ptCc1987_TRKTRUCK    ; CM_$aaCcETruck
                 ; AA2_$ptCc1987_TSTINFOAW   ; CM_$aaCcEW
                 ; AA2_$ptCc1987_THLPKW      ; CM_$aaCcPKWTHL
                 ; AA2_$ptCc1987_THLTRK      ; CM_$aaCcPKWTHL
                 ; AA2_$ptCc1987_THLPKWTRK   ; CM_$aaCcTHLCarTruck
                 ; AA2_$ptCc1687_TSTINFODAT  ; CM_$aaCcTVPMCP
                 ; AA2_$ptCc1987_TRKUPG      ; CM_$aaCcTRKUPG
                 ; AA2_$ptCc1687_CORE_ESIPKG ; CM_$aaCcCoReESIPKG
                 ; AA2_$ptCc1987_KTS250SD    ; CM_$aaCcKTS250ECUD


INSERT_UPDATE App; code[unique = true]   ; contentModules(code); $catalogVersion[unique = true];
                 ; AA2_$ptCc1987_TRKOHW3 ; CM_$aaCcETOHW3
                 ; AA2_$ptCc1687_CRR950  ; CM_$aaCcCRR950


UPDATE AppLicense; code[unique = true] ; $catalogVersion[unique = true]; contentModules(code)[default = CM_$aaCcSDSDA];
                 ; AA2_$ptCc1987P12051 ;

INSERT_UPDATE App; code[unique = true]   ; boms(code); $catalogVersion[unique = true]
                 ; AA2_$ptCc1987_TRKOHW3 ; MAT_$aaCcTrFZ,MAT_$aaCcTrD,MAT_$aaCcTrSp,MAT_$aaCcTrWt,MAT_$aaCcTrTD,MAT_$aaCcTrETK

INSERT_UPDATE App; code[unique = true]       ; $supercategories; $catalogVersion[unique = true];
                 ; AA2_$ptCc1987_ALLTRUCKS   ; cat_1010201
                 ; AA2_$ptCc1687_CSFSA500    ; cat_201
                 ; AA2_$ptCc1687_CSFSA7XX    ; cat_201
                 ; AA2_$ptCc1687_DCICRI      ; cat_401
                 ; AA2_$ptCc1687_DCICRIN     ; cat_401
                 ; AA2_$ptCc1987_ESIADV      ; cat_10101
                 ; AA2_$ptCc1987_ESIREPCAT   ; cat_1010101
                 ; AA2_$ptCc1987_ESIREPD     ; cat_1010101
                 ; AA2_$ptCc1987_ESIREPE     ; cat_1010101
                 ; AA2_$ptCc1987_ESIDIAG     ; cat_10101
                 ; AA2_$ptCc1987_ESIMASTER   ; cat_10101
                 ; AA2_$ptCc1987_TRKOHW1     ; cat_10102
                 ; AA2_$ptCc1987_TRKOHW2     ; cat_10102
                 ; AA2_$ptCc1987_TRKTRUCK    ; cat_10102
                 ; AA2_$ptCc1987_TSTINFOAW   ; cat_40101
                 ; AA2_$ptCc1987_THLPKW      ; cat_1010102
                 ; AA2_$ptCc1987_THLTRK      ; cat_1010202
                 ; AA2_$ptCc1987_THLPKWTRK   ; cat_1010102,cat_1010202
                 ; AA2_$ptCc1687_TSTINFODAT  ; cat_40101
                 ; AA2_$ptCc1987_TRKUPG      ; cat_10102
                 ; AA2_$ptCc1687_CORE_ESIPKG ; cat_1010103
                 ; AA2_$ptCc1987_KTS250SD    ; cat_1010104
                 ; AA2_$ptCc1687_CRR950      ; cat_401
                 ; AA2_$ptCc1987_TRKOHW3     ; cat_10102
                 ; AA2_$ptCc1987_ADAS_ONE    ; cat_501


INSERT_UPDATE App; code[unique = true]       ; eulaContainers(code); $catalogVersion[unique = true]
                 ; AA2_$ptCc1987_TSTINFOAW   ; AA2_ESItronic
                 ; AA2_$ptCc1687_DCICRI      ; AA2_DCICRI
                 ; AA2_$ptCc1687_DCICRIN     ; AA2_DCICRIN
                 ; AA2_$ptCc1687_TSTINFODAT  ; AA2_ESItronic
                 ; AA2_$ptCc1687_CSFSA500    ; AA2_FSA
                 ; AA2_$ptCc1687_CSFSA7XX    ; AA2_FSA
                 ; AA2_$ptCc1687_CORE_ESIPKG ; AA2_CoRe
                 ; AA2_$ptCc1987_KTS250SD    ; AA2_ESItronic
                 ; AA2_$ptCc1687_CRR950      ; AA2_ESItronic
                 ; AA2_$ptCc1987_ADAS_ONE    ; AA2_ADAS
