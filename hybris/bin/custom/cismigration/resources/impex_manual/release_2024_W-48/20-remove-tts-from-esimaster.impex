#% impex.setLocale( Locale.GERMAN );
#% impex.enableCodeExecution(true);

$atCc = 040
$deCc = 049

$productCatalog = aav2ProductCatalog
$catalogVersion = catalogversion(catalog(id[default = $productCatalog]), version[default = 'Staged'])

# add content modules to the THL apps AT
INSERT_UPDATE App; code[unique = true]     ; contentModules(code); $catalogVersion[unique = true];
                 ; AA2_$atCc1987_THLPKW    ; CM_$atCcTTS
                 ; AA2_$atCc1987_THLPKWTRK ; CM_$atCcTTS
                 ; AA2_$atCc1987_THLTRK    ; CM_$atCcTTS

# add content modules to the THL apps DE
INSERT_UPDATE App; code[unique = true]     ; contentModules(code); $catalogVersion[unique = true];
                 ; AA2_$deCc1987_THLPKW    ; CM_$atCcTTS
                 ; AA2_$deCc1987_THLPKWTRK ; CM_$atCcTTS
                 ; AA2_$deCc1987_THLTRK    ; CM_$atCcTTS

# required for code execution
UPDATE GenericItem[processor = de.hybris.platform.commerceservices.impex.impl.ConfigPropertyImportProcessor]; pk[unique = true]

# remove tts from esimaster
REMOVE Product2ContentModule; source[unique = true]; target[unique = true];
#% impex.initDatabase( "$config-db.url", "$config-db.username","$config-db.password","$config-db.driver");
"#% impex.includeSQLData(""SELECT app.pk AS source, cm.pk AS target FROM products AS app JOIN product2contentmodule AS pcm ON app.pk = pcm.sourcePK JOIN contentmodule AS cm ON pcm.targetPK = cm.pk WHERE app.p_code = 'AA2_0401987_ESIMASTER' AND cm.p_code = 'CM_040TTS'"");";
"#% impex.includeSQLData(""SELECT app.pk AS source, cm.pk AS target FROM products AS app JOIN product2contentmodule AS pcm ON app.pk = pcm.sourcePK JOIN contentmodule AS cm ON pcm.targetPK = cm.pk WHERE app.p_code = 'AA2_0491987_ESIMASTER' AND cm.p_code = 'CM_040TTS'"");"
