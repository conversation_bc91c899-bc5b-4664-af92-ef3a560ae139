#% impex.enableCodeExecution(true);
UPDATE GenericItem[processor=de.hybris.platform.commerceservices.impex.impl.ConfigPropertyImportProcessor];pk[unique=true]
# 1) Clears any distributor assignment on all existing carts
UPDATE Cart; pk[unique=true]; aadistributorcompany(pk)
#% impex.initDatabase( "$config-db.url", "$config-db.username","$config-db.password","$config-db.driver");
"#% impex.includeSQLData(""SELECT pk FROM carts "");"

# 2) Removes the defaultDistributor on every IoTCompany of aastore
UPDATE IoTCompany; pk[unique=true]; defaultaaDistributor(pk)
#% impex.initDatabase( "$config-db.url", "$config-db.username","$config-db.password","$config-db.driver");
"#% impex.includeSQLData(""SELECT pk FROM usergroups WHERE typepkstring = (SELECT pk FROM composedtypes where internalcode = 'IoTCompany') AND p_store = (SELECT pk FROM basestore item_t0 WHERE ( item_t0.p_uid  = 'aastore'))"");"
