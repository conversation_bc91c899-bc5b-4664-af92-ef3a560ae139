#% impex.setLocale( Locale.GERMAN );
#% setAccessibility(true)
#% impex.definitions.put("$currentDateTime", new java.text.SimpleDateFormat("dd.MM.yyyy HH:mm:ss").format(new Date()))

# DK
$cc = 045
$atCc = 040
$aaPackageName = com.sast.aa.dk.

$productCatalog = aav2ProductCatalog
$catalogVersion = catalogversion(catalog(id[default = $productCatalog]), version[default = 'Staged'])
$supercategories = supercategories(code, $catalogVersion)
$baseProduct = baseProduct(code, $catalogVersion)
$approved = approvalstatus(code)[default = 'approved']
$taxGroup = Europe1PriceFactory_PTG(code)[default = eu-vat-full]
$emailAddress = <EMAIL>



UPDATE GenericItem[processor = de.hybris.platform.commerceservices.impex.impl.ConfigPropertyImportProcessor]; pk[unique = true]
$staticLegalResourcesUrl = $config-aa.corporate.home

$privacyPolicyUrl = $staticLegalResourcesUrl/Datenschutzhinweise_ESItronic_AT_2021-01.pdf

# please make sure, to use right DELEVELOPER COMPANY for Denmark in the right environment.
# live
$companyId = 37fc3456-94b3-4d3d-a83a-52d2413b4116
# demo
# $companyId = dee8bdda-3321-4853-bc09-32f85de66c2f
# dev
# $companyId = b590385e-9141-4546-ae0f-5b2bfd8858a8
# local
# $companyId = 8b871953-7b66-476d-9a42-4681a99bda2e

$countryRestricted = true

$crr950DieselInjectorsRepairSoftDescription = CRR 950 Diesel-Injektor-Reparatur-Software
$crr950DieselInjectorsRepairSoftDescription_en = The CRR 950 guides and support the professional repairs of magnetic solenoid common-rail injectors from Bosch
$crr950DieselInjectorsRepairSoftwareBrimName1L = CRR 950 Diesel Injectors (1 License)
$crr950DieselInjectorsRepairSoftwareBrimName3L = CRR 950 Diesel Injectors (3 Licenses)


INSERT_UPDATE App; code[unique = true]; packageName               ; name[lang = da]                            ; name[lang = en]                          ; summary[lang = da]                          ; summary[lang = en]                             ; description[lang = da]                      ; description[lang = en]; emailAddress[default = $emailAddress]; submittedBy(uid); storeAvailabilityMode(code)[default = RESTRICTED_BUYER_GROUP]; unit(code)[default = pieces]; variantType(code)[default = AppLicense]; $catalogVersion[unique = true]; $approved; $taxGroup; privacyPolicyUrl[default = $privacyPolicyUrl]; termsOfUseUrl; supportPageUrl; productWebsiteUrl; company(uid)[default = $companyId]; countryRestricted[default = $countryRestricted]
                 ; AA2_$cc1687_CRR950 ; $aaPackageName1687_CRR950 ; CRR 950 Diesel-Injektor-Reparatur-Software ; CRR 950 Diesel Injectors Repair Software ; $crr950DieselInjectorsRepairSoftDescription ; $crr950DieselInjectorsRepairSoftDescription_en ; $crr950DieselInjectorsRepairSoftDescription ; $crr950DieselInjectorsRepairSoftDescription_en

INSERT_UPDATE ProductContainer; code[unique = true] ; app(code, $catalogVersion); company(uid)[unique = true][default = $companyId];
                              ; pcaa_$cc1687_CRR950 ; AA2_$cc1687_CRR950


INSERT_UPDATE AppLicense; code[unique = true]; $baseProduct       ; sellerProductId; brimName[lang = en]                            ; brimName[lang = de]                            ; licenseType(code)[default = SUBSCRIPTION]; runtime(code)          ; bundleInfo(code); specifiedPrice; billingSystemStatus(code)[default = NEW]; unit(code)[default = pieces]; $catalogVersion[unique = true]; $approved; $taxGroup; enabledCountries(isocode)[default = 'DK']; availabilityStatus(code)[default = PUBLISHED]
                        ; AA2_$cc1687P15137  ; AA2_$cc1687_CRR950 ; 1687P15137     ; $crr950DieselInjectorsRepairSoftwareBrimName1L ; $crr950DieselInjectorsRepairSoftwareBrimName1L ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 140           ;
                        ; AA2_$cc1687P15139  ; AA2_$cc1687_CRR950 ; 1687P15139     ; $crr950DieselInjectorsRepairSoftwareBrimName3L ; $crr950DieselInjectorsRepairSoftwareBrimName3L ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 210           ;

UPDATE AppLicense; code[unique = true]; userGroups(uid); $catalogVersion[unique = true];
                 ; AA2_$cc1687P15137  ; IDW000
                 ; AA2_$cc1687P15139  ; IDW000

INSERT_UPDATE PriceRow; product(code, $catalogVersion)[unique = true]; price; currency(isocode)[unique = true, default = EUR]; unit(code[unique = true, default = pieces]); minqtd[default = 1]; unitFactor[default = 1]; net[default = true]; ug[unique = true, default = '']
                      ; AA2_$cc1687P15137                          ; 140  ;
                      ; AA2_$cc1687P15139                          ; 210  ;

INSERT_UPDATE App; code[unique = true]; $supercategories; $catalogVersion[unique = true];
                 ; AA2_$cc1687_CRR950 ; cat_401

INSERT_UPDATE App; code[unique = true]; contentModules(code); $catalogVersion[unique = true];
                 ; AA2_$cc1687_CRR950 ; CM_$atCcCRR950

INSERT_UPDATE App; code[unique = true]; eulaContainers(code); $catalogVersion[unique = true]
                 ; AA2_$cc1687_CRR950 ; AA2_CRR

INSERT_UPDATE App; code[unique = true]; icon(qualifier, $catalogVersion[unique = true]); $catalogVersion[unique = true]
                 ; AA2_$cc1687_CRR950 ; pcaa2_$atCc1687_CRR950_iconContainer_01