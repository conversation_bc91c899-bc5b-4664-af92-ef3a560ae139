#% impex.setLocale( Locale.GERMAN );
# numerical isocode for AT, used as prefix for product code
# IS
$countryCc = 354
$atCc = 040
$aaPackageName = com.sast.aa.is.

$productCatalog = aav2ProductCatalog
$catalogVersion = catalogversion(catalog(id[default = $productCatalog]), version[default = 'Staged'])
$supercategories = supercategories(code, $catalogVersion)
$baseProduct = baseProduct(code, $catalogVersion)
$approved = approvalstatus(code)[default = 'approved']
$taxGroup = Europe1PriceFactory_PTG(code)[default = eu-vat-full]
$emailAddress = <EMAIL>

UPDATE GenericItem[processor = de.hybris.platform.commerceservices.impex.impl.ConfigPropertyImportProcessor]; pk[unique = true]
$staticLegalResourcesUrl = $config-aa.corporate.home

$privacyPolicyUrl = $staticLegalResourcesUrl/Datenschutzhinweise_ESItronic_AT_2021-01.pdf

# please make sure, to use right DELEVELOPER COMPANY for Denmark in the right environment.
# live
$companyId = 37fc3456-94b3-4d3d-a83a-52d2413b4116
# demo
# $companyId = dee8bdda-3321-4853-bc09-32f85de66c2f
# dev
# $companyId = b590385e-9141-4546-ae0f-5b2bfd8858a8
# local
# $companyId = 8b871953-7b66-476d-9a42-4681a99bda2e

$countryRestricted = true

$ohw3Description = In dem Diagnose Paket Baumaschinen und Motoren stehen Informationen zur Diagnose, Wartung und Reparatur von landwirtschaftlich genutzten Fahrzeugen zur Verfügung. Enthalten sind unter anderem Einstell- und Parametrierfunktionen an Hydrauliksystemen.
$ohw3Description_en = The diagnosis package for construction machinery and engines provides information on the diagnosis, maintenance and repair of agricultural vehicles. Included are, among other things, adjustment and parameterisation functions for hydraulic systems.

$ohw3S1BrimName = ESI[tronic] Truck OHW III Unlimited
$ohw3M3BrimName = ESI[tronic] Truck OHW III Unlimited Multi
$ohw3Full3YM3BrimName = ESI[tronic] Truck OHW III (3y) Multi


# Note: BEA PC and BEA 750 are not available in AT. The App entities are already imported on Live though, therefore they remain here too.
INSERT_UPDATE App; code[unique = true]        ; packageName                ; name[lang = da]           ; name[lang = en]           ; summary[lang = da]; summary[lang = en]  ; description[lang = da]; description[lang = en]; emailAddress[default = $emailAddress]; submittedBy(uid); storeAvailabilityMode(code)[default = RESTRICTED_BUYER_GROUP]; unit(code)[default = pieces]; variantType(code)[default = AppLicense]; $catalogVersion[unique = true]; $approved; $taxGroup; privacyPolicyUrl[default = $privacyPolicyUrl]; termsOfUseUrl; supportPageUrl; productWebsiteUrl; company(uid)[default = $companyId]; countryRestricted[default = $countryRestricted]
                 ; AA2_$countryCc1987_TRKOHW3 ; $aaPackageName1987_TRKOHW3 ; Off Highway III (OHW III) ; ESI[tronic] Truck OHW III ; $ohw3Description  ; $ohw3Description_en ; $ohw3Description      ; $ohw3Description_en

INSERT_UPDATE ProductContainer; code[unique = true]         ; app(code, $catalogVersion); company(uid)[unique = true][default = $companyId];
                              ; pcaa_$countryCc1987_TRKOHW3 ; AA2_$countryCc1987_TRKOHW3


INSERT_UPDATE AppLicense; code[unique = true]      ; $baseProduct               ; sellerProductId; brimName[lang = en]   ; brimName[lang = de]   ; licenseType(code)[default = SUBSCRIPTION]; runtime(code)          ; bundleInfo(code); specifiedPrice; billingSystemStatus(code)[default = NEW]; unit(code)[default = pieces]; $catalogVersion[unique = true]; $approved; $taxGroup; enabledCountries(isocode)[default = 'IS']; availabilityStatus(code)[default = PUBLISHED]
                        ; AA2_$countryCc1987P12254 ; AA2_$countryCc1987_TRKOHW3 ; 1987P12254     ; $ohw3S1BrimName       ; $ohw3S1BrimName       ;                            ; runtime_subs_unlimited ; BI_S_1          ; 380           ;
                        ; AA2_$countryCc1987P12257 ; AA2_$countryCc1987_TRKOHW3 ; 1987P12257     ; $ohw3M3BrimName       ; $ohw3M3BrimName       ;                            ; runtime_subs_unlimited ; BI_M_3          ; 484           ;
                        ; AA2_$countryCc1987P12256 ; AA2_$countryCc1987_TRKOHW3 ; 1987P12256     ; $ohw3Full3YM3BrimName ; $ohw3Full3YM3BrimName ; "FULL"                                   ; runtime_full_3y        ; BI_M_3          ; 1597          ;


UPDATE AppLicense; code[unique = true]      ; userGroups(uid); $catalogVersion[unique = true];
                 ; AA2_$countryCc1987P12254 ; IDW000,WD0001  ;
                 ; AA2_$countryCc1987P12257 ; IDW000,WD0001  ;
                 ; AA2_$countryCc1987P12256 ; IDW000,WD0001  ;

INSERT_UPDATE PriceRow; product(code, $catalogVersion)[unique = true]; price; currency(isocode)[unique = true, default = EUR]; unit(code[unique = true, default = pieces]); minqtd[default = 1]; unitFactor[default = 1]; net[default = true]; ug[unique = true, default = '']
                      ; AA2_$countryCc1987P12254                     ; 475
                      ; AA2_$countryCc1987P12257                     ; 605
                      ; AA2_$countryCc1987P12256                     ; 1597


INSERT_UPDATE App; code[unique = true]        ; boms(code); $catalogVersion[unique = true]
                 ; AA2_$countryCc1987_TRKOHW3 ; MAT_$atCcTrFZ,MAT_$atCcTrD,MAT_$atCcTrSp,MAT_$atCcTrWt,MAT_$atCcTrTD,MAT_$atCcTrETK


INSERT_UPDATE App; code[unique = true]        ; $supercategories; $catalogVersion[unique = true];
                 ; AA2_$countryCc1987_TRKOHW3 ; cat_10102

INSERT_UPDATE App; code[unique = true]        ; contentModules(code); $catalogVersion[unique = true];
                 ; AA2_$countryCc1987_TRKOHW3 ; CM_$atCcETOHW3

INSERT_UPDATE App; code[unique = true]; icon(qualifier, $catalogVersion[unique = true]); $catalogVersion[unique = true]
                 ; AA2_$countryCc1987_TRKOHW3 ; pcaa2_$atCc1987_TRKOHW3_iconContainer_01

