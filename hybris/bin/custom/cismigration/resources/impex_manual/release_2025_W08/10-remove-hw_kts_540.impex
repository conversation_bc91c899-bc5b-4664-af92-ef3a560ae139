UPDATE GenericItem[processor = de.hybris.platform.commerceservices.impex.impl.ConfigPropertyImportProcessor]; pk[unique = true]

$clAttrModifiersVariants = system = '$classificationCatalog', version = '1.0', translator = de.hybris.platform.catalog.jalo.classification.impex.ClassificationAttributeTranslator;
$emailAddress = <EMAIL>
$productCatalog = aav2ProductCatalog
$classificationCatalog = aaClassificationCatalog
$catalogVersion = catalogversion(catalog(id[default = $productCatalog]), version[default = 'Staged'])[unique = true, default = $productCatalog:Staged]
$classCatalogVersion = catalogversion(catalog(id[default = 'aaClassificationCatalog']), version[default = '1.0'])[unique = true, default = 'aaClassificationCatalog:1.0']
$classSystemVersion = systemVersion(catalog(id[default = 'aaClassificationCatalog']), version[default = '1.0'])[unique = true]
$class = classificationClass(ClassificationClass.code, $classCatalogVersion)[unique = true]
$attribute = classificationAttribute(code, $classSystemVersion)[unique = true]
$staticLegalResourcesUrl = $config-aa.corporate.home
$privacyPolicyUrl = $staticLegalResourcesUrl/Datenschutzhinweise_ESItronic_AT_2021-01.pdf

# @formatter:off
$featureHR = @hardware-requirements,100[$clAttrModifiersVariants]; # hardware needed
$featureVT = @vehicle-types,100[$clAttrModifiersVariants]; # vehicle type
# @formatter:on
$atCc = 040
$beCc = 032
$cyCc = 357
$deCc = 049
$dkCc = 045
$eeCc = 372
$esCc = 034
$fiCc = 358
$frCc = 033
$grCc = 030
$hrCc = 385
$isCc = 354
$ltCc = 370
$ptCc = 351
$siCc = 386
$lvCc = 371
$noCc = 047
$seCc = 046
$nlCc = 031
$mtCc = 356

INSERT_UPDATE App; code[unique = true]     ; $featureHR                                                                   ; $catalogVersion;
                 ; AA2_$atCc1987_ESIADV    ; hw_kts_560,hw_kts_590,hw_kts_465,hw_kts_960,hw_kts_980,hw_kts_995,hw_kts_350 ;
                 ; AA2_$atCc1987_ESIDIAG   ; hw_kts_560,hw_kts_590,hw_kts_465,hw_kts_960,hw_kts_980,hw_kts_995,hw_kts_350 ;
                 ; AA2_$atCc1987_ESIMASTER ; hw_kts_560,hw_kts_590,hw_kts_465,hw_kts_960,hw_kts_980,hw_kts_995,hw_kts_350 ;
                 ; AA2_$beCc1987_ESIADV    ; hw_kts_560,hw_kts_590,hw_kts_465,hw_kts_960,hw_kts_980,hw_kts_995,hw_kts_350 ;
                 ; AA2_$beCc1987_ESIDIAG   ; hw_kts_560,hw_kts_590,hw_kts_465,hw_kts_960,hw_kts_980,hw_kts_995,hw_kts_350 ;
                 ; AA2_$beCc1987_ESIMASTER ; hw_kts_560,hw_kts_590,hw_kts_465,hw_kts_960,hw_kts_980,hw_kts_995,hw_kts_350 ;
                 ; AA2_$cyCc1987_ESIADV    ; hw_kts_560,hw_kts_590,hw_kts_465,hw_kts_960,hw_kts_980,hw_kts_995,hw_kts_350 ;
                 ; AA2_$cyCc1987_ESIDIAG   ; hw_kts_560,hw_kts_590,hw_kts_465,hw_kts_960,hw_kts_980,hw_kts_995,hw_kts_350 ;
                 ; AA2_$cyCc1987_ESIMASTER ; hw_kts_560,hw_kts_590,hw_kts_465,hw_kts_960,hw_kts_980,hw_kts_995,hw_kts_350 ;
                 ; AA2_$deCc1987_ESIADV    ; hw_kts_560,hw_kts_590,hw_kts_465,hw_kts_960,hw_kts_980,hw_kts_995,hw_kts_350 ;
                 ; AA2_$deCc1987_ESIDIAG   ; hw_kts_560,hw_kts_590,hw_kts_465,hw_kts_960,hw_kts_980,hw_kts_995,hw_kts_350 ;
                 ; AA2_$deCc1987_ESIMASTER ; hw_kts_560,hw_kts_590,hw_kts_465,hw_kts_960,hw_kts_980,hw_kts_995,hw_kts_350 ;
                 ; AA2_$dkCc1987_ESIADV    ; hw_kts_560,hw_kts_590,hw_kts_465,hw_kts_960,hw_kts_980,hw_kts_995,hw_kts_350 ;
                 ; AA2_$dkCc1987_ESIDIAG   ; hw_kts_560,hw_kts_590,hw_kts_465,hw_kts_960,hw_kts_980,hw_kts_995,hw_kts_350 ;
                 ; AA2_$dkCc1987_ESIMASTER ; hw_kts_560,hw_kts_590,hw_kts_465,hw_kts_960,hw_kts_980,hw_kts_995,hw_kts_350 ;
                 ; AA2_$eeCc1987_ESIADV    ; hw_kts_560,hw_kts_590,hw_kts_465,hw_kts_960,hw_kts_980,hw_kts_995,hw_kts_350 ;
                 ; AA2_$eeCc1987_ESIDIAG   ; hw_kts_560,hw_kts_590,hw_kts_465,hw_kts_960,hw_kts_980,hw_kts_995,hw_kts_350 ;
                 ; AA2_$eeCc1987_ESIMASTER ; hw_kts_560,hw_kts_590,hw_kts_465,hw_kts_960,hw_kts_980,hw_kts_995,hw_kts_350 ;
                 ; AA2_$esCc1987_ESIADV    ; hw_kts_560,hw_kts_590,hw_kts_465,hw_kts_960,hw_kts_980,hw_kts_995,hw_kts_350 ;
                 ; AA2_$esCc1987_ESIDIAG   ; hw_kts_560,hw_kts_590,hw_kts_465,hw_kts_960,hw_kts_980,hw_kts_995,hw_kts_350 ;
                 ; AA2_$esCc1987_ESIMASTER ; hw_kts_560,hw_kts_590,hw_kts_465,hw_kts_960,hw_kts_980,hw_kts_995,hw_kts_350 ;
                 ; AA2_$fiCc1987_ESIADV    ; hw_kts_560,hw_kts_590,hw_kts_465,hw_kts_960,hw_kts_980,hw_kts_995,hw_kts_350 ;
                 ; AA2_$fiCc1987_ESIDIAG   ; hw_kts_560,hw_kts_590,hw_kts_465,hw_kts_960,hw_kts_980,hw_kts_995,hw_kts_350 ;
                 ; AA2_$fiCc1987_ESIMASTER ; hw_kts_560,hw_kts_590,hw_kts_465,hw_kts_960,hw_kts_980,hw_kts_995,hw_kts_350 ;
                 ; AA2_$frCc1987_ESIADV    ; hw_kts_560,hw_kts_590,hw_kts_465,hw_kts_960,hw_kts_980,hw_kts_995,hw_kts_350 ;
                 ; AA2_$frCc1987_ESIDIAG   ; hw_kts_560,hw_kts_590,hw_kts_465,hw_kts_960,hw_kts_980,hw_kts_995,hw_kts_350 ;
                 ; AA2_$frCc1987_ESIMASTER ; hw_kts_560,hw_kts_590,hw_kts_465,hw_kts_960,hw_kts_980,hw_kts_995,hw_kts_350 ;
                 ; AA2_$grCc1987_ESIADV    ; hw_kts_560,hw_kts_590,hw_kts_465,hw_kts_960,hw_kts_980,hw_kts_995,hw_kts_350 ;
                 ; AA2_$grCc1987_ESIDIAG   ; hw_kts_560,hw_kts_590,hw_kts_465,hw_kts_960,hw_kts_980,hw_kts_995,hw_kts_350 ;
                 ; AA2_$grCc1987_ESIMASTER ; hw_kts_560,hw_kts_590,hw_kts_465,hw_kts_960,hw_kts_980,hw_kts_995,hw_kts_350 ;
                 ; AA2_$hrCc1987_ESIADV    ; hw_kts_560,hw_kts_590,hw_kts_465,hw_kts_960,hw_kts_980,hw_kts_995,hw_kts_350 ;
                 ; AA2_$hrCc1987_ESIDIAG   ; hw_kts_560,hw_kts_590,hw_kts_465,hw_kts_960,hw_kts_980,hw_kts_995,hw_kts_350 ;
                 ; AA2_$hrCc1987_ESIMASTER ; hw_kts_560,hw_kts_590,hw_kts_465,hw_kts_960,hw_kts_980,hw_kts_995,hw_kts_350 ;
                 ; AA2_$isCc1987_ESIADV    ; hw_kts_560,hw_kts_590,hw_kts_465,hw_kts_960,hw_kts_980,hw_kts_995,hw_kts_350 ;
                 ; AA2_$isCc1987_ESIDIAG   ; hw_kts_560,hw_kts_590,hw_kts_465,hw_kts_960,hw_kts_980,hw_kts_995,hw_kts_350 ;
                 ; AA2_$isCc1987_ESIMASTER ; hw_kts_560,hw_kts_590,hw_kts_465,hw_kts_960,hw_kts_980,hw_kts_995,hw_kts_350 ;
                 ; AA2_$ltCc1987_ESIADV    ; hw_kts_560,hw_kts_590,hw_kts_465,hw_kts_960,hw_kts_980,hw_kts_995,hw_kts_350 ;
                 ; AA2_$ltCc1987_ESIDIAG   ; hw_kts_560,hw_kts_590,hw_kts_465,hw_kts_960,hw_kts_980,hw_kts_995,hw_kts_350 ;
                 ; AA2_$ltCc1987_ESIMASTER ; hw_kts_560,hw_kts_590,hw_kts_465,hw_kts_960,hw_kts_980,hw_kts_995,hw_kts_350 ;
                 ; AA2_$ptCc1987_ESIADV    ; hw_kts_560,hw_kts_590,hw_kts_465,hw_kts_960,hw_kts_980,hw_kts_995,hw_kts_350 ;
                 ; AA2_$ptCc1987_ESIDIAG   ; hw_kts_560,hw_kts_590,hw_kts_465,hw_kts_960,hw_kts_980,hw_kts_995,hw_kts_350 ;
                 ; AA2_$ptCc1987_ESIMASTER ; hw_kts_560,hw_kts_590,hw_kts_465,hw_kts_960,hw_kts_980,hw_kts_995,hw_kts_350 ;
                 ; AA2_$siCc1987_ESIADV    ; hw_kts_560,hw_kts_590,hw_kts_465,hw_kts_960,hw_kts_980,hw_kts_995,hw_kts_350 ;
                 ; AA2_$siCc1987_ESIDIAG   ; hw_kts_560,hw_kts_590,hw_kts_465,hw_kts_960,hw_kts_980,hw_kts_995,hw_kts_350 ;
                 ; AA2_$siCc1987_ESIMASTER ; hw_kts_560,hw_kts_590,hw_kts_465,hw_kts_960,hw_kts_980,hw_kts_995,hw_kts_350 ;
                 ; AA2_$lvCc1987_ESIADV    ; hw_kts_560,hw_kts_590,hw_kts_465,hw_kts_960,hw_kts_980,hw_kts_995,hw_kts_350 ;
                 ; AA2_$lvCc1987_ESIDIAG   ; hw_kts_560,hw_kts_590,hw_kts_465,hw_kts_960,hw_kts_980,hw_kts_995,hw_kts_350 ;
                 ; AA2_$lvCc1987_ESIMASTER ; hw_kts_560,hw_kts_590,hw_kts_465,hw_kts_960,hw_kts_980,hw_kts_995,hw_kts_350 ;
                 ; AA2_$noCc1987_ESIADV    ; hw_kts_560,hw_kts_590,hw_kts_465,hw_kts_960,hw_kts_980,hw_kts_995,hw_kts_350 ;
                 ; AA2_$noCc1987_ESIDIAG   ; hw_kts_560,hw_kts_590,hw_kts_465,hw_kts_960,hw_kts_980,hw_kts_995,hw_kts_350 ;
                 ; AA2_$noCc1987_ESIMASTER ; hw_kts_560,hw_kts_590,hw_kts_465,hw_kts_960,hw_kts_980,hw_kts_995,hw_kts_350 ;
                 ; AA2_$seCc1987_ESIADV    ; hw_kts_560,hw_kts_590,hw_kts_465,hw_kts_960,hw_kts_980,hw_kts_995,hw_kts_350 ;
                 ; AA2_$seCc1987_ESIDIAG   ; hw_kts_560,hw_kts_590,hw_kts_465,hw_kts_960,hw_kts_980,hw_kts_995,hw_kts_350 ;
                 ; AA2_$seCc1987_ESIMASTER ; hw_kts_560,hw_kts_590,hw_kts_465,hw_kts_960,hw_kts_980,hw_kts_995,hw_kts_350 ;
                 ; AA2_$nlCc1987_ESIADV    ; hw_kts_560,hw_kts_590,hw_kts_465,hw_kts_960,hw_kts_980,hw_kts_995,hw_kts_350 ;
                 ; AA2_$nlCc1987_ESIDIAG   ; hw_kts_560,hw_kts_590,hw_kts_465,hw_kts_960,hw_kts_980,hw_kts_995,hw_kts_350 ;
                 ; AA2_$nlCc1987_ESIMASTER ; hw_kts_560,hw_kts_590,hw_kts_465,hw_kts_960,hw_kts_980,hw_kts_995,hw_kts_350 ;
                 ; AA2_$mtCc1987_ESIADV    ; hw_kts_560,hw_kts_590,hw_kts_465,hw_kts_960,hw_kts_980,hw_kts_995,hw_kts_350 ;
                 ; AA2_$mtCc1987_ESIDIAG   ; hw_kts_560,hw_kts_590,hw_kts_465,hw_kts_960,hw_kts_980,hw_kts_995,hw_kts_350 ;
                 ; AA2_$mtCc1987_ESIMASTER ; hw_kts_560,hw_kts_590,hw_kts_465,hw_kts_960,hw_kts_980,hw_kts_995,hw_kts_350 ;


INSERT_UPDATE ClassAttributeAssignment; $class; $attribute                ; attributeValues(code, $classSystemVersion)[mode = remove]
                                      ; 100   ; hardware-requirements,100 ; hw_kts_540


REMOVE ClassificationAttributeValue; code[unique = true]; $classSystemVersion
                                   ; hw_kts_540         ;


REMOVE CatalogUnawareMedia; code[unique = true];
                          ; icon_hw_kts_540    ;

REMOVE CatalogUnawareMediaContainer; qualifier[unique = true]  ;
                                   ; icon_hw_kts_540_container ;
