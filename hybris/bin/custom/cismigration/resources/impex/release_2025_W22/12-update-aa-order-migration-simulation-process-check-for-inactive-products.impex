INSERT_UPDATE DynamicProcessDefinition; code[unique = true]                   ; active[unique = true]; version[unique = true];content
                                      ; aa-order-migration-simulation-process ; true                 ; 1                     ;"
<process xmlns='http://www.hybris.de/xsd/processdefinition' start='validateInactivityStatus' name='aa-order-migration-simulation-process'
        processClass='com.sast.cis.aa.core.model.OrderMigrationBusinessProcessModel' onError='error'>

    <action id='validateInactivityStatus' bean='validateInactivityStatusAction'>
        <transition name='OK' to='validateOrderDraftStatus'/>
        <transition name='NOK' to='error'/>
    </action>

    <action id='validateOrderDraftStatus' bean='validateOrderDraftStatusAction'>
        <transition name='OK' to='setOrderDraftStatusToInProgress'/>
        <transition name='NOK' to='error'/>
    </action>

    <action id='setOrderDraftStatusToInProgress' bean='setOrderDraftStatusToInProgressAction'>
        <transition name='OK' to='sendValidationRequestToLmp'/>
        <transition name='NOK' to='error'/>
    </action>

    <action id='sendValidationRequestToLmp' bean='sendValidationRequestToLmpAction'>
        <transition name='OK' to='waitForLmpValidationResponse'/>
        <transition name='NOK' to='error'/>
    </action>

    <wait id='waitForLmpValidationResponse' then='error'>
        <case event='lmp-validation-response-event'>
                <choice id='success' then='notifyCmtOnSuccessfulMigrationSimulation'/>
                <choice id='fail' then='notifyCmtAboutLmpValidationFailure'/>
        </case>
    </wait>

    <action id='notifyCmtAboutLmpValidationFailure' bean='notifyCmtAboutLmpValidationFailureAction'>
        <transition name='OK' to='cleanupAfterSimulationFailure'/>
        <transition name='NOK' to='error'/>
    </action>

    <action id='notifyCmtOnSuccessfulMigrationSimulation' bean='notifyCmtOnSuccessfulMigrationAction'>
        <transition name='OK' to='cleanupAfterSimulationSuccess'/>
        <transition name='NOK' to='error'/>
    </action>

    <action id='cleanupAfterSimulationSuccess' bean='cleanupMigrationSimulationResourcesAction'>
        <transition name='OK' to='success'/>
        <transition name='NOK' to='error'/>
    </action>

    <action id='cleanupAfterSimulationFailure' bean='cleanupMigrationSimulationResourcesAction'>
        <transition name='OK' to='failed'/>
        <transition name='NOK' to='error'/>
    </action>

    <end id='error' state='ERROR'>All went wrong.</end>
    <end id='failed' state='FAILED'>Migration simulation failed.</end>
    <end id='success' state='SUCCEEDED'>Migration simulation succeeded.</end>
</process>"

