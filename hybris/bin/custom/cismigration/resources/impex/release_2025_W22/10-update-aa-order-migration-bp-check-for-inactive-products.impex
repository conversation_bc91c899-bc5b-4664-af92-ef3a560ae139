INSERT_UPDATE DynamicProcessDefinition; code[unique = true]        ; active[unique = true]; version[unique = true]; content
                                      ; aa-order-migration-process ; true                 ; 9                     ; "
<process xmlns='http://www.hybris.de/xsd/processdefinition' start='validateInactivityStatus' name='aa-order-migration-process'
        processClass='com.sast.cis.aa.core.model.OrderMigrationBusinessProcessModel' onError='error'>

    <action id='validateInactivityStatus' bean='validateInactivityStatusAction'>
        <transition name='OK' to='validateOrderDraftStatus'/>
        <transition name='NOK' to='error'/>
    </action>

    <action id='validateOrderDraftStatus' bean='validateOrderDraftStatusAction'>
        <transition name='OK' to='setOrderDraftStatusToInProgress'/>
        <transition name='NOK' to='error'/>
    </action>

    <action id='setOrderDraftStatusToInProgress' bean='setOrderDraftStatusToInProgressAction'>
        <transition name='OK' to='sendValidationRequestToLmp'/>
        <transition name='NOK' to='error'/>
    </action>

    <action id='sendValidationRequestToLmp' bean='sendValidationRequestToLmpAction'>
        <transition name='OK' to='waitForLmpValidationResponse'/>
        <transition name='NOK' to='error'/>
    </action>

    <wait id='waitForLmpValidationResponse' then='error'>
        <case event='lmp-validation-response-event'>
                <choice id='success' then='createOrderFromMigrationOrderDraft'/>
                <choice id='fail' then='notifyCmtAboutLmpValidationFailure'/>
        </case>
    </wait>

    <action id='notifyCmtAboutLmpValidationFailure' bean='notifyCmtAboutLmpValidationFailureAction'>
        <transition name='OK' to='failed'/>
        <transition name='NOK' to='error'/>
    </action>

    <action id='createOrderFromMigrationOrderDraft' bean='createOrderFromMigrationOrderDraftAction'>
        <transition name='OK' to='routeByBillingTypeAfterOrderCreation'/>
        <transition name='NOK' to='error'/>
    </action>

    <action id='routeByBillingTypeAfterOrderCreation' bean='migrationOrderBillingRoutingAction'>
        <transition name='BILLABLE' to='assignPaymentDataToOrder'/>
        <transition name='NON_BILLABLE' to='exportOrderToLmp'/>
        <transition name='ERROR' to='error'/>
    </action>

    <action id='assignPaymentDataToOrder' bean='assignPaymentDataToOrderAction'>
        <transition name='OK' to='exportOrderToBrim'/>
        <transition name='NOK' to='notifyCmtAboutUnavailablePaymentData'/>
    </action>

    <action id='notifyCmtAboutUnavailablePaymentData' bean='notifyCmtAboutUnavailablePaymentDataAction'>
        <transition name='OK' to='failed'/>
        <transition name='NOK' to='error'/>
    </action>

    <action id='exportOrderToBrim' bean='brimExportMigrationOrderAction'>
        <transition name='OK' to='waitForBrimOrderExportResponseEvent'/>
        <transition name='NOK' to='error'/>
    </action>

    <wait id='waitForBrimOrderExportResponseEvent' then='error'>
        <case event='order-response-event'>
                <choice id='success' then='exportOrderToLmp'/>
                <choice id='fail' then='notifyCmtAboutBrimExportFailure'/>
        </case>
    </wait>

    <action id='notifyCmtAboutBrimExportFailure' bean='notifyCmtAboutBrimExportFailureAction'>
        <transition name='OK' to='failed'/>
        <transition name='NOK' to='error'/>
    </action>

    <action id='exportOrderToLmp' bean='exportOrderToLmpAction'>
        <transition name='OK' to='waitForLmpOrderExportResponseEvent'/>
        <transition name='NOK' to='error'/>
    </action>

    <wait id='waitForLmpOrderExportResponseEvent' then='error'>
        <case event='lmp-order-response-event'>
                <choice id='success' then='setOrderDraftStatusCompleted'/>
                <choice id='fail' then='routeByBillingTypeAfterLmpExportFailure'/>
        </case>
    </wait>

    <action id='routeByBillingTypeAfterLmpExportFailure' bean='migrationOrderBillingRoutingAction'>
        <transition name='BILLABLE' to='cancelOrderInBrim'/>
        <transition name='NON_BILLABLE' to='notifyCmtAboutLmpExportFailure'/>
        <transition name='ERROR' to='error'/>
    </action>

    <action id='cancelOrderInBrim' bean='cancelOrderInBrimAction'>
        <transition name='OK' to='notifyCmtAboutLmpExportFailure'/>
        <transition name='NOK' to='error'/>
    </action>

    <action id='notifyCmtAboutLmpExportFailure' bean='notifyCmtAboutLmpExportFailureAction'>
        <transition name='OK' to='failed'/>
        <transition name='NOK' to='error'/>
    </action>

    <action id='setOrderDraftStatusCompleted' bean='migrationOrderDraftCompletedAction'>
        <transition name='OK' to='routeByBillingTypeAfterDraftCompletion'/>
        <transition name='NOK' to='error'/>
    </action>

    <action id='routeByBillingTypeAfterDraftCompletion' bean='migrationOrderBillingRoutingAction'>
        <transition name='BILLABLE' to='notifyCmtOnSuccessfulMigration'/>
        <transition name='NON_BILLABLE' to='setOrderStatusToCompletedForFull'/>
        <transition name='ERROR' to='error'/>
    </action>

    <action id='setOrderStatusToCompletedForFull' bean='migrationOrderCompletedAction'>
        <transition name='OK' to='notifyCmtOnSuccessfulMigration'/>
        <transition name='NOK' to='error'/>
    </action>

    <action id='notifyCmtOnSuccessfulMigration' bean='notifyCmtOnSuccessfulMigrationAction'>
        <transition name='OK' to='routeByOrderStatusAfterSuccessNotification'/>
        <transition name='NOK' to='error'/>
    </action>

    <action id='routeByOrderStatusAfterSuccessNotification' bean='migrationOrderStatusRoutingAction'>
        <transition name='OPEN' to='waitForBrimOrderFinalizationEvent'/>
        <transition name='COMPLETED' to='success'/>
        <transition name='REJECTED' to='failed'/>
        <transition name='OTHER' to='error'/>
    </action>

    <wait id='waitForBrimOrderFinalizationEvent' then='error'>
        <case event='order-response-event'>
                <choice id='success' then='success'/>
                <choice id='error' then='error'/>
        </case>
    </wait>

    <end id='error' state='ERROR'>All went wrong.</end>
    <end id='failed' state='FAILED'>Order migration failed.</end>
    <end id='success' state='SUCCEEDED'>Order Migrated.</end>
</process>"
