#% impex.enableCodeExecution(true);

$deCc = 049

$productCatalog = aav2ProductCatalog
$catalogVersion = catalogversion(catalog(id[default = $productCatalog]), version[default = 'Staged'])


#% if: !"live".equals("$config-spring.profiles.active") && !"demo".equals("$config-spring.profiles.active") && !"dev".equals("$config-spring.profiles.active");
UPDATE AppLicense; code[unique = true] ; availabilityStatus(code)[default = UNPUBLISHED]; $catalogVersion[unique = true];
                 ; AA2_$deCc1987P12990 ;
                 ; AA2_$deCc1987P12359 ;
                 ; AA2_$deCc1987P12404 ;
                 ; AA2_$deCc1987P12784 ;
                 ; AA2_$deCc1987P12295 ;
                 ; AA2_$deCc1987P12297 ;
                 ; AA2_$deCc1987P12998 ;
                 ; AA2_$deCc1987P12970 ;

INSERT_UPDATE App; code[unique = true]     ; masterEnabled[default = false]; $catalogVersion[unique = true];
                 ; AA2_$deCc1987_ESIREPCAT ;
                 ; AA2_$deCc1987_ESIREPD   ;
                 ; AA2_$deCc1987_ESIREPE   ;
                 ; AA2_$deCc1987_TRKUPG    ;
#% endif:
