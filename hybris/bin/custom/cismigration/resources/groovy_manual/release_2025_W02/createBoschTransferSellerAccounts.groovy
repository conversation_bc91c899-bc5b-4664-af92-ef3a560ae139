package groovy_manual.release_2025_W02

import com.sast.cis.core.enums.PspSellerAccountStatus
import com.sast.cis.core.model.IoTCompanyModel
import com.sast.cis.core.service.company.IotCompanyService
import com.sast.cis.payment.boschtransfer.model.BoschSellerAccountModel
import com.sast.cis.payment.boschtransfer.model.BoschSepaCollectionAccountModel
import de.hybris.platform.servicelayer.model.ModelService

class CreateBoschTransferSellerAccounts {
    private IotCompanyService iotCompanyService
    private ModelService modelService

    private createSeller(SellerAccountData sellerAccountData) {
        IoTCompanyModel company = iotCompanyService.getCompanyByUid(sellerAccountData.companyId).orElseThrow()

        if (!company.getPspSellerAccounts().findAll {it instanceof BoschSellerAccountModel}.isEmpty()) {
            System.out.println("Company ${company.uid} (${company.name} - ${company.country.isocode}) already has BOSCH_TRANSFER seller account")
            return
        }

        BoschSepaCollectionAccountModel sepaCollectionAccount = modelService.create(BoschSepaCollectionAccountModel.class)
        sepaCollectionAccount.setIban(sellerAccountData.iban)
        sepaCollectionAccount.setBic(sellerAccountData.bic)
        sepaCollectionAccount.setBankName(sellerAccountData.bankName)
        modelService.save(sepaCollectionAccount)

        BoschSellerAccountModel sellerAccount = modelService.create(BoschSellerAccountModel.class);
        sellerAccount.setCompany(company);
        sellerAccount.setSepaCollectionAccount(sepaCollectionAccount)
        sellerAccount.setStatus(PspSellerAccountStatus.ACTIVE)
        modelService.save(sellerAccount)

        System.out.println("Company ${company.uid} (${company.name} - ${company.country.isocode}) has new seller account ${sellerAccount.accountId}")
    }

    private class SellerAccountData {
        private final String companyId
        private final String iban
        private final String bic
        private final String bankName

        SellerAccountData(String companyId, String iban, String bic, String bankName) {
            this.companyId = companyId
            this.iban = iban
            this.bic = bic
            this.bankName = bankName
        }
    }

    def run() {
        [
                new SellerAccountData('f90f9664-7871-4161-bbb2-cc2f5abc8a56', '***************************', 'CRBAGRAAXXX', 'Alpha Bank'),                         // GR
                new SellerAccountData('768d8ea9-6254-47f1-ab93-a3168b18e17e', '***************************', 'SOGEFRPP', 'Société Générale, Ag. De St. Denis'),    // FR
        ].each { createSeller(it) }
    }
}

new CreateBoschTransferSellerAccounts(
        iotCompanyService: iotCompanyService,
        modelService: modelService
).run()