package groovy_manual.release_1_31

import com.sast.cis.core.enums.PspSellerAccountStatus
import com.sast.cis.core.model.IoTCompanyModel
import com.sast.cis.core.service.company.IotCompanyService
import com.sast.cis.payment.boschtransfer.model.BoschSellerAccountModel
import com.sast.cis.payment.boschtransfer.model.BoschSepaCollectionAccountModel
import de.hybris.platform.servicelayer.model.ModelService

class CreateBoschTransferSellerAccounts {
    private IotCompanyService iotCompanyService
    private ModelService modelService

    private createSeller(SellerAccountData sellerAccountData) {
        IoTCompanyModel company = iotCompanyService.getCompanyByUid(sellerAccountData.companyId).orElseThrow()

        if (!company.getPspSellerAccounts().findAll {it instanceof BoschSellerAccountModel}.isEmpty()) {
            System.out.println("Company ${company.uid} (${company.name} - ${company.country.isocode}) already has BOSCH_TRANSFER seller account")
            return
        }

        BoschSepaCollectionAccountModel sepaCollectionAccount = modelService.create(BoschSepaCollectionAccountModel.class)
        sepaCollectionAccount.setIban(sellerAccountData.iban)
        sepaCollectionAccount.setBic(sellerAccountData.bic)
        sepaCollectionAccount.setBankName(sellerAccountData.bankName)
        modelService.save(sepaCollectionAccount)

        BoschSellerAccountModel sellerAccount = modelService.create(BoschSellerAccountModel.class);
        sellerAccount.setCompany(company);
        sellerAccount.setSepaCollectionAccount(sepaCollectionAccount)
        sellerAccount.setStatus(PspSellerAccountStatus.ACTIVE)
        modelService.save(sellerAccount)

        System.out.println("Company ${company.uid} (${company.name} - ${company.country.isocode}) has new seller account ${sellerAccount.accountId}")
    }

    private class SellerAccountData {
        private final String companyId
        private final String iban
        private final String bic
        private final String bankName

        SellerAccountData(String companyId, String iban, String bic, String bankName) {
            this.companyId = companyId
            this.iban = iban
            this.bic = bic
            this.bankName = bankName
        }
    }

    def run() {
        [
                new SellerAccountData('03cb8bd0-f982-4bb8-8ece-e5528b3dc795', '*************************', 'DEUTPTPLXXX', 'Deutsche Bank'),           // PT
                new SellerAccountData('1c15899b-cae4-49ff-92d7-3462452a60d1', '*********************', 'RZBHHR2X', 'Raiffeisenbank Austria d.d.'),    // HR
                new SellerAccountData('37fc3456-94b3-4d3d-a83a-52d2413b4116', '******************', 'DABADKKK', 'Danske Bank'),                       // DK
                new SellerAccountData('556f83c0-bfb8-403e-b28d-2826852d826b', '************************', 'DABASESX', 'Danske Bank'),                 // SE
                new SellerAccountData('bfe0a45e-215a-41cd-bfe6-5838e7659c1d', '******************', 'DABAFIHH', 'DANSKE BANK A/S, FINLAND'),          // FI
                new SellerAccountData('c5f3e918-fd1c-4e8c-8c42-133d0a15ec34', '***************', 'DABANO22', 'Danske Bank'),                          // NO
        ].each { createSeller(it) }
    }
}

new CreateBoschTransferSellerAccounts(
        iotCompanyService: iotCompanyService,
        modelService: modelService
).run()
