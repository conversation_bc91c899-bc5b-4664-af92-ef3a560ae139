package groovy_manual.aa

import com.sast.cis.core.dao.AppDao
import com.sast.cis.core.dao.CatalogVersion
import com.sast.cis.core.service.AppLicenseService


class PricingReviewData {
    AppDao appDao
    AppLicenseService appLicenseService

    def run() {
        System.out.println("name,bestellnummerEinmalkauf,bestellnummerAbo,preisEinmalkauf,preisAbo,waehrung")
        appDao.getAppsForCatalogVersion("aaProductCatalog", CatalogVersion.STAGED).stream()
            .filter({it.code.startsWith('AA_040')})
            .forEach({
                def bestellnummerEinmalkauf = appLicenseService.getFullAppLicenseForAzenaTenant(it).map ({it.getSellerProductId()}).orElse('')
                def bestellnummerAbo = appLicenseService.getSubscriptionAppLicense(it).map ({it.getSellerProductId()}).orElse('')
                def fullLicensePrice = appLicenseService.getFullAppLicenseForAzenaTenant(it).map({it.specifiedPrice}).orElse(null)
                def subscriptionLicensePrice = appLicenseService.getSubscriptionAppLicense(it).map({it.specifiedPrice}).orElse(null)
                System.out.println("${it.name},${bestellnummerEinmalkauf},${bestellnummerAbo},${fullLicensePrice != null ? fullLicensePrice : ''},${subscriptionLicensePrice != null ? subscriptionLicensePrice : ''},EUR")
            })

    }
}

new PricingReviewData(
    appDao: appDao,
    appLicenseService: appLicenseService
).run()
