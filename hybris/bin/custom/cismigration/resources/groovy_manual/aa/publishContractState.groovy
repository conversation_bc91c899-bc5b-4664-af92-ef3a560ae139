package groovy_manual.aa

import com.sast.cis.core.constants.AwsResource
import com.sast.cis.core.dao.buyercontract.BuyerContractDao
import com.sast.cis.core.dto.portal.SubscriptionDto
import com.sast.cis.core.model.BuyerContractModel
import com.sast.cis.core.service.ObjectMapperService
import com.sast.cis.core.service.aws.MessageQueueService
import de.hybris.platform.servicelayer.dto.converter.Converter

class PublishContractStateScript {
    Converter<BuyerContractModel, SubscriptionDto> subscriptionConverter
    MessageQueueService messageQueueService
    ObjectMapperService objectMapperService
    BuyerContractDao buyerContractDao



    private void publishUpdateEvent(final String contractCode) {
        def buyerContract = buyerContractDao.getByContractCode(contractCode)
                .orElseThrow()
        System.out.println("${buyerContract.code} - ${buyerContract.endDate}")
        SubscriptionDto subscriptionDto = subscriptionConverter.convert(buyerContract)
        String message = objectMapperService.toJsonUnescaped(subscriptionDto);
        messageQueueService.publishMessage(message, AwsResource.SUBSCRIPTION_TOPIC);
    }


    def run() {
        [
                'CONTRACT IDS HERE'
        ].each {
            try {
                publishUpdateEvent(it)
            } catch (Exception e) {
                System.out.println("A boo boo happened: ${e.getMessage()}")
            }
        }
    }
}

new PublishContractStateScript(
        subscriptionConverter: subscriptionConverter,
        messageQueueService: messageQueueService,
        objectMapperService: objectMapperService,
        buyerContractDao: buyerContractDao
).run()
