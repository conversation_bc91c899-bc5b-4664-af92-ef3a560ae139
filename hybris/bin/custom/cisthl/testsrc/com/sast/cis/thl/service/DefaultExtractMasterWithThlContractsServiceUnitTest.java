package com.sast.cis.thl.service;

import com.sast.cis.core.CisTimeService;
import com.sast.cis.core.dao.buyercontract.BuyerContractDao;
import com.sast.cis.core.enums.TerminationRuleUnit;
import com.sast.cis.core.model.AppLicenseModel;
import com.sast.cis.core.model.SubscriptionContractModel;
import com.sast.cis.core.model.TerminationRulePeriodModel;
import com.sast.cis.core.service.FeatureToggleService;
import com.sast.cis.core.util.BusinessProcessUtil;
import com.sast.cis.thl.model.EsiMasterTHLMigrationProcessModel;
import de.hybris.bootstrap.annotations.UnitTest;
import de.hybris.platform.core.model.order.AbstractOrderEntryModel;
import de.hybris.platform.core.model.order.AbstractOrderModel;
import de.hybris.platform.processengine.BusinessProcessService;
import de.hybris.platform.servicelayer.model.ModelService;
import de.hybris.platform.servicelayer.user.UserService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.Date;
import java.util.List;

import static com.sast.cis.core.constants.DynamicBusinessProcessesDefinitions.ESIMASTER_THL_MIGRATION_PROCESS;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyInt;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@UnitTest
@RunWith(MockitoJUnitRunner.Silent.class)
public class DefaultExtractMasterWithThlContractsServiceUnitTest {

    private static final String CODE = "CODE";
    private static final String SUB_CODE = "SUB_CODE";
    private static final String ADD_ON_THL = "THL";
    @Mock
    private BuyerContractDao buyerContractDao;
    @Mock
    private CisTimeService cisTimeService;
    @Mock
    private SubscriptionContractModel subscriptionContractModel;
    @Mock
    private AppLicenseModel master;
    @Mock
    private AbstractOrderEntryModel masterOrderEntry;
    @Mock
    private AppLicenseModel thl;
    @Mock
    private AbstractOrderEntryModel thlOrderEntry;
    @Mock
    private AbstractOrderModel order;
    @Mock
    private ModelService modelService;
    @Mock
    private TerminationRulePeriodModel noticePeriod;
    @Mock
    private EsiMasterTHLMigrationProcessModel businessProcess;
    @Mock
    private FeatureToggleService featureToggleService;
    @Mock
    private BusinessProcessService businessProcessService;
    @Mock
    private UserService userService;
    @Mock
    private BusinessProcessUtil businessProcessUtil;

    private DefaultExtractMasterWithThlContractsService service;

    @Before
    public void setup() {
        when(subscriptionContractModel.getOrderEntry()).thenReturn(masterOrderEntry);
        when(subscriptionContractModel.getCode()).thenReturn(SUB_CODE);
        when(masterOrderEntry.getOrder()).thenReturn(order);
        when(masterOrderEntry.getProduct()).thenReturn(master);
        when(master.getCode()).thenReturn(CODE);
        when(master.getAddonThl()).thenReturn(ADD_ON_THL);
        when(thlOrderEntry.getOrder()).thenReturn(order);
        when(thlOrderEntry.getProduct()).thenReturn(thl);
        when(thl.getCode()).thenReturn(ADD_ON_THL);
        when(buyerContractDao.listUncancelledUnmigratedSubscriptions(eq(CODE), any(Date.class), any(Date.class)))
            .thenReturn(List.of(subscriptionContractModel));
        when(cisTimeService.getCurrentUtcTime()).thenReturn(ZonedDateTime.of(2024, 1, 1, 0, 0, 0, 0, ZoneId.of("UTC")));
        when(cisTimeService.getZonedDate(anyInt(), anyInt(), anyInt())).thenReturn(
            ZonedDateTime.of(2025, 1, 1, 0, 0, 0, 0, ZoneId.of("UTC")));
        when(order.getEntries()).thenReturn(List.of(masterOrderEntry));
        when(noticePeriod.getValue()).thenReturn(56);
        when(noticePeriod.getUnit()).thenReturn(TerminationRuleUnit.DAY);
        when(businessProcessUtil.generateProcessId(anyString(), anyString(), anyString())).thenReturn("processId");
        when(businessProcessService.createProcess(anyString(), eq(ESIMASTER_THL_MIGRATION_PROCESS.getValue()))).thenReturn(businessProcess);
        // injection should be done after the mock setup due to constructor logic
        service = new DefaultExtractMasterWithThlContractsService(buyerContractDao, cisTimeService, modelService, featureToggleService,
            businessProcessService, userService, businessProcessUtil);
    }

    @Test
    public void test_extract() {
        service.extract(CODE, noticePeriod, 1);
        assertCallingBusinessProcess();
    }

    @Test
    public void test_after_thl_feature_live_date() {
        when(cisTimeService.getCurrentUtcTime()).thenReturn(ZonedDateTime.of(2026, 3, 1, 0, 0, 0, 0, ZoneId.of("UTC")));
        service.extract(CODE, noticePeriod, 1);
        verify(businessProcessService, never()).createProcess(anyString(), eq(ESIMASTER_THL_MIGRATION_PROCESS.getValue()));
    }

    @Test
    public void test_extract_no_interactions() {
        when(buyerContractDao.listUncancelledUnmigratedSubscriptions(eq(CODE), any(Date.class), any(Date.class)))
            .thenReturn(List.of());
        service.extract(CODE, noticePeriod, 1);
        verify(businessProcessService, never()).createProcess(anyString(), eq(ESIMASTER_THL_MIGRATION_PROCESS.getValue()));
    }

    @Test
    public void test_existing_thl() {
        when(order.getEntries()).thenReturn(List.of(masterOrderEntry, thlOrderEntry));
        service.extract(CODE, noticePeriod, 1);
        verify(businessProcessService, never()).createProcess(anyString(), eq(ESIMASTER_THL_MIGRATION_PROCESS.getValue()));
    }

    private void assertCallingBusinessProcess() {
        verify(businessProcessService, times(1)).createProcess(anyString(), eq(ESIMASTER_THL_MIGRATION_PROCESS.getValue()));
        verify(modelService, times(1)).save(eq(businessProcess));
        verify(businessProcessService, times(1)).startProcess(eq(businessProcess));
    }

}