package com.sast.cis.approval.workflows.appapproval;

import com.google.common.collect.ImmutableList;
import com.sast.cis.approval.service.ApprovalEmailService;
import com.sast.cis.core.enums.RejectionCode;
import com.sast.cis.core.model.*;
import com.tngtech.java.junit.dataprovider.DataProvider;
import com.tngtech.java.junit.dataprovider.DataProviderRunner;
import com.tngtech.java.junit.dataprovider.UseDataProvider;
import de.hybris.bootstrap.annotations.UnitTest;
import de.hybris.platform.core.model.ItemModel;
import de.hybris.platform.servicelayer.model.ModelService;
import de.hybris.platform.workflow.model.WorkflowActionModel;
import de.hybris.platform.workflow.model.WorkflowDecisionModel;
import generated.com.sast.cis.core.model.*;
import generated.de.hybris.platform.workflow.model.WorkflowActionBuilder;
import generated.de.hybris.platform.workflow.model.WorkflowDecisionBuilder;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnit;
import org.mockito.junit.MockitoRule;

import java.util.Set;
import java.util.stream.Collectors;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@UnitTest
@RunWith(DataProviderRunner.class)
public class PostAppVersionReviewCheckJobUnitTest {
    private static final WorkflowDecisionModel APPROVE_APP_VERSION_DECISION = WorkflowDecisionBuilder.generate()
        .withCode("approveAppVersionAfterReview")
        .buildInstance();
    private static final WorkflowDecisionModel DECLINE_APP_VERSION_DECISION = WorkflowDecisionBuilder.generate()
        .withCode("declineAppVersionAfterReview")
        .buildInstance();

    private static final String VALID_APPVERSIONDRAFT_CODE = "TestVersionCode";
    private static final String DEFAULT_ECCN = "a.1234";
    private static final boolean DEFAULT_DUAL_USE = true;

    @Rule
    public MockitoRule mockitoRule = MockitoJUnit.rule();

    @Mock
    private ModelService modelService;

    @Mock
    private ApprovalEmailService approvalEmailService;

    @InjectMocks
    private PostAppVersionReviewCheckJob postAppVersionReviewCheckJob;

    private AppVersionModel appVersion;
    private AppVersionDraftModel appVersionDraft;
    private AppModel app;
    private WorkflowActionModel workflowAction;
    private ProductContainerModel productContainer;
    private ApkMediaModel apk;

    @Before
    public void setUp() {
        apk = ApkMediaBuilder.generate().withVersionName("newVersion").buildMockInstance();

        appVersionDraft = AppVersionDraftBuilder.generate()
            .withCode(VALID_APPVERSIONDRAFT_CODE)
            .withEccn(DEFAULT_ECCN)
            .withDualUse(DEFAULT_DUAL_USE)
            .withApk(apk)
            .buildInstance();

        appVersion = AppVersionBuilder.generate()
            .withEccn(DEFAULT_ECCN)
            .withDualUse(DEFAULT_DUAL_USE)
            .buildInstance();

        IoTCompanyModel developerCompany = IoTCompanyBuilder.generate()
            .withName("Developer Company")
            .withManualAppApprovalEnabled(true)
            .buildMockInstance();

        DeveloperModel developer = DeveloperBuilder.generate().withName("developer1").withCompany(developerCompany).buildMockInstance();

        app = AppBuilder.generate()
            .withName("appName")
            .withSubmittedBy(developer)
            .withVersions(Set.of(appVersion))
            .buildInstance();

        productContainer = ProductContainerBuilder.generate()
            .withAppVersionDraft(appVersionDraft)
            .withCompany(developerCompany)
            .withApp(app)
            .buildInstance();

        ImmutableList<ItemModel> attachmentList = ImmutableList.of(productContainer);

        workflowAction = WorkflowActionBuilder.generate()
            .withDecisions(ImmutableList.of(APPROVE_APP_VERSION_DECISION, DECLINE_APP_VERSION_DECISION))
            .buildMockInstance();
        when(workflowAction.getAttachmentItems()).thenReturn(attachmentList);
        when(modelService.create(RejectionReasonModel.class)).thenReturn(RejectionReasonBuilder.generate()
            .buildInstance());
    }

    @DataProvider
    public static Object[][] matchingDecisionTestCases() {
        return new Object[][] {
            { false, null, false, null, APPROVE_APP_VERSION_DECISION },
            { true, null, true, null, APPROVE_APP_VERSION_DECISION },
            { true, DEFAULT_ECCN, true, DEFAULT_ECCN, APPROVE_APP_VERSION_DECISION },
            { false, DEFAULT_ECCN, false, DEFAULT_ECCN, APPROVE_APP_VERSION_DECISION },

            { false, DEFAULT_ECCN, true, DEFAULT_ECCN, DECLINE_APP_VERSION_DECISION },
            { true, DEFAULT_ECCN, true, "asdf", DECLINE_APP_VERSION_DECISION },
            { false, null, true, null, DECLINE_APP_VERSION_DECISION },
        };
    }

    @Test
    @UseDataProvider("matchingDecisionTestCases")
    public void testExportDataComparison(boolean givenDraftDualUse, String givenDraftEccn, boolean givenAppDualUse, String givenAppEccn,
        WorkflowDecisionModel expectedDecision) {
        appVersion.setEccn(givenAppEccn);
        appVersion.setDualUse(givenAppDualUse);

        appVersionDraft.setEccn(givenDraftEccn);
        appVersionDraft.setDualUse(givenDraftDualUse);

        WorkflowDecisionModel actualDecision = postAppVersionReviewCheckJob.perform(workflowAction);
        assertThat(actualDecision).isEqualTo(expectedDecision);
    }

    @Test(expected = IllegalStateException.class)
    public void testShouldThrowForProductContainerWithoutApp() {
        productContainer.setApp(null);
        postAppVersionReviewCheckJob.perform(workflowAction);
    }

    @Test(expected = IllegalStateException.class)
    public void testShouldThrowForProductContainerWithoutVersionDraft() {
        productContainer.setAppVersionDraft(null);
        postAppVersionReviewCheckJob.perform(workflowAction);
    }

    @Test(expected = IllegalStateException.class)
    public void testShouldThrowIfAppHasNoVersions() {
        app.setVersions(Set.of());
        postAppVersionReviewCheckJob.perform(workflowAction);
    }

    @Test(expected = IllegalStateException.class)
    public void testShouldThrowIfAppHasNullVersions() {
        app.setVersions(null);
        postAppVersionReviewCheckJob.perform(workflowAction);
    }

    @Test
    public void testShouldSetCorrectRejectionReason() {
        appVersionDraft.setEccn("asdf");
        postAppVersionReviewCheckJob.perform(workflowAction);

        assertThat(appVersionDraft.getRejectionReasons()).isNotNull();
        Set<RejectionCode> actualRejectionCodes = appVersionDraft.getRejectionReasons().stream()
            .map(RejectionReasonModel::getRejectionCode)
            .collect(Collectors.toSet());
        assertThat(actualRejectionCodes).contains(RejectionCode.EXPORT_CLASSIFICATION);
    }
}
