package com.sast.cis.aa.core.process;

import com.sast.cis.aa.core.priceexport.service.AaPriceExportService;
import com.sast.cis.aa.core.model.GroupPriceUpdateProcessModel;
import com.sast.cis.core.billingintegration.exception.BillingIntegrationException;
import com.sast.cis.core.model.AppLicenseModel;
import de.hybris.platform.processengine.action.AbstractSimpleDecisionAction;
import de.hybris.platform.task.RetryLaterException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
public class ExportGroupPricesAction extends AbstractSimpleDecisionAction<GroupPriceUpdateProcessModel> {
    private static final Logger LOG = LoggerFactory.getLogger(ExportGroupPricesAction.class);
    private AaPriceExportService aaPriceExportService;

    public ExportGroupPricesAction(AaPriceExportService aaPriceExportService) {
        this.aaPriceExportService = aaPriceExportService;
        setModelService(modelService);
    }

    @Override
    public Transition executeAction(GroupPriceUpdateProcessModel groupPriceUpdateProcess) {
        if (groupPriceUpdateProcess == null) {
            LOG.error("Could not find group Price Update process");
            return Transition.NOK;
        }

        AppLicenseModel appLicense = groupPriceUpdateProcess.getAppLicense();

        try {
            aaPriceExportService.exportPrices(appLicense);
        } catch (BillingIntegrationException e) {
            final String msg = String.format("Failed to export group prices for process=%s", groupPriceUpdateProcess.getCode());
            LOG.error(msg, e);
            throw new RetryLaterException(msg, e);
        }

        return Transition.OK;
    }
}
