package com.sast.cis.aa.core.migration.service;

import com.sast.cis.aa.core.model.MigrationOrderDraftModel;
import com.sast.cis.aa.core.model.MigrationOrderEntryDraftModel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collection;

@Service
@Slf4j
@RequiredArgsConstructor
public class MigrationProductInactivityStatusValidator {
    private final MigrationAppLicenseService migrationAppLicenseService;

    /**
     * Validates if all entries in a migration order draft have consistent inactivity status.
     * All products must match the draft's inactivity flag.
     *
     * @param migrationOrderDraft the migration order draft to validate
     * @return true if all entries are consistent with the draft's inactivity flag, false otherwise
     */
    public boolean validateConsistentInactivityStatus(final MigrationOrderDraftModel migrationOrderDraft) {
        final Collection<MigrationOrderEntryDraftModel> entries = migrationOrderDraft.getEntries();
        if (entries == null || entries.isEmpty()) {
            LOG.error("Migration order draft entries are null or empty for draft with code: {}", migrationOrderDraft.getCode());
            return false;
        }

        final boolean isDraftInactive = migrationOrderDraft.isInactiveProductMigration();

        for (MigrationOrderEntryDraftModel entry : entries) {
            final boolean isProductInactive = !migrationAppLicenseService.isAppLicenseActive(entry.getAppLicense());

            if (isDraftInactive != isProductInactive) {
                LOG.error("Product inactivity status mismatch in migration order draft: {}. " +
                        "Draft inactivity flag: {}, product inactivity status: {}",
                    migrationOrderDraft.getCode(), isDraftInactive, isProductInactive);
                return false;
            }
        }

        LOG.info("Validated {} entries in migration order draft {}: all products have CONSISTENT inactivity status (inactive flag: {}).",
            entries.size(), migrationOrderDraft.getCode(), isDraftInactive);
        return true;
    }
}
