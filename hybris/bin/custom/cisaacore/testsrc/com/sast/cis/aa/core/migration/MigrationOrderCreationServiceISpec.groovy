package com.sast.cis.aa.core.migration

import com.sast.cis.aa.core.model.MigrationOrderDraftModel
import com.sast.cis.aa.core.model.MigrationOrderEntryDraftModel
import com.sast.cis.core.constants.Currency
import com.sast.cis.core.enums.ActivationMode
import com.sast.cis.core.model.AaDistributorCompanyModel
import com.sast.cis.core.model.AppLicenseModel
import com.sast.cis.core.model.IoTCompanyModel
import com.sast.cis.core.model.RuntimeModel
import com.sast.cis.test.utils.SampleDataCreator
import de.hybris.bootstrap.annotations.IntegrationTest
import de.hybris.platform.core.enums.OrderStatus
import de.hybris.platform.core.enums.PaymentStatus
import de.hybris.platform.core.model.c2l.CurrencyModel
import de.hybris.platform.core.model.user.UserGroupModel
import de.hybris.platform.europe1.model.PriceRowModel
import de.hybris.platform.servicelayer.ServicelayerTransactionalSpockSpecification
import de.hybris.platform.servicelayer.model.ModelService
import de.hybris.platform.servicelayer.search.FlexibleSearchService
import generated.com.sast.cis.aa.core.model.*
import org.assertj.core.util.DateUtil

import javax.annotation.Resource

import static com.sast.cis.aa.core.enums.MigrationMode.EXECUTE
import static com.sast.cis.core.dao.CatalogVersion.ONLINE
import static com.sast.cis.core.enums.LicenseAvailabilityStatus.UNPUBLISHED
import static com.sast.cis.test.utils.TestDataConstants.*
import static de.hybris.platform.catalog.enums.ArticleApprovalStatus.APPROVED

@IntegrationTest
class MigrationOrderCreationServiceISpec extends ServicelayerTransactionalSpockSpecification {

    @Resource
    private ModelService modelService

    @Resource
    private MigrationOrderCreationService migrationOrderCreationService

    @Resource
    private FlexibleSearchService flexibleSearchService

    private final SampleDataCreator sampleDataCreator = new SampleDataCreator()

    private MigrationOrderDraftModel migrationOrderDraft
    private MigrationOrderEntryDraftModel esiMigrationOrderEntryDraft
    private MigrationOrderEntryDraftModel thlMigrationOrderEntryDraft
    private IoTCompanyModel buyerCompany
    private AaDistributorCompanyModel distributorCompany

    private AppLicenseModel esiLicense
    private AppLicenseModel thlLicense
    private PriceRowModel esiWd2GroupPrice
    private PriceRowModel thlWd2GroupPrice

    private RuntimeModel subsUnlimited
    private CurrencyModel eur
    private UserGroupModel wd2Group

    void setup() {
        def developer = sampleDataCreator.getDeveloperByInternalUserId(AA_AUSTRIA1_COMPANY_DEVELOPER_UID)
        buyerCompany = sampleDataCreator.getCompanyByUidOrThrow(AA_BUYER_COMPANY_UID)
        distributorCompany = sampleDataCreator.getAaDistributorCompany(AA_DISTRIBUTOR_COMPANY_UID)
        wd2Group = sampleDataCreator.getUserGroup("WD0002")
        subsUnlimited = findRuntime("runtime_subs_unlimited")
        eur = sampleDataCreator.getCurrency(Currency.EUR)

        def esiApp = sampleDataCreator.createApp("AA_MIGRATION_ESI", "aa.esi.MigrationOrderCreationServiceISpec", developer, AA_PRODUCT_CATALOG, ONLINE, APPROVED)
        esiLicense = sampleDataCreator.createSubscriptionAppLicense(esiApp)
        esiLicense.setRuntime(subsUnlimited)
        esiWd2GroupPrice = sampleDataCreator.addPriceRow(esiLicense, 1000.0, eur, 0)
        esiWd2GroupPrice.setBillingPriceCode("WD0002_${esiLicense.code}")
        esiWd2GroupPrice.setUg(wd2Group.getUserPriceGroup())
        modelService.saveAll(esiWd2GroupPrice, esiLicense)

        def thlApp = sampleDataCreator.createApp("AA_MIGRATION_THL", "aa.thl.MigrationOrderCreationServiceISpec", developer, AA_PRODUCT_CATALOG, ONLINE, APPROVED)
        thlLicense = sampleDataCreator.createSubscriptionAppLicense(thlApp)
        thlLicense.setRuntime(subsUnlimited)
        thlWd2GroupPrice = sampleDataCreator.addPriceRow(thlLicense, 500.0, eur, 0)
        thlWd2GroupPrice.setBillingPriceCode("WD0002_${thlLicense.code}")
        thlWd2GroupPrice.setUg(wd2Group.getUserPriceGroup())
        modelService.saveAll(thlWd2GroupPrice, thlLicense)

        def contractMigrationProcess = ContractMigrationProcessBuilder.generate()
                .withCode("CONTRACT_MIGRATION_PROCESS_1")
                .withCmtProcessExecutionId("1")
                .withOwnerCompanyId(buyerCompany.getAaExternalCustomerId())
                .withCmtProcessExecutionCreationDate(DateUtil.now())
                .withSourceContractMigrationRequestCode("REQUEST_1")
                .withMigrationMode(EXECUTE)
                .buildIntegrationInstance()

        def esiMigrationContract = MigrationContractBuilder.generate()
                .withCode("ESI_MIGRATION_CONTRACT_1")
                .withCmtId("100")
                .withLicenseType("AZCU")
                .withUmpContractId("*********")
                .withDistributor(AA_DISTRIBUTOR_COMPANY_UID)
                .withMaterialId("esi-material-id")
                .withUniqueBundleIdentifier("esi-bundle-id")
                .withStartDate(DateUtil.yesterday())
                .withEndDate(DateUtil.tomorrow())
                .withContractMigrationProcess(contractMigrationProcess)
                .buildIntegrationInstance()
        def esiMigrationContractGroup = MigrationContractGroupBuilder.generate()
                .withCode("ESI_MIGRATION_CONTRACT_GROUP_1")
                .withUniqueBundleIdentifier("esi-bundle-id")
                .withMigrationContracts(Set.of(esiMigrationContract))
                .buildIntegrationInstance()
        esiMigrationOrderEntryDraft = MigrationOrderEntryDraftBuilder.generate()
                .withCode("ESI_MIGRATION_ORDER_ENTRY_DRAFT_1")
                .withAppLicense(esiLicense)
                .withContractGroups(Set.of(esiMigrationContractGroup))
                .withQuantity(1)
                .buildIntegrationInstance()

        def thlMigrationContract = MigrationContractBuilder.generate()
                .withCode("THL_MIGRATION_CONTRACT_1")
                .withCmtId("200")
                .withLicenseType("AZCU")
                .withUmpContractId("*********")
                .withDistributor(AA_DISTRIBUTOR_COMPANY_UID)
                .withMaterialId("thl-material-id")
                .withUniqueBundleIdentifier("thl-bundle-id")
                .withStartDate(DateUtil.yesterday())
                .withEndDate(DateUtil.tomorrow())
                .withContractMigrationProcess(contractMigrationProcess)
                .buildIntegrationInstance()
        def thlMigrationContractGroup = MigrationContractGroupBuilder.generate()
                .withCode("THL_MIGRATION_CONTRACT_GROUP_1")
                .withUniqueBundleIdentifier("thl-bundle-id")
                .withMigrationContracts(Set.of(thlMigrationContract))
                .buildIntegrationInstance()
        thlMigrationOrderEntryDraft = MigrationOrderEntryDraftBuilder.generate()
                .withCode("THL_MIGRATION_ORDER_ENTRY_DRAFT_1")
                .withAppLicense(thlLicense)
                .withContractGroups(Set.of(thlMigrationContractGroup))
                .withQuantity(1)
                .buildIntegrationInstance()

        migrationOrderDraft = MigrationOrderDraftBuilder.generate()
                .withCode("MIGRATION_ORDER_DRAFT_1")
                .withCompany(buyerCompany)
                .withAaDistributor(distributorCompany)
                .withEntries(Set.of(esiMigrationOrderEntryDraft, thlMigrationOrderEntryDraft))
                .withContractMigrationProcess(contractMigrationProcess)
                .buildIntegrationInstance()

        modelService.saveAll(
                contractMigrationProcess,
                esiMigrationContract, esiMigrationContractGroup, esiMigrationOrderEntryDraft,
                thlMigrationContract, thlMigrationContractGroup, thlMigrationOrderEntryDraft,
                migrationOrderDraft
        )
    }

    def "should create an order from draft"() {
        when:
        def order = migrationOrderCreationService.createOrderFromDraft(migrationOrderDraft)

        then:
        order
        !order.getFirstInvoiceNote()
        order == migrationOrderDraft.resultingOrder
        order.company == buyerCompany
        order.aaDistributorCompany == distributorCompany
        order.user
        order.user.uid == AA_AUSTRIA1_COMPANY_INTEGRATOR_UID
        order.currency == buyerCompany.getCountry().getCurrency()
        order.entries.size() == 2
        order.status == OrderStatus.CREATED
        order.paymentStatus == PaymentStatus.PAID_RECURRING
        order.activationMode == ActivationMode.IMMEDIATE

        def esiOrderEntry = order.entries.find { it.product == esiLicense }
        esiOrderEntry
        esiOrderEntry.quantity == esiMigrationOrderEntryDraft.quantity
        esiOrderEntry.basePrice == esiLicense.getCurrentlyValidPrices().find({ price -> price.ug == null && price.getCurrency() == eur }).price // list price

        def thlOrderEntry = order.entries.find { it.product == thlLicense }
        thlOrderEntry
        thlOrderEntry.quantity == thlMigrationOrderEntryDraft.quantity
        thlOrderEntry.basePrice == thlLicense.getCurrentlyValidPrices().find({ price -> price.ug == null && price.getCurrency() == eur }).price // list price
    }

    def "given company belongs to group when create order then use group price"() {
        given:
        buyerCompany.setAaCustomerGroup(wd2Group)
        buyerCompany.setGroups(Set.of(wd2Group))
        modelService.save(buyerCompany)

        when:
        def order = migrationOrderCreationService.createOrderFromDraft(migrationOrderDraft)

        then:
        order
        order == migrationOrderDraft.resultingOrder
        !order.getFirstInvoiceNote()
        order.entries.size() == 2

        def esiOrderEntry = order.entries.find { it.product == esiLicense }
        esiOrderEntry
        esiOrderEntry.quantity == esiMigrationOrderEntryDraft.quantity
        esiOrderEntry.basePrice == esiWd2GroupPrice.getPrice()
        esiOrderEntry.billingPriceCode == esiWd2GroupPrice.getBillingPriceCode()

        def thlOrderEntry = order.entries.find { it.product == thlLicense }
        thlOrderEntry
        thlOrderEntry.quantity == thlMigrationOrderEntryDraft.quantity
        thlOrderEntry.basePrice == thlWd2GroupPrice.getPrice()
        thlOrderEntry.billingPriceCode == thlWd2GroupPrice.getBillingPriceCode()
    }

    def "given discounted product combination and group has a discount when create order then apply discount"() {
        given:
        esiLicense.setAddonThl(thlLicense.getCode())
        esiLicense.setThlGroupDiscount(['WD0002': '10'])
        thlLicense.setThlGroupDiscount(['WD0002': '20'])
        buyerCompany.setAaCustomerGroup(wd2Group)
        buyerCompany.setGroups(Set.of(wd2Group))
        modelService.saveAll(esiLicense, thlLicense, buyerCompany)

        when:
        def order = migrationOrderCreationService.createOrderFromDraft(migrationOrderDraft)

        then:
        order
        order == migrationOrderDraft.resultingOrder
        !order.getFirstInvoiceNote()
        order.entries.size() == 2

        def esiOrderEntry = order.entries.find { it.product == esiLicense }
        esiOrderEntry
        esiOrderEntry.quantity == esiMigrationOrderEntryDraft.quantity
        esiOrderEntry.billingPriceCode == esiWd2GroupPrice.getBillingPriceCode()
        esiOrderEntry.originalPrice == esiWd2GroupPrice.getPrice()
        esiOrderEntry.basePrice == esiWd2GroupPrice.getPrice() * 0.9 // Apply 10% discount
        esiOrderEntry.addOnThl == thlLicense.getCode()
        esiOrderEntry.addOnUg == wd2Group.getUid()

        def thlOrderEntry = order.entries.find { it.product == thlLicense }
        thlOrderEntry
        thlOrderEntry.quantity == thlMigrationOrderEntryDraft.quantity
        thlOrderEntry.billingPriceCode == thlWd2GroupPrice.getBillingPriceCode()
        thlOrderEntry.originalPrice == thlWd2GroupPrice.getPrice()
        thlOrderEntry.basePrice == thlWd2GroupPrice.getPrice() * 0.8 // Apply 20% discount
        thlOrderEntry.addOnUg == wd2Group.getUid()
    }

    def "given discounted product combination and group has no discount when create order then do not apply discount"() {
        given:
        esiLicense.setAddonThl(thlLicense.getCode())
        esiLicense.setThlGroupDiscount(['IDW000': '10'])
        thlLicense.setThlGroupDiscount(['IDW000': '20'])
        buyerCompany.setAaCustomerGroup(wd2Group)
        buyerCompany.setGroups(Set.of(wd2Group))
        modelService.saveAll(esiLicense, thlLicense, buyerCompany)

        when:
        def order = migrationOrderCreationService.createOrderFromDraft(migrationOrderDraft)

        then:
        order
        order == migrationOrderDraft.resultingOrder
        !order.getFirstInvoiceNote()
        order.entries.size() == 2

        def esiOrderEntry = order.entries.find { it.product == esiLicense }
        esiOrderEntry
        esiOrderEntry.quantity == esiMigrationOrderEntryDraft.quantity
        esiOrderEntry.basePrice == esiWd2GroupPrice.getPrice()
        esiOrderEntry.billingPriceCode == esiWd2GroupPrice.getBillingPriceCode()
        !esiOrderEntry.addOnThl
        !esiOrderEntry.addOnUg
        !esiOrderEntry.originalPrice

        def thlOrderEntry = order.entries.find { it.product == thlLicense }
        thlOrderEntry
        thlOrderEntry.quantity == thlMigrationOrderEntryDraft.quantity
        thlOrderEntry.basePrice == thlWd2GroupPrice.getPrice()
        thlOrderEntry.billingPriceCode == thlWd2GroupPrice.getBillingPriceCode()
        !thlOrderEntry.addOnThl
        !thlOrderEntry.addOnUg
        !thlOrderEntry.originalPrice
    }

    def "given product combination without discount when create order then do not apply any discount"() {
        given:
        esiLicense.setAddonThl(null)
        esiLicense.setThlGroupDiscount([:])
        thlLicense.setThlGroupDiscount([:])
        buyerCompany.setAaCustomerGroup(wd2Group)
        buyerCompany.setGroups(Set.of(wd2Group))
        modelService.saveAll(esiLicense, thlLicense, buyerCompany)

        when:
        def order = migrationOrderCreationService.createOrderFromDraft(migrationOrderDraft)

        then:
        order
        order == migrationOrderDraft.resultingOrder
        !order.getFirstInvoiceNote()
        order.entries.size() == 2


        def esiOrderEntry = order.entries.find { it.product == esiLicense }
        esiOrderEntry
        esiOrderEntry.quantity == esiMigrationOrderEntryDraft.quantity
        esiOrderEntry.basePrice == esiWd2GroupPrice.getPrice()
        esiOrderEntry.billingPriceCode == esiWd2GroupPrice.getBillingPriceCode()
        !esiOrderEntry.addOnThl
        !esiOrderEntry.addOnUg
        !esiOrderEntry.originalPrice

        def thlOrderEntry = order.entries.find { it.product == thlLicense }
        thlOrderEntry
        thlOrderEntry.quantity == thlMigrationOrderEntryDraft.quantity
        thlOrderEntry.basePrice == thlWd2GroupPrice.getPrice()
        thlOrderEntry.billingPriceCode == thlWd2GroupPrice.getBillingPriceCode()
        !thlOrderEntry.addOnThl
        !thlOrderEntry.addOnUg
        !thlOrderEntry.originalPrice
    }

    def "given indirect sales conditions when create order then first invoice note is set"() {
        given:
        buyerCompany.setAaCustomerGroup(wd2Group)
        buyerCompany.setGroups(Set.of(wd2Group))
        migrationOrderDraft.setManagedAccount(buyerCompany)
        modelService.saveAll(buyerCompany, migrationOrderDraft)

        when:
        def order = migrationOrderCreationService.createOrderFromDraft(migrationOrderDraft)

        then:
        order
        order == migrationOrderDraft.resultingOrder
        order.getFirstInvoiceNote().contains(buyerCompany.getAaExternalCustomerId())
        order.entries.size() == 2

        def esiOrderEntry = order.entries.find { it.product == esiLicense }
        esiOrderEntry
        esiOrderEntry.quantity == esiMigrationOrderEntryDraft.quantity
        esiOrderEntry.basePrice == esiWd2GroupPrice.getPrice()
        esiOrderEntry.billingPriceCode == esiWd2GroupPrice.getBillingPriceCode()

        def thlOrderEntry = order.entries.find { it.product == thlLicense }
        thlOrderEntry
        thlOrderEntry.quantity == thlMigrationOrderEntryDraft.quantity
        thlOrderEntry.basePrice == thlWd2GroupPrice.getPrice()
        thlOrderEntry.billingPriceCode == thlWd2GroupPrice.getBillingPriceCode()
    }

    def "given discontinued product contracts when create order then buyer contracts has correct end date"() {
        given:
        esiLicense.setAvailabilityStatus(UNPUBLISHED)
        thlLicense.setAvailabilityStatus(UNPUBLISHED)
        esiLicense.setThlGroupDiscount([:])
        thlLicense.setThlGroupDiscount([:])
        buyerCompany.setAaCustomerGroup(wd2Group)
        buyerCompany.setGroups(Set.of(wd2Group))
        modelService.saveAll(esiLicense, thlLicense, buyerCompany)

        when:
        def order = migrationOrderCreationService.createOrderFromDraft(migrationOrderDraft)

        then:
        order
        order == migrationOrderDraft.resultingOrder
        order.entries.size() == 2


        def esiOrderEntry = order.entries.find { it.product == esiLicense }
        esiOrderEntry
        esiOrderEntry.quantity == esiMigrationOrderEntryDraft.quantity
        esiOrderEntry.basePrice == esiWd2GroupPrice.getPrice()
        esiOrderEntry.billingPriceCode == esiWd2GroupPrice.getBillingPriceCode()
        !esiOrderEntry.addOnThl
        !esiOrderEntry.addOnUg
        !esiOrderEntry.originalPrice
        def esiEntryBuyerContractEndDates = esiOrderEntry.getBuyerContracts().stream().map { it.endDate }.toList()
        esiEntryBuyerContractEndDates.size() == 1
        esiEntryBuyerContractEndDates[0] == esiMigrationOrderEntryDraft.getContractGroups().first().getMigrationContracts().first().getEndDate()

        def thlOrderEntry = order.entries.find { it.product == thlLicense }
        thlOrderEntry
        thlOrderEntry.quantity == thlMigrationOrderEntryDraft.quantity
        thlOrderEntry.basePrice == thlWd2GroupPrice.getPrice()
        thlOrderEntry.billingPriceCode == thlWd2GroupPrice.getBillingPriceCode()
        !thlOrderEntry.addOnThl
        !thlOrderEntry.addOnUg
        !thlOrderEntry.originalPrice
        def thlEntryBuyerContractEndDates = thlOrderEntry.getBuyerContracts().stream().map { it.endDate }.toList()
        thlEntryBuyerContractEndDates.size() == 1
        thlEntryBuyerContractEndDates[0] == thlMigrationOrderEntryDraft.getContractGroups().first().getMigrationContracts().first().getEndDate()
    }

    RuntimeModel findRuntime(String runtimeCode) {
        def runtime = new RuntimeModel()
        runtime.setCode(runtimeCode)
        flexibleSearchService.getModelByExample(runtime)
    }
}
