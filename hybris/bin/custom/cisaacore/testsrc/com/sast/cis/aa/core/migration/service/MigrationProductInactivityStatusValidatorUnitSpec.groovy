package com.sast.cis.aa.core.migration.service

import com.sast.cis.aa.core.model.MigrationOrderDraftModel
import com.sast.cis.aa.core.model.MigrationOrderEntryDraftModel
import com.sast.cis.core.model.AppLicenseModel
import de.hybris.bootstrap.annotations.UnitTest
import de.hybris.platform.testframework.JUnitPlatformSpecification

@UnitTest
class MigrationProductInactivityStatusValidatorUnitSpec extends JUnitPlatformSpecification {

    MigrationAppLicenseService migrationAppLicenseService
    MigrationProductInactivityStatusValidator validator

    def setup() {
        migrationAppLicenseService = Mock()
        validator = new MigrationProductInactivityStatusValidator(migrationAppLicenseService)
    }

    def "validateConsistentInactivityStatus returns true when all products are inactive"() {
        given:
        def draft = Mock(MigrationOrderDraftModel)
        def entry1 = Mock(MigrationOrderEntryDraftModel)
        def entry2 = Mock(MigrationOrderEntryDraftModel)
        def appLicense1 = Mock(AppLicenseModel)
        def appLicense2 = Mock(AppLicenseModel)

        draft.getEntries() >> [entry1, entry2]
        draft.isInactiveProductMigration() >> true
        draft.getCode() >> "TEST-DRAFT"

        // Both entries are considered inactive
        entry1.getAppLicense() >> appLicense1
        entry2.getAppLicense() >> appLicense2

        migrationAppLicenseService.isAppLicenseActive(appLicense1) >> false
        migrationAppLicenseService.isAppLicenseActive(appLicense2) >> false

        when:
        def result = validator.validateConsistentInactivityStatus(draft)

        then:
        result
    }

    def "validateConsistentInactivityStatus returns true when all products are not inactive"() {
        given:
        def draft = Mock(MigrationOrderDraftModel)
        def entry1 = Mock(MigrationOrderEntryDraftModel)
        def entry2 = Mock(MigrationOrderEntryDraftModel)
        def appLicense1 = Mock(AppLicenseModel)
        def appLicense2 = Mock(AppLicenseModel)

        draft.getEntries() >> [entry1, entry2]
        draft.isInactiveProductMigration() >> false
        draft.getCode() >> "TEST-DRAFT"

        // Both entries are not inactive
        entry1.getAppLicense() >> appLicense1
        entry2.getAppLicense() >> appLicense2

        migrationAppLicenseService.isAppLicenseActive(appLicense1) >> true
        migrationAppLicenseService.isAppLicenseActive(appLicense2) >> true

        when:
        def result = validator.validateConsistentInactivityStatus(draft)

        then:
        result
    }

    def "validateConsistentInactivityStatus returns false when products have mixed inactivity statuses"() {
        given:
        def draft = Mock(MigrationOrderDraftModel)
        def entry1 = Mock(MigrationOrderEntryDraftModel)
        def entry2 = Mock(MigrationOrderEntryDraftModel)
        def appLicense1 = Mock(AppLicenseModel)
        def appLicense2 = Mock(AppLicenseModel)

        draft.getEntries() >> [entry1, entry2]
        draft.isInactiveProductMigration() >> true
        draft.getCode() >> "TEST-DRAFT"

        // Configure entry1 to be inactive
        entry1.getAppLicense() >> appLicense1
        migrationAppLicenseService.isAppLicenseActive(appLicense1) >> false

        // Configure entry2 to be NOT inactive
        entry2.getAppLicense() >> appLicense2
        migrationAppLicenseService.isAppLicenseActive(appLicense2) >> true

        when:
        def result = validator.validateConsistentInactivityStatus(draft)

        then:
        !result
    }

    def "validateConsistentInactivityStatus returns false for empty entries"() {
        given:
        def draft = Mock(MigrationOrderDraftModel)
        draft.getEntries() >> []
        draft.getCode() >> "TEST-DRAFT"

        when:
        def result = validator.validateConsistentInactivityStatus(draft)

        then:
        !result
    }

    def "validateConsistentInactivityStatus handles null AppLicense gracefully"() {
        given:
        def draft = Mock(MigrationOrderDraftModel)
        def entry = Mock(MigrationOrderEntryDraftModel)
        draft.getEntries() >> [entry]
        draft.isInactiveProductMigration() >> true
        draft.getCode() >> "TEST-DRAFT"

        entry.getAppLicense() >> null
        migrationAppLicenseService.isAppLicenseActive(null) >> false

        when:
        def result = validator.validateConsistentInactivityStatus(draft)

        then:
        result
    }
}
