package com.sast.cis.aa.core.reports.service

import com.sast.cis.aa.core.model.AaDistributorCommissionReportModel
import com.sast.cis.aa.core.reports.dto.*
import de.hybris.bootstrap.annotations.UnitTest
import de.hybris.platform.servicelayer.media.MediaService
import de.hybris.platform.servicelayer.model.ModelService
import de.hybris.platform.testframework.JUnitPlatformSpecification
import org.apache.poi.xssf.usermodel.XSSFRow
import org.apache.poi.xssf.usermodel.XSSFWorkbook
import org.apache.poi.xssf.usermodel.XSSFWorkbookFactory
import org.junit.Test

import static com.sast.cis.aa.core.constants.AaDistributorCommissionReportConstants.DISTRIBUTOR_COMMISSION_COLUMNS;


@UnitTest
class CommissionReportExportServiceUnitSpec extends JUnitPlatformSpecification {

    private CommissionReportService commissionReportService = Mock()
    private ModelService modelService = Mock()
    private MediaService mediaService = Mock()
    private AaDistributorCommissionReportModel reportModel = Mock()

    private CommissionReportExportService commissionReportExportService
    private Date date = new Date()

    private AaCommissionReportRow commissionReportRow
    private AaAddress aaAddress

    // report record data
    private String CITY = "test city"
    private String HOUSE_NUMBER = "1"
    private String COUNTRY_CODE = "AT"
    private String ZIP_CODE = "5163"
    private String REGION = ""
    private String STREET = "Test Street"
    private String BILLING_EMAIL = "<EMAIL>"
    private String BPMD_ID = "bpmd_id_123"
    private String KP1_ID = "kp1_id"
    private String COMMUNICATION_LANG = "DE"
    private String BUYER_COMPANY_NAME = "test Buyer"
    private Set<String> CUSTOMER_GROUPS = Set.of("IDW", "BSC")
    private String MARKETPLACE_ID = "marketplace_id_123"
    private BigDecimal COMMISSION = 15.0
    private String DISTRIBUTOR_NAME = "Distributor name"
    private String DISTRIBUTOR_ID = "distributor_id_1"
    private Date INVOICE_DATE = new Date()
    private String INVOICE_NUMBER = "123"
    private String ORDER_ID = "order_123"
    private String PAYMENT_STATUS = "PAID"
    private String CURRENCY = "EUR"
    private BigDecimal PRICE = 100.0
    private String PRODUCT_NAME = "product_name_1"
    private String PRODUCT_ID = "product_id_1"
    private int QUANTITY = 1
    private int INVOICE_ITEM_POSITION = 1

    void setup() {

        aaAddress = AaAddress.builder()
                .city(CITY)
                .houseNumber(HOUSE_NUMBER)
                .countryIsoCode(COUNTRY_CODE)
                .postalCode(ZIP_CODE)
                .region(REGION)
                .street(STREET)
                .build()

        SoldToParty soldToParty = SoldToParty.builder()
                .aaAddress(aaAddress)
                .billingEmail(BILLING_EMAIL)
                .bpmdId(BPMD_ID)
                .kp1Id(KP1_ID)
                .communicationLanguageIsoCode(COMMUNICATION_LANG)
                .companyName(BUYER_COMPANY_NAME)
                .customerGroups(CUSTOMER_GROUPS)
                .marketplaceId(MARKETPLACE_ID)
                .build()

        AaReportDistributor distributor = AaReportDistributor.builder()
                .companyName(DISTRIBUTOR_NAME)
                .id(DISTRIBUTOR_ID)
                .countryIsocode(COUNTRY_CODE)
                .build()

        AaInvoice invoice = AaInvoice.builder()
                .invoiceDate(INVOICE_DATE)
                .invoiceNumber(INVOICE_NUMBER)
                .orderId(ORDER_ID)
                .paymentStatus(PAYMENT_STATUS)
                .build()

        AaInvoiceItem invoiceItem = AaInvoiceItem.builder()
                .currency(CURRENCY)
                .price(PRICE)
                .productName(PRODUCT_NAME)
                .sellerProductId(PRODUCT_ID)
                .quantity(QUANTITY)
                .position(INVOICE_ITEM_POSITION)
                .build()

        commissionReportRow = AaCommissionReportRow.builder()
                .buyerCompany(soldToParty)
                .commission(COMMISSION)
                .distributor(distributor)
                .invoice(invoice)
                .invoiceItem(invoiceItem)
                .build()

        reportModel.getStartDate() >> date
        reportModel.getEndDate() >> date
    }

    void verifyHeaders(XSSFWorkbook workbook) {
        XSSFRow headerRow = workbook.getSheetAt(0).getRow(0)
        verifyAll (headerRow) {
            headerRow.size() == 20
            for (int cellNum = 0; cellNum < headerRow.getLastCellNum(); cellNum++) {
                assert headerRow.getCell(cellNum).getStringCellValue() == DISTRIBUTOR_COMMISSION_COLUMNS.get(cellNum).cellName()
            }
        }
    }

    void verifyDataRow(XSSFWorkbook workbook) {
        XSSFRow dataRow = workbook.getSheetAt(0).getRow(1)
        verifyAll (dataRow) {
            dataRow.getCell(0).getStringCellValue() == DISTRIBUTOR_NAME
            dataRow.getCell(1).getStringCellValue() == COUNTRY_CODE
            dataRow.getCell(2).getStringCellValue() == KP1_ID
            dataRow.getCell(3).getStringCellValue() == BUYER_COMPANY_NAME
            dataRow.getCell(4).getStringCellValue() == ORDER_ID
            dataRow.getCell(5).getStringCellValue() == aaAddress.street()
            dataRow.getCell(6).getStringCellValue() == aaAddress.houseNumber()
            dataRow.getCell(7).getStringCellValue() == aaAddress.postalCode()
            dataRow.getCell(8).getStringCellValue() == aaAddress.city()
            dataRow.getCell(9).getStringCellValue() == INVOICE_NUMBER
            dataRow.getCell(10).getNumericCellValue() == INVOICE_ITEM_POSITION + 1
            dataRow.getCell(11).getDateCellValue() == INVOICE_DATE
            dataRow.getCell(12).getStringCellValue() == PRODUCT_ID
            dataRow.getCell(13).getStringCellValue() == PRODUCT_NAME
            dataRow.getCell(14).getNumericCellValue() == QUANTITY
            dataRow.getCell(15).getNumericCellValue() == PRICE.doubleValue()
            dataRow.getCell(16).getStringCellValue() == CURRENCY
            dataRow.getCell(17).getNumericCellValue() == COMMISSION.doubleValue()
            dataRow.getCell(18).getStringCellValue() == CURRENCY
            dataRow.getCell(19).getStringCellValue() == PAYMENT_STATUS
        }
    }

    @Test
    void 'create report and validate records'() {
        given:
        commissionReportExportService =
                new CommissionReportExportService(modelService, mediaService, commissionReportService)

        when:
        ByteArrayOutputStream report = commissionReportExportService.createExcelReport(reportModel)

        then:
        commissionReportService.getAllAaCommissionReportRows(date, date) >> List.of(commissionReportRow)

        XSSFWorkbook workbook = new XSSFWorkbookFactory().create(new ByteArrayInputStream(report.toByteArray()))

        workbook.getSheetAt(0).getPhysicalNumberOfRows() == 2
        verifyHeaders(workbook)
        verifyDataRow(workbook)
    }
}
