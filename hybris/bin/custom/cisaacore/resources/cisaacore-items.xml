<?xml version="1.0" encoding="ISO-8859-1"?>
<items 	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
			xsi:noNamespaceSchemaLocation="items.xsd">

	<collectiontypes>
		<collectiontype code="ContentModuleIdList" elementtype="java.lang.String" autocreate="true" type="list"/>
		<collectiontype code="SellerContactCollection" elementtype="SellerContact" autocreate="true" type="set"/>
	</collectiontypes>

	<enumtypes>
		<enumtype code="MigrationOrderDraftStatus" autocreate="true" generate="true">
			<value code="READY" />
			<value code="IN_PROGRESS" />
			<value code="COMPLETED" />
		</enumtype>
		<enumtype code="MigrationMode" autocreate="true" generate="true">
			<value code="SIMULATE" /> <!-- Simulate a migration. Run all validation checks without making any changes. -->
			<value code="EXECUTE" /> <!-- Performs the full migration. -->
		</enumtype>
	</enumtypes>
	<relations>
		<relation code="App2Bolton" localized="false">
			<description>Relation between App and App to represent the bolt-on set</description>
			<deployment table="app2boltons" typecode="26100"/>
			<sourceElement type="App" qualifier="boltons" cardinality="many" collectiontype="list">
				<modifiers read="true" write="true" search="true" optional="true"/>
			</sourceElement>
			<targetElement type="App" qualifier="parentApp" cardinality="many" collectiontype="list"/>
		</relation>
		<relation code="App2Bom" localized="false">
			<description>Relation between App and Bill of Material to represent the content of the purchased App(paket)</description>
			<deployment table="app2bom" typecode="26101"/>
			<sourceElement type="App" qualifier="packets" cardinality="many" collectiontype="list">
				<modifiers read="true" write="true" search="true" optional="true"/>
			</sourceElement>
			<targetElement type="Material" qualifier="boms" cardinality="many" collectiontype="list">
				<modifiers read="true" write="true" search="true" optional="true"/>
			</targetElement>
		</relation>
		<relation code="Bundle2Group" localized="false">
			<description>Relation between App and App to represent the group of bundles</description>
			<sourceElement type="App" qualifier="bundles" cardinality="many" collectiontype="list">
				<modifiers read="true" write="true" search="true" optional="true" partof="true"/>
			</sourceElement>
			<targetElement type="App" qualifier="packetProduct" cardinality="one"/>
		</relation>
		<relation code="Product2ContentModule" localized="false">
			<description>Relation between Product and Content Modules to represent the licenses to issue</description>
			<deployment table="product2contentmodule" typecode="26105"/>
			<sourceElement type="Product" qualifier="products" cardinality="many" collectiontype="list">
				<modifiers read="true" write="true" search="true" optional="true"/>
			</sourceElement>
			<targetElement type="ContentModule" qualifier="contentModules" cardinality="many" collectiontype="list">
				<modifiers read="true" write="true" search="true" optional="true"/>
			</targetElement>
		</relation>
		<relation code="Material2EulaContainer" localized="false">
			<description>Country specific End User License Agreements applicable to Apps containing assigned Materials</description>
			<deployment table="material2eulacontainer" typecode="26108"/>
			<sourceElement type="Material" qualifier="materials" cardinality="many" collectiontype="set">
				<modifiers read="true" write="true" search="true" optional="true"/>
			</sourceElement>
			<targetElement type="EulaContainer" qualifier="eulaContainers" cardinality="many" collectiontype="set">
				<modifiers read="true" write="true" search="true" optional="true"/>
			</targetElement>
		</relation>
		<relation code="MigrationContracts2ContractMigrationProcess" localized="false">
			<description>Contracts that are part of a Contract Migration Process</description>
			<sourceElement type="ContractMigrationProcess" qualifier="contractMigrationProcess" cardinality="one">
				<modifiers read="true" write="false" search="true" optional="false" initial="true"/>
			</sourceElement>
			<targetElement type="MigrationContract" qualifier="migrationContracts" cardinality="many" collectiontype="set">
				<modifiers read="true" write="true" search="false" partof="true" optional="true"/>
			</targetElement>
		</relation>

		<relation code="MigrationOrderDrafts2ContractMigrationProcess" localized="false">
			<description>
				Relation between MigrationOrderDrafts and the ContractMigrationProcess that contains the Migration Contracts
			</description>
			<sourceElement type="MigrationOrderDraft" qualifier="migrationOrderDrafts" cardinality="many" collectiontype="set">
				<modifiers read="true" write="true" search="false" partof="true" optional="true"/>
			</sourceElement>
			<targetElement type="ContractMigrationProcess" qualifier="contractMigrationProcess" cardinality="one">
				<modifiers read="true" write="false" search="true" optional="false" initial="true"/>
			</targetElement>
		</relation>

		<relation code="MigrationOrderDraft2MigrationOrderEntryDrafts" localized="false">
			<sourceElement type="MigrationOrderDraft" qualifier="migrationOrderDraft" cardinality="one">
				<modifiers read="true" write="true" search="true" optional="true"/>
			</sourceElement>
			<targetElement type="MigrationOrderEntryDraft" qualifier="entries" cardinality="many" collectiontype="set">
				<modifiers read="true" write="true" search="true" optional="false" partof="true"/>
			</targetElement>
		</relation>

		<relation code="MigrationOrderEntryDraft2MigrationContractGroups" localized="false">
			<sourceElement type="MigrationOrderEntryDraft" qualifier="migrationOrderEntryDraft" cardinality="one">
				<modifiers read="true" write="true" search="true" optional="true"/>
			</sourceElement>
			<targetElement type="MigrationContractGroup" qualifier="contractGroups" cardinality="many" collectiontype="set">
				<modifiers read="true" write="true" search="true" optional="false" partof="true"/>
			</targetElement>
		</relation>

		<relation code="MigrationContractGroups2MigrationContracts" localized="false">
			<deployment table="mcontractgroup2mcontract" typecode="26120"/>
			<sourceElement type="MigrationContractGroup" cardinality="many" collectiontype="set" navigable="false"/>
			<targetElement type="MigrationContract" qualifier="migrationContracts" cardinality="many" collectiontype="set">
				<modifiers read="true" write="true" search="true" optional="false"/>
			</targetElement>
		</relation>

        <relation code="MigrationOrderDraft2OrderMigrationBusinessProcess" localized="false">
            <sourceElement type="MigrationOrderDraft" cardinality="one" qualifier="migrationOrderDraft"/>
            <targetElement type="OrderMigrationBusinessProcess" cardinality="many" qualifier="businessProcesses" collectiontype="set"/>
        </relation>
	</relations>

	<itemtypes>
		<itemtype code="Product" autocreate="false" generate="false">
			<attributes>
				<attribute qualifier="sellerProductId" type="java.lang.String">
					<modifiers read="true" write="true" search="true" optional="true" partof="true"/>
					<persistence type="property"/>
				</attribute>
			</attributes>
			<indexes>
				<index name="seller_productId_idx" >
					<key attribute="sellerProductId"/>
				</index>
			</indexes>
		</itemtype>

		<itemtype code="App" autocreate="false" generate="false">
			<attributes>
				<attribute qualifier="productNote" type="localized:java.lang.String">
					<modifiers read="true" write="true" search="true" optional="true"/>
					<persistence type="property"/>
				</attribute>
			</attributes>
		</itemtype>

		<itemtype code="Material" autocreate="true" generate="true" extends="GenericItem">
			<deployment table="material" typecode="26102" />
			<attributes>
				<attribute qualifier="code" type="java.lang.String">
					<defaultvalue/>
					<description>Material code</description>
					<modifiers optional="false" unique="true"/>
					<persistence type="property" />
				</attribute>
				<attribute qualifier="name" type="localized:java.lang.String">
					<defaultvalue/>
					<description>Localized name of the BOM element.</description>
					<persistence type="property"/>
					<modifiers optional="false"/>
				</attribute>
				<attribute qualifier="description" type="localized:java.lang.String">
					<description>Localized description of the BOM element</description>
					<persistence type="property">
                        <columntype database="oracle">
                            <value>CLOB</value>
                        </columntype>
                        <columntype database="sap">
                            <value>NCLOB</value>
                        </columntype>
                        <columntype database="mysql">
                            <value>MEDIUMTEXT</value>
                        </columntype>
                        <columntype database="sqlserver">
                            <value>nvarchar(max)</value>
                        </columntype>
                        <columntype database="hsqldb">
                            <value>LONGVARCHAR</value>
                        </columntype>
                        <columntype>
                            <value>HYBRIS.LONG_STRING</value>
                        </columntype>
                    </persistence>
				</attribute>
				<attribute qualifier="videoUrl" type="localized:java.lang.String">
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                    <description>This field will contain video URL of the BOM element.</description>
                </attribute>
			</attributes>
			<indexes>
				<index name="bomCode_idx" unique="true">
					<key attribute="code"/>
				</index>
			</indexes>
		</itemtype>

		<itemtype code="PriceDraft" autocreate="false" generate="false">
			<attributes>
				<attribute qualifier="appLicenseCode" type="java.lang.String">
					<description>Code of the AppLicense that owns the PriceDraft</description>
					<persistence type="property"/>
					<modifiers read="true" write="true" search="true" optional="true"/>
				</attribute>
				<attribute qualifier="promotion" type="java.lang.Boolean">
					<description>Flag which signifies whether this PriceDraft has a special offer or not</description>
					<persistence type="property"/>
					<defaultvalue>java.lang.Boolean.FALSE</defaultvalue>
					<modifiers read="true" write="true" search="true" optional="true"/>
				</attribute>
			</attributes>
		</itemtype>

		<itemtype code="IotCompany" autocreate="false" generate="false">
			<attributes>
				<attribute qualifier="aaExternalCustomerId" type="java.lang.String">
					<description>Company ID used in AA external systems.</description>
					<persistence type="property"/>
					<modifiers read="true" write="true" search="true" optional="true"/>
				</attribute>
				<attribute qualifier="aaDistributor" type="AaDistributor">
					<description>Currently assigned AA distributor</description>
					<persistence type="property"/>
					<modifiers read="true" write="true" search="true" optional="true" partof="true"/>
				</attribute>
				<attribute qualifier="defaultAaDistributor" type="AaDistributorCompany">
					<description>Default distributor for this company</description>
					<persistence type="property"/>
					<modifiers read="true" write="true" search="true" optional="true"/>
				</attribute>
				<attribute qualifier="aaCustomerGroup" type="UserGroup">
					<persistence type="property"/>
					<modifiers read="true" write="true" search="true" optional="true" removable="false"/>
				</attribute>
				<attribute qualifier="sellerContacts" type="SellerContactCollection">
					<persistence type="property"/>
					<modifiers read="true" write="true" search="true" optional="true"/>
				</attribute>
				<attribute qualifier="licensingEmail" type="java.lang.String">
					<persistence type="property"/>
					<modifiers read="true" write="true" search="true" optional="true"/>
				</attribute>
				<attribute qualifier="aaImported" type="java.lang.Boolean">
					<description>
						Indicates if the company is imported, as opposed to being created via a regular onboarding process.
						Companies created as part of the AA Migration process are considered imported.
					</description>
					<persistence type="property"/>
					<defaultvalue>java.lang.Boolean.FALSE</defaultvalue>
					<modifiers read="true" write="true" search="true" optional="true"/>
				</attribute>
			</attributes>
		</itemtype>

		<itemtype code="AbstractOrder" autocreate="false" generate="false">
			<attributes>
				<attribute qualifier="aaDistributorCompany" type="AaDistributorCompany">
					<description>Distributor for this order</description>
					<persistence type="property"/>
					<modifiers read="true" write="true" search="true" optional="true"/>
				</attribute>
			</attributes>
		</itemtype>

		<itemtype code="AaDistributor" autocreate="true" generate="true" extends="GenericItem" deprecatedSince="2023-09">
			<deployment table="aa_distributor" typecode="26103" />
			<attributes>
				<attribute qualifier="umpDistributorId" type="java.lang.String">
					<description>UMP Distributor ID for AA distributor</description>
					<persistence type="property"/>
					<modifiers read="true" write="true" search="true" optional="true"/>
				</attribute>
				<attribute qualifier="distributorCompanyName" type="java.lang.String">
					<description>Company Name of the AA distributor</description>
					<persistence type="property"/>
					<modifiers read="true" write="true" search="true" optional="true"/>
				</attribute>
			</attributes>
		</itemtype>

		<itemtype code="ContentModule" autocreate="true" generate="true" extends="GenericItem">
			<description>
				Content Module. The AA products consist of one or more Content Modules.
				These are sent in the Order message to the License Management Portal, which then uses them to issue licences in the UPM system.
				Some of these content modules correspond to Materials, while others correspond to the Product themselves.
				The Content Modules are to be used only for license management purposes.
				In the Store (e.g. in the PDP or in the product comparison feature) the Materials are used.
			</description>
			<deployment table="contentmodule" typecode="26104" />
			<attributes>
				<attribute qualifier="code" type="java.lang.String">
					<description>ContentModule code</description>
					<persistence type="property" />
					<modifiers read="true" write="false" search="true" optional="false" initial="true" unique="true"/>
				</attribute>
				<attribute qualifier="name" type="java.lang.String">
					<description>Name of the ContentModule</description>
					<persistence type="property"/>
					<modifiers read="true" write="true" search="true" optional="true"/>
				</attribute>
				<attribute qualifier="containerType" type="java.lang.String">
					<persistence type="property"/>
					<modifiers read="true" write="true" search="true" optional="true"/>
				</attribute>
				<attribute qualifier="lmpModuleCode" type="java.lang.String">
					<persistence type="property"/>
					<modifiers read="true" write="true" search="true" optional="true"/>
				</attribute>
			</attributes>
			<indexes>
				<index name="content_module_unique_idx" unique="true">
					<key attribute="code"/>
				</index>
			</indexes>
		</itemtype>

		<itemtype code="RuntimeContentModule" autocreate="true" generate="true" extends="GenericItem">
			<description>
				Content Module for Runtime. Runtime specific Content Module details.
				Certain details about Content Modules of a product are runtime specific. Depending on the runtime different details have
				to be sent to the License Management Portal.
			</description>
			<deployment table="runtimecontentmodule" typecode="26106" />
			<attributes>
				<attribute qualifier="runtime" type="Runtime">
					<persistence type="property"/>
					<modifiers read="true" write="false" search="true" optional="false" initial="true"/>
				</attribute>
				<attribute qualifier="contentModule" type="ContentModule">
					<persistence type="property"/>
					<modifiers read="true" write="false" search="true" optional="false" initial="true"/>
				</attribute>
				<attribute qualifier="contentModuleIds" type="ContentModuleIdList">
					<persistence type="property"/>
					<modifiers read="true" write="true" search="true" optional="true" />
				</attribute>
			</attributes>
			<indexes>
				<index name="runtime_content_module_unique_idx" unique="true">
					<key attribute="runtime"/>
					<key attribute="contentModule"/>
				</index>
			</indexes>
		</itemtype>

		<itemtype code="GroupPriceUpdateProcess" autocreate="true" generate="true" extends="BusinessProcess">
			<description>Process related to Group Prices update</description>
			<attributes>
				<attribute type="AppLicense" qualifier="appLicense">
					<persistence type="property"/>
				</attribute>
				<attribute type="SyncItemCronJob" qualifier="syncCronJob">
					<persistence type="property"/>
				</attribute>
			</attributes>
		</itemtype>

		<itemtype code="ListPriceUpdateProcess" autocreate="true" generate="true" extends="BusinessProcess">
			<description>Process related to list price update</description>
			<attributes>
				<attribute type="AppLicense" qualifier="appLicense">
					<persistence type="property"/>
				</attribute>
				<attribute type="SyncItemCronJob" qualifier="syncCronJob">
					<persistence type="property"/>
				</attribute>
			</attributes>
		</itemtype>

		<itemtype code="SellerContact" autocreate="true" generate="true" extends="GenericItem">
			<description>Seller company contact for each country</description>
			<deployment table="sellercontact" typecode="26111"/>
			<attributes>
				<attribute type="Country" qualifier="country">
					<persistence type="property"/>
					<modifiers read="true" write="false" search="true" optional="false" initial="true" unique="true"/>
				</attribute>
				<attribute type="java.lang.String" qualifier="name">
					<persistence type="property"/>
				</attribute>
				<attribute type="java.lang.String" qualifier="email">
					<persistence type="property"/>
				</attribute>
				<attribute type="java.lang.String" qualifier="phone">
					<persistence type="property"/>
				</attribute>
			</attributes>
			<indexes>
				<index name="sellercontact_unique_idx" unique="true">
					<key attribute="country"/>
				</index>
			</indexes>
		</itemtype>

		<itemtype code="Country" autocreate="false" generate="false">
			<attributes>
				<attribute qualifier="aaDistributorCommission" type="java.math.BigDecimal">
					<description>Distributor commission (in percentage from 0% to 100%) applied for the sales by distributors for the attached country.
					</description>
					<persistence type="property"/>
					<modifiers read="true" write="true" search="true" optional="true"/>
				</attribute>
			</attributes>
		</itemtype>

		<itemtype code="AaDistributorCommissionReport" extends="CatalogUnawareMedia">
			<attributes>
				<attribute qualifier="startDate" type="java.util.Date">
					<modifiers read="true" write="true" search="true" optional="true"/>
					<persistence type="property"/>
				</attribute>
				<attribute qualifier="endDate" type="java.util.Date">
					<modifiers read="true" write="true" search="true" optional="true"/>
					<persistence type="property"/>
				</attribute>
			</attributes>
		</itemtype>

		<itemtype code="DistributorSyncCronJob" extends="CronJob"/>
		<itemtype code="UpdateChargePlanCronJob" extends="CronJob"/>

		<itemtype code="PriceRow" autocreate="false" generate="false">
			<description>Extending the PriceRow type from core with additional attributes.</description>
			<attributes>
				<attribute qualifier="specialOffer" type="java.lang.Boolean">
					<persistence type="property"/>
					<defaultvalue>java.lang.Boolean.FALSE</defaultvalue>
					<modifiers read="true" write="true" search="true" optional="true"/>
				</attribute>
			</attributes>
		</itemtype>

		<itemtype code="AbstractOrderEntry" autocreate="false" generate="false">
			<attributes>
				<attribute qualifier="specialOffer" type="java.lang.Boolean">
					<defaultvalue>java.lang.Boolean.FALSE</defaultvalue>
					<persistence type="property"/>
					<modifiers read="true" write="true" search="true" optional="true"/>
				</attribute>
			</attributes>
		</itemtype>

        <itemtype code="PriceListUrl" autocreate="true" generate="true" extends="GenericItem">
            <description>Country specific price list URL</description>
            <deployment table="pricelisturl" typecode="26112"/>
            <attributes>
                <attribute qualifier="country" type="Country">
                    <description>Country the URL applies to</description>
                    <persistence type="property"/>
                    <modifiers read="true" write="false" search="true" optional="false" initial="true"/>
                </attribute>
                <attribute qualifier="url" type="java.lang.String">
                    <description>URL for accessing the price list</description>
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="false" initial="true"/>
                </attribute>
            </attributes>
            <indexes>
                <index name="price_list_url_unique_idx" unique="true">
                    <key attribute="country"/>
                </index>
            </indexes>
        </itemtype>

		<itemtype code="ContractMigrationProcess">
			<deployment table="contractmigrationprocess" typecode="26113"/>
			<attributes>
				<attribute qualifier="code" type="java.lang.String">
					<description>Contract Migration Identifier</description>
					<persistence type="property"/>
					<modifiers read="true" write="true" search="true" optional="false" initial="true" unique="true"/>
				</attribute>
				<attribute qualifier="sourceContractMigrationRequestCode" type="java.lang.String">
					<description>Identifier of the Source Contract Migration Request</description>
					<persistence type="property"/>
					<modifiers read="true" write="false" search="true" optional="false" initial="true"/>
				</attribute>
				<attribute qualifier="cmtProcessExecutionId" type="java.lang.String">
					<description>
						Identifier of the corresponding CMT migration process execution.
					</description>
					<persistence type="property"/>
					<modifiers read="true" write="true" search="true" optional="false" initial="true"/>
				</attribute>
				<attribute qualifier="cmtProcessExecutionCreationDate" type="java.util.Date">
					<description>
						Creation date of the corresponding CMT migration process execution.
					</description>
					<modifiers read="true" write="true" search="true" optional="true"/>
					<persistence type="property"/>
				</attribute>
				<attribute qualifier="ownerCompanyId" type="java.lang.String">
					<description>
						ESI Customer ID of the company that is the owner of the migration contracts.
					</description>
					<persistence type="property"/>
					<modifiers read="true" write="true" search="true" optional="false" initial="true"/>
				</attribute>
				<attribute qualifier="countryIsocode" type="java.lang.String">
					<description>2 character isocode of the companies country</description>
					<modifiers read="true" write="true" search="true" optional="true"/>
					<persistence type="property"/>
				</attribute>
				<attribute qualifier="migrationMode" type="MigrationMode">
					<description>
						Defines the execution mode of the migration process.
					</description>
					<modifiers read="true" write="true" search="true" optional="false" initial="true"/>
					<persistence type="property"/>
				</attribute>
			</attributes>
			<indexes>
				<index name="contractmigrationprocess_code_idx" unique="true">
					<key attribute="code"/>
				</index>
			</indexes>
		</itemtype>

		<itemtype code="MigrationContract">
			<description>
				Represents the contract to be migrated as part of a Contract Migration Process.
				This entity contains the details of the contract as received from the source system.
				It is also associated to the Order Item that effectively creates the newly migrated contract in the target systems.
			</description>
			<deployment table="migrationcontract" typecode="26114"/>
			<attributes>
				<attribute qualifier="code" type="java.lang.String">
					<description>Contract Identifier</description>
					<persistence type="property"/>
					<modifiers read="true" write="true" search="true" optional="false" initial="true" unique="true"/>
				</attribute>
				<attribute qualifier="cmtId" type="java.lang.String">
					<description>
						Contract Identifier in CMT
					</description>
					<modifiers read="true" write="true" search="true" optional="false" initial="true"/>
					<persistence type="property"/>
				</attribute>
				<attribute qualifier="umpContractId" type="java.lang.String">
					<description>
						UMP Contract ID of the contract to be migrated.
					</description>
					<persistence type="property"/>
					<modifiers read="true" write="true" search="true" optional="false" initial="true"/>
				</attribute>
				<attribute qualifier="uniqueBundleIdentifier" type="java.lang.String">
					<description>
						Unique identifier of the bundle to which the contract belongs.
					</description>
					<persistence type="property"/>
					<modifiers read="true" write="true" search="true" optional="false" initial="true"/>
				</attribute>
				<attribute qualifier="materialId" type="java.lang.String">
					<description>
						Material ID of the contract to be migrated.
						A material can represent a product or a content module.
					</description>
					<persistence type="property"/>
					<modifiers read="true" write="true" search="true" optional="false" initial="true"/>
				</attribute>
				<attribute qualifier="licenseType" type="java.lang.String">
					<description>
						License type of the contract to be migrated.
						This corresponds to the license type in the source system.
					</description>
					<persistence type="property"/>
					<modifiers read="true" write="true" search="true" optional="false" initial="true"/>
				</attribute>
				<attribute qualifier="startDate" type="java.util.Date">
					<description>
						Contract start date.
					</description>
					<modifiers read="true" write="true" search="true" optional="true"/>
					<persistence type="property"/>
				</attribute>
				<attribute qualifier="endDate" type="java.util.Date">
					<description>
						Contract end date.
					</description>
					<modifiers read="true" write="true" search="true" optional="true"/>
					<persistence type="property"/>
				</attribute>
				<attribute qualifier="distributor" type="java.lang.String">
					<description>
						Identifier of the AA distributor.
					</description>
					<persistence type="property"/>
					<modifiers read="true" write="true" search="true" optional="true"/>
				</attribute>
				<attribute qualifier="managedAccount" type="java.lang.String">
					<description>
						Identifier of the ManagedAccount.
					</description>
					<persistence type="property"/>
					<modifiers read="true" write="true" search="true" optional="true"/>
				</attribute>
			</attributes>
			<indexes>
				<index name="migrationcontract_code_idx" unique="true">
					<key attribute="code"/>
				</index>
			</indexes>
		</itemtype>

		<itemtype code="MigrationOrderDraft">
			<deployment table="migrationorderdraft" typecode="26116"/>
			<attributes>
				<attribute qualifier="code" type="java.lang.String">
					<persistence type="property"/>
					<modifiers read="true" write="true" search="true" optional="false" initial="true" unique="true"/>
				</attribute>
				<attribute qualifier="company" type="IoTCompany">
					<persistence type="property"/>
					<modifiers read="true" write="true" search="true" optional="false" initial="true"/>
				</attribute>
				<attribute qualifier="aaDistributor" type="AaDistributorCompany">
					<persistence type="property"/>
					<modifiers read="true" write="true" search="true" optional="true"/>
				</attribute>
				<attribute qualifier="resultingOrder" type="Order">
					<persistence type="property"/>
					<modifiers read="true" write="true" search="true" optional="true"/>
				</attribute>
				<attribute qualifier="status" type="MigrationOrderDraftStatus">
					<modifiers initial="true" read="true" write="true" optional="false" />
					<persistence type="property" />
					<defaultvalue>READY</defaultvalue>
				</attribute>
				<attribute qualifier="managedAccount" type="IoTCompany">
					<persistence type="property"/>
					<modifiers read="true" write="true" search="true" optional="true"/>
				</attribute>
				<attribute qualifier="inactiveProductMigration" type="boolean">
					<persistence type="property"/>
					<modifiers read="true" write="true" search="true" optional="true"/>
				</attribute>
			</attributes>
			<indexes>
				<index name="migrationorderdraft_code_idx" unique="true">
					<key attribute="code"/>
				</index>
			</indexes>
		</itemtype>

		<itemtype code="MigrationOrderEntryDraft">
			<deployment table="migrationorderentrydraft" typecode="26117"/>
			<attributes>
				<attribute qualifier="code" type="java.lang.String">
					<persistence type="property"/>
					<modifiers read="true" write="true" search="true" optional="false" initial="true" unique="true"/>
				</attribute>
				<attribute qualifier="appLicense" type="AppLicense">
					<persistence type="property"/>
					<modifiers read="true" write="true" search="true" optional="false" initial="true"/>
				</attribute>
				<attribute qualifier="quantity" type="java.lang.Integer">
					<persistence type="property"/>
					<modifiers read="true" write="true" search="true" optional="false" initial="true"/>
				</attribute>
				<attribute qualifier="resultingOrderEntry" type="OrderEntry">
					<persistence type="property"/>
					<modifiers read="true" write="true" search="true" optional="true"/>
				</attribute>
			</attributes>
			<indexes>
				<index name="migrationorderentrydraft_code_idx" unique="true">
					<key attribute="code"/>
				</index>
			</indexes>
		</itemtype>

        <itemtype code="MigrationContractGroup">
            <deployment table="migrationcontractgroup" typecode="26118"/>
            <attributes>
                <attribute qualifier="code" type="java.lang.String">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="false" initial="true" unique="true"/>
                </attribute>
				<attribute qualifier="uniqueBundleIdentifier" type="java.lang.String">
					<persistence type="property"/>
					<modifiers read="true" write="true" search="true" optional="true"/>
				</attribute>
            </attributes>
            <indexes>
                <index name="migrationcontractgroup_code_idx" unique="true">
                    <key attribute="code"/>
                </index>
            </indexes>
        </itemtype>

		<itemtype code="ProcessResultDetails">
			<deployment table="processresultdetails" typecode="26119"/>
			<attributes>
				<attribute type="java.lang.String" qualifier="lmpValidationMessage">
					<description>lmp validation message.</description>
					<persistence type="property">
						<columntype database="oracle">
							<value>CLOB</value>
						</columntype>
						<columntype database="sap">
							<value>NCLOB</value>
						</columntype>
						<columntype>
							<value>HYBRIS.LONG_STRING</value>
						</columntype>
					</persistence>
				</attribute>
				<attribute type="java.lang.String" qualifier="lmpExportMessage">
					<description>LMP export response</description>
					<persistence type="property">
						<columntype>
							<value>HYBRIS.LONG_STRING</value>
						</columntype>
					</persistence>
				</attribute>
				<attribute type="java.lang.String" qualifier="brimErrorMessage">
					<description>Error message reported by BRIM to Migration OrderExport.</description>
					<persistence type="property">
						<columntype database="oracle">
							<value>CLOB</value>
						</columntype>
						<columntype database="sap">
							<value>NCLOB</value>
						</columntype>
						<columntype>
							<value>HYBRIS.LONG_STRING</value>
						</columntype>
					</persistence>
				</attribute>
			</attributes>
		</itemtype>

        <itemtype code="OrderMigrationBusinessProcess" autocreate="true" generate="true" extends="BusinessProcess">
            <description>
				Process for migrating an AA order. The starting point for the migration is a Migration Order Draft.
			</description>
			<attributes>
			<attribute qualifier="resultDetails" type="ProcessResultDetails">
				<persistence type="property"/>
				<modifiers read="true" write="true" search="true" initial="true"/>
			</attribute>
			</attributes>
        </itemtype>
    </itemtypes>
</items>
