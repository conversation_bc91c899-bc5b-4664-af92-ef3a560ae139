<?xml version="1.0" encoding="UTF-8"?>
<!--
 [y] hybris Platform

 Copyright (c) 2018 SAP SE or an SAP affiliate company.  All rights reserved.

 This software is the confidential and proprietary information of SAP
 ("Confidential Information"). You shall not disclose such Confidential
 Information and shall use it only in accordance with the terms of the
 license agreement you entered into with SAP.
-->
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:int="http://www.springframework.org/schema/integration"
	xmlns:file="http://www.springframework.org/schema/integration/file"
	xmlns:context="http://www.springframework.org/schema/context"
	xmlns:p="http://www.springframework.org/schema/p"
	xsi:schemaLocation="http://www.springframework.org/schema/integration http://www.springframework.org/schema/integration/spring-integration.xsd 
		http://www.springframework.org/schema/integration/file http://www.springframework.org/schema/integration/file/spring-integration-file.xsd
		http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
		http://www.springframework.org/schema/context
           http://www.springframework.org/schema/context/spring-context.xsd">
	
	<context:annotation-config/> 
	<!-- Transformer converters mappings -->
	<bean id="batchMediaConverterMapping" 
		  class="de.hybris.platform.acceleratorservices.dataimport.batch.converter.mapping.impl.DefaultConverterMapping" 
		  p:mapping="media" 
		  p:converter-ref="batchMediaConverter"/>

	<bean id="batchMediaContainerConverterMapping" 
		  class="de.hybris.platform.acceleratorservices.dataimport.batch.converter.mapping.impl.DefaultConverterMapping" 
		  p:mapping="media" 
		  p:converter-ref="batchMediaContainerConverter"/>

	<bean id="batchMediaProductConverterMapping" 
		  class="de.hybris.platform.acceleratorservices.dataimport.batch.converter.mapping.impl.DefaultConverterMapping" 
		  p:mapping="media" 
		  p:converter-ref="batchMediaProductConverter"/>
							
	<!-- Converters -->
	<bean id="batchMediaConverter" class="de.hybris.platform.acceleratorservices.dataimport.batch.converter.impl.DefaultImpexConverter">
		<property name="header">
			<value>#{defaultImpexProductHeader}
				# Import media from filesystem
				$importFolder=file:$BASE_SOURCE_DIR$/images
				INSERT_UPDATE Media;mediaFormat(qualifier);code[unique=true];mime[default='image/jpeg'];$catalogVersion;folder(qualifier)[default=images];realfilename;@media[translator=de.hybris.platform.impex.jalo.media.MediaDataTranslator]
			</value>
		</property>
		<property name="impexRow">
			<value>;1200Wx1200H;/1200Wx1200H/{+1};;;;{+1};$importFolder/1200Wx1200H/{+1}
;515Wx515H;/515Wx515H/{+1};;;;{+1};$importFolder/515Wx515H/{+1}
;300Wx300H;/300Wx300H/{+1};;;;{+1};$importFolder/300Wx300H/{+1}
;96Wx96H;/96Wx96H/{+1};;;;{+1};$importFolder/96Wx96H/{+1}
;65Wx65H;/65Wx65H/{+1};;;;{+1};$importFolder/65Wx65H/{+1}
;30Wx30H;/30Wx30H/{+1};;;;{+1};$importFolder/30Wx30H/{+1}</value>
		</property>
	</bean>
	<bean id="batchMediaContainerConverter" class="de.hybris.platform.acceleratorservices.dataimport.batch.converter.impl.DefaultImpexConverter">
		<property name="header">
			<value>#{defaultImpexProductHeader}
				# Import media container
				INSERT_UPDATE MediaContainer;qualifier[unique=true];medias(code, $catalogVersion);$catalogVersion
			</value>
		</property>
		<property name="impexRow">
			<value>;{1};/1200Wx1200H/{1},/515Wx515H/{1},/300Wx300H/{1},/96Wx96H/{1},/65Wx65H/{1},/30Wx30H/{1}</value>
		</property>
	</bean>
	<bean id="batchMediaProductConverter" class="de.hybris.platform.acceleratorservices.dataimport.batch.converter.impl.DefaultImpexConverter">
		<property name="header">
			<value>#{defaultImpexProductHeader}
				$thumbnail=thumbnail(code, $catalogVersion)
				$thumbnails=thumbnails(code, $catalogVersion)
				$picture=picture(code, $catalogVersion)
				$detail=detail(code, $catalogVersion)
				$normal=normal(code, $catalogVersion)
				$others=others(code, $catalogVersion)
				$galleryImages=galleryImages(qualifier, $catalogVersion)
				# Import product media
				UPDATE Product;code[unique=true];$picture;$thumbnail;$detail;$others;$normal;$thumbnails;$galleryImages;sequenceId[translator=de.hybris.platform.acceleratorservices.dataimport.batch.converter.GreaterSequenceIdTranslator];$catalogVersion
			</value>
		</property>
		<property name="impexRow">
			<value>;{+0};/300Wx300H/{1};/96Wx96H/{1};/1200Wx1200H/{1};/515Wx515H/{1},/96Wx96H/{1},/30Wx30H/{1};/300Wx300H/{1};/96Wx96H/{1};{1};{S}</value>
		</property>
	</bean>
</beans>