package com.sast.cis.devcon.api.trialextension.controller;

import com.sast.cis.core.service.TranslationService;
import com.sast.cis.devcon.api.controller.BaseControllerUnitTest;
import com.sast.cis.devcon.api.controller.handlers.DevconRestApiExceptionHandler;
import com.sast.cis.devcon.api.data.TrialExtensionRequestList;
import com.sast.cis.devcon.api.trialextension.facade.TrialExtensionRequestFacade;
import com.sast.cis.trialextension.data.TrialExtensionRequestData;
import com.sast.cis.trialextension.enums.TrialExtensionRequestStatus;
import de.hybris.bootstrap.annotations.UnitTest;
import lombok.SneakyThrows;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.List;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@UnitTest
@RunWith(MockitoJUnitRunner.Silent.class)
public class TrialExtensionRequestControllerUnitTest extends BaseControllerUnitTest {
    private static final String BASE_PATH = "/trial-extension-requests";
    @Mock
    private TrialExtensionRequestFacade trialExtensionRequestFacade;
    @Mock
    private TranslationService translationService;

    @InjectMocks
    private TrialExtensionRequestController trialExtensionRequestController;

    @Before
    public void setUp() {
        this.mockMvc = MockMvcBuilders
            .standaloneSetup(trialExtensionRequestController)
            .setControllerAdvice(new DevconRestApiExceptionHandler(translationService))
            .build();
    }

    @Test
    @SneakyThrows
    public void getTrialExtensionRequests_existsRequestsWithGivenStatus_returnsSuccessfulResponse() {
        TrialExtensionRequestData extensionRequestData = new TrialExtensionRequestData().withRequestId("requestId")
            .withStatus(TrialExtensionRequestStatus.REQUESTED.getCode());
        when(trialExtensionRequestFacade.getTrialExtensionRequests(TrialExtensionRequestStatus.REQUESTED))
            .thenReturn(new TrialExtensionRequestList().withTrialExtensionRequests(List.of(extensionRequestData)));

        String jsonResponse = mockMvc.perform(get(BASE_PATH)
            .contentType(MediaType.APPLICATION_JSON).param("status",TrialExtensionRequestStatus.REQUESTED.getCode()))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andReturn().getResponse().getContentAsString();

        TrialExtensionRequestList actualData = toObject(jsonResponse, TrialExtensionRequestList.class);
        assertThat(actualData).isNotNull();
        assertThat(actualData.getTrialExtensionRequests())
            .hasSize(1)
            .usingRecursiveFieldByFieldElementComparator()
            .containsExactly(extensionRequestData);
    }

    @Test
    @SneakyThrows
    public void getTrialExtensionRequests_noRequestsWithGivenStatus_returnsSuccessfulWithEmptyResponse() {
        when(trialExtensionRequestFacade.getTrialExtensionRequests(TrialExtensionRequestStatus.REQUESTED))
            .thenReturn(new TrialExtensionRequestList().withTrialExtensionRequests(List.of()));

        String jsonResponse = mockMvc.perform(get(BASE_PATH)
            .contentType(MediaType.APPLICATION_JSON).param("status",TrialExtensionRequestStatus.REQUESTED.getCode()))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andReturn().getResponse().getContentAsString();

        TrialExtensionRequestList actualData = toObject(jsonResponse, TrialExtensionRequestList.class);
        assertThat(actualData).isNotNull();
        assertThat(actualData.getTrialExtensionRequests()).isNotNull();
    }

    @Test
    @SneakyThrows
    public void getTrialExtensionRequests_unSupportedStatusRequest_returnsBadRequestResponse() {
        when(trialExtensionRequestFacade.getTrialExtensionRequests(TrialExtensionRequestStatus.REQUESTED))
            .thenReturn(new TrialExtensionRequestList().withTrialExtensionRequests(List.of()));

        mockMvc.perform(get(BASE_PATH)
            .contentType(MediaType.APPLICATION_JSON).param("status","SOMERANDOMTEXT"))
            .andExpect(status().isBadRequest());
    }

    @Test
    public void approveTrialExtensionRequests_returnsSuccessfulResponse() throws Exception {
        final String trialExtensionRequestId = UUID.randomUUID().toString();
        mockMvc.perform(post(String.format("%s/{trialExtensionRequestId}/confirm", BASE_PATH), trialExtensionRequestId)
            .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isNoContent());

        verify(trialExtensionRequestFacade).approveTrialExtensionRequest(trialExtensionRequestId);
    }

    @Test
    public void declineTrialExtensionRequests_returnsSuccessfulResponse() throws Exception {
        final String trialExtensionRequestId = UUID.randomUUID().toString();
        mockMvc.perform(post(String.format("%s/{trialExtensionRequestId}/decline", BASE_PATH), trialExtensionRequestId)
                .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isNoContent());

        verify(trialExtensionRequestFacade).declineTrialExtensionRequest(trialExtensionRequestId);
    }
}
