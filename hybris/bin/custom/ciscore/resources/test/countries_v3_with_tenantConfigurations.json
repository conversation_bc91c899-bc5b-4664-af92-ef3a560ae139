[{"isoCode": "AE", "name": "U.A.Emirates", "activeInStore": true, "canSell": false, "canBuy": true, "blockedForDualUseApps": true, "inEU": false, "taxCategory": "AE1", "blockedCountriesCommercial": [], "blockedCountriesTrial": [], "paymentProviders": ["STRIPE", "DPG"], "paymentMethods": ["ACH", "CREDIT_CARD"], "validation": {"postalCode": {"required": true, "pattern": "^.*$", "example": "Please enter 000"}, "state": null, "region": null, "taxId": {"required": true, "pattern": "^[0-9]{15}$", "example": "**********12345"}}, "tenantConfigurations": [{"isoCode": "AE", "tenant": "AZENA", "canRegister": false, "storefrontEnabled": false, "importedCompaniesCanBuy": false, "currency": "USD", "languages": ["en"], "defaultLanguage": "en", "allowedCrossCountriesSales": []}, {"isoCode": "AE", "tenant": "AA", "canRegister": false, "storefrontEnabled": false, "importedCompaniesCanBuy": false, "currency": "USD", "languages": ["en"], "defaultLanguage": "en", "allowedCrossCountriesSales": ["AE"]}]}, {"isoCode": "AT", "name": "Austria", "activeInStore": true, "canSell": true, "canBuy": true, "blockedForDualUseApps": false, "inEU": true, "taxCategory": "AT0", "blockedCountriesCommercial": [], "blockedCountriesTrial": [], "paymentProviders": ["STRIPE", "DPG"], "paymentMethods": ["SEPA", "CREDIT_CARD"], "validation": {"postalCode": {"required": true, "pattern": "^[0-9]{4}$", "example": "1234"}, "state": null, "region": null, "taxId": {"required": true, "pattern": "^ATU[0-9]{8}$", "example": "*ATU*12345678"}}, "tenantConfigurations": [{"isoCode": "AT", "tenant": "AZENA", "canRegister": true, "storefrontEnabled": true, "importedCompaniesCanBuy": false, "currency": "EUR", "languages": ["en"], "defaultLanguage": "en", "allowedCrossCountriesSales": []}, {"isoCode": "AT", "tenant": "AA", "canRegister": true, "storefrontEnabled": true, "importedCompaniesCanBuy": true, "currency": "EUR", "languages": ["de", "en"], "defaultLanguage": "de", "allowedCrossCountriesSales": ["AT"]}]}, {"isoCode": "AU", "name": "Australia", "activeInStore": true, "canSell": true, "canBuy": false, "blockedForDualUseApps": false, "inEU": false, "taxCategory": "AU0", "blockedCountriesCommercial": [], "blockedCountriesTrial": [], "paymentProviders": ["STRIPE", "DPG"], "paymentMethods": ["ACH", "CREDIT_CARD"], "validation": {"postalCode": {"required": true, "pattern": "^[0-9]{4}$", "example": "1234"}, "state": null, "region": null, "taxId": {"required": true, "pattern": "^[0-9]{11}$", "example": "**********1"}}, "tenantConfigurations": [{"isoCode": "AU", "tenant": "AZENA", "canRegister": false, "storefrontEnabled": false, "importedCompaniesCanBuy": false, "currency": "USD", "languages": ["en"], "defaultLanguage": "en", "allowedCrossCountriesSales": []}, {"isoCode": "AU", "tenant": "AA", "canRegister": false, "storefrontEnabled": false, "importedCompaniesCanBuy": false, "currency": "USD", "languages": ["en"], "defaultLanguage": "en", "allowedCrossCountriesSales": ["AU"]}]}, {"isoCode": "BE", "name": "Belgium", "activeInStore": true, "canSell": true, "canBuy": true, "blockedForDualUseApps": false, "inEU": true, "taxCategory": "BE0", "blockedCountriesCommercial": [], "blockedCountriesTrial": [], "paymentProviders": ["STRIPE", "DPG"], "paymentMethods": ["SEPA", "CREDIT_CARD"], "validation": {"postalCode": {"required": true, "pattern": "^[0-9]{4}$", "example": "1234"}, "state": null, "region": null, "taxId": {"required": true, "pattern": "^BE[0-9]{10}$", "example": "*BE***********"}}, "tenantConfigurations": [{"isoCode": "BE", "tenant": "AA", "canRegister": false, "storefrontEnabled": false, "importedCompaniesCanBuy": false, "currency": "EUR", "languages": ["en"], "defaultLanguage": "en", "allowedCrossCountriesSales": ["BE"]}, {"isoCode": "BE", "tenant": "AZENA", "canRegister": true, "storefrontEnabled": true, "importedCompaniesCanBuy": false, "currency": "EUR", "languages": ["en"], "defaultLanguage": "en", "allowedCrossCountriesSales": []}]}, {"isoCode": "BG", "name": "Bulgaria", "activeInStore": true, "canSell": true, "canBuy": true, "blockedForDualUseApps": false, "inEU": true, "taxCategory": "BG0", "blockedCountriesCommercial": [], "blockedCountriesTrial": [], "paymentProviders": ["STRIPE", "DPG"], "paymentMethods": ["SEPA", "CREDIT_CARD"], "validation": {"postalCode": {"required": true, "pattern": "^[0-9]{4}$", "example": "1234"}, "state": null, "region": null, "taxId": {"required": true, "pattern": "^BG[0-9]{9,10}$", "example": "*BG**********, *BG***********"}}, "tenantConfigurations": [{"isoCode": "BG", "tenant": "AZENA", "canRegister": true, "storefrontEnabled": true, "importedCompaniesCanBuy": false, "currency": "EUR", "languages": ["en"], "defaultLanguage": "en", "allowedCrossCountriesSales": []}, {"isoCode": "BG", "tenant": "AA", "canRegister": false, "storefrontEnabled": false, "importedCompaniesCanBuy": false, "currency": "EUR", "languages": ["en"], "defaultLanguage": "en", "allowedCrossCountriesSales": ["BG"]}]}, {"isoCode": "BR", "name": "Brazil", "activeInStore": false, "canSell": false, "canBuy": false, "blockedForDualUseApps": false, "inEU": false, "taxCategory": null, "blockedCountriesCommercial": [], "blockedCountriesTrial": [], "paymentProviders": ["DPG"], "paymentMethods": ["ACH"], "validation": {"postalCode": {"required": true, "pattern": "^.*$", "example": "anything"}, "state": null, "region": {"required": true, "pattern": "^.*$", "regions": [{"abbrev": "AC", "name": "Acre"}, {"abbrev": "AL", "name": "Alagoas"}, {"abbrev": "AM", "name": "Amazonas"}, {"abbrev": "AP", "name": "Amapá"}, {"abbrev": "BA", "name": "Bahia"}, {"abbrev": "CE", "name": "Ceará"}, {"abbrev": "DF", "name": "Distrito Federal"}, {"abbrev": "ES", "name": "Espírito Santo"}, {"abbrev": "GO", "name": "Goiás"}, {"abbrev": "MA", "name": "Maranhão"}, {"abbrev": "MG", "name": "Minas Gerais"}, {"abbrev": "MS", "name": "Mato Grosso do Sul"}, {"abbrev": "MT", "name": "<PERSON><PERSON>"}, {"abbrev": "PA", "name": "Pará"}, {"abbrev": "PB", "name": "Paraíba"}, {"abbrev": "PE", "name": "Pernambuco"}, {"abbrev": "PI", "name": "Piauí"}, {"abbrev": "PR", "name": "Paraná"}, {"abbrev": "RJ", "name": "Rio de Janeiro"}, {"abbrev": "RN", "name": "Rio Grande do Norte"}, {"abbrev": "RO", "name": "Rondônia"}, {"abbrev": "RR", "name": "Roraima"}, {"abbrev": "RS", "name": "Rio Grande do Sul"}, {"abbrev": "SC", "name": "Santa Catarina"}, {"abbrev": "SE", "name": "<PERSON><PERSON><PERSON>"}, {"abbrev": "SP", "name": "São Paulo"}, {"abbrev": "TO", "name": "Tocantins"}]}, "taxId": {"required": true, "pattern": "^.*$", "example": "anything"}}, "tenantConfigurations": [{"isoCode": "BR", "tenant": "AA", "canRegister": false, "storefrontEnabled": false, "importedCompaniesCanBuy": false, "currency": "USD", "languages": ["en"], "defaultLanguage": "en", "allowedCrossCountriesSales": ["BR"]}, {"isoCode": "BR", "tenant": "AZENA", "canRegister": false, "storefrontEnabled": false, "importedCompaniesCanBuy": false, "currency": "USD", "languages": ["en"], "defaultLanguage": "en", "allowedCrossCountriesSales": []}]}, {"isoCode": "CA", "name": "Canada", "activeInStore": true, "canSell": true, "canBuy": true, "blockedForDualUseApps": false, "inEU": false, "taxCategory": "CA2", "blockedCountriesCommercial": ["CA"], "blockedCountriesTrial": [], "paymentProviders": ["STRIPE", "DPG"], "paymentMethods": ["ACH", "CREDIT_CARD"], "validation": {"postalCode": {"required": true, "pattern": "^(?!.*[DFIOQU])[A-VXY][0-9][A-Z]\\s[0-9][A-Z][0-9]$", "example": "A1A 1A1"}, "state": {"required": true, "pattern": ".*", "regions": [{"abbrev": "AB", "name": "Alberta"}, {"abbrev": "BC", "name": "British Columbia"}, {"abbrev": "MB", "name": "Manitoba"}, {"abbrev": "NB", "name": "New Brunswick"}, {"abbrev": "NL", "name": "Newfundland/Labrador"}, {"abbrev": "NS", "name": "Nova Scotia"}, {"abbrev": "NT", "name": "Northwest Terr."}, {"abbrev": "NU", "name": "Nunavut"}, {"abbrev": "ON", "name": "Ontario"}, {"abbrev": "PE", "name": "Prince Edward Island"}, {"abbrev": "QC", "name": "Quebec"}, {"abbrev": "SK", "name": "Saskatchewan"}, {"abbrev": "YT", "name": "Yukon"}, {"abbrev": "ZZ", "name": "Outside province"}]}, "region": null, "taxId": {"required": true, "pattern": "^[0-9]{9}$", "example": "*********"}}, "tenantConfigurations": [{"isoCode": "CA", "tenant": "AZENA", "canRegister": false, "storefrontEnabled": false, "importedCompaniesCanBuy": false, "currency": "USD", "languages": ["en"], "defaultLanguage": "en", "allowedCrossCountriesSales": []}, {"isoCode": "CA", "tenant": "AA", "canRegister": false, "storefrontEnabled": false, "importedCompaniesCanBuy": false, "currency": "USD", "languages": ["en"], "defaultLanguage": "en", "allowedCrossCountriesSales": ["CA"]}]}, {"isoCode": "CH", "name": "Switzerland", "activeInStore": true, "canSell": true, "canBuy": true, "blockedForDualUseApps": false, "inEU": false, "taxCategory": "CH3", "blockedCountriesCommercial": ["CH"], "blockedCountriesTrial": [], "paymentProviders": ["STRIPE", "DPG"], "paymentMethods": ["SEPA", "CREDIT_CARD"], "validation": {"postalCode": {"required": true, "pattern": "^[0-9]{4}$", "example": "1234"}, "state": null, "region": null, "taxId": {"required": true, "pattern": "^CHE-\\d{3}\\.\\d{3}\\.\\d{3}$", "example": "*CHE*-123.456.789"}}, "tenantConfigurations": [{"isoCode": "CH", "tenant": "AA", "canRegister": false, "storefrontEnabled": true, "importedCompaniesCanBuy": false, "currency": "EUR", "languages": ["de", "en"], "defaultLanguage": "de", "allowedCrossCountriesSales": ["CH"]}, {"isoCode": "CH", "tenant": "AZENA", "canRegister": false, "storefrontEnabled": false, "importedCompaniesCanBuy": false, "currency": "EUR", "languages": ["en"], "defaultLanguage": "en", "allowedCrossCountriesSales": []}]}, {"isoCode": "CY", "name": "Cyprus", "activeInStore": true, "canSell": true, "canBuy": true, "blockedForDualUseApps": false, "inEU": true, "taxCategory": "CY0", "blockedCountriesCommercial": [], "blockedCountriesTrial": [], "paymentProviders": ["STRIPE", "DPG"], "paymentMethods": ["SEPA", "CREDIT_CARD"], "validation": {"postalCode": {"required": true, "pattern": "^[0-9]{4}$", "example": "1234"}, "state": null, "region": null, "taxId": {"required": true, "pattern": "^CY[0-9]{8}[A-Z]$", "example": "*CY*12345678X"}}, "tenantConfigurations": [{"isoCode": "CY", "tenant": "AA", "canRegister": false, "storefrontEnabled": false, "importedCompaniesCanBuy": false, "currency": "EUR", "languages": ["en"], "defaultLanguage": "en", "allowedCrossCountriesSales": ["CY"]}, {"isoCode": "CY", "tenant": "AZENA", "canRegister": true, "storefrontEnabled": true, "importedCompaniesCanBuy": false, "currency": "EUR", "languages": ["en"], "defaultLanguage": "en", "allowedCrossCountriesSales": []}]}, {"isoCode": "CZ", "name": "Czech Republic", "activeInStore": true, "canSell": true, "canBuy": true, "blockedForDualUseApps": false, "inEU": true, "taxCategory": "CZ0", "blockedCountriesCommercial": [], "blockedCountriesTrial": [], "paymentProviders": ["STRIPE", "DPG"], "paymentMethods": ["SEPA", "CREDIT_CARD"], "validation": {"postalCode": {"required": true, "pattern": "^[0-9]{3}\\s[0-9]{2}$", "example": "123 45"}, "state": null, "region": null, "taxId": {"required": true, "pattern": "^CZ[0-9]{8,10}$", "example": "*CZ*12345678, *CZ**********, *CZ***********"}}, "tenantConfigurations": [{"isoCode": "CZ", "tenant": "AZENA", "canRegister": true, "storefrontEnabled": true, "importedCompaniesCanBuy": false, "currency": "EUR", "languages": ["en"], "defaultLanguage": "en", "allowedCrossCountriesSales": []}, {"isoCode": "CZ", "tenant": "AA", "canRegister": false, "storefrontEnabled": false, "importedCompaniesCanBuy": false, "currency": "EUR", "languages": ["en"], "defaultLanguage": "en", "allowedCrossCountriesSales": ["CZ"]}]}, {"isoCode": "DE", "name": "Germany", "activeInStore": true, "canSell": true, "canBuy": true, "blockedForDualUseApps": false, "inEU": true, "taxCategory": "DE0", "blockedCountriesCommercial": [], "blockedCountriesTrial": [], "paymentProviders": ["STRIPE", "DPG"], "paymentMethods": ["SEPA", "CREDIT_CARD"], "validation": {"postalCode": {"required": true, "pattern": "^[0-9]{5}$", "example": "12345"}, "state": null, "region": null, "taxId": {"required": true, "pattern": "^DE[0-9]{9}$", "example": "*DE**********"}}, "tenantConfigurations": [{"isoCode": "DE", "tenant": "AZENA", "canRegister": true, "storefrontEnabled": true, "importedCompaniesCanBuy": false, "currency": "EUR", "languages": ["en"], "defaultLanguage": "en", "allowedCrossCountriesSales": []}, {"isoCode": "DE", "tenant": "AA", "canRegister": false, "storefrontEnabled": false, "importedCompaniesCanBuy": false, "currency": "EUR", "languages": ["en"], "defaultLanguage": "en", "allowedCrossCountriesSales": ["DE"]}]}, {"isoCode": "DK", "name": "Denmark", "activeInStore": true, "canSell": true, "canBuy": true, "blockedForDualUseApps": false, "inEU": true, "taxCategory": "DK0", "blockedCountriesCommercial": [], "blockedCountriesTrial": [], "paymentProviders": ["STRIPE", "DPG"], "paymentMethods": ["SEPA", "CREDIT_CARD"], "validation": {"postalCode": {"required": true, "pattern": "^[0-9]{4}$", "example": "1234"}, "state": null, "region": null, "taxId": {"required": true, "pattern": "^DK[0-9]{8}$", "example": "*DK*12345678"}}, "tenantConfigurations": [{"isoCode": "DK", "tenant": "AA", "canRegister": true, "storefrontEnabled": true, "importedCompaniesCanBuy": false, "currency": "EUR", "languages": ["en", "da"], "defaultLanguage": "da", "allowedCrossCountriesSales": ["DK"]}, {"isoCode": "DK", "tenant": "AZENA", "canRegister": true, "storefrontEnabled": true, "importedCompaniesCanBuy": false, "currency": "EUR", "languages": ["en"], "defaultLanguage": "en", "allowedCrossCountriesSales": []}]}, {"isoCode": "EE", "name": "Estonia", "activeInStore": true, "canSell": true, "canBuy": true, "blockedForDualUseApps": false, "inEU": true, "taxCategory": "EE0", "blockedCountriesCommercial": [], "blockedCountriesTrial": [], "paymentProviders": ["STRIPE", "DPG"], "paymentMethods": ["SEPA", "CREDIT_CARD"], "validation": {"postalCode": {"required": true, "pattern": "^[0-9]{5}$", "example": "12345"}, "state": null, "region": null, "taxId": {"required": true, "pattern": "^EE[0-9]{9}$", "example": "*EE**********"}}, "tenantConfigurations": [{"isoCode": "EE", "tenant": "AA", "canRegister": true, "storefrontEnabled": true, "importedCompaniesCanBuy": false, "currency": "EUR", "languages": ["en", "et", "fi"], "defaultLanguage": "et", "allowedCrossCountriesSales": ["EE"]}, {"isoCode": "EE", "tenant": "AZENA", "canRegister": true, "storefrontEnabled": true, "importedCompaniesCanBuy": false, "currency": "EUR", "languages": ["en"], "defaultLanguage": "en", "allowedCrossCountriesSales": []}]}, {"isoCode": "ES", "name": "Spain", "activeInStore": true, "canSell": true, "canBuy": true, "blockedForDualUseApps": false, "inEU": true, "taxCategory": "ES0", "blockedCountriesCommercial": [], "blockedCountriesTrial": [], "paymentProviders": ["STRIPE", "DPG"], "paymentMethods": ["SEPA", "CREDIT_CARD"], "validation": {"postalCode": {"required": true, "pattern": "^[0-9]{5}$", "example": "12345"}, "state": null, "region": null, "taxId": {"required": true, "pattern": "^ES[0-9A-Z][0-9]{7}[0-9A-Z]$", "example": "*ES**********, *ES*12345678A, *ES*A12345678, *ES*A1234567A"}}, "tenantConfigurations": [{"isoCode": "ES", "tenant": "AZENA", "canRegister": true, "storefrontEnabled": true, "importedCompaniesCanBuy": false, "currency": "EUR", "languages": ["en"], "defaultLanguage": "en", "allowedCrossCountriesSales": []}, {"isoCode": "ES", "tenant": "AA", "canRegister": false, "storefrontEnabled": false, "importedCompaniesCanBuy": false, "currency": "EUR", "languages": ["en"], "defaultLanguage": "en", "allowedCrossCountriesSales": ["ES"]}]}, {"isoCode": "FI", "name": "Finland", "activeInStore": true, "canSell": true, "canBuy": true, "blockedForDualUseApps": false, "inEU": true, "taxCategory": "FI0", "blockedCountriesCommercial": [], "blockedCountriesTrial": [], "paymentProviders": ["STRIPE", "DPG"], "paymentMethods": ["SEPA", "CREDIT_CARD"], "validation": {"postalCode": {"required": true, "pattern": "^[0-9]{5}$", "example": "12345"}, "state": null, "region": null, "taxId": {"required": true, "pattern": "^FI[0-9]{8}$", "example": "*FI*12345678"}}, "tenantConfigurations": [{"isoCode": "FI", "tenant": "AA", "canRegister": true, "storefrontEnabled": true, "importedCompaniesCanBuy": false, "currency": "EUR", "languages": ["en", "fi", "sv"], "defaultLanguage": "fi", "allowedCrossCountriesSales": ["FI"]}, {"isoCode": "FI", "tenant": "AZENA", "canRegister": true, "storefrontEnabled": true, "importedCompaniesCanBuy": false, "currency": "EUR", "languages": ["en"], "defaultLanguage": "en", "allowedCrossCountriesSales": []}]}, {"isoCode": "FR", "name": "France", "activeInStore": true, "canSell": true, "canBuy": true, "blockedForDualUseApps": false, "inEU": true, "taxCategory": "FR0", "blockedCountriesCommercial": [], "blockedCountriesTrial": [], "paymentProviders": ["STRIPE", "DPG"], "paymentMethods": ["SEPA", "CREDIT_CARD"], "validation": {"postalCode": {"required": true, "pattern": "^[0-9]{5}$", "example": "12345"}, "state": null, "region": null, "taxId": {"required": true, "pattern": "^FR[0-9A-Z]{2}[0-9]{9}$", "example": "*FR*AB*********, *FR***********1"}}, "tenantConfigurations": [{"isoCode": "FR", "tenant": "AZENA", "canRegister": true, "storefrontEnabled": true, "importedCompaniesCanBuy": false, "currency": "EUR", "languages": ["en"], "defaultLanguage": "en", "allowedCrossCountriesSales": []}, {"isoCode": "FR", "tenant": "AA", "canRegister": false, "storefrontEnabled": false, "importedCompaniesCanBuy": false, "currency": "EUR", "languages": ["en"], "defaultLanguage": "en", "allowedCrossCountriesSales": ["FR"]}]}, {"isoCode": "GB", "name": "United Kingdom", "activeInStore": true, "canSell": true, "canBuy": true, "blockedForDualUseApps": false, "inEU": false, "taxCategory": "GB0", "blockedCountriesCommercial": [], "blockedCountriesTrial": [], "paymentProviders": ["STRIPE", "DPG"], "paymentMethods": ["SEPA", "CREDIT_CARD"], "validation": {"postalCode": {"required": true, "pattern": "^[A-Z]{1,2}([0-9]{1,2}|[0-9][A-Z])\\s[0-9][A-Z]{2}$", "example": "A1A 1AA, AA1A 1AA, A1 1AA, A12 1AA, AA1 1AA, AA12 1AA"}, "state": null, "region": null, "taxId": {"required": false, "pattern": "^[a-zA-Z0-9]{1,12}$", "example": "*GB*0123456, *GB*0*********"}}, "tenantConfigurations": [{"isoCode": "GB", "tenant": "AZENA", "canRegister": true, "storefrontEnabled": true, "importedCompaniesCanBuy": false, "currency": "EUR", "languages": ["en"], "defaultLanguage": "en", "allowedCrossCountriesSales": []}, {"isoCode": "GB", "tenant": "AA", "canRegister": false, "storefrontEnabled": false, "importedCompaniesCanBuy": false, "currency": "EUR", "languages": ["en"], "defaultLanguage": "en", "allowedCrossCountriesSales": ["GB"]}]}, {"isoCode": "GR", "name": "Greece", "activeInStore": true, "canSell": true, "canBuy": true, "blockedForDualUseApps": false, "inEU": true, "taxCategory": "GR0", "blockedCountriesCommercial": [], "blockedCountriesTrial": [], "paymentProviders": ["STRIPE", "DPG"], "paymentMethods": ["SEPA", "CREDIT_CARD"], "validation": {"postalCode": {"required": true, "pattern": "^[0-9]{3}\\s[0-9]{2}$", "example": "123 45"}, "state": null, "region": null, "taxId": {"required": true, "pattern": "^EL[0-9]{9}$", "example": "*EL**********"}}, "tenantConfigurations": [{"isoCode": "GR", "tenant": "AZENA", "canRegister": true, "storefrontEnabled": true, "importedCompaniesCanBuy": false, "currency": "EUR", "languages": ["en"], "defaultLanguage": "en", "allowedCrossCountriesSales": []}, {"isoCode": "GR", "tenant": "AA", "canRegister": false, "storefrontEnabled": false, "importedCompaniesCanBuy": false, "currency": "EUR", "languages": ["en"], "defaultLanguage": "en", "allowedCrossCountriesSales": ["GR"]}]}, {"isoCode": "HR", "name": "Croatia", "activeInStore": true, "canSell": true, "canBuy": true, "blockedForDualUseApps": false, "inEU": true, "taxCategory": "HR0", "blockedCountriesCommercial": [], "blockedCountriesTrial": [], "paymentProviders": ["STRIPE", "DPG"], "paymentMethods": ["SEPA", "CREDIT_CARD"], "validation": {"postalCode": {"required": true, "pattern": "^[0-9]{5}$", "example": "12345"}, "state": null, "region": null, "taxId": {"required": true, "pattern": "^HR[0-9]{11}$", "example": "*HR***********1"}}, "tenantConfigurations": [{"isoCode": "HR", "tenant": "AZENA", "canRegister": true, "storefrontEnabled": true, "importedCompaniesCanBuy": false, "currency": "EUR", "languages": ["en"], "defaultLanguage": "en", "allowedCrossCountriesSales": []}, {"isoCode": "HR", "tenant": "AA", "canRegister": true, "storefrontEnabled": true, "importedCompaniesCanBuy": false, "currency": "EUR", "languages": ["en", "hr"], "defaultLanguage": "hr", "allowedCrossCountriesSales": ["HR"]}]}, {"isoCode": "HU", "name": "Hungary", "activeInStore": true, "canSell": true, "canBuy": true, "blockedForDualUseApps": false, "inEU": true, "taxCategory": "HU0", "blockedCountriesCommercial": [], "blockedCountriesTrial": [], "paymentProviders": ["STRIPE", "DPG"], "paymentMethods": ["SEPA", "CREDIT_CARD"], "validation": {"postalCode": {"required": true, "pattern": "^[0-9]{4}$", "example": "1234"}, "state": null, "region": null, "taxId": {"required": true, "pattern": "^HU[0-9]{8}$", "example": "*HU*12345678"}}, "tenantConfigurations": [{"isoCode": "HU", "tenant": "AZENA", "canRegister": true, "storefrontEnabled": true, "importedCompaniesCanBuy": false, "currency": "EUR", "languages": ["en"], "defaultLanguage": "en", "allowedCrossCountriesSales": []}, {"isoCode": "HU", "tenant": "AA", "canRegister": false, "storefrontEnabled": false, "importedCompaniesCanBuy": false, "currency": "EUR", "languages": ["en"], "defaultLanguage": "en", "allowedCrossCountriesSales": ["HU"]}]}, {"isoCode": "ID", "name": "Indonesia", "activeInStore": true, "canSell": false, "canBuy": false, "blockedForDualUseApps": true, "inEU": false, "taxCategory": "ID1", "blockedCountriesCommercial": [], "blockedCountriesTrial": [], "paymentProviders": ["DPG"], "paymentMethods": ["ACH"], "validation": {"postalCode": {"required": true, "pattern": "^[0-9]{5}$", "example": "12345"}, "state": null, "region": {"required": false, "pattern": "", "regions": [{"abbrev": "AC", "name": "Aceh"}, {"abbrev": "BA", "name": "Bali"}, {"abbrev": "BB", "name": "<PERSON><PERSON><PERSON><PERSON>."}, {"abbrev": "BE", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"abbrev": "BT", "name": "<PERSON><PERSON>"}, {"abbrev": "GO", "name": "Gorontalo"}, {"abbrev": "JA", "name": "Jambi"}, {"abbrev": "JB", "name": "<PERSON>awa Barat"}, {"abbrev": "JI", "name": "<PERSON><PERSON>"}, {"abbrev": "JK", "name": "Jakarta Raya"}, {"abbrev": "JT", "name": "Jawa Tengah"}, {"abbrev": "JW", "name": "<PERSON><PERSON>"}, {"abbrev": "KA", "name": "Kalimantan"}, {"abbrev": "KB", "name": "Kalimantan Barat"}, {"abbrev": "KI", "name": "<PERSON><PERSON><PERSON>"}, {"abbrev": "KR", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"abbrev": "KS", "name": "<PERSON><PERSON><PERSON>"}, {"abbrev": "KT", "name": "Kalimantan Tengah"}, {"abbrev": "KU", "name": "Kalimantan Utara"}, {"abbrev": "LA", "name": "Lampung"}, {"abbrev": "MA", "name": "Maluku"}, {"abbrev": "ML", "name": "Maluku"}, {"abbrev": "MU", "name": "Maluku Utara"}, {"abbrev": "NB", "name": "Nusa Tenggara Barat"}, {"abbrev": "NT", "name": "Nusa Tenggara Timur"}, {"abbrev": "NU", "name": "Nusa Tenggara"}, {"abbrev": "PA", "name": "Papua"}, {"abbrev": "PB", "name": "Papua Barat"}, {"abbrev": "PP", "name": "Papua"}, {"abbrev": "RI", "name": "<PERSON><PERSON><PERSON>"}, {"abbrev": "SA", "name": "Sulawesi Utara"}, {"abbrev": "SB", "name": "Sumatera Barat"}, {"abbrev": "SG", "name": "Sulawesi Tenggara"}, {"abbrev": "SL", "name": "Sulawesi"}, {"abbrev": "SM", "name": "<PERSON><PERSON><PERSON>"}, {"abbrev": "SN", "name": "Sulaw<PERSON>"}, {"abbrev": "SR", "name": "Sulawesi Barat"}, {"abbrev": "SS", "name": "<PERSON><PERSON><PERSON>"}, {"abbrev": "ST", "name": "Sulawesi Ten<PERSON>"}, {"abbrev": "SU", "name": "<PERSON><PERSON><PERSON>"}, {"abbrev": "YO", "name": "Yogyakarta"}]}, "taxId": {"required": true, "pattern": "^[0-9]{15}$", "example": "**********12345"}}, "tenantConfigurations": [{"isoCode": "ID", "tenant": "AZENA", "canRegister": false, "storefrontEnabled": false, "importedCompaniesCanBuy": false, "currency": "USD", "languages": ["en"], "defaultLanguage": "en", "allowedCrossCountriesSales": []}, {"isoCode": "ID", "tenant": "AA", "canRegister": false, "storefrontEnabled": false, "importedCompaniesCanBuy": false, "currency": "USD", "languages": ["en"], "defaultLanguage": "en", "allowedCrossCountriesSales": ["ID"]}]}, {"isoCode": "IE", "name": "Ireland", "activeInStore": true, "canSell": true, "canBuy": true, "blockedForDualUseApps": false, "inEU": false, "taxCategory": "IE0", "blockedCountriesCommercial": [], "blockedCountriesTrial": [], "paymentProviders": ["STRIPE", "DPG"], "paymentMethods": ["SEPA", "CREDIT_CARD"], "validation": {"postalCode": {"required": true, "pattern": "(?:^[AC-FHKNPRTV-Y][0-9]{2}|D6W)[ -]?[0-9AC-FHKNPRTV-Y]{4}$", "example": "A12 AA12, A12 A1A1, A12 A123"}, "state": null, "region": null, "taxId": {"required": true, "pattern": "^IE[0-9]{7}[A-Z][AH]?$|^[0-9]{6}AB$|^[0-9][A-Z][0-9]{5}[A-Z]$", "example": "*IE*1234567A, *IE*1234567AA, *IE*1234567AH, *IE*123456AB, *IE*1A12345A"}}, "tenantConfigurations": [{"isoCode": "IE", "tenant": "AZENA", "canRegister": true, "storefrontEnabled": true, "importedCompaniesCanBuy": false, "currency": "EUR", "languages": ["en"], "defaultLanguage": "en", "allowedCrossCountriesSales": []}, {"isoCode": "IE", "tenant": "AA", "canRegister": false, "storefrontEnabled": false, "importedCompaniesCanBuy": false, "currency": "EUR", "languages": ["en"], "defaultLanguage": "en", "allowedCrossCountriesSales": ["IE"]}]}, {"isoCode": "IN", "name": "India", "activeInStore": true, "canSell": false, "canBuy": true, "blockedForDualUseApps": true, "inEU": false, "taxCategory": "IN3", "blockedCountriesCommercial": ["IN"], "blockedCountriesTrial": [], "paymentProviders": ["STRIPE", "DPG"], "paymentMethods": ["ACH", "CREDIT_CARD"], "validation": {"postalCode": {"required": true, "pattern": "^[0-9]{6}$", "example": "123456"}, "state": null, "region": null, "taxId": {"required": true, "pattern": "^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[0-9]{1}[0-9A-Z]{2}$", "example": "12ABCDE3456F7GH"}}, "tenantConfigurations": [{"isoCode": "IN", "tenant": "AA", "canRegister": false, "storefrontEnabled": false, "importedCompaniesCanBuy": false, "currency": "USD", "languages": ["en"], "defaultLanguage": "en", "allowedCrossCountriesSales": ["IN"]}, {"isoCode": "IN", "tenant": "AZENA", "canRegister": false, "storefrontEnabled": false, "importedCompaniesCanBuy": false, "currency": "USD", "languages": ["en"], "defaultLanguage": "en", "allowedCrossCountriesSales": []}]}, {"isoCode": "IS", "name": "Iceland", "activeInStore": true, "canSell": false, "canBuy": true, "blockedForDualUseApps": false, "inEU": false, "taxCategory": "IS3", "blockedCountriesCommercial": [], "blockedCountriesTrial": [], "paymentProviders": ["STRIPE", "DPG"], "paymentMethods": ["SEPA", "CREDIT_CARD"], "validation": {"postalCode": {"required": true, "pattern": "^[0-9]{3}$", "example": "123"}, "state": null, "region": null, "taxId": {"required": true, "pattern": "^[0-9]{5}$", "example": "12345"}}, "tenantConfigurations": [{"isoCode": "IS", "tenant": "AA", "canRegister": true, "storefrontEnabled": true, "importedCompaniesCanBuy": false, "currency": "EUR", "languages": ["en", "da"], "defaultLanguage": "en", "allowedCrossCountriesSales": ["IS"]}, {"isoCode": "IS", "tenant": "AZENA", "canRegister": false, "storefrontEnabled": false, "importedCompaniesCanBuy": false, "currency": "EUR", "languages": ["en"], "defaultLanguage": "en", "allowedCrossCountriesSales": []}]}, {"isoCode": "IT", "name": "Italy", "activeInStore": true, "canSell": false, "canBuy": false, "blockedForDualUseApps": false, "inEU": true, "taxCategory": "IT0", "blockedCountriesCommercial": [], "blockedCountriesTrial": [], "paymentProviders": ["STRIPE", "DPG"], "paymentMethods": ["SEPA", "CREDIT_CARD"], "validation": {"postalCode": {"required": true, "pattern": "^[0-9]{5}$", "example": "12345"}, "state": null, "region": {"required": true, "pattern": ".*", "regions": [{"abbrev": "21", "name": "Piemonte"}, {"abbrev": "23", "name": "Valle d´Aosta"}, {"abbrev": "25", "name": "Lombardia"}, {"abbrev": "32", "name": "Trentino-Alto Adige"}, {"abbrev": "34", "name": "Veneto"}, {"abbrev": "36", "name": "Friuli-Venezia Giulia"}, {"abbrev": "42", "name": "Liguria"}, {"abbrev": "45", "name": "Emilia-Romagna"}, {"abbrev": "52", "name": "Toscana"}, {"abbrev": "55", "name": "Umbria"}, {"abbrev": "57", "name": "Marche"}, {"abbrev": "62", "name": "Lazio"}, {"abbrev": "65", "name": "Abruzzo"}, {"abbrev": "67", "name": "<PERSON><PERSON><PERSON>"}, {"abbrev": "72", "name": "Campania"}, {"abbrev": "75", "name": "Puglia"}, {"abbrev": "77", "name": "Basilicata"}, {"abbrev": "78", "name": "Calabria"}, {"abbrev": "82", "name": "Sicilia"}, {"abbrev": "88", "name": "Sardegna"}]}, "taxId": {"required": true, "pattern": "^IT[0-9]{11}$", "example": "*IT***********1"}}, "tenantConfigurations": [{"isoCode": "IT", "tenant": "AA", "canRegister": false, "storefrontEnabled": false, "importedCompaniesCanBuy": false, "currency": "EUR", "languages": ["en"], "defaultLanguage": "en", "allowedCrossCountriesSales": ["IT"]}, {"isoCode": "IT", "tenant": "AZENA", "canRegister": false, "storefrontEnabled": false, "importedCompaniesCanBuy": false, "currency": "EUR", "languages": ["en"], "defaultLanguage": "en", "allowedCrossCountriesSales": []}]}, {"isoCode": "JP", "name": "Japan", "activeInStore": true, "canSell": false, "canBuy": true, "blockedForDualUseApps": false, "inEU": false, "taxCategory": "JP1", "blockedCountriesCommercial": [], "blockedCountriesTrial": [], "paymentProviders": ["STRIPE", "DPG"], "paymentMethods": ["ACH", "CREDIT_CARD"], "validation": {"postalCode": {"required": true, "pattern": "^[0-9]{3}-[0-9]{4}$", "example": "123-4567"}, "state": null, "region": {"required": true, "pattern": ".*", "regions": [{"abbrev": "01", "name": "Hokkaido"}, {"abbrev": "02", "name": "<PERSON><PERSON><PERSON>"}, {"abbrev": "03", "name": "Iwate"}, {"abbrev": "04", "name": "<PERSON><PERSON><PERSON>"}, {"abbrev": "05", "name": "<PERSON><PERSON><PERSON>"}, {"abbrev": "06", "name": "Yamagata"}, {"abbrev": "07", "name": "Fukushima"}, {"abbrev": "08", "name": "<PERSON><PERSON><PERSON>"}, {"abbrev": "09", "name": "Tochigi"}, {"abbrev": "10", "name": "<PERSON><PERSON>"}, {"abbrev": "11", "name": "<PERSON><PERSON><PERSON>"}, {"abbrev": "12", "name": "Chiba"}, {"abbrev": "13", "name": "Tokyo"}, {"abbrev": "14", "name": "Kanagawa"}, {"abbrev": "15", "name": "Niigata"}, {"abbrev": "16", "name": "Toyama"}, {"abbrev": "17", "name": "<PERSON><PERSON><PERSON>"}, {"abbrev": "18", "name": "<PERSON><PERSON><PERSON>"}, {"abbrev": "19", "name": "<PERSON><PERSON><PERSON>"}, {"abbrev": "20", "name": "Nagano"}, {"abbrev": "21", "name": "Gifu"}, {"abbrev": "22", "name": "Shizuoka"}, {"abbrev": "23", "name": "<PERSON><PERSON>"}, {"abbrev": "24", "name": "<PERSON><PERSON>"}, {"abbrev": "25", "name": "Shiga"}, {"abbrev": "26", "name": "Kyoto"}, {"abbrev": "27", "name": "Osaka"}, {"abbrev": "28", "name": "Hyogo"}, {"abbrev": "29", "name": "<PERSON><PERSON>"}, {"abbrev": "30", "name": "<PERSON><PERSON><PERSON>"}, {"abbrev": "31", "name": "Tottori"}, {"abbrev": "32", "name": "<PERSON><PERSON><PERSON>"}, {"abbrev": "33", "name": "<PERSON><PERSON>"}, {"abbrev": "34", "name": "Hiroshima"}, {"abbrev": "35", "name": "<PERSON><PERSON><PERSON>"}, {"abbrev": "36", "name": "Tokushima"}, {"abbrev": "37", "name": "Kagawa"}, {"abbrev": "38", "name": "<PERSON><PERSON><PERSON>"}, {"abbrev": "39", "name": "<PERSON><PERSON>"}, {"abbrev": "40", "name": "<PERSON><PERSON><PERSON>"}, {"abbrev": "41", "name": "Saga"}, {"abbrev": "42", "name": "Nagasaki"}, {"abbrev": "43", "name": "<PERSON><PERSON>"}, {"abbrev": "44", "name": "<PERSON><PERSON>"}, {"abbrev": "45", "name": "<PERSON><PERSON><PERSON>"}, {"abbrev": "46", "name": "Kagoshima"}, {"abbrev": "47", "name": "Okinawa"}]}, "taxId": {"required": true, "pattern": "^[0-9]{10,13}$", "example": "**********, **********1, **********12, **********123"}}, "tenantConfigurations": [{"isoCode": "JP", "tenant": "AZENA", "canRegister": false, "storefrontEnabled": false, "importedCompaniesCanBuy": false, "currency": "USD", "languages": ["en"], "defaultLanguage": "en", "allowedCrossCountriesSales": []}, {"isoCode": "JP", "tenant": "AA", "canRegister": false, "storefrontEnabled": false, "importedCompaniesCanBuy": false, "currency": "USD", "languages": ["en"], "defaultLanguage": "en", "allowedCrossCountriesSales": ["JP"]}]}, {"isoCode": "KR", "name": "Republic of Korea", "activeInStore": true, "canSell": true, "canBuy": true, "blockedForDualUseApps": true, "inEU": false, "taxCategory": "KR2", "blockedCountriesCommercial": ["KR"], "blockedCountriesTrial": [], "paymentProviders": ["STRIPE", "DPG"], "paymentMethods": ["ACH", "CREDIT_CARD"], "validation": {"postalCode": {"required": true, "pattern": "^[0-9]{5}$", "example": "12345"}, "state": null, "region": null, "taxId": {"required": true, "pattern": "^[0-9]{10}$", "example": "**********"}}, "tenantConfigurations": [{"isoCode": "KR", "tenant": "AZENA", "canRegister": false, "storefrontEnabled": false, "importedCompaniesCanBuy": false, "currency": "USD", "languages": ["en"], "defaultLanguage": "en", "allowedCrossCountriesSales": []}, {"isoCode": "KR", "tenant": "AA", "canRegister": false, "storefrontEnabled": false, "importedCompaniesCanBuy": false, "currency": "USD", "languages": ["en"], "defaultLanguage": "en", "allowedCrossCountriesSales": ["KR"]}]}, {"isoCode": "LT", "name": "Lithuania", "activeInStore": true, "canSell": true, "canBuy": true, "blockedForDualUseApps": false, "inEU": true, "taxCategory": "LT0", "blockedCountriesCommercial": [], "blockedCountriesTrial": [], "paymentProviders": ["STRIPE", "DPG"], "paymentMethods": ["SEPA", "CREDIT_CARD"], "validation": {"postalCode": {"required": true, "pattern": "^[0-9]{5}$", "example": "12345"}, "state": null, "region": null, "taxId": {"required": true, "pattern": "^LT[0-9]{9}$|^LT[0-9]{12}$", "example": "*LT**********, *LT***********12"}}, "tenantConfigurations": [{"isoCode": "LT", "tenant": "AZENA", "canRegister": true, "storefrontEnabled": true, "importedCompaniesCanBuy": false, "currency": "EUR", "languages": ["en"], "defaultLanguage": "en", "allowedCrossCountriesSales": []}, {"isoCode": "LT", "tenant": "AA", "canRegister": true, "storefrontEnabled": true, "importedCompaniesCanBuy": false, "currency": "EUR", "languages": ["en", "lt"], "defaultLanguage": "lt", "allowedCrossCountriesSales": ["LT"]}]}, {"isoCode": "LU", "name": "Luxembourg", "activeInStore": true, "canSell": true, "canBuy": true, "blockedForDualUseApps": false, "inEU": true, "taxCategory": "LU0", "blockedCountriesCommercial": [], "blockedCountriesTrial": [], "paymentProviders": ["STRIPE", "DPG"], "paymentMethods": ["SEPA", "CREDIT_CARD"], "validation": {"postalCode": {"required": true, "pattern": "^[0-9]{4}$", "example": "1234"}, "state": null, "region": null, "taxId": {"required": true, "pattern": "^LU[0-9]{8}$", "example": "*LU*12345678"}}, "tenantConfigurations": [{"isoCode": "LU", "tenant": "AZENA", "canRegister": true, "storefrontEnabled": true, "importedCompaniesCanBuy": false, "currency": "EUR", "languages": ["en"], "defaultLanguage": "en", "allowedCrossCountriesSales": []}, {"isoCode": "LU", "tenant": "AA", "canRegister": false, "storefrontEnabled": false, "importedCompaniesCanBuy": false, "currency": "EUR", "languages": ["en"], "defaultLanguage": "en", "allowedCrossCountriesSales": ["LU"]}]}, {"isoCode": "LV", "name": "Latvia", "activeInStore": true, "canSell": true, "canBuy": true, "blockedForDualUseApps": false, "inEU": true, "taxCategory": "LV0", "blockedCountriesCommercial": [], "blockedCountriesTrial": [], "paymentProviders": ["STRIPE", "DPG"], "paymentMethods": ["SEPA", "CREDIT_CARD"], "validation": {"postalCode": {"required": true, "pattern": "^[0-9]{4}$", "example": "1234"}, "state": null, "region": null, "taxId": {"required": true, "pattern": "^LV[0-9]{11}$", "example": "*LV***********1"}}, "tenantConfigurations": [{"isoCode": "LV", "tenant": "AZENA", "canRegister": true, "storefrontEnabled": true, "importedCompaniesCanBuy": false, "currency": "EUR", "languages": ["en"], "defaultLanguage": "en", "allowedCrossCountriesSales": []}, {"isoCode": "LV", "tenant": "AA", "canRegister": true, "storefrontEnabled": true, "importedCompaniesCanBuy": false, "currency": "EUR", "languages": ["en", "lv"], "defaultLanguage": "lv", "allowedCrossCountriesSales": ["LV"]}]}, {"isoCode": "MT", "name": "Malta", "activeInStore": true, "canSell": true, "canBuy": true, "blockedForDualUseApps": false, "inEU": true, "taxCategory": "MT0", "blockedCountriesCommercial": [], "blockedCountriesTrial": [], "paymentProviders": ["STRIPE", "DPG"], "paymentMethods": ["SEPA", "CREDIT_CARD"], "validation": {"postalCode": {"required": true, "pattern": "^[A-Z]{3}\\s[0-9]{4}$", "example": "VLT 1234"}, "state": null, "region": null, "taxId": {"required": true, "pattern": "^MT[0-9]{8}$", "example": "*MT*12345678"}}, "tenantConfigurations": [{"isoCode": "MT", "tenant": "AZENA", "canRegister": true, "storefrontEnabled": true, "importedCompaniesCanBuy": false, "currency": "EUR", "languages": ["en"], "defaultLanguage": "en", "allowedCrossCountriesSales": []}, {"isoCode": "MT", "tenant": "AA", "canRegister": false, "storefrontEnabled": false, "importedCompaniesCanBuy": false, "currency": "EUR", "languages": ["en"], "defaultLanguage": "en", "allowedCrossCountriesSales": ["MT"]}]}, {"isoCode": "MX", "name": "Mexico", "activeInStore": true, "canSell": false, "canBuy": false, "blockedForDualUseApps": false, "inEU": false, "taxCategory": "MX1", "blockedCountriesCommercial": [], "blockedCountriesTrial": [], "paymentProviders": ["STRIPE", "DPG"], "paymentMethods": ["ACH", "CREDIT_CARD"], "validation": {"postalCode": {"required": true, "pattern": "^[0-9]{5}$", "example": "12345"}, "state": {"required": true, "pattern": ".*", "regions": [{"abbrev": "AGU", "name": "Aguascalientes"}, {"abbrev": "BCN", "name": "Baja California"}, {"abbrev": "BCS", "name": "Baja California Sur"}, {"abbrev": "CAM", "name": "Campeche"}, {"abbrev": "CHH", "name": "Chihuahua"}, {"abbrev": "CHP", "name": "Chiapas"}, {"abbrev": "CMX", "name": "Ciudad de Mexico"}, {"abbrev": "COA", "name": "Coahuila"}, {"abbrev": "COL", "name": "Colima"}, {"abbrev": "DUR", "name": "Durango"}, {"abbrev": "GRO", "name": "Guerrero"}, {"abbrev": "GUA", "name": "Guanajuato"}, {"abbrev": "HID", "name": "Hidalgo"}, {"abbrev": "JAL", "name": "Jalisco"}, {"abbrev": "MEX", "name": "México"}, {"abbrev": "MIC", "name": "Michoacán"}, {"abbrev": "MOR", "name": "<PERSON><PERSON>"}, {"abbrev": "NAY", "name": "Nayarit"}, {"abbrev": "NLE", "name": "Nuevo León"}, {"abbrev": "OAX", "name": "Oaxaca"}, {"abbrev": "PUE", "name": "Puebla"}, {"abbrev": "QUE", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"abbrev": "ROO", "name": "Quintana Roo"}, {"abbrev": "SIN", "name": "Sinaloa"}, {"abbrev": "SLP", "name": "San Luis Potosí"}, {"abbrev": "SON", "name": "Sonora"}, {"abbrev": "TAB", "name": "Tabasco"}, {"abbrev": "TAM", "name": "Tamaulip<PERSON>"}, {"abbrev": "TLA", "name": "Tlaxcala"}, {"abbrev": "VER", "name": "Veracruz"}, {"abbrev": "YUC", "name": "Yucatán"}, {"abbrev": "ZAC", "name": "Zacatecas"}]}, "region": null, "taxId": {"required": true, "pattern": "^[A-Z]{3,4}[0-9]{6}[A-Z0-9]{3}$", "example": "ABCD123456A12, ABC123456A12"}}, "tenantConfigurations": [{"isoCode": "MX", "tenant": "AA", "canRegister": false, "storefrontEnabled": false, "importedCompaniesCanBuy": false, "currency": "USD", "languages": ["en"], "defaultLanguage": "en", "allowedCrossCountriesSales": ["MX"]}, {"isoCode": "MX", "tenant": "AZENA", "canRegister": false, "storefrontEnabled": false, "importedCompaniesCanBuy": false, "currency": "USD", "languages": ["en"], "defaultLanguage": "en", "allowedCrossCountriesSales": []}]}, {"isoCode": "MY", "name": "Malaysia", "activeInStore": true, "canSell": false, "canBuy": false, "blockedForDualUseApps": false, "inEU": false, "taxCategory": "MY1", "blockedCountriesCommercial": [], "blockedCountriesTrial": [], "paymentProviders": ["DPG"], "paymentMethods": ["ACH"], "validation": {"postalCode": {"required": true, "pattern": "^.*$", "example": "anything"}, "state": null, "region": {"required": true, "pattern": "^.*$", "regions": [{"abbrev": "01", "name": "<PERSON><PERSON>"}, {"abbrev": "02", "name": "Kedah"}, {"abbrev": "03", "name": "<PERSON><PERSON><PERSON>"}, {"abbrev": "04", "name": "<PERSON><PERSON>"}, {"abbrev": "05", "name": "<PERSON><PERSON><PERSON>"}, {"abbrev": "06", "name": "<PERSON><PERSON>"}, {"abbrev": "07", "name": "<PERSON><PERSON><PERSON>"}, {"abbrev": "08", "name": "<PERSON><PERSON>"}, {"abbrev": "09", "name": "<PERSON><PERSON>"}, {"abbrev": "10", "name": "Selangor"}, {"abbrev": "11", "name": "Terengganu"}, {"abbrev": "12", "name": "Sabah"}, {"abbrev": "13", "name": "Sarawak"}, {"abbrev": "14", "name": "Kuala Lumpur"}, {"abbrev": "15", "name": "<PERSON><PERSON>"}, {"abbrev": "16", "name": "<PERSON><PERSON><PERSON>"}]}, "taxId": {"required": true, "pattern": "^.*$", "example": "anything"}}, "tenantConfigurations": [{"isoCode": "MY", "tenant": "AA", "canRegister": false, "storefrontEnabled": false, "importedCompaniesCanBuy": false, "currency": "USD", "languages": ["en"], "defaultLanguage": "en", "allowedCrossCountriesSales": ["MY"]}, {"isoCode": "MY", "tenant": "AZENA", "canRegister": false, "storefrontEnabled": false, "importedCompaniesCanBuy": false, "currency": "USD", "languages": ["en"], "defaultLanguage": "en", "allowedCrossCountriesSales": []}]}, {"isoCode": "NL", "name": "Netherlands", "activeInStore": true, "canSell": true, "canBuy": true, "blockedForDualUseApps": false, "inEU": true, "taxCategory": "NL0", "blockedCountriesCommercial": [], "blockedCountriesTrial": [], "paymentProviders": ["STRIPE", "DPG"], "paymentMethods": ["SEPA", "CREDIT_CARD"], "validation": {"postalCode": {"required": true, "pattern": "^[0-9]{4}\\s[A-Z]{2}$", "example": "1234 AA"}, "state": null, "region": null, "taxId": {"required": true, "pattern": "^NL[0-9]{9}B[0-9]{2}$", "example": "*NL**********B12"}}, "tenantConfigurations": [{"isoCode": "NL", "tenant": "AA", "canRegister": false, "storefrontEnabled": false, "importedCompaniesCanBuy": false, "currency": "EUR", "languages": ["en"], "defaultLanguage": "en", "allowedCrossCountriesSales": ["NL"]}, {"isoCode": "NL", "tenant": "AZENA", "canRegister": true, "storefrontEnabled": true, "importedCompaniesCanBuy": false, "currency": "EUR", "languages": ["en"], "defaultLanguage": "en", "allowedCrossCountriesSales": []}]}, {"isoCode": "NO", "name": "Norway", "activeInStore": true, "canSell": true, "canBuy": true, "blockedForDualUseApps": false, "inEU": false, "taxCategory": "NO1", "blockedCountriesCommercial": [], "blockedCountriesTrial": [], "paymentProviders": ["STRIPE", "DPG"], "paymentMethods": ["SEPA", "CREDIT_CARD"], "validation": {"postalCode": {"required": true, "pattern": "^[0-9]{4}$", "example": "1234"}, "state": null, "region": null, "taxId": {"required": true, "pattern": "^[0-9]{9}MVA$", "example": "**********MVA*"}}, "tenantConfigurations": [{"isoCode": "NO", "tenant": "AA", "canRegister": true, "storefrontEnabled": true, "importedCompaniesCanBuy": false, "currency": "EUR", "languages": ["en", "no"], "defaultLanguage": "no", "allowedCrossCountriesSales": ["NO"]}, {"isoCode": "NO", "tenant": "AZENA", "canRegister": true, "storefrontEnabled": true, "importedCompaniesCanBuy": false, "currency": "EUR", "languages": ["en"], "defaultLanguage": "en", "allowedCrossCountriesSales": []}]}, {"isoCode": "NZ", "name": "New Zealand", "activeInStore": true, "canSell": false, "canBuy": false, "blockedForDualUseApps": false, "inEU": false, "taxCategory": "NZ3", "blockedCountriesCommercial": [], "blockedCountriesTrial": [], "paymentProviders": ["DPG"], "paymentMethods": ["ACH"], "validation": {"postalCode": {"required": true, "pattern": "^[0-9]{4}$", "example": "1234"}, "state": null, "region": null, "taxId": {"required": true, "pattern": "^[0-9]{8}$", "example": "12345678"}}, "tenantConfigurations": [{"isoCode": "NZ", "tenant": "AZENA", "canRegister": false, "storefrontEnabled": false, "importedCompaniesCanBuy": false, "currency": "USD", "languages": ["en"], "defaultLanguage": "en", "allowedCrossCountriesSales": []}, {"isoCode": "NZ", "tenant": "AA", "canRegister": false, "storefrontEnabled": false, "importedCompaniesCanBuy": false, "currency": "USD", "languages": ["en"], "defaultLanguage": "en", "allowedCrossCountriesSales": ["NZ"]}]}, {"isoCode": "PL", "name": "Poland", "activeInStore": true, "canSell": true, "canBuy": true, "blockedForDualUseApps": false, "inEU": true, "taxCategory": "PL0", "blockedCountriesCommercial": [], "blockedCountriesTrial": [], "paymentProviders": ["STRIPE", "DPG"], "paymentMethods": ["SEPA", "CREDIT_CARD"], "validation": {"postalCode": {"required": true, "pattern": "^[0-9]{2}-[0-9]{3}$", "example": "12-123"}, "state": null, "region": null, "taxId": {"required": true, "pattern": "^PL[0-9]{10}$", "example": "*PL***********"}}, "tenantConfigurations": [{"isoCode": "PL", "tenant": "AA", "canRegister": false, "storefrontEnabled": false, "importedCompaniesCanBuy": false, "currency": "EUR", "languages": ["en"], "defaultLanguage": "en", "allowedCrossCountriesSales": ["PL"]}, {"isoCode": "PL", "tenant": "AZENA", "canRegister": true, "storefrontEnabled": true, "importedCompaniesCanBuy": false, "currency": "EUR", "languages": ["en"], "defaultLanguage": "en", "allowedCrossCountriesSales": []}]}, {"isoCode": "PT", "name": "Portugal", "activeInStore": true, "canSell": true, "canBuy": true, "blockedForDualUseApps": false, "inEU": true, "taxCategory": "PT0", "blockedCountriesCommercial": [], "blockedCountriesTrial": [], "paymentProviders": ["STRIPE", "DPG"], "paymentMethods": ["SEPA", "CREDIT_CARD"], "validation": {"postalCode": {"required": true, "pattern": "^[0-9]{4}-[0-9]{3}$", "example": "1234-123"}, "state": null, "region": null, "taxId": {"required": true, "pattern": "^PT[0-9]{9}$", "example": "*PT**********"}}, "tenantConfigurations": [{"isoCode": "PT", "tenant": "AA", "canRegister": true, "storefrontEnabled": true, "importedCompaniesCanBuy": false, "currency": "EUR", "languages": ["en", "pt"], "defaultLanguage": "pt", "allowedCrossCountriesSales": ["PT"]}, {"isoCode": "PT", "tenant": "AZENA", "canRegister": true, "storefrontEnabled": true, "importedCompaniesCanBuy": false, "currency": "EUR", "languages": ["en"], "defaultLanguage": "en", "allowedCrossCountriesSales": []}]}, {"isoCode": "RO", "name": "Romania", "activeInStore": true, "canSell": true, "canBuy": true, "blockedForDualUseApps": false, "inEU": true, "taxCategory": "RO0", "blockedCountriesCommercial": [], "blockedCountriesTrial": [], "paymentProviders": ["STRIPE", "DPG"], "paymentMethods": ["SEPA", "CREDIT_CARD"], "validation": {"postalCode": {"required": true, "pattern": "^[0-9]{6}$", "example": "040106"}, "state": null, "region": null, "taxId": {"required": true, "pattern": "^RO[0-9]{2,10}$", "example": "*RO*01, *RO*0*********"}}, "tenantConfigurations": [{"isoCode": "RO", "tenant": "AA", "canRegister": false, "storefrontEnabled": false, "importedCompaniesCanBuy": false, "currency": "EUR", "languages": ["en"], "defaultLanguage": "en", "allowedCrossCountriesSales": ["RO"]}, {"isoCode": "RO", "tenant": "AZENA", "canRegister": true, "storefrontEnabled": true, "importedCompaniesCanBuy": false, "currency": "EUR", "languages": ["en"], "defaultLanguage": "en", "allowedCrossCountriesSales": []}]}, {"isoCode": "SE", "name": "Sweden", "activeInStore": true, "canSell": true, "canBuy": true, "blockedForDualUseApps": false, "inEU": true, "taxCategory": "SE0", "blockedCountriesCommercial": [], "blockedCountriesTrial": [], "paymentProviders": ["STRIPE", "DPG"], "paymentMethods": ["SEPA", "CREDIT_CARD"], "validation": {"postalCode": {"required": true, "pattern": "^[0-9]{3}\\s[0-9]{2}$", "example": "123 45"}, "state": null, "region": null, "taxId": {"required": true, "pattern": "^SE[0-9]{12}$", "example": "*SE***********12"}}, "tenantConfigurations": [{"isoCode": "SE", "tenant": "AZENA", "canRegister": true, "storefrontEnabled": true, "importedCompaniesCanBuy": false, "currency": "EUR", "languages": ["en"], "defaultLanguage": "en", "allowedCrossCountriesSales": []}, {"isoCode": "SE", "tenant": "AA", "canRegister": true, "storefrontEnabled": true, "importedCompaniesCanBuy": false, "currency": "EUR", "languages": ["en", "sv"], "defaultLanguage": "sv", "allowedCrossCountriesSales": ["SE"]}]}, {"isoCode": "SG", "name": "Singapore", "activeInStore": true, "canSell": true, "canBuy": true, "blockedForDualUseApps": true, "inEU": false, "taxCategory": "SG1", "blockedCountriesCommercial": ["SG"], "blockedCountriesTrial": [], "paymentProviders": ["STRIPE", "DPG"], "paymentMethods": ["ACH", "CREDIT_CARD"], "validation": {"postalCode": {"required": true, "pattern": "^[0-9]{6}$", "example": "123456"}, "state": null, "region": null, "taxId": {"required": true, "pattern": "^[0-9]{8,9}[A-Z]{1}$", "example": "12345678A, *********A"}}, "tenantConfigurations": [{"isoCode": "SG", "tenant": "AA", "canRegister": false, "storefrontEnabled": false, "importedCompaniesCanBuy": false, "currency": "USD", "languages": ["en"], "defaultLanguage": "en", "allowedCrossCountriesSales": ["SG"]}, {"isoCode": "SG", "tenant": "AZENA", "canRegister": false, "storefrontEnabled": false, "importedCompaniesCanBuy": false, "currency": "USD", "languages": ["en"], "defaultLanguage": "en", "allowedCrossCountriesSales": []}]}, {"isoCode": "SI", "name": "Slovenia", "activeInStore": true, "canSell": true, "canBuy": true, "blockedForDualUseApps": false, "inEU": true, "taxCategory": "SI0", "blockedCountriesCommercial": [], "blockedCountriesTrial": [], "paymentProviders": ["STRIPE"], "paymentMethods": ["SEPA", "CREDIT_CARD"], "validation": {"postalCode": {"required": true, "pattern": "^[0-9]{4}$", "example": "1234"}, "state": null, "region": null, "taxId": {"required": true, "pattern": "^SI[0-9]{8}$", "example": "*SI*12345678"}}, "tenantConfigurations": [{"isoCode": "SI", "tenant": "AZENA", "canRegister": true, "storefrontEnabled": true, "importedCompaniesCanBuy": false, "currency": "EUR", "languages": ["en"], "defaultLanguage": "en", "allowedCrossCountriesSales": []}, {"isoCode": "SI", "tenant": "AA", "canRegister": true, "storefrontEnabled": true, "importedCompaniesCanBuy": false, "currency": "EUR", "languages": ["en", "sl"], "defaultLanguage": "sl", "allowedCrossCountriesSales": ["SI"]}]}, {"isoCode": "SK", "name": "Slovakia", "activeInStore": true, "canSell": true, "canBuy": true, "blockedForDualUseApps": false, "inEU": true, "taxCategory": "SK0", "blockedCountriesCommercial": [], "blockedCountriesTrial": [], "paymentProviders": ["STRIPE", "DPG"], "paymentMethods": ["SEPA", "CREDIT_CARD"], "validation": {"postalCode": {"required": true, "pattern": "^[0-9]{3}\\s[0-9]{2}$", "example": "123 45"}, "state": null, "region": null, "taxId": {"required": true, "pattern": "^SK[0-9]{10}$", "example": "*SK***********"}}, "tenantConfigurations": [{"isoCode": "SK", "tenant": "AZENA", "canRegister": true, "storefrontEnabled": true, "importedCompaniesCanBuy": false, "currency": "EUR", "languages": ["en"], "defaultLanguage": "en", "allowedCrossCountriesSales": []}, {"isoCode": "SK", "tenant": "AA", "canRegister": false, "storefrontEnabled": false, "importedCompaniesCanBuy": false, "currency": "EUR", "languages": ["en"], "defaultLanguage": "en", "allowedCrossCountriesSales": ["SK"]}]}, {"isoCode": "TR", "name": "Turkey", "activeInStore": true, "canSell": false, "canBuy": true, "blockedForDualUseApps": true, "inEU": false, "taxCategory": "TR0", "blockedCountriesCommercial": [], "blockedCountriesTrial": [], "paymentProviders": ["STRIPE", "DPG"], "paymentMethods": ["SEPA", "CREDIT_CARD"], "validation": {"postalCode": {"required": true, "pattern": "^[0-9]{5}$", "example": "12345"}, "state": null, "region": null, "taxId": {"required": true, "pattern": "^[0-9]{10}$", "example": "**********"}}, "tenantConfigurations": [{"isoCode": "TR", "tenant": "AZENA", "canRegister": false, "storefrontEnabled": false, "importedCompaniesCanBuy": false, "currency": "EUR", "languages": ["en"], "defaultLanguage": "en", "allowedCrossCountriesSales": []}, {"isoCode": "TR", "tenant": "AA", "canRegister": false, "storefrontEnabled": false, "importedCompaniesCanBuy": false, "currency": "EUR", "languages": ["en"], "defaultLanguage": "en", "allowedCrossCountriesSales": ["TR"]}]}, {"isoCode": "TW", "name": "Taiwan", "activeInStore": true, "canSell": false, "canBuy": true, "blockedForDualUseApps": true, "inEU": false, "taxCategory": "TW0", "blockedCountriesCommercial": [], "blockedCountriesTrial": [], "paymentProviders": ["STRIPE", "DPG"], "paymentMethods": ["ACH", "CREDIT_CARD"], "validation": {"postalCode": {"required": true, "pattern": "^[0-9]{5}$", "example": "12345"}, "state": null, "region": null, "taxId": {"required": true, "pattern": "^[0-9]{8}$", "example": "12345678"}}, "tenantConfigurations": [{"isoCode": "TW", "tenant": "AZENA", "canRegister": false, "storefrontEnabled": false, "importedCompaniesCanBuy": false, "currency": "USD", "languages": ["en"], "defaultLanguage": "en", "allowedCrossCountriesSales": []}, {"isoCode": "TW", "tenant": "AA", "canRegister": false, "storefrontEnabled": false, "importedCompaniesCanBuy": false, "currency": "USD", "languages": ["en"], "defaultLanguage": "en", "allowedCrossCountriesSales": ["TW"]}]}, {"isoCode": "US", "name": "United States of America", "activeInStore": true, "canSell": true, "canBuy": true, "blockedForDualUseApps": false, "inEU": false, "taxCategory": "US2", "blockedCountriesCommercial": [], "blockedCountriesTrial": [], "paymentProviders": ["STRIPE", "DPG"], "paymentMethods": ["ACH", "CREDIT_CARD"], "validation": {"postalCode": {"required": true, "pattern": "^[0-9]{5}\\-[0-9]{4}$", "example": "12345-1234"}, "state": {"required": true, "pattern": ".*", "regions": [{"abbrev": "AK", "name": "Alaska"}, {"abbrev": "AL", "name": "Alabama"}, {"abbrev": "AR", "name": "Arkansas"}, {"abbrev": "AS", "name": "American Samoa"}, {"abbrev": "AZ", "name": "Arizona"}, {"abbrev": "CA", "name": "California"}, {"abbrev": "CO", "name": "Colorado"}, {"abbrev": "CT", "name": "Connecticut"}, {"abbrev": "DC", "name": "District of Columbia"}, {"abbrev": "DE", "name": "Delaware"}, {"abbrev": "FL", "name": "Florida"}, {"abbrev": "GA", "name": "Georgia"}, {"abbrev": "GU", "name": "Guam"}, {"abbrev": "HI", "name": "Hawaii"}, {"abbrev": "IA", "name": "Iowa"}, {"abbrev": "ID", "name": "Idaho"}, {"abbrev": "IL", "name": "Illinois"}, {"abbrev": "IN", "name": "Indiana"}, {"abbrev": "KS", "name": "Kansas"}, {"abbrev": "KY", "name": "Kentucky"}, {"abbrev": "LA", "name": "Louisiana"}, {"abbrev": "MA", "name": "Massachusetts"}, {"abbrev": "MD", "name": "Maryland"}, {"abbrev": "ME", "name": "Maine"}, {"abbrev": "MI", "name": "Michigan"}, {"abbrev": "MN", "name": "Minnesota"}, {"abbrev": "MO", "name": "Missouri"}, {"abbrev": "MP", "name": "North. Mariana Isl."}, {"abbrev": "MS", "name": "Mississippi"}, {"abbrev": "MT", "name": "Montana"}, {"abbrev": "NC", "name": "North Carolina"}, {"abbrev": "ND", "name": "North Dakota"}, {"abbrev": "NE", "name": "Nebraska"}, {"abbrev": "NH", "name": "New Hampshire"}, {"abbrev": "NJ", "name": "New Jersey"}, {"abbrev": "NM", "name": "New Mexico"}, {"abbrev": "NV", "name": "Nevada"}, {"abbrev": "NY", "name": "New York"}, {"abbrev": "OH", "name": "Ohio"}, {"abbrev": "OK", "name": "Oklahoma"}, {"abbrev": "OR", "name": "Oregon"}, {"abbrev": "PA", "name": "Pennsylvania"}, {"abbrev": "PR", "name": "Puerto Rico"}, {"abbrev": "RI", "name": "Rhode Island"}, {"abbrev": "SC", "name": "South Carolina"}, {"abbrev": "SD", "name": "South Dakota"}, {"abbrev": "TN", "name": "Tennessee"}, {"abbrev": "TX", "name": "Texas"}, {"abbrev": "UT", "name": "Utah"}, {"abbrev": "VA", "name": "Virginia"}, {"abbrev": "VI", "name": "Virgin Islands, U.S."}, {"abbrev": "VT", "name": "Vermont"}, {"abbrev": "WA", "name": "Washington"}, {"abbrev": "WI", "name": "Wisconsin"}, {"abbrev": "WV", "name": "West Virginia"}, {"abbrev": "WY", "name": "Wyoming"}]}, "region": null, "taxId": {"required": true, "pattern": "^[0-9]{2}-[0-9]{7}$", "example": "12-1234567"}}, "tenantConfigurations": [{"isoCode": "US", "tenant": "AZENA", "canRegister": true, "storefrontEnabled": true, "importedCompaniesCanBuy": false, "currency": "USD", "languages": ["en"], "defaultLanguage": "en", "allowedCrossCountriesSales": []}, {"isoCode": "US", "tenant": "AA", "canRegister": false, "storefrontEnabled": false, "importedCompaniesCanBuy": false, "currency": "USD", "languages": ["en"], "defaultLanguage": "en", "allowedCrossCountriesSales": ["US"]}]}, {"isoCode": "VN", "name": "Vietnam", "activeInStore": true, "canSell": false, "canBuy": true, "blockedForDualUseApps": true, "inEU": false, "taxCategory": "VN1", "blockedCountriesCommercial": [], "blockedCountriesTrial": [], "paymentProviders": ["STRIPE", "DPG"], "paymentMethods": ["ACH", "CREDIT_CARD"], "validation": {"postalCode": {"required": true, "pattern": "^[0-9]{5}$", "example": "12345"}, "state": null, "region": null, "taxId": {"required": true, "pattern": "^[0-9]{10,16}$", "example": "**********, **********1, **********12, **********123, **********1234, **********12345, **********123456"}}, "tenantConfigurations": [{"isoCode": "VN", "tenant": "AZENA", "canRegister": false, "storefrontEnabled": false, "importedCompaniesCanBuy": false, "currency": "USD", "languages": ["en"], "defaultLanguage": "en", "allowedCrossCountriesSales": []}, {"isoCode": "VN", "tenant": "AA", "canRegister": false, "storefrontEnabled": false, "importedCompaniesCanBuy": false, "currency": "USD", "languages": ["en"], "defaultLanguage": "en", "allowedCrossCountriesSales": ["VN"]}]}, {"isoCode": "ZA", "name": "South Africa", "activeInStore": true, "canSell": false, "canBuy": false, "blockedForDualUseApps": true, "inEU": false, "taxCategory": "ZA1", "blockedCountriesCommercial": [], "blockedCountriesTrial": [], "paymentProviders": ["DPG"], "paymentMethods": ["ACH"], "validation": {"postalCode": {"required": true, "pattern": "^[0-9]{4}$", "example": "1234"}, "state": null, "region": {"required": true, "pattern": ".*", "regions": [{"abbrev": "EC", "name": "Eastern Cape"}, {"abbrev": "FS", "name": "Free State"}, {"abbrev": "GP", "name": "Gauteng"}, {"abbrev": "KZN", "name": "Kwazulu-Natal"}, {"abbrev": "LP", "name": "Limpopo"}, {"abbrev": "MP", "name": "Mpumalanga"}, {"abbrev": "NC", "name": "Northern Cape"}, {"abbrev": "NW", "name": "North-West"}, {"abbrev": "WC", "name": "Western Cape"}]}, "taxId": {"required": true, "pattern": "^[0-9]{10}$", "example": "**********"}}, "tenantConfigurations": [{"isoCode": "ZA", "tenant": "AA", "canRegister": false, "storefrontEnabled": false, "importedCompaniesCanBuy": false, "currency": "USD", "languages": ["en"], "defaultLanguage": "en", "allowedCrossCountriesSales": ["ZA"]}, {"isoCode": "ZA", "tenant": "AZENA", "canRegister": false, "storefrontEnabled": false, "importedCompaniesCanBuy": false, "currency": "USD", "languages": ["en"], "defaultLanguage": "en", "allowedCrossCountriesSales": []}]}]