<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android" android:versionCode="1" android:versionName="1.0" android:compileSdkVersion="23" android:compileSdkVersionCodename="6.0-2438415" package="com.bosch.cbsis.remotealerting.intrusiontestclient" platformBuildVersionCode="25" platformBuildVersionName="7.1.1">
	<uses-sdk android:minSdkVersion="14" android:targetSdkVersion="25" />
	<meta-data android:name="android.support.VERSION" android:value="25.3.0" />
	<uses-permission android:name="android.permission.INTERNET" />
	<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
	<uses-permission android:name="android.permission.WAKE_LOCK" />
	<uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
	<permission android:name="com.bosch.cbsis.remotealerting.intrusiontestclient.permission.C2D_MESSAGE" android:protectionLevel="0x2" />
	<uses-permission android:name="com.bosch.cbsis.remotealerting.intrusiontestclient.permission.C2D_MESSAGE" />
	<uses-feature android:name="com.securityandsafetythings.devicecapabilities.PTZ" android:required="true" />
	<application android:theme="resourceId:0x7f0800a3" android:label="Remote Portal Cloud Messaging" android:icon="res/drawable-xxxhdpi-v4/ic_remote_portal_logo.png" android:debuggable="true" android:allowBackup="true">
		<meta-data android:name="com.google.firebase.messaging.default_notification_icon" android:resource="res/drawable-xxxhdpi-v4/ic_remote_portal_logo.png" />
		<meta-data android:name="com.google.firebase.messaging.default_notification_color" android:resource="ffffa00" />
		<activity android:label="Remote Portal Cloud Messaging" android:name="com.bosch.cbsis.remotealerting.intrusiontestclient.MainActivity">
			<intent-filter>
				<action android:name="android.intent.action.MAIN" />
				<category android:name="android.intent.category.LAUNCHER" />
			</intent-filter>
		</activity>
		<service android:name="com.bosch.cbsis.remotealerting.intrusiontestclient.MyFirebaseMessagingService">
			<intent-filter>
				<action android:name="com.google.firebase.MESSAGING_EVENT" />
			</intent-filter>
		</service>
		<service android:name="com.bosch.cbsis.remotealerting.intrusiontestclient.MyFirebaseInstanceIDService">
			<intent-filter>
				<action android:name="com.google.firebase.INSTANCE_ID_EVENT" />
			</intent-filter>
		</service>
		<service android:name="com.bosch.cbsis.remotealerting.intrusiontestclient.MyJobService" android:exported="false">
			<intent-filter>
				<action android:name="com.firebase.jobdispatcher.ACTION_EXECUTE" />
			</intent-filter>
		</service>
		<receiver android:name="com.google.android.gms.measurement.AppMeasurementReceiver" android:enabled="true" android:exported="false" />
		<receiver android:name="com.google.android.gms.measurement.AppMeasurementInstallReferrerReceiver" android:permission="android.permission.INSTALL_PACKAGES" android:enabled="true">
			<intent-filter>
				<action android:name="com.android.vending.INSTALL_REFERRER" />
			</intent-filter>
		</receiver>
		<service android:name="com.google.android.gms.measurement.AppMeasurementService" android:enabled="true" android:exported="false" />
		<service android:name="com.google.firebase.messaging.FirebaseMessagingService" android:exported="true">
			<intent-filter android:priority="-500">
				<action android:name="com.google.firebase.MESSAGING_EVENT" />
			</intent-filter>
		</service>
		<receiver android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver" android:permission="com.google.android.c2dm.permission.SEND" android:exported="true">
			<intent-filter>
				<action android:name="com.google.android.c2dm.intent.RECEIVE" />
				<category android:name="com.bosch.cbsis.remotealerting.intrusiontestclient" />
			</intent-filter>
		</receiver>
		<receiver android:name="com.google.firebase.iid.FirebaseInstanceIdInternalReceiver" android:exported="false" />
		<service android:name="com.google.firebase.iid.FirebaseInstanceIdService" android:exported="true">
			<intent-filter android:priority="-500">
				<action android:name="com.google.firebase.INSTANCE_ID_EVENT" />
			</intent-filter>
		</service>
		<provider android:name="com.google.firebase.provider.FirebaseInitProvider" android:exported="false" android:authorities="com.bosch.cbsis.remotealerting.intrusiontestclient.firebaseinitprovider" android:initOrder="100" />
		<meta-data android:name="com.google.android.gms.version" android:value="10298000" />
		<service android:name="com.firebase.jobdispatcher.GooglePlayReceiver" android:permission="com.google.android.gms.permission.BIND_NETWORK_TASK_SERVICE" android:exported="true">
			<intent-filter>
				<action android:name="com.google.android.gms.gcm.ACTION_TASK_READY" />
			</intent-filter>
		</service>
	</application>
</manifest>
