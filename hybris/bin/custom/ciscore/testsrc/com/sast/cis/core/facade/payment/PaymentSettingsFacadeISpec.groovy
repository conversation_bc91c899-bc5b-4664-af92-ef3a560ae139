package com.sast.cis.core.facade.payment


import com.sast.cis.core.enums.PaymentProvider
import com.sast.cis.core.exceptions.InvalidStateException
import com.sast.cis.core.exceptions.payment.PaymentInfoNotFoundException
import com.sast.cis.core.model.IntegratorModel
import com.sast.cis.core.model.SepaMandatePaymentInfoModel
import com.sast.cis.core.service.customer.integrator.IntegratorService
import de.hybris.bootstrap.annotations.IntegrationTest
import de.hybris.platform.core.model.order.payment.PaymentInfoModel
import de.hybris.platform.servicelayer.ServicelayerTransactionalSpockSpecification
import de.hybris.platform.servicelayer.model.ModelService
import de.hybris.platform.servicelayer.user.UserService
import generated.com.sast.cis.core.model.SepaCreditTransferPaymentInfoBuilder
import generated.com.sast.cis.core.model.SepaMandatePaymentInfoBuilder
import generated.com.sast.cis.core.model.StripeCreditCardPaymentInfoBuilder
import org.junit.Test
import spock.lang.Unroll

import javax.annotation.Resource

import static com.sast.cis.test.utils.TestDataConstants.SAMPLE_DATA_INTEGRATOR_UID

@IntegrationTest
class PaymentSettingsFacadeISpec extends ServicelayerTransactionalSpockSpecification {

    private static Map<String, PaymentInfoModel> map = [:]
    private static final String CREDIT_CARD_CODE_1 = "pm_cc_1"
    private static final String CREDIT_CARD_CODE_2 = "pm_cc_2"
    private static final String SEPA_DD_CODE_1 = "pm_dd_1"
    private static final String SEPA_DD_CODE_2 = "pm_dd_2"
    private static final String INVOICE_CODE_1 = "pm_inv_2"


    @Resource
    private PaymentSettingsFacade paymentSettingsFacade

    @Resource
    private IntegratorService integratorService

    @Resource
    private ModelService modelService

    @Resource
    private UserService userService

    private PaymentInfoModel ccPaymentInfo1
    private PaymentInfoModel ccPaymentInfo2
    private PaymentInfoModel ddPaymentInfo1
    private PaymentInfoModel ddPaymentInfo2
    private PaymentInfoModel invoicePaymentInfo
    private IntegratorModel integrator


    def setup() {
        integrator = integratorService.getIntegratorByInternalUserId(SAMPLE_DATA_INTEGRATOR_UID)
        ccPaymentInfo2 = creditCardPaymentInfo(CREDIT_CARD_CODE_2, integrator)
        ccPaymentInfo1 = creditCardPaymentInfo(CREDIT_CARD_CODE_1, integrator)
        ddPaymentInfo1 = sepaMandatePaymentInfo(SEPA_DD_CODE_1, integrator)
        ddPaymentInfo2 = sepaMandatePaymentInfo(SEPA_DD_CODE_2, integrator)
        invoicePaymentInfo = invoicePaymentInfo(INVOICE_CODE_1, integrator)
        modelService.saveAll(ccPaymentInfo1, ccPaymentInfo2, ddPaymentInfo1, ddPaymentInfo2, invoicePaymentInfo)

        map << [
                (CREDIT_CARD_CODE_1): ccPaymentInfo1,
                (CREDIT_CARD_CODE_2): ccPaymentInfo2,
                (INVOICE_CODE_1)    : invoicePaymentInfo
        ]

        userService.currentUser = integrator
    }

    @Test
    def "get payment settings data "() {
        when:
        def paymentSettingsData = paymentSettingsFacade.getPaymentSettingsData()

        then:
        paymentSettingsData.ccPaymentInfos
        paymentSettingsData.ccPaymentInfos.collect { it.id }.toSet()
                .containsAll(ccPaymentInfo1.pk.toString(), ccPaymentInfo2.pk.toString())

        paymentSettingsData.directDebitPaymentInfos
        paymentSettingsData.directDebitPaymentInfos.collect { it.id }.toSet()
                .containsAll(ddPaymentInfo1.pk.toString(), ddPaymentInfo2.pk.toString())

        paymentSettingsData.invoicePaymentInfos
        paymentSettingsData.invoicePaymentInfos.collect { it.id }.toSet()
                .containsAll(invoicePaymentInfo.pk.toString())
    }


    @Test
    @Unroll
    def "remove payment id"() {
        given:
        integrator.paymentInfos = paymentInfos.collect { map[it] }
        integrator.defaultPaymentInfo = map[defaultPaymentInfo]
        modelService.save(integrator)
        modelService.refresh(integrator)

        when:
        PaymentInfoModel piToRemove = map[paymenCodeToRemove]
        paymentSettingsFacade.removePaymentInfoForId(piToRemove.pk as String)

        then:
        !(modelService.get(piToRemove.pk) as PaymentInfoModel).isSaved()

        if (!integrator.defaultPaymentInfo) {
            null == resultPaymentInfo
        } else {
            integrator.defaultPaymentInfo.code == resultPaymentInfo
        }
        where:
        hasCreditLimit | paymenCodeToRemove | paymentInfos                                             | defaultPaymentInfo || resultPaymentInfo
        true           | CREDIT_CARD_CODE_1 | [CREDIT_CARD_CODE_1, INVOICE_CODE_1, CREDIT_CARD_CODE_2] | CREDIT_CARD_CODE_1 || CREDIT_CARD_CODE_2
        true           | CREDIT_CARD_CODE_1 | [CREDIT_CARD_CODE_1, INVOICE_CODE_1]                     | CREDIT_CARD_CODE_1 || INVOICE_CODE_1
    }


    @Test
    @Unroll
    def "update default payment method"() {
        given:
        integrator.paymentInfos = paymentInfos.collect { map[it] }
        integrator.defaultPaymentInfo = map[defaultPaymentInfo]
        modelService.save(integrator)
        modelService.refresh(integrator)

        when:
        PaymentInfoModel piToRemove = map[paymenCodeToSetDefault]
        paymentSettingsFacade.setPaymentInfoAsDefault(piToRemove.pk as String)


        then:
        if (!integrator.defaultPaymentInfo) {
            null == resultPaymentInfo
        } else {
            integrator.defaultPaymentInfo.code == resultPaymentInfo
        }

        where:
        paymenCodeToSetDefault | paymentInfos                                             | defaultPaymentInfo || resultPaymentInfo
        CREDIT_CARD_CODE_2     | [CREDIT_CARD_CODE_1, INVOICE_CODE_1, CREDIT_CARD_CODE_2] | CREDIT_CARD_CODE_1 || CREDIT_CARD_CODE_2
        CREDIT_CARD_CODE_1     | [CREDIT_CARD_CODE_1, INVOICE_CODE_1, CREDIT_CARD_CODE_2] | CREDIT_CARD_CODE_2 || CREDIT_CARD_CODE_1
    }


    @Test
    @Unroll
    def "update default payment method errors"() {
        given:
        integrator.paymentInfos = paymentInfos.collect { map[it] }
        integrator.defaultPaymentInfo = map[defaultPaymentInfo]
        modelService.save(integrator)
        modelService.refresh(integrator)

        when:
        PaymentInfoModel piToRemove = map[paymenCodeToSetDefault] as PaymentInfoModel
        paymentSettingsFacade.setPaymentInfoAsDefault(piToRemove.pk as String)


        then:
        thrown(exception)

        if (!integrator.defaultPaymentInfo) {
            null == resultPaymentInfo
        } else {
            integrator.defaultPaymentInfo.code == resultPaymentInfo
        }

        where:
        paymenCodeToSetDefault | paymentInfos                         | defaultPaymentInfo || resultPaymentInfo  | exception
        INVOICE_CODE_1         | [CREDIT_CARD_CODE_1, INVOICE_CODE_1] | CREDIT_CARD_CODE_1 || CREDIT_CARD_CODE_1 | InvalidStateException
        CREDIT_CARD_CODE_2     | [CREDIT_CARD_CODE_1, INVOICE_CODE_1] | CREDIT_CARD_CODE_1 || CREDIT_CARD_CODE_1 | PaymentInfoNotFoundException
    }

    PaymentInfoModel creditCardPaymentInfo(String code, IntegratorModel integrator) {
        StripeCreditCardPaymentInfoBuilder.generate()
                .withCode(code)
                .withPaymentMethodId("paymentMethodOfCartId")
                .withPaymentProvider(PaymentProvider.STRIPE)
                .withSaved(true)
                .withReusable(true)
                .withDuplicate(false)
                .withUser(integrator)
                .buildIntegrationInstance()
    }

    PaymentInfoModel invoicePaymentInfo(String code, IntegratorModel integrator) {
        SepaCreditTransferPaymentInfoBuilder.generate()
                .withCode(code)
                .withPaymentProvider(PaymentProvider.STRIPE)
                .withSaved(true)
                .withDuplicate(false)
                .withUser(integrator)
                .buildIntegrationInstance()

    }

    SepaMandatePaymentInfoModel sepaMandatePaymentInfo(String code, IntegratorModel integrator) {
        SepaMandatePaymentInfoBuilder.generate()
                .withCode(code)
                .withPaymentProvider(PaymentProvider.PGW)
                .withMandateReference(UUID.randomUUID().toString())
                .withSaved(true)
                .withDuplicate(false)
                .withUser(integrator)
                .buildIntegrationInstance()

    }

}
