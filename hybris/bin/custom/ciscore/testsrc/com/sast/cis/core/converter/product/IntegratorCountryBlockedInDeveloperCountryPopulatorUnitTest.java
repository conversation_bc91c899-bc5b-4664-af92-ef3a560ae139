package com.sast.cis.core.converter.product;

import com.sast.cis.core.model.AppModel;
import com.sast.cis.core.model.IoTCompanyModel;
import com.sast.cis.core.service.company.IotCompanyService;
import com.sast.cis.core.service.country.IntegratorCountryService;
import de.hybris.bootstrap.annotations.UnitTest;
import de.hybris.platform.commercefacades.product.data.ProductData;
import de.hybris.platform.core.model.c2l.CountryModel;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@UnitTest
@RunWith(MockitoJUnitRunner.Silent.class)
public class IntegratorCountryBlockedInDeveloperCountryPopulatorUnitTest {

    @Mock
    private IotCompanyService companyService;

    @Mock
    private IntegratorCountryService integratorCountryService;

    @InjectMocks
    private IntegratorCountryBlockedInDeveloperCountryPopulator populator;

    @Mock
    private AppModel app;

    @Mock
    private IoTCompanyModel integratorCompany;

    @Mock
    private IoTCompanyModel developerCompany;

    @Mock
    private CountryModel integratorCountry;

    @Mock
    private CountryModel developerCountry;

    private final ProductData productData = new ProductData();

    @Before
    public void setup() {
        when(app.getCompany()).thenReturn(developerCompany);

        when(developerCompany.getCountry()).thenReturn(developerCountry);
        when(integratorCompany.getCountry()).thenReturn(integratorCountry);

        when(companyService.getCurrentCompany()).thenReturn(Optional.of(integratorCompany));

        when(integratorCountryService.isIntegratorCountryBlockedInDeveloperCountry(integratorCountry, developerCountry)).thenReturn(false);
    }

    @Test
    public void testPopulate() {
        populator.populate(app, productData);

        assertThat(productData.isIntegratorCountryBlockedInDeveloperCountry()).isFalse();
    }

    @Test
    public void givenIntegratorCountryBlockedInDeveloperCountry_whenPopulate_thenSetToTrue() {
        when(integratorCountryService.isIntegratorCountryBlockedInDeveloperCountry(integratorCountry, developerCountry)).thenReturn(true);

        populator.populate(app, productData);

        assertThat(productData.isIntegratorCountryBlockedInDeveloperCountry()).isTrue();
    }
}
