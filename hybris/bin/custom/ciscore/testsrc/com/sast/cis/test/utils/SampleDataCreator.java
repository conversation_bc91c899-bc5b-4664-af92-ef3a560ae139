package com.sast.cis.test.utils;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableSet;
import com.sast.cis.aa.core.enums.MigrationMode;
import com.sast.cis.aa.core.model.ContractMigrationProcessModel;
import com.sast.cis.aa.core.model.MigrationContractGroupModel;
import com.sast.cis.aa.core.model.MigrationContractModel;
import com.sast.cis.aa.core.model.MigrationOrderDraftModel;
import com.sast.cis.aa.core.model.MigrationOrderEntryDraftModel;
import com.sast.cis.core.constants.BaseStoreEnum;
import com.sast.cis.core.constants.CiscoreConstants;
import com.sast.cis.core.constants.Currency;
import com.sast.cis.core.dao.CatalogVersion;
import com.sast.cis.core.distributor.AaDistributorService;
import com.sast.cis.core.enums.*;
import com.sast.cis.core.model.*;
import com.sast.cis.core.service.DaoQueryService;
import com.sast.cis.core.service.company.IotCompanyService;
import com.sast.cis.core.service.customer.developer.DeveloperService;
import com.sast.cis.core.service.customer.integrator.IntegratorService;
import com.sast.cis.core.service.migration.CountryMigrationConfigurationService;
import com.sast.cis.core.util.Base58UUIDCodeGenerator;
import com.sast.cis.payment.boschtransfer.model.BoschSellerAccountModel;
import com.sast.cis.payment.dpg.enums.DpgSellerAccountStatus;
import com.sast.cis.payment.dpg.model.DpgSellerAccountModel;
import com.sast.subscription.model.ContractCancellationInfoModel;
import com.sast.subscription.model.FutureContractCancellationBusinessProcessModel;
import de.hybris.platform.basecommerce.model.site.BaseSiteModel;
import de.hybris.platform.catalog.enums.ArticleApprovalStatus;
import de.hybris.platform.catalog.model.CatalogModel;
import de.hybris.platform.catalog.model.CatalogUnawareMediaModel;
import de.hybris.platform.catalog.model.CatalogVersionModel;
import de.hybris.platform.cms2.model.site.CMSSiteModel;
import de.hybris.platform.core.enums.OrderStatus;
import de.hybris.platform.core.model.ItemModel;
import de.hybris.platform.core.model.c2l.CountryModel;
import de.hybris.platform.core.model.c2l.CurrencyModel;
import de.hybris.platform.core.model.c2l.LanguageModel;
import de.hybris.platform.core.model.media.MediaContainerModel;
import de.hybris.platform.core.model.media.MediaFolderModel;
import de.hybris.platform.core.model.media.MediaModel;
import de.hybris.platform.core.model.order.AbstractOrderModel;
import de.hybris.platform.core.model.order.CartEntryModel;
import de.hybris.platform.core.model.order.CartModel;
import de.hybris.platform.core.model.order.OrderEntryModel;
import de.hybris.platform.core.model.order.OrderModel;
import de.hybris.platform.core.model.order.payment.PaymentInfoModel;
import de.hybris.platform.core.model.product.ProductModel;
import de.hybris.platform.core.model.user.UserGroupModel;
import de.hybris.platform.core.model.user.UserModel;
import de.hybris.platform.europe1.model.PriceRowModel;
import de.hybris.platform.mediaconversion.model.ConversionGroupModel;
import de.hybris.platform.ordercancel.model.OrderCancelRecordModel;
import de.hybris.platform.orderprocessing.model.OrderProcessModel;
import de.hybris.platform.payment.dto.TransactionStatus;
import de.hybris.platform.payment.dto.TransactionStatusDetails;
import de.hybris.platform.payment.enums.PaymentTransactionType;
import de.hybris.platform.payment.model.PaymentTransactionModel;
import de.hybris.platform.processengine.enums.ProcessState;
import de.hybris.platform.product.UnitService;
import de.hybris.platform.product.VariantsService;
import de.hybris.platform.servicelayer.exceptions.AmbiguousIdentifierException;
import de.hybris.platform.servicelayer.i18n.CommonI18NService;
import de.hybris.platform.servicelayer.i18n.I18NService;
import de.hybris.platform.servicelayer.media.MediaService;
import de.hybris.platform.servicelayer.model.ModelService;
import de.hybris.platform.servicelayer.search.FlexibleSearchQuery;
import de.hybris.platform.servicelayer.search.FlexibleSearchService;
import de.hybris.platform.servicelayer.user.UserService;
import de.hybris.platform.store.BaseStoreModel;
import de.hybris.platform.store.services.BaseStoreService;
import generated.com.sast.cis.aa.core.model.ContractMigrationProcessBuilder;
import generated.com.sast.cis.aa.core.model.MigrationContractBuilder;
import generated.com.sast.cis.aa.core.model.MigrationContractGroupBuilder;
import generated.com.sast.cis.aa.core.model.MigrationOrderDraftBuilder;
import generated.com.sast.cis.aa.core.model.MigrationOrderEntryDraftBuilder;
import generated.com.sast.cis.core.model.*;
import generated.com.sast.cis.payment.boschtransfer.model.BoschSellerAccountBuilder;
import generated.com.sast.cis.payment.boschtransfer.model.BoschSepaCollectionAccountBuilder;
import generated.com.sast.cis.payment.dpg.model.DpgSellerAccountBuilder;
import generated.com.sast.subscription.model.ContractCancellationInfoBuilder;
import generated.com.sast.subscription.model.FutureContractCancellationBusinessProcessBuilder;
import generated.de.hybris.platform.catalog.model.CatalogBuilder;
import generated.de.hybris.platform.catalog.model.CatalogUnawareMediaBuilder;
import generated.de.hybris.platform.catalog.model.CatalogVersionBuilder;
import generated.de.hybris.platform.core.model.media.MediaBuilder;
import generated.de.hybris.platform.core.model.media.MediaContainerBuilder;
import generated.de.hybris.platform.core.model.order.CartBuilder;
import generated.de.hybris.platform.core.model.order.CartEntryBuilder;
import generated.de.hybris.platform.core.model.order.OrderBuilder;
import generated.de.hybris.platform.core.model.order.OrderEntryBuilder;
import generated.de.hybris.platform.europe1.model.PriceRowBuilder;
import generated.de.hybris.platform.ordercancel.model.OrderCancelRecordBuilder;
import generated.de.hybris.platform.orderprocessing.model.OrderProcessBuilder;
import generated.de.hybris.platform.payment.model.PaymentTransactionBuilder;
import generated.de.hybris.platform.payment.model.PaymentTransactionEntryBuilder;
import org.apache.commons.lang3.time.DateUtils;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Optional;
import java.util.Random;
import java.util.Set;
import java.util.concurrent.TimeUnit;

import static com.sast.cis.core.constants.CiscoreConstants.*;
import static com.sast.cis.core.constants.Currency.EUR;
import static com.sast.cis.core.constants.Currency.USD;
import static com.sast.cis.core.constants.DynamicBusinessProcessesDefinitions.AA_BILLING_ORDER_PROCESS;
import static com.sast.cis.core.constants.DynamicBusinessProcessesDefinitions.FUTURE_CONTRACT_CANCELLATION_PROCESS;
import static com.sast.cis.core.constants.DynamicBusinessProcessesDefinitions.OPEN_ORDER_CANCEL_PROCESS;
import static com.sast.cis.core.enums.LicenseType.EVALUATION;
import static com.sast.cis.core.enums.LicenseType.FULL;
import static com.sast.cis.core.enums.LicenseType.SUBSCRIPTION;
import static com.sast.cis.test.utils.Country.AUSTRIA;
import static com.sast.cis.test.utils.Country.GERMANY;
import static com.sast.cis.test.utils.Country.USA;
import static com.sast.cis.test.utils.TestDataConstants.*;
import static com.sast.cis.test.utils.TestDataConstants.PRODUCT_CONTAINER.*;
import static de.hybris.platform.catalog.enums.ArticleApprovalStatus.APPROVED;
import static de.hybris.platform.core.Registry.getApplicationContext;
import static java.time.ZoneOffset.UTC;
import static java.util.Calendar.SECOND;
import static java.util.Locale.ENGLISH;
import static java.util.Locale.GERMAN;
import static java.util.UUID.randomUUID;
import static java.util.stream.Collectors.toSet;
import static org.awaitility.Awaitility.await;

public class SampleDataCreator {
    public static final String APP_VIDEO_SOURCE = "https://www.youtube.com/watch?v=dQw4w9WgXcQ";
    public static final String STORE_CONTENT_DRAFT_PREFIX = "StoreContentDraft-";
    public static final String VERSION_DRAFT_PREFIX = "VersionDraft-";
    public static final String COUNTRIES_AND_PRICES_DRAFT_CODE_PREFIX = "CNP-";
    public static final String PRIVACY_POLICY = "www.google.com";
    public static final String PRODUCT_CONTAINER_CODE_PREFIX = "testContainer_";
    public static final String APP_CODE_PREFIX = "AppForContainer_";
    public static final String APP_NAME_PREFIX_EN = "name_";
    public static final String APP_NAME_PREFIX_DE = "Name_";
    public static final String PACKAGE_NAME_PREFIX = "containerpackage.";
    public static final String PACKAGE_NAME = PACKAGE_NAME_PREFIX + "testpackagename";
    public static final String APP_URL = "www.sample-app.com";
    public static final String APP_PHONE_NUMBER = "1234support";
    public static final String APP_CONTACT_EMAIL = "<EMAIL>";
    public static final String APP_DESCRIPTION_EN = "app description";
    public static final String APP_DESCRIPTION_DE = "App-Beschreibung";
    public static final String APP_SUMMARY_EN = "app summary";
    public static final String APP_SUMMARY_DE = "App-Zusammenfassung";
    public static final String BRIM_NAME_PREFIX = "BrimName_";
    public static final double EUR_PRICE = 93;
    public static final double USD_PRICE = 130;
    public static final String CHANGELOG = "changelog";
    public static final String APPLICENSE_PREFIX = "applicense-";
    public static final String MEDIA_CONTAINER_CODE_SUFFIX = "-container";
    public static final String SCREENSHOT_PREFIX = "screenshot-";
    public static final String ICON_PREFIX = "icon-";
    public static final int DEFAULT_MIN_ANDROID_API_VERSION = 27;
    public static final int DEFAULT_SDK_ADDON_VERSION = 1;

    public static final String COUNTRY_EULA_ESI_AT = "https://cdn.esitronic.de/eula/ESItronic/AT_EULA.html";
    public static final String COUNTRY_EULA_ESI_DEFAULT = "https://cdn.esitronic.de/eula/ESItronic/COMMON_EULA.html";

    private static final String APP_DRAFT_PREFIX = "appDraft_";
    private static final String FIND_IOT_COMPANY_FOR_UID = "SELECT {" + IoTCompanyModel.PK + "} FROM {" + IoTCompanyModel._TYPECODE
        + "} WHERE {" + IoTCompanyModel.UID + "}=?uid";
    private static final String STORECONTENT_DRAFT_CODE_PREFIX = "SCD_";

    private final FlexibleSearchService flexibleSearchService = getApplicationContext().getBean(FlexibleSearchService.class);
    private final ModelService modelService = getApplicationContext().getBean(ModelService.class);
    private final VariantsService variantsService = getApplicationContext().getBean("variantsService", VariantsService.class);
    private final DeveloperService developerService = getApplicationContext().getBean(DeveloperService.class);
    private final UserService userService = getApplicationContext().getBean("defaultUserService", UserService.class);
    private final IntegratorService integratorService = getApplicationContext().getBean(IntegratorService.class);
    private final CommonI18NService commonI18NService = getApplicationContext().getBean(CommonI18NService.class);
    private final UnitService unitService = getApplicationContext().getBean(UnitService.class);
    private final MediaService mediaService = getApplicationContext().getBean(MediaService.class);
    private final DaoQueryService daoQueryService = getApplicationContext().getBean(DaoQueryService.class);
    private final I18NService i18NService = getApplicationContext().getBean(I18NService.class);
    private final BaseStoreService baseStoreService = getApplicationContext().getBean(BaseStoreService.class);
    private final IotCompanyService iotCompanyService = getApplicationContext().getBean(IotCompanyService.class);
    private final AaDistributorService aaDistributorService = getApplicationContext().getBean(AaDistributorService.class);
    private final CountryMigrationConfigurationService countryMigrationConfigurationService = getApplicationContext().getBean(
        CountryMigrationConfigurationService.class);

    public static String getProductContainerCodeFromTitle(String title) {
        return PRODUCT_CONTAINER_CODE_PREFIX + title;
    }

    private static byte[] getImageTestData(String imageDataPath) {
        try {
            return Files.readAllBytes(
                Paths.get(SampleDataCreator.class.getClassLoader().getResource(imageDataPath).getFile()));
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public byte[] getImageTestData() {
        return getImageTestData("testsrc/images/valid-screenshot-cat.png");
    }

    public byte[] getPdfDocumentTestData() {
        return getImageTestData("testsrc/documents/sample.pdf");
    }

    public byte[] getAlternativeImageTestData() {
        return getImageTestData("testsrc/images/valid-screenshot-cat.jpg");
    }

    public List<AppModel> createApps(int numberOfApps, String catalogVersion, boolean withVersion) {
        return createApps(numberOfApps, getOrCreateProductCatalogVersion(catalogVersion), withVersion, 0);
    }

    public List<AppModel> createApps(int numberOfApps, CatalogVersion catalogVersion, boolean withVersion) {
        return createApps(numberOfApps, catalogVersion, withVersion, 0);
    }

    public List<AppModel> createApps(int numberOfApps, CatalogVersion catalogVersion, boolean withVersion, int offset) {
        return createApps(numberOfApps, getOrCreateProductCatalogVersion(catalogVersion), withVersion, offset);
    }

    public List<AppModel> createApps(int numberOfApps, CatalogVersionModel catalogVersion, boolean withVersion, int offset) {
        List<AppModel> apps = new ArrayList<>();
        DeveloperModel developer = getDeveloperByInternalUserId(SAMPLE_DATA_DEVELOPER_A1_UID);

        for (int i = offset; i < numberOfApps + offset; i++) {
            AppModel baseProduct = createApp(getAppNameForIndex(i), getPackageNameForIndex(i), developer, catalogVersion);
            AppLicenseModel appLicense = createFullAppLicense(getAppVersionCodeForIndex(i), baseProduct);
            AppLicenseModel evaluationVersionLicense = createEvalForFullAppLicense(appLicense);
            if (withVersion) {
                createAppVersion(getAppNameForIndex(i) + "_version", baseProduct, catalogVersion, getAppNameForIndex(i) + "_apk");
            }
            modelService.saveAll(baseProduct, developer, appLicense, evaluationVersionLicense);
            apps.add(baseProduct);
        }
        return apps;
    }

    public AppModel createApp(String code, String packageName, CatalogVersion catalogVersion) {
        return createApp(code, packageName, CiscoreConstants.CIS_PRODUCT_CATALOG, catalogVersion);
    }

    public AppModel createAppWithMultipleLicensesOfType(String code, String packageName, CatalogVersion catalogVersion,
        LicenseType licenseType) {
        DeveloperModel developer = getDeveloperByInternalUserId(SAMPLE_DATA_DEVELOPER_A1_UID);
        return createAppWithMultipleLicensesOfType(code, packageName, developer, catalogVersion, licenseType, APPROVED);
    }

    public AppModel createAppWithMultipleLicensesOfType(String code, String packageName, DeveloperModel developer,
        CatalogVersion catalogVersion, LicenseType licenseType, ArticleApprovalStatus approvalStatus) {
        AppModel app = createApp(code, packageName, developer, CiscoreConstants.CIS_PRODUCT_CATALOG, catalogVersion, approvalStatus);
        createAppLicense(APPLICENSE_PREFIX + FULL_SUFFIX, app, approvalStatus, licenseType);
        createAppLicense(APPLICENSE_PREFIX + FULL_SUFFIX + "_1", app, approvalStatus, licenseType);
        return app;
    }

    public AppModel createApp(String code, String packageName, String catalogId, CatalogVersion catalogVersion) {
        UserModel currentUser = userService.getCurrentUser();
        DeveloperModel developer = getDeveloperByInternalUserId(SAMPLE_DATA_DEVELOPER_A1_UID);
        developerService.setCurrentDeveloper(developer);

        AppModel app = createApp(code, packageName, developer, catalogId, catalogVersion, APPROVED);
        userService.setCurrentUser(currentUser);
        return app;
    }

    public AppModel createApp(String code, String packageName, DeveloperModel developer, CatalogVersion catalogVersion) {
        return createApp(code, packageName, developer, catalogVersion, APPROVED);
    }

    public AppModel createApp(String code, String packageName, DeveloperModel developer, CatalogVersionModel catalogVersion) {
        return createApp(code, packageName, developer, catalogVersion, APPROVED);
    }

    public Date ensureProductContainerModifiedTimeIsInThePast(ProductContainerModel productContainer) {
        modelService.refresh(productContainer);
        await().atMost(5, TimeUnit.SECONDS).until(() -> !DateUtils.truncatedEquals(new Date(), productContainer.getModifiedtime(), SECOND));
        return productContainer.getModifiedtime();
    }

    public ProductContainerModel createProductContainerWithAppDraft(String title, IoTCompanyModel company) {
        ProductContainerModel container = createEmptyProductContainer(title, company);
        AppDraftModel appDraft = modelService.create(AppDraftModel.class);
        appDraft.setCode(APP_DRAFT_PREFIX + container.getCode());
        container.setAppDraft(appDraft);
        StoreContentDraftModel storeContentDraft = modelService.create(StoreContentDraftModel.class);
        storeContentDraft.setCode(STORECONTENT_DRAFT_CODE_PREFIX + container.getCode());
        storeContentDraft.setName(title, Locale.ENGLISH);
        storeContentDraft.setEula(EulaBuilder.generate().withType(EulaType.STANDARD).buildIntegrationInstance());
        appDraft.setStoreContentDraft(storeContentDraft);
        AppVideoModel appVideoModel = new AppVideoModel();
        appVideoModel.setStatus(AppVideoStatus.NOT_FOUND);
        appVideoModel.setSource(APP_VIDEO_SOURCE);
        appVideoModel.setType(AppVideoType.YOUTUBE);
        appVideoModel.setExternalId("somethingweird");
        storeContentDraft.setVideo(appVideoModel);
        modelService.saveAll(container, container.getAppDraft(), container.getAppDraft().getStoreContentDraft());

        return container;
    }

    public ProductContainerModel createEmptyProductContainer(String title, IoTCompanyModel company) {
        ProductContainerModel container = ProductContainerBuilder.generate()
            .withTitle(title)
            .withCode(getProductContainerCodeFromTitle(title))
            .withCompany(company)
            .buildIntegrationInstance();
        modelService.saveAll(container);
        return container;
    }

    public ProductContainerModel createProductContainerWithApp(String title, DeveloperModel developer) {
        return createProductContainerWithApp(title, developer, PACKAGE_NAME);
    }

    public ProductContainerModel createProductContainerWithApp(String title, DeveloperModel developer, String packageName) {
        return createProductContainerWithApp(title, developer, packageName, CatalogVersion.STAGED);
    }

    public ProductContainerModel createProductContainerWithApp(String title, DeveloperModel developer, String packageName,
        CatalogVersion catalogVersion) {
        return createProductContainerWithAppAndApprovalStatus(title, developer, packageName, catalogVersion, APPROVED);
    }

    private ProductContainerModel createProductContainerWithAppAndApprovalStatus(String title, DeveloperModel developer,
        String packageName, CatalogVersion catalogVersion, ArticleApprovalStatus approvalStatus) {
        ProductContainerModel container = createEmptyProductContainer(title, developer.getCompany());
        AppModel app = createApp(APP_CODE_PREFIX + title, packageName, developer, catalogVersion,
            approvalStatus);

        AppVideoModel appVideoModel = AppVideoBuilder.generate()
            .withStatus(AppVideoStatus.NOT_FOUND)
            .withType(AppVideoType.YOUTUBE)
            .withSource(APP_VIDEO_SOURCE)
            .withExternalId("something")
            .buildIntegrationInstance();

        app.setVideo(appVideoModel);

        createEvalForFullAppLicense(createFullAppLicense(APPLICENSE_PREFIX + title, app));
        container.setApp(app);

        modelService.save(container);
        return container;
    }

    public ProductContainerModel createReleasableProductContainer(String title, DeveloperModel developer, Set<LicenseType> licenses) {
        ProductContainerModel container = createEmptyProductContainer(title, developer.getCompany());
        CatalogVersionModel catalogVersion = getOrCreateProductCatalogVersion(CatalogVersion.STAGED);
        AppDraftModel appDraft = AppDraftBuilder.generate()
            .withCode(APP_DRAFT_PREFIX + title)
            .withStoreContentDraft(createStoreContentDraft(title, catalogVersion))
            .withCountriesAndPricesDraft(createCountriesAndPricesDraft(title, licenses))
            .withStoreAvailabilityMode(StoreAvailabilityMode.PUBLIC)
            .buildIntegrationInstance();
        container.setAppDraft(appDraft);
        container.setAppVersionDraft(createAppVersionDraft(10L));

        modelService.save(container);
        return container;
    }

    public ProductContainerModel createProductContainerWithAppDraft(String title, DeveloperModel developer) {
        ProductContainerModel container = createProductContainerWithAppDraft(title, developer.getCompany());
        CatalogVersionModel catalogVersion = getOrCreateProductCatalogVersion(CatalogVersion.STAGED);
        AppDraftModel appDraft = AppDraftBuilder.generate()
            .withCode(APP_DRAFT_PREFIX + title)
            .withStoreContentDraft(createStoreContentDraft(title, catalogVersion))
            .buildIntegrationInstance();
        container.setAppDraft(appDraft);
        container.setAppVersionDraft(createAppVersionDraft(10L));

        modelService.save(container);
        return container;
    }

    private StoreContentDraftModel createStoreContentDraft(String title, CatalogVersionModel catalogVersion) {
        AppVideoModel appVideoModel = AppVideoBuilder.generate()
            .withSource(APP_VIDEO_SOURCE)
            .withExternalId("external-id")
            .withType(AppVideoType.YOUTUBE)
            .withStatus(AppVideoStatus.NOT_FOUND)
            .buildIntegrationInstance();

        return StoreContentDraftBuilder.generate()
            .withCode(STORE_CONTENT_DRAFT_PREFIX + title)
            .withEmailAddress(EMAIL)
            .withPrivacyPolicyUrl(PRODUCT_CONTAINER.PRIVACY_POLICY)
            .withName(NAME_GERMAN, Locale.GERMAN)
            .withName(title, Locale.ENGLISH)
            .withSummary(SUMMARY_ENGLISH, Locale.ENGLISH)
            .withSummary(SUMMARY_GERMAN, Locale.GERMAN)
            .withDescription(DESCRIPTION_GERMAN, Locale.GERMAN)
            .withDescription(DESCRIPTION_ENGLISH, Locale.ENGLISH)
            .withSupportPageUrl(PRODUCT_CONTAINER.SUPPORT_PAGE)
            .withProductWebsiteUrl(PRODUCT_CONTAINER.PRODUCT_WEBSITE)
            .withSupportPhoneNumber(PRODUCT_CONTAINER.SUPPORT_PHONE)
            .withScreenshots(ImmutableList.of(createScreenshotInMediaContainer(SCREENSHOT_PREFIX + title, catalogVersion, true)))
            .withIcon(createIconInMediaContainer(ICON_PREFIX + title, catalogVersion, true))
            .withVideo(appVideoModel)
            .withEula(EulaBuilder.generate().withType(EulaType.STANDARD).buildIntegrationInstance())
            .buildIntegrationInstance();
    }

    private StoreContentDraftModel createInitialStoreContentDraft(String title) {
        return StoreContentDraftBuilder.generate()
            .withCode(STORE_CONTENT_DRAFT_PREFIX + title)
            .withName(title, Locale.ENGLISH)
            .buildIntegrationInstance();
    }

    public ProductContainerModel createProductContainerWithCountriesAndPricesDraft(String title, DeveloperModel developer,
        Set<LicenseType> licenses) {
        ProductContainerModel container = createEmptyProductContainer(title, developer.getCompany());
        AppDraftModel appDraft = AppDraftBuilder.generate()
            .withCode(APP_DRAFT_PREFIX + title)
            .withStoreAvailabilityMode(StoreAvailabilityMode.PUBLIC)
            .withCountriesAndPricesDraft(createCountriesAndPricesDraft(title, licenses))
            .withStoreContentDraft(createInitialStoreContentDraft(title))
            .buildIntegrationInstance();
        container.setAppDraft(appDraft);
        modelService.save(container);
        return container;
    }

    public AppVersionDraftModel createAppVersionDraft(long versionCode) {
        AppVersionDraftModel appVersionDraft = createBasicAppVersionDraft(versionCode);
        modelService.save(appVersionDraft);
        return appVersionDraft;
    }

    private AppVersionDraftModel createBasicAppVersionDraft(long versionCode) {
        ApkMediaModel appApk = getOrCreateApkMedia("appAPK" + randomUUID().toString(), VERSION_DRAFT_PREFIX + "appSuffix", versionCode,
            PACKAGE_NAME);

        return AppVersionDraftBuilder.generate()
            .withApk(appApk)
            .withCode(randomUUID().toString())
            .withChangelog(CHANGELOG, Locale.ENGLISH)
            .buildIntegrationInstance();
    }

    public AppVersionDraftModel createAppVersionDraftWithOwner(long versionCode, ItemModel owner) {
        AppVersionDraftModel versionDraft = createBasicAppVersionDraft(versionCode);
        versionDraft.setOwner(owner);
        modelService.save(versionDraft);
        return versionDraft;
    }

    private CountriesAndPricesDraftModel createCountriesAndPricesDraft(String title, Set<LicenseType> licenses) {
        Set<AppLicenseDraftModel> draftLicenses = licenses.stream().map(
                license -> AppLicenseDraftBuilder.generate()
                    .withLicenseType(license)
                    .withEnabledCountries(ImmutableSet.of(getCountry(GERMANY)))
                    .withAvailabilityStatus(LicenseAvailabilityStatus.PUBLISHED)
                    .withSpecifiedPrice(getDefaultSpecifiedPriceForLicensetype(license))
                    .buildIntegrationInstance())
            .collect(toSet());

        return CountriesAndPricesDraftBuilder.generate().withCode(COUNTRIES_AND_PRICES_DRAFT_CODE_PREFIX + title)
            .withAppLicenses(draftLicenses)
            .buildIntegrationInstance();
    }

    private Double getDefaultSpecifiedPriceForLicensetype(final LicenseType licenseType) {
        return Set.of(FULL, SUBSCRIPTION).contains(licenseType) ? EUR_PRICE : null;
    }

    public ProductContainerModel createProductContainerWithEmptyStoreContentDraft(String title, DeveloperModel developer) {
        ProductContainerModel container = createEmptyProductContainer(title, developer.getCompany());

        AppDraftModel appDraft = AppDraftBuilder.generate()
            .withCode(APP_DRAFT_PREFIX + title)
            .withStoreContentDraft(StoreContentDraftBuilder.generate()
                .withCode(STORE_CONTENT_DRAFT_PREFIX + title)
                .withName(title, Locale.ENGLISH)
                .withEula(EulaBuilder.generate().withType(EulaType.STANDARD).buildIntegrationInstance())
                .buildIntegrationInstance())
            .buildIntegrationInstance();
        container.setAppDraft(appDraft);
        modelService.save(container);
        return container;
    }

    public AppModel createApp(String code, String packageName, CatalogVersion catalogVersion, ArticleApprovalStatus approvalStatus) {
        UserModel currentUser = userService.getCurrentUser();
        DeveloperModel developer = developerService.getDeveloperByInternalUserId(SAMPLE_DATA_DEVELOPER_A1_UID);
        developerService.setCurrentDeveloper(developer);

        AppModel app = createApp(code, packageName, developer, catalogVersion, approvalStatus);
        userService.setCurrentUser(currentUser);
        return app;
    }

    private AppModel createApp(String code, String packageName, DeveloperModel developer, CatalogVersionModel catalogVersion,
        ArticleApprovalStatus approvalStatus) {
        AppModel app = initAppModel(code, packageName, developer, catalogVersion, approvalStatus)
            .buildIntegrationInstance();
        modelService.save(app);
        return app;
    }

    public AppModel createApp(String code, String packageName, DeveloperModel developer, CatalogVersion catalogVersion,
        ArticleApprovalStatus approvalStatus) {
        return createApp(code, packageName, developer, CiscoreConstants.CIS_PRODUCT_CATALOG, catalogVersion, approvalStatus);
    }

    public AppModel createApp(String code, String packageName, DeveloperModel developer, String catalogId, CatalogVersion catalogVersion,
        ArticleApprovalStatus approvalStatus) {
        CatalogModel catalogModel = getOrCreateCatalog(catalogId);
        CatalogVersionModel catalogVersionModel = getOrCreateProductCatalogVersion(catalogModel, catalogVersion.getVersionName());
        AppBuilder<?, ?> appBuilder = initAppModel(code, packageName, developer, catalogVersionModel, approvalStatus);
        AppModel app = appBuilder.buildIntegrationInstance();
        modelService.save(app);
        return app;
    }

    private AppBuilder<?, ?> initAppModel(String code, String packageName, DeveloperModel developer,
        CatalogVersionModel catalogVersionModel, ArticleApprovalStatus approvalStatus) {

        return AppBuilder.generate()
            .withCode(code)
            .withIcon(createIconInMediaContainer(ICON_PREFIX + code, catalogVersionModel, true))
            .withGalleryImages(ImmutableList.of(createScreenshotInMediaContainer(SCREENSHOT_PREFIX + code, catalogVersionModel, true)))
            .withSubmittedBy(developer)
            .withName(APP_NAME_PREFIX_EN + code, Locale.ENGLISH)
            .withName(APP_NAME_PREFIX_DE + code, Locale.GERMAN)
            .withStoreAvailabilityMode(StoreAvailabilityMode.PUBLIC)
            .withCatalogVersion(catalogVersionModel)
            .withPackageName(packageName)
            .withCompany(developer.getCompany())
            .withApprovalStatus(approvalStatus)
            .withVariantType(variantsService.getVariantTypeForCode(VERSION_VARIANT_CODE))
            .withPrivacyPolicyUrl(PRIVACY_POLICY)
            .withEmailAddress(APP_CONTACT_EMAIL)
            .withSupportPhoneNumber(APP_PHONE_NUMBER)
            .withSupportPageUrl(APP_URL)
            .withProductWebsiteUrl(APP_URL)
            .withDescription(APP_DESCRIPTION_EN, ENGLISH)
            .withDescription(APP_DESCRIPTION_DE, GERMAN)
            .withSummary(APP_SUMMARY_EN, ENGLISH)
            .withSummary(APP_SUMMARY_DE, GERMAN)
            .withEula(EulaBuilder.generate().withType(EulaType.STANDARD).buildIntegrationInstance());

    }

    public void createAppLicenses(String code, ProductModel baseProduct, ArticleApprovalStatus articleApprovalStatus) {
        createAppLicense(code, baseProduct, articleApprovalStatus, LicenseType.FULL);
        createAppLicense(code + EVALUATION_SUFFIX, baseProduct, articleApprovalStatus, LicenseType.EVALUATION);
    }

    public AppLicenseModel createFullAppLicense(ProductModel baseProduct) {
        return createFullAppLicense(baseProduct.getCode() + "_full", baseProduct);
    }

    public AppLicenseModel createSubscriptionAppLicense(ProductModel baseProduct) {
        AppLicenseModel appLicense = createAppLicense(baseProduct.getCode() + "_subscription", baseProduct, APPROVED, SUBSCRIPTION);

        addPriceRow(appLicense, EUR_PRICE, getCurrency(EUR), 1L);
        addPriceRow(appLicense, USD_PRICE, getCurrency(USD), 1L);

        return appLicense;
    }

    public AppLicenseModel createSubscriptionAppLicenseWithRuntime(ProductModel baseProduct) {
        AppLicenseModel appLicense = createSubscriptionAppLicense(baseProduct);
        RuntimeModel runtime = RuntimeBuilder.generate()
            .withCode(appLicense.getCode() + "_runtime")
            .withDefaultRuntimeTerminationRule(createContractTerminationRule(appLicense.getCode() + "_runtime"))
            .buildIntegrationInstance();
        appLicense.setRuntime(runtime);
        return appLicense;
    }

    private ContractTerminationRuleModel createContractTerminationRule(String codePrefix) {
        return ContractTerminationRuleBuilder.generate()
            .withCode(codePrefix + "_contract")
            .withNoticePeriod(createTerminationRulePeriodModel(codePrefix + "_notice", 56, TerminationRuleUnit.DAY))
            .withGracePeriod(createTerminationRulePeriodModel(codePrefix + "_grace", 14, TerminationRuleUnit.DAY))
            .withFollowUpPeriod(createTerminationRulePeriodModel(codePrefix + "_followup", 14, TerminationRuleUnit.DAY))
            .buildIntegrationInstance();
    }

    private TerminationRulePeriodModel createTerminationRulePeriodModel(String code, int period, TerminationRuleUnit unit) {
        return TerminationRulePeriodBuilder.generate()
            .withCode(code)
            .withValue(period)
            .withUnit(unit)
            .buildIntegrationInstance();
    }

    public AppLicenseModel createSubscriptionAppLicenseWithFuturePrice(ProductModel baseProduct) {
        AppLicenseModel appLicense = createAppLicense(
            baseProduct.getCode() + "_subscription_with_future_price", baseProduct, APPROVED, LicenseType.SUBSCRIPTION);

        Date startTimeForCurrentlyValidPrice = Date.from(LocalDateTime.of(1990, 4, 8, 1, 2, 3).toInstant(UTC));
        Date endTimeForCurrentlyValidPrice = Date.from(LocalDate.now().plusMonths(1).minusDays(1).atStartOfDay().toInstant(UTC));
        Date startTimeForFuturePrice = Date.from(LocalDate.now().plusMonths(1).atStartOfDay().toInstant(UTC));

        addPriceRowWithStartAndEndTime(appLicense, EUR_PRICE, getCurrency(EUR), 1L, startTimeForCurrentlyValidPrice,
            endTimeForCurrentlyValidPrice);
        addPriceRowWithStartAndEndTime(appLicense, USD_PRICE, getCurrency(USD), 1L, startTimeForCurrentlyValidPrice,
            endTimeForCurrentlyValidPrice);

        addPriceRowWithStartAndEndTime(appLicense, EUR_PRICE * 2, getCurrency(EUR), 1L, startTimeForFuturePrice, null);
        addPriceRowWithStartAndEndTime(appLicense, USD_PRICE * 2, getCurrency(USD), 1L, startTimeForFuturePrice, null);

        return appLicense;
    }

    public AppLicenseModel createFullAppLicense(String code, ProductModel baseProduct) {
        AppLicenseModel appLicense = createAppLicense(code, baseProduct, APPROVED, LicenseType.FULL);

        addPriceRow(appLicense, EUR_PRICE, getCurrency(EUR), 1L);
        addPriceRow(appLicense, USD_PRICE, getCurrency(USD), 1L);

        return appLicense;
    }

    public AppLicenseModel createEvalAppLicense(String code, ProductModel baseProduct) {
        AppLicenseModel appLicense = createAppLicense(code, baseProduct, APPROVED, LicenseType.EVALUATION);

        addPriceRow(appLicense, 0, getCurrency(EUR), 1L);
        addPriceRow(appLicense, 0, getCurrency(USD), 1L);

        return appLicense;
    }

    public AppLicenseModel createEvalForFullAppLicense(AppLicenseModel fullAppLicense) {
        AppLicenseModel evalAppLicense = createAppLicense(fullAppLicense.getCode() + EVALUATION_SUFFIX,
            fullAppLicense.getBaseProduct(), APPROVED, EVALUATION);

        addPriceRow(evalAppLicense, 0, getCurrency(EUR), 1L);
        addPriceRow(evalAppLicense, 0, getCurrency(USD), 1L);

        return evalAppLicense;
    }

    public AppLicenseModel createAppLicense(
        String code, ProductModel baseProduct, ArticleApprovalStatus approvalStatus, LicenseType licenseType) {

        CatalogVersionModel versionModel = baseProduct.getCatalogVersion();
        AppLicenseBuilder<?, ?> appLicenseBuilder = initAppLicense(code, baseProduct, versionModel, approvalStatus, licenseType);
        AppLicenseModel appLicense = appLicenseBuilder
            .buildIntegrationInstance();
        modelService.save(appLicense);
        return appLicense;
    }

    public AppLicenseModel createAppLicense(String code, ProductModel baseProduct,
        ArticleApprovalStatus approvalStatus, LicenseType licenseType, BillingSystemStatus billingSystemStatus) {
        CatalogVersionModel versionModel = baseProduct.getCatalogVersion();
        AppLicenseBuilder<?, ?> appLicenseBuilder = initAppLicense(code, baseProduct, versionModel, approvalStatus, licenseType);
        AppLicenseModel appLicense = appLicenseBuilder
            .withBillingSystemStatus(billingSystemStatus)
            .buildIntegrationInstance();
        modelService.save(appLicense);
        return appLicense;
    }

    private AppLicenseBuilder<?, ?> initAppLicense(String code, ProductModel baseProduct, CatalogVersionModel versionModel,
        ArticleApprovalStatus approvalStatus, LicenseType licenseType) {
        return AppLicenseBuilder.generate()
            .withCode(code)
            .withCatalogVersion(versionModel)
            .withBaseProduct(baseProduct)
            .withApprovalStatus(approvalStatus)
            .withLicenseType(licenseType)
            .withAvailabilityStatus(LicenseAvailabilityStatus.PUBLISHED)
            .withEnabledCountries(ImmutableSet.of(getCountry(GERMANY), getCountry(USA)))
            .withBillingSystemStatus(BillingSystemStatus.IN_SYNC)
            .withBrimName(BRIM_NAME_PREFIX + code, ENGLISH)
            .withSpecifiedPrice(getDefaultSpecifiedPriceForLicensetype(licenseType))
            .withUnit(unitService.getUnitForCode("pieces"));
    }

    private List<PermissionModel> getOrCreatePermissions() {
        List<PermissionModel> permissions = new ArrayList<>();
        PermissionModel permission = new PermissionModel();

        permission.setTechnicalName("PermissionTechnicalName");

        List<PermissionModel> permissionsByExample = flexibleSearchService.getModelsByExample(permission);
        if (!permissionsByExample.isEmpty()) {
            permission = permissionsByExample.iterator().next();
        }

        permission.setName("PermissionName-EN", Locale.ENGLISH);
        permission.setName("PermissionName-DE", Locale.GERMAN);

        modelService.save(permission);
        permissions.add(permission);

        return permissions;
    }

    private MediaContainerModel createIconInMediaContainer(String code, CatalogVersionModel catalogVersion, Boolean used) {
        return createImageInMediaContainer(code, catalogVersion, used, "Icon-Conversion");
    }

    public MediaContainerModel createScreenshotInMediaContainer(String code, CatalogVersionModel catalogVersion, Boolean used) {
        return createImageInMediaContainer(code, catalogVersion, used, "Screenshot-Conversion");
    }

    private MediaContainerModel createImageInMediaContainer(String code, CatalogVersionModel versionModel, Boolean used,
        String conversionGroupCode) {
        ConversionGroupModel sample = new ConversionGroupModel();
        sample.setCode(conversionGroupCode);
        ConversionGroupModel conversionGroup = flexibleSearchService.getModelByExample(sample);

        MediaContainerModel mediaContainer = MediaContainerBuilder.generate()
            .withCatalogVersion(versionModel)
            .withQualifier(code + MEDIA_CONTAINER_CODE_SUFFIX)
            .withMedias(ImmutableList.of(createMediaModel(code, versionModel, used, IMAGES_MEDIA_FOLDER)))
            .withConversionGroup(conversionGroup)
            .buildIntegrationInstance();
        modelService.save(mediaContainer);

        return mediaContainer;
    }

    public PdfMediaModel createPdfDocument(String code, String name, CatalogVersionModel catalogVersion, Boolean used) {
        return createPdfMediaModel(code, name, catalogVersion, used);
    }

    private MediaModel createMediaModel(String code, CatalogVersionModel versionModel, Boolean used, String folder) {
        byte[] fileContent = getImageTestData();
        return createMediaModel(code, versionModel, used, fileContent, folder);
    }

    private PdfMediaModel createPdfMediaModel(String code, String name, CatalogVersionModel versionModel, Boolean used) {
        byte[] fileContent = getPdfDocumentTestData();
        return createPdfMediaModel(code, name, versionModel, used, fileContent);
    }

    public CatalogUnawareMediaModel getOrCreateCatalogUnawareMedia(String code) {
        CatalogUnawareMediaModel example = new CatalogUnawareMediaModel();
        example.setCode(code);

        List<CatalogUnawareMediaModel> modelByExample = flexibleSearchService.getModelsByExample(example);
        if (!modelByExample.isEmpty()) {
            return modelByExample.iterator().next();
        }

        CatalogUnawareMediaModel media = CatalogUnawareMediaBuilder.generate().withCode(code).buildIntegrationInstance();
        modelService.save(media);

        InputStream inputStream = new ByteArrayInputStream("this is for test".getBytes());
        mediaService.setStreamForMedia(media, inputStream, MEDIA_FILENAME, null);
        modelService.refresh(media);
        return media;
    }

    public ApkMediaModel getOrCreateApkMedia(String code, String versionName, long versionCode, String packageName) {
        ApkMediaModel example = new ApkMediaModel();
        example.setCode(code);

        List<ApkMediaModel> modelByExample = flexibleSearchService.getModelsByExample(example);
        if (!modelByExample.isEmpty()) {
            return modelByExample.iterator().next();
        }

        ApkSignatureModel signatureModel = ApkSignatureBuilder.generate()
            .withCertificateSha256("02e3121434546f118349d9d56289cba077b55af1a70fd6db737ef13e5ad0cba1")
            .withSignatureVersion(SignatureVersion.V2)
            .withValidTo(Date.from(LocalDateTime.of(2040, 9, 30, 1, 1, 1).toInstant(UTC)))
            .withCode(randomUUID().toString())
            .withSubjectCName("Tom Developer")
            .buildIntegrationInstance();

        MediaFolderModel folder = mediaService.getFolder(APK_MEDIA_FOLDER_NAME);
        ApkMediaModel media = ApkMediaBuilder.generate()
            .withCode(code)
            .withSignatures(Collections.singletonList(signatureModel))
            .withPermissions(getOrCreatePermissions())
            .withVersionCode(versionCode)
            .withVersionName(versionName)
            .withPackageName(packageName)
            .withSdkAddonVersion(DEFAULT_SDK_ADDON_VERSION)
            .withMinAndroidApiVersion(DEFAULT_MIN_ANDROID_API_VERSION)
            .withFolder(folder)
            .buildIntegrationInstance();
        modelService.save(media);

        InputStream inputStream = new ByteArrayInputStream("this is for test".getBytes());
        mediaService.setStreamForMedia(media, inputStream, MEDIA_FILENAME, null);
        modelService.refresh(media);
        return media;
    }

    public MediaContainerModel createUploadedIcon(String code) {
        return createIconInMediaContainer(code, getOrCreateProductCatalogVersion(CatalogVersion.STAGED), false);
    }

    public CurrencyModel getCurrency(Currency currency) {
        return commonI18NService.getCurrency(currency.name());
    }

    public CountryModel getCountry(Country country) {
        return commonI18NService.getCountry(country.getIsocode());
    }

    public LanguageModel getLanguage(Language language) {
        return commonI18NService.getLanguage(language.getIsocode());
    }

    public UserGroupModel getUserGroup(final String userGroupUid) {
        return userService.getUserGroupForUID(userGroupUid);
    }

    private MediaModel createMediaModel(String code, CatalogVersionModel versionModel, Boolean used, byte[] fileContent, String folder) {
        MediaModel media = MediaBuilder.generate()
            .withCode(code)
            .withCatalogVersion(versionModel)
            .withUsed(used)
            .withFolder(mediaService.getFolder(folder))
            .buildIntegrationInstance();
        modelService.save(media);

        InputStream inputStream = new ByteArrayInputStream(fileContent);
        mediaService.setStreamForMedia(media, inputStream, MEDIA_FILENAME, null);
        modelService.refresh(media);
        return media;
    }

    private PdfMediaModel createPdfMediaModel(String code, String name, CatalogVersionModel versionModel, Boolean used,
        byte[] fileContent) {
        PdfMediaModel pdfMedia = PdfMediaBuilder.generate()
            .withCode(code)
            .withCatalogVersion(versionModel)
            .withUsed(used)
            .withAltText(name)
            .buildIntegrationInstance();
        modelService.save(pdfMedia);

        InputStream inputStream = new ByteArrayInputStream(fileContent);
        mediaService.setStreamForMedia(pdfMedia, inputStream, MEDIA_FILENAME, null);
        modelService.refresh(pdfMedia);
        return pdfMedia;
    }

    private PriceRowModel addPriceRow(ProductModel product, double amount, CurrencyModel currency, Long minQuantity, Date startTime,
        Date endTime) {
        PriceRowModel priceRow = PriceRowBuilder.generate()
            .withProductId(product.getCode())
            .withCurrency(currency)
            .withPrice(amount)
            .withStartTime(startTime)
            .withEndTime(endTime)
            .withUnit(unitService.getUnitForCode("pieces"))
            .withMinqtd(minQuantity)
            .withNet(true)
            .buildIntegrationInstance();
        modelService.saveAll(priceRow, product);
        return priceRow;
    }

    public PriceRowModel addPriceRow(ProductModel product, double amount, CurrencyModel currency, Long minQuantity) {
        Date defaultStartTime = Date.from(LocalDateTime.of(1990, 4, 8, 1, 2, 3).toInstant(UTC));
        return addPriceRow(product, amount, currency, minQuantity, defaultStartTime, null);
    }

    public PriceRowModel addPriceRowWithStartAndEndTime(
        ProductModel product, double amount, CurrencyModel currency, Long minQuantity, Date startTime, Date endTime) {
        return addPriceRow(product, amount, currency, minQuantity, startTime, endTime);
    }

    public BaseSiteModel getCmsSite(String uid) {
        BaseSiteModel cmsSite = new CMSSiteModel();
        cmsSite.setUid(uid);
        return flexibleSearchService.getModelByExample(cmsSite);
    }

    public CatalogVersionModel getOrCreateProductCatalogVersion(CatalogVersion version) {
        return getOrCreateProductCatalogVersion(version.getVersionName());
    }

    private CatalogVersionModel getOrCreateProductCatalogVersion(String version) {
        CatalogModel catalog = getOrCreateCatalog(CiscoreConstants.CIS_PRODUCT_CATALOG);

        return getOrCreateProductCatalogVersion(catalog, version);
    }

    public CatalogVersionModel getOrCreateProductCatalogVersion(CatalogModel catalog, String version) {
        CatalogVersionModel catalogVersionExample = new CatalogVersionModel();
        catalogVersionExample.setVersion(version);
        catalogVersionExample.setCatalog(catalog);

        List<CatalogVersionModel> catalogVersions = flexibleSearchService.getModelsByExample(catalogVersionExample);
        if (!catalogVersions.isEmpty()) {
            return catalogVersions.get(0);
        }

        CatalogVersionModel catalogVersionModel = CatalogVersionBuilder.generate()
            .withCatalog(catalog)
            .withVersion(version)
            .withActive(true)
            .buildIntegrationInstance();
        catalog.setActiveCatalogVersion(catalogVersionModel);
        modelService.saveAll(catalogVersionModel, catalog);
        return catalogVersionModel;
    }

    public CatalogModel getOrCreateCatalog(String id) {
        CatalogModel catalogModelExample = new CatalogModel();
        catalogModelExample.setId(id);

        List<CatalogModel> catalogModels = flexibleSearchService.getModelsByExample(catalogModelExample);
        if (!catalogModels.isEmpty()) {
            return catalogModels.iterator().next();
        }

        CatalogModel catalogModel = CatalogBuilder.generate()
            .withId(id)
            .buildIntegrationInstance();
        modelService.save(catalogModel);
        return catalogModel;
    }

    public Optional<IoTCompanyModel> getIotCompanyForUid(String uid) {
        FlexibleSearchQuery query = new FlexibleSearchQuery(FIND_IOT_COMPANY_FOR_UID);
        query.addQueryParameter("uid", uid);
        return daoQueryService.searchSingleResult(query,
            () -> new AmbiguousIdentifierException("Found multiple companies with uid=" + uid));
    }

    public IoTCompanyModel createIotCompany() {
        final String companyUid = randomUUID().toString();
        final IoTCompanyModel company = IoTCompanyBuilder.generate()
            .withUid(companyUid)
            .withName(String.format("NAME_%s", companyUid))
            .withCountry(this.getCountry(Country.GERMANY))
            .buildIntegrationInstance();
        company.setApprovalStatus(CompanyApprovalStatus.UNAPPROVED);
        modelService.save(company);
        return company;
    }

    public IoTCompanyModel createIotCompanyWithCompanyIdAndBaseStore(String companyId, BaseStoreEnum baseStore) {
        final IoTCompanyModel company = IoTCompanyBuilder.generate()
            .withUid(companyId)
            .withName(String.format("NAME_%s", companyId))
            .withCountry(this.getCountry(Country.GERMANY))
            .withStore(baseStoreService.getBaseStoreForUid(baseStore.getBaseStoreUid()))
            .buildIntegrationInstance();
        company.setApprovalStatus(CompanyApprovalStatus.UNAPPROVED);
        modelService.save(company);
        return company;
    }

    public IoTCompanyModel createBaseStoreIotCompany(BaseStoreEnum baseStore) {
        final IoTCompanyModel company = createIotCompany();
        company.setStore(baseStoreService.getBaseStoreForUid(baseStore.getBaseStoreUid()));
        modelService.save(company);
        return company;
    }

    private Date prepareDate(int year, int month, int day) {
        return Date.from(LocalDate.of(year, month, day).atStartOfDay(i18NService.getCurrentTimeZone().toZoneId()).toInstant());
    }

    public OrderModel createOrder(OrderStatus status) {
        return createOrder(getIntegratorByInternalUserId(SAMPLE_DATA_INTEGRATOR_UID), status);
    }

    public OrderModel createOrder(IntegratorModel integrator, OrderStatus status) {
        return OrderBuilder.generate()
            .withStatus(status)
            .withCurrency(getCurrency(EUR))
            .withDate(prepareDate(2018, 7, 1))
            .withUser(integrator)
            .withCompany(integrator.getCompany())
            .withStore(integrator.getCompany().getStore())
            .buildIntegrationInstance();
    }

    public OrderEntryModel createOrderEntry(OrderModel order, ProductModel product, Long quantity) {
        return OrderEntryBuilder.generate()
            .withUnit(unitService.getUnitForCode("pieces"))
            .withOrder(order)
            .withProduct(product)
            .withQuantity(quantity)
            .buildIntegrationInstance();
    }

    public SubscriptionContractModel createSubscription(String orderNumber, OrderEntryModel orderEntry, String cancelIdempotencyKey,
        Date cancelRequestedOn) {
        return SubscriptionContractBuilder.generate()
            .withOrderEntry(orderEntry)
            .withLegacySubscriptionId(randomUUID().toString())
            .withCancelIdempotencyKey(cancelIdempotencyKey)
            .withCancelledDate(cancelRequestedOn)
            .withContractTerminationRule(createTerminationRule(cancelIdempotencyKey))
            .buildIntegrationInstance();
    }

    public SubscriptionContractModel createSubscription(final OrderEntryModel orderEntry) {
        return createSubscription(orderEntry, null);
    }

    public SubscriptionContractModel createSubscription(
        final OrderEntryModel orderEntry,
        final ContractTerminationRuleModel terminationRule) {

        return SubscriptionContractBuilder.generate()
            .withOrderEntry(orderEntry)
            .withLegacySubscriptionId(randomUUID().toString())
            .withContractTerminationRule(terminationRule)
            .buildIntegrationInstance();
    }

    public ContractTerminationRuleModel createTerminationRule(String uniqueCodePrefix) {
        var thirtyDays = TerminationRulePeriodBuilder.generate()
            .withCode(uniqueCodePrefix + "-30days")
            .withValue(30)
            .withUnit(TerminationRuleUnit.DAY)
            .buildIntegrationInstance();
        var oneYear = TerminationRulePeriodBuilder.generate()
            .withCode(uniqueCodePrefix = "-1year")
            .withValue(1)
            .withUnit(TerminationRuleUnit.YEAR)
            .buildIntegrationInstance();

        return ContractTerminationRuleBuilder.generate()
            .withCode(uniqueCodePrefix + "-rule")
            .withInitialPeriod(oneYear)
            .withFollowUpPeriod(oneYear)
            .withNoticePeriod(thirtyDays)
            .withGracePeriod(thirtyDays)
            .buildIntegrationInstance();
    }

    public CartModel createCart() {
        IntegratorModel integrator = getIntegratorByInternalUserId(SAMPLE_DATA_INTEGRATOR_UID);

        CartModel cart = CartBuilder.generate()
            .withCurrency(getCurrency(EUR))
            .withDate(prepareDate(2018, 7, 1))
            .withUser(integrator)
            .withCompany(integrator.getCompany())
            .withGuid(randomUUID().toString())
            .withStore(baseStoreService.getBaseStoreForUid(BaseStoreEnum.AZENA.getBaseStoreUid()))
            .buildIntegrationInstance();
        modelService.save(cart);
        return cart;
    }

    public CartEntryModel createCartEntry(CartModel cart, AppLicenseModel appLicense, Long quantity) {
        CartEntryModel entry = CartEntryBuilder.generate()
            .withUnit(unitService.getUnitForCode("pieces"))
            .withOrder(cart)
            .withProduct(appLicense)
            .withQuantity(quantity)
            .buildIntegrationInstance();
        modelService.save(entry);
        return entry;
    }

    public AppVersionModel createAppVersion(String code, AppModel app, CatalogVersion catalogVersion, String apkCode) {
        return createAppVersion(code, app, getOrCreateProductCatalogVersion(catalogVersion), apkCode);
    }

    public AppVersionModel createAppVersion(String code, AppModel app, CatalogVersionModel catalogVersion, String apkCode) {
        ApkMediaModel appApk = getOrCreateApkMedia(apkCode, VERSION_DRAFT_PREFIX + "appSuffix", 0,
            app.getPackageName());
        AppVersionModel appVersion = AppVersionBuilder.generate()
            .withCode(code)
            .withApp(app)
            .withApk(appApk)
            .withCatalogVersion(catalogVersion)
            .withEccn("eccn_" + code)
            .buildIntegrationInstance();
        modelService.save(appVersion);
        return appVersion;
    }

    public AppVersionDraftModel createAppVersionDraft(String code, String packageName, ArticleApprovalStatus approvalStatus,
        String apkCode) {
        ApkMediaModel appApk = getOrCreateApkMedia(apkCode, VERSION_DRAFT_PREFIX + "appSuffix", 0, packageName);
        AppVersionDraftModel appVersionDraft = AppVersionDraftBuilder.generate()
            .withCode(code)
            .withApk(appApk)
            .withApprovalStatus(approvalStatus)
            .buildIntegrationInstance();
        modelService.save(appVersionDraft);
        return appVersionDraft;
    }

    public AppVersionDraftModel createAppVersionDraftWithOwner(String code, String packageName, ArticleApprovalStatus approvalStatus,
        String apkCode, ProductContainerModel productContainer) {
        ApkMediaModel appApk = getOrCreateApkMedia(apkCode, VERSION_DRAFT_PREFIX + "appSuffix", 0, packageName);
        AppVersionDraftModel appVersionDraft = AppVersionDraftBuilder.generate()
            .withCode(code)
            .withApk(appApk)
            .withApprovalStatus(approvalStatus)
            .withOwner(productContainer)
            .buildIntegrationInstance();
        modelService.save(appVersionDraft);
        return appVersionDraft;
    }

    public Set<AppLicenseDraftModel> createAppDraftLicenses(Set<LicenseType> enabledLicenses) {
        return enabledLicenses.stream()
            .map(licenseType -> createAppLicenseDraft(enabledLicenses, licenseType))
            .collect(toSet());
    }

    public AppLicenseDraftModel createAppLicenseDraft(final LicenseType licenseType) {
        return AppLicenseDraftBuilder.generate()
            .withLicenseType(licenseType)
            .withEnabledCountries(Set.of(getCountry(GERMANY)))
            .withAvailabilityStatus(LicenseAvailabilityStatus.PUBLISHED)
            .withSpecifiedPrice(getDefaultSpecifiedPriceForLicensetype(licenseType))
            .buildIntegrationInstance();
    }

    private AppLicenseDraftModel createAppLicenseDraft(Set<LicenseType> enabledLicenses, LicenseType licenseType) {
        AppLicenseDraftModel license = modelService.create(AppLicenseDraftModel.class);
        license.setLicenseType(licenseType);
        license.setAvailabilityStatus(LicenseAvailabilityStatus.PUBLISHED);
        license.setEnabledCountries(enabledLicenses.contains(licenseType) ? ImmutableSet.of(getCountry(GERMANY)) : Collections.emptySet());
        license.setSubmittedBy(getDeveloperByInternalUserId(SAMPLE_DATA_DEVELOPER_A1_UID));
        license.setSpecifiedPrice(getDefaultSpecifiedPriceForLicensetype(licenseType));
        modelService.save(license);

        return license;
    }

    public PaymentTransactionModel createPaymentTransaction(PaymentTransactionType type, String requestToken, AbstractOrderModel order) {
        var paymentTransaction = PaymentTransactionBuilder.generate()
            .withCode(randomUUID().toString())
            .withType(type)
            .withRequestId(requestToken)
            .withRequestToken(requestToken)
            .withPaymentProvider(PaymentProvider.STRIPE.getCode())
            .withOrder(order)
            .withCurrency(order.getCurrency())
            .withPlannedAmount(BigDecimal.valueOf(order.getTotalPrice()))
            .buildIntegrationInstance();

        modelService.save(paymentTransaction);
        return paymentTransaction;
    }

    public PaymentTransactionModel createSuccessfullyCapturedTransaction(AbstractOrderModel order, PaymentInfoModel paymentInfo) {
        var authEntry = PaymentTransactionEntryBuilder.generate()
            .withCode(Base58UUIDCodeGenerator.generateCode("entry"))
            .withType(PaymentTransactionType.AUTHORIZATION)
            .withTransactionStatus(TransactionStatus.ACCEPTED.name())
            .withTransactionStatusDetails(TransactionStatusDetails.SUCCESFULL.name())
            .withRequestToken(Base58UUIDCodeGenerator.generateCode("entryToken"))
            .withSubscriptionID(Base58UUIDCodeGenerator.generateCode("sid"))
            .buildIntegrationInstance();

        var captureEntry = PaymentTransactionEntryBuilder.generate()
            .withCode(Base58UUIDCodeGenerator.generateCode("entry"))
            .withType(PaymentTransactionType.CAPTURE)
            .withTransactionStatus(TransactionStatus.ACCEPTED.name())
            .withTransactionStatusDetails(TransactionStatusDetails.SUCCESFULL.name())
            .withRequestToken(Base58UUIDCodeGenerator.generateCode("entryToken"))
            .buildIntegrationInstance();

        var paymentTransaction = PaymentTransactionBuilder.generate()
            .withCode(Base58UUIDCodeGenerator.generateCode("transaction"))
            .withType(PaymentTransactionType.AUTHORIZATION)
            .withRequestId(Base58UUIDCodeGenerator.generateCode("requestId"))
            .withRequestToken(Base58UUIDCodeGenerator.generateCode("requestToken"))
            .withPaymentProvider(paymentInfo.getPaymentProvider().getCode())
            .withOrder(order)
            .withCurrency(order.getCurrency())
            .withInfo(paymentInfo)
            .withPlannedAmount(BigDecimal.valueOf(order.getTotalPrice()))
            .withEntries(List.of(authEntry, captureEntry))
            .buildIntegrationInstance();

        modelService.save(paymentTransaction);
        return paymentTransaction;
    }

    public EulaContainerModel createEulaContainer() {
        final EulaModel atEula = EulaBuilder.generate()
            .withType(EulaType.CUSTOM)
            .withCustomUrl(COUNTRY_EULA_ESI_AT)
            .buildIntegrationInstance();
        final EulaModel defaultEula = EulaBuilder.generate()
            .withType(EulaType.CUSTOM)
            .withCustomUrl(COUNTRY_EULA_ESI_DEFAULT)
            .buildIntegrationInstance();
        final CountryEulaModel atCountryEula = CountryEulaBuilder.generate()
            .withEula(atEula)
            .withCountry(getCountry(AUSTRIA))
            .buildIntegrationInstance();
        final CountryEulaModel defaultCountryEula = CountryEulaBuilder.generate()
            .withEula(defaultEula)
            .buildIntegrationInstance();
        final EulaContainerModel eulaContainer = EulaContainerBuilder.generate()
            .withCode(Base58UUIDCodeGenerator.generateCode("eula"))
            .withCountryEulas(Set.of(atCountryEula, defaultCountryEula))
            .buildIntegrationInstance();

        modelService.saveAll(atEula, defaultEula, atCountryEula, defaultCountryEula, eulaContainer);
        return eulaContainer;
    }

    public DpgSellerAccountModel createDpgSellerAccount(IoTCompanyModel sellerCompany) {
        var dpgSellerAccount = DpgSellerAccountBuilder.generate()
            .withAccountId("DPG_TEST_" + sellerCompany.getUid())
            .withBillingSystemStatus(BillingSystemStatus.IN_SYNC)
            .withDpgStatus(DpgSellerAccountStatus.ACCOUNT_ID_GENERATED)
            .withStatus(PspSellerAccountStatus.ACTIVE)
            .withCompany(sellerCompany)
            .withPaymentProvider(PaymentProvider.DPG)
            .buildIntegrationInstance();
        modelService.save(dpgSellerAccount);
        modelService.refresh(sellerCompany);
        return dpgSellerAccount;
    }

    public BoschSellerAccountModel createBoschSellerAccount(IoTCompanyModel sellerCompany) {
        var boschCollectionAccount = BoschSepaCollectionAccountBuilder.generate()
            .withCode("BTCOLL_TEST_" + sellerCompany.getUid())
            .withBankName("First Ferengi Interplanetary")
            .withBic("BYLADEM1001")
            .withIban("**********************")
            .buildIntegrationInstance();
        modelService.save(boschCollectionAccount);
        var boschSellerAccount = BoschSellerAccountBuilder.generate()
            .withAccountId("BTSELLER_TEST_" + sellerCompany.getUid())
            .withBillingSystemStatus(BillingSystemStatus.IN_SYNC)
            .withStatus(PspSellerAccountStatus.ACTIVE)
            .withCompany(sellerCompany)
            .withPaymentProvider(PaymentProvider.BOSCH_TRANSFER)
            .withSepaCollectionAccount(boschCollectionAccount)
            .buildIntegrationInstance();
        modelService.save(boschSellerAccount);
        modelService.refresh(sellerCompany);
        return boschSellerAccount;
    }

    public DeveloperModel getDeveloperByInternalUserId(final String internalUserId) {
        return developerService.getDeveloperByInternalUserId(internalUserId);
    }

    public IntegratorModel getIntegratorByInternalUserId(final String internalUserId) {
        return integratorService.getIntegratorByInternalUserId(internalUserId);
    }

    public IoTCompanyModel getCompanyByUidOrThrow(final String companyId) {
        return iotCompanyService.getCompanyByUidOrThrow(companyId);
    }

    public AaDistributorCompanyModel getAaDistributorCompany(final String distributorId) {
        return aaDistributorService.findAaDistributorByIdOrThrow(distributorId);
    }

    public CountryMigrationConfigurationModel getCountryMigrationConfiguration(final CountryModel country) {
        return countryMigrationConfigurationService.findByCountryOrThrow(country);
    }

    public BaseStoreModel getBaseStore(final BaseStoreEnum baseStore) {
        return baseStoreService.getBaseStoreForUid(baseStore.getBaseStoreUid());
    }

    public ContractMigrationProcessModel createContractMigrationProcess(final IoTCompanyModel buyerCompany) {
        final ContractMigrationProcessModel contractMigrationProcess = ContractMigrationProcessBuilder.generate()
            .withCode("CONTRACT_MIGRATION_PROCESS_%s".formatted(randomUUID()))
            .withCmtProcessExecutionId("%s".formatted(new Random().nextLong()))
            .withOwnerCompanyId(buyerCompany.getAaExternalCustomerId())
            .withCmtProcessExecutionCreationDate(Date.from(LocalDate.now().atStartOfDay().toInstant(UTC)))
            .withSourceContractMigrationRequestCode("REQUEST_%s".formatted(randomUUID()))
            .withMigrationMode(MigrationMode.EXECUTE)
            .buildIntegrationInstance();
        modelService.save(contractMigrationProcess);
        return contractMigrationProcess;
    }

    public FutureContractCancellationBusinessProcessModel createFutureDatedContractCancellationBp(final BuyerContractModel buyerContract) {
        final ContractCancellationInfoModel contractCancellationInfo = ContractCancellationInfoBuilder.generate()
            .withContractToCancel(buyerContract)
            .withRequestedDate(Date.from(LocalDate.now().atStartOfDay().toInstant(UTC)))
            .buildIntegrationInstance();
        final FutureContractCancellationBusinessProcessModel bp = FutureContractCancellationBusinessProcessBuilder.generate()
            .withCode("FUTURE_CONTRACT_CANCELLATION_BP_%s".formatted(randomUUID()))
            .withCancellationInfo(contractCancellationInfo)
            .withProcessDefinitionName(FUTURE_CONTRACT_CANCELLATION_PROCESS.getValue())
            .buildIntegrationInstance();
        modelService.save(bp);
        return bp;
    }

    public OrderProcessModel createOrderBp(final OrderModel order) {
        final OrderProcessModel orderProcess = OrderProcessBuilder.generate()
            .withCode("ORDER_BP_%s".formatted(randomUUID()))
            .withOrder(order)
            .withProcessDefinitionName(AA_BILLING_ORDER_PROCESS.getValue())
            .buildIntegrationInstance();
        modelService.save(orderProcess);
        return orderProcess;
    }

    public MigrationContractModel createMigrationContract(final ContractMigrationProcessModel contractMigrationProcess) {
        final MigrationContractModel migrationContract = MigrationContractBuilder.generate()
            .withCode("MIGRATION_CONTRACT_%s".formatted(randomUUID()))
            .withCmtId("%s".formatted(new Random().nextLong()))
            .withUmpContractId("%s".formatted(randomUUID()))
            .withLicenseType("AZCU")
            .withDistributor("distributor-id")
            .withMaterialId("material-id")
            .withUniqueBundleIdentifier("bundle-id")
            .withStartDate(Date.from(LocalDate.now().minusDays(1).atStartOfDay().toInstant(UTC)))
            .withEndDate(Date.from(LocalDate.now().plusDays(1).atStartOfDay().toInstant(UTC)))
            .withContractMigrationProcess(contractMigrationProcess)
            .buildIntegrationInstance();
        modelService.save(migrationContract);
        return migrationContract;
    }

    public MigrationContractGroupModel createMigrationContractGroup(final Set<MigrationContractModel> migrationContracts) {
        final MigrationContractGroupModel migrationContractGroup = MigrationContractGroupBuilder.generate()
            .withCode("MIGRATION_CONTRACT_GROUP_%s".formatted(randomUUID()))
            .withUniqueBundleIdentifier("bundle-id")
            .withMigrationContracts(migrationContracts)
            .buildIntegrationInstance();
        modelService.save(migrationContractGroup);
        return migrationContractGroup;
    }

    public MigrationOrderEntryDraftModel createMigrationOrderDraftEntry(
        final AppLicenseModel license, final Set<MigrationContractGroupModel> contractGroups) {

        final MigrationOrderEntryDraftModel migrationOrderEntryDraft = MigrationOrderEntryDraftBuilder.generate()
            .withCode("MIGRATION_ORDER_ENTRY_DRAFT_%s".formatted(randomUUID()))
            .withAppLicense(license)
            .withContractGroups(contractGroups)
            .withQuantity(1)
            .buildIntegrationInstance();
        modelService.save(migrationOrderEntryDraft);
        return migrationOrderEntryDraft;
    }

    public MigrationOrderDraftModel createMigrationOrderDraft(
        final IoTCompanyModel buyerCompany,
        final ContractMigrationProcessModel contractMigrationProcess,
        final Set<MigrationOrderEntryDraftModel> entryDrafts) {

        final MigrationOrderDraftModel migrationOrderDraft = MigrationOrderDraftBuilder.generate()
            .withCode("MIGRATION_ORDER_DRAFT_%s".formatted(randomUUID()))
            .withCompany(buyerCompany)
            .withContractMigrationProcess(contractMigrationProcess)
            .withEntries(entryDrafts)
            .buildIntegrationInstance();

        modelService.save(migrationOrderDraft);
        return migrationOrderDraft;
    }
    public OpenOrderCancelBusinessProcessModel createOpenOrderCancelBusinessProcess(final OrderModel order) {
        final OrderCancelRecordModel orderCancelRecord = OrderCancelRecordBuilder.generate()
            .withOrder(order)
            .withInProgress(true)
            .withOwner(order)
            .buildIntegrationInstance();
        order.setModificationRecords(ImmutableSet.of(orderCancelRecord));
        final String processId = "openOrderCancellationBusinessProcess-order_%s-uuid_%s".formatted(
            order.getCode(),
            randomUUID()
        );
        final OpenOrderCancelBusinessProcessModel bp = OpenOrderCancelBusinessProcessBuilder.generate()
            .withCode(processId)
            .withOrder(order)
            .withProcessDefinitionName(OPEN_ORDER_CANCEL_PROCESS.getValue())
            .withState(ProcessState.WAITING)
            .buildIntegrationInstance();
        modelService.saveAll(order,bp);
        return bp;
    }
}
