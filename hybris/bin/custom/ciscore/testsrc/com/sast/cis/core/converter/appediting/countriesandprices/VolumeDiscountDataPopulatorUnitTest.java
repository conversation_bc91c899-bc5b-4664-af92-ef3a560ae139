package com.sast.cis.core.converter.appediting.countriesandprices;

import com.sast.cis.core.data.VolumeDiscountData;
import de.hybris.bootstrap.annotations.UnitTest;
import de.hybris.platform.core.PK;
import generated.com.sast.cis.core.model.VolumeDiscountBuilder;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.*;

@UnitTest
public class VolumeDiscountDataPopulatorUnitTest {
    private VolumeDiscountDataPopulator populator;

    @Before
    public void setUp() throws Exception {
        populator = new VolumeDiscountDataPopulator();
    }

    @Test
    public void populate_populatesExpectedFields() {
        var model = VolumeDiscountBuilder.generate()
            .withDiscount(2)
            .withMinQuantity(2)
            .buildMockInstance();
        when(model.getPk()).thenReturn(PK.fromLong(2));

        var populatedDiscount = mock(VolumeDiscountData.class);

        populator.populate(model, populatedDiscount);
        verify(populatedDiscount).setPk(2L);
        verify(populatedDiscount).setMinQuantity(2);
        verify(populatedDiscount).setDiscount(2);
        verifyNoMoreInteractions(populatedDiscount);
    }
}
