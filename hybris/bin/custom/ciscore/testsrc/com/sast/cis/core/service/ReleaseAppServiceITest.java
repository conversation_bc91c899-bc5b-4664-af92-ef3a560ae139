package com.sast.cis.core.service;

import com.google.common.collect.ImmutableSet;
import com.sast.cis.core.dao.IndustryDao;
import com.sast.cis.core.enums.EulaType;
import com.sast.cis.core.enums.LicenseType;
import com.sast.cis.core.enums.StoreAvailabilityMode;
import com.sast.cis.core.model.*;
import com.sast.cis.core.service.customer.developer.DeveloperService;
import com.sast.cis.test.utils.SampleDataCreator;
import com.sast.cis.test.utils.SessionCatalogRule;
import com.sast.cis.test.utils.TestDataConstants;
import de.hybris.bootstrap.annotations.IntegrationTest;
import de.hybris.platform.catalog.enums.ArticleApprovalStatus;
import de.hybris.platform.core.model.media.MediaContainerModel;
import de.hybris.platform.europe1.enums.ProductTaxGroup;
import de.hybris.platform.europe1.model.PriceRowModel;
import de.hybris.platform.product.UnitService;
import de.hybris.platform.servicelayer.ServicelayerTest;
import de.hybris.platform.servicelayer.exceptions.ModelSavingException;
import de.hybris.platform.servicelayer.model.ModelService;
import de.hybris.platform.site.BaseSiteService;
import de.hybris.platform.testframework.HybrisJUnit4ClassRunner;
import de.hybris.platform.testframework.RunListeners;
import de.hybris.platform.testframework.runlistener.ItemCreationListener;
import org.assertj.core.data.Offset;
import org.assertj.core.groups.Tuple;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.runner.RunWith;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Set;
import java.util.UUID;

import static com.sast.cis.core.constants.CiscoreConstants.*;
import static com.sast.cis.core.constants.Currency.EUR;
import static com.sast.cis.core.constants.Currency.USD;
import static com.sast.cis.core.enums.LicenseType.*;
import static com.sast.cis.test.utils.Country.GERMANY;
import static com.sast.cis.test.utils.SampleDataCreator.EUR_PRICE;
import static com.sast.cis.test.utils.SampleDataCreator.USD_PRICE;
import static com.sast.cis.test.utils.TestDataConstants.PRODUCT_CONTAINER.*;
import static org.assertj.core.api.Assertions.assertThat;

@IntegrationTest
@RunWith(HybrisJUnit4ClassRunner.class)
@RunListeners({ ItemCreationListener.class })
public class ReleaseAppServiceITest extends ServicelayerTest {

    private static final String APP_TITLE = "AppTitleReleaseApp";
    private static final String ECCN = "0B001";
    private static final double TOLERANCE = 0.0000001;
    private static final boolean NEW_COUNTRY_ADDING_CONSENT = true;

    @Rule
    public SessionCatalogRule sessionCatalogRule = SessionCatalogRule.stagedCatalog();
    private final SampleDataCreator sampleDataCreator = new SampleDataCreator();

    @Resource
    private ReleaseAppService releaseAppService;

    @Resource
    private DeveloperService developerService;

    @Resource
    private ModelService modelService;

    @Resource
    private UnitService unitService;

    @Resource
    private BaseSiteService baseSiteService;

    @Resource
    private IndustryDao industryDao;

    private DeveloperModel developer;
    private AppDraftModel appDraft;
    private AppVersionDraftModel appVersionDraft;
    private ProductContainerModel productContainer;
    private List<MediaContainerModel> screenshots;
    private MediaContainerModel icon;
    private ApkMediaModel apk;
    private Date testStartTime;
    private IndustryModel industry;

    @Before
    public void setup() {
        setUpDeveloperAndApp(ImmutableSet.of(FULL, EVALUATION, SUBSCRIPTION));
        baseSiteService.setCurrentBaseSite(baseSiteService.getBaseSiteForUID(DEVELOPER_CONSOLE_BASE_SITE_UID), true);
        testStartTime = new Date();
        industry = industryDao.getAllEnabledIndustries().get(0);
    }

    private void setUpDeveloperAndApp(Set<LicenseType> licenses) {
        developer = developerService.getDeveloperByInternalUserId(TestDataConstants.SAMPLE_DATA_DEVELOPER_A1_UID);
        developerService.setCurrentDeveloper(developer);
        productContainer = sampleDataCreator.createReleasableProductContainer(ReleaseAppServiceITest.APP_TITLE, developer, licenses);
        appDraft = productContainer.getAppDraft();
        appVersionDraft = productContainer.getAppVersionDraft();
        screenshots = productContainer.getAppDraft().getStoreContentDraft().getScreenshots();
        icon = productContainer.getAppDraft().getStoreContentDraft().getIcon();
        apk = appVersionDraft.getApk();

        appDraft.setSubmittedBy(developer);
        appDraft.setNewCountryAddingAllowed(NEW_COUNTRY_ADDING_CONSENT);
        modelService.save(appDraft);

        appVersionDraft.setEccn(ECCN);
        appVersionDraft.setDualUse(true);
        modelService.save(appVersionDraft);
    }

    @Test
    public void release_allFieldsValidWithEvalLicense_appIsReleasedWithTwoVariants() {
        String draftApkCode = productContainer.getAppVersionDraft().getCode();
        StoreContentDraftModel storeContentDraft = productContainer.getAppDraft().getStoreContentDraft();
        storeContentDraft.setIndustries(ImmutableSet.of(industry));
        modelService.save(storeContentDraft);

        releaseAppService.release(productContainer);

        assertThat(modelService.isRemoved(appDraft)).isTrue();
        assertThat(modelService.isRemoved(apk)).isTrue();
        assertThat(productContainer.getAppDraft()).isNull();

        AppModel app = productContainer.getApp();
        validateAppFields(app);
        assertThat(app.getStoreAvailabilityMode()).isEqualTo(StoreAvailabilityMode.PUBLIC);
        assertThat(app.getVariants()).hasSize(3);

        AppLicenseModel fullAppLicense = getAppLicense(app, LicenseType.FULL);
        AppLicenseModel evalAppLicense = getAppLicense(app, LicenseType.EVALUATION);
        AppLicenseModel subscriptionAppLicense = getAppLicense(app, LicenseType.SUBSCRIPTION);
        validateFullAppLicenseFields(fullAppLicense, app);
        validateSubscriptionAppLicenseFields(subscriptionAppLicense, app);
        validateEvalAppLicenseFields(evalAppLicense, app);

        assertThat(app.getVersions()).hasSize(1);
        AppVersionModel appVersion = app.getVersions().iterator().next();
        assertThat(appVersion.getApk()).isNotNull();
        assertThat(appVersion.getApk().getPermissions()).extracting(PermissionModel::getTechnicalName)
            .containsExactly("PermissionTechnicalName");
        assertThat(appVersion.getApk().getCode()).isNotEqualTo(draftApkCode);
        assertThat(appVersion.getApk().getVersionCode()).isEqualTo(10L);
        assertThat(appVersion.getApk().getVersionName()).isEqualTo(SampleDataCreator.VERSION_DRAFT_PREFIX + "appSuffix");
        assertThat(appVersion.getChangelog(Locale.ENGLISH)).isEqualTo(SampleDataCreator.CHANGELOG);
        assertThat(appVersion.getEccn()).isEqualTo(ECCN);
        assertThat(appVersion.isDualUse()).isTrue();
        assertThat(app.getIndustries()).hasSize(1);
        assertThat(app.getIndustries().iterator().next().getName()).isEqualTo(industry.getName());
    }

    @Test
    public void release_allFieldsValidWithoutEvalLicense_appIsReleasedWithOnlyFullLicense() {
        AppDraftModel appDraft = productContainer.getAppDraft();
        CountriesAndPricesDraftModel countriesAndPricesDraft = appDraft.getCountriesAndPricesDraft();
        AppLicenseDraftModel fullLicenseDraft = sampleDataCreator.createAppLicenseDraft(LicenseType.FULL);
        countriesAndPricesDraft.setAppLicenses(Set.of(fullLicenseDraft));
        modelService.save(fullLicenseDraft);
        modelService.save(countriesAndPricesDraft);

        String draftApkCode = productContainer.getAppVersionDraft().getCode();

        releaseAppService.release(productContainer);

        assertThat(modelService.isRemoved(this.appDraft)).isTrue();
        assertThat(modelService.isRemoved(apk)).isTrue();
        assertThat(productContainer.getAppDraft()).isNull();

        AppModel app = productContainer.getApp();
        validateAppFields(app);
        assertThat(app.getStoreAvailabilityMode()).isEqualTo(StoreAvailabilityMode.PUBLIC);

        assertThat(app.getVariants()).hasSize(1);
        AppLicenseModel fullAppLicense = getAppLicense(app, LicenseType.FULL);
        validateFullAppLicenseFields(fullAppLicense, app);

        assertThat(app.getVersions()).hasSize(1);
        AppVersionModel appVersion = app.getVersions().iterator().next();
        assertThat(appVersion.getApk()).isNotNull();
        assertThat(appVersion.getApk().getPermissions()).extracting(PermissionModel::getTechnicalName)
            .containsExactly("PermissionTechnicalName");
        assertThat(appVersion.getApk().getCode()).isNotEqualTo(draftApkCode);
        assertThat(appVersion.getApk().getVersionCode()).isEqualTo(10L);
        assertThat(appVersion.getApk().getVersionName()).isEqualTo(SampleDataCreator.VERSION_DRAFT_PREFIX + "appSuffix");
        assertThat(appVersion.getChangelog(Locale.ENGLISH)).isEqualTo(SampleDataCreator.CHANGELOG);
        assertThat(appVersion.getEccn()).isEqualTo(ECCN);
        assertThat(appVersion.isDualUse()).isTrue();
    }

    @Test
    public void release_disabledDraftResultsInDisabledApp() {
        productContainer.getAppDraft().setStoreAvailabilityMode(StoreAvailabilityMode.UNAVAILABLE);
        modelService.save(productContainer.getAppDraft());

        releaseAppService.release(productContainer);

        AppModel app = productContainer.getApp();
        assertThat(app.getStoreAvailabilityMode()).isEqualTo(StoreAvailabilityMode.UNAVAILABLE);
    }

    @Test
    public void release_noNewCountryAddingConsentDraftResultsInNoNewCountryConsentApp() {
        productContainer.getAppDraft().setNewCountryAddingAllowed(false);
        modelService.save(productContainer.getAppDraft());

        releaseAppService.release(productContainer);

        AppModel app = productContainer.getApp();
        assertThat(app.isNewCountryAddingAllowed()).isFalse();
    }

    @Test
    public void release_withNewCountryAddingConsentDraftResultsInNoNewCountryConsentApp() {
        productContainer.getAppDraft().setNewCountryAddingAllowed(true);
        modelService.save(productContainer.getAppDraft());

        releaseAppService.release(productContainer);

        AppModel app = productContainer.getApp();
        assertThat(app.isNewCountryAddingAllowed()).isTrue();
    }

    @Test
    public void release_appAlreadyReleased_failsAndThrowsException() {
        ApkMediaModel copyOfApkMediaModel = modelService.clone(productContainer.getAppVersionDraft().getApk());
        copyOfApkMediaModel.setCode("copied_" + copyOfApkMediaModel.getCode());
        modelService.save(copyOfApkMediaModel);
        AppVersionDraftModel copiedAppVersionDraft = modelService.clone(productContainer.getAppVersionDraft());
        copiedAppVersionDraft.setCode("copied_" + copiedAppVersionDraft.getCode());
        copiedAppVersionDraft.setApk(copyOfApkMediaModel);
        modelService.save(copiedAppVersionDraft);
        AppDraftModel copiedAppDraft = modelService.clone(appDraft);
        copiedAppDraft.setCode("copied_" + appDraft.getCode());
        copiedAppDraft.getCountriesAndPricesDraft().setCode(UUID.randomUUID().toString());
        copiedAppDraft.getStoreContentDraft().setCode(UUID.randomUUID().toString());
        modelService.saveAll(copiedAppDraft, productContainer);

        releaseAppService.release(productContainer);

        productContainer.setAppVersionDraft(copiedAppVersionDraft);
        productContainer.setApp(null);
        productContainer.setAppDraft(copiedAppDraft);
        modelService.save(productContainer);

        try {
            releaseAppService.release(productContainer);
            Assert.fail("Expected ModelSavingException");
        } catch (ModelSavingException e) {
            assertThat(e.getMessage()).contains(
                "org.springframework.dao.DuplicateKeyException: query; SQL []; Duplicate entry 'containerpackage.testpackagename");
        }
    }

    private AppLicenseModel getAppLicense(AppModel app, LicenseType licenseType) {
        return (AppLicenseModel) app.getVariants().stream()
            .filter(appVariant -> ((AppLicenseModel) appVariant).getLicenseType() == licenseType).findAny().get();
    }

    private void validateAppFields(AppModel app) {
        assertThat(app).isNotNull();
        assertThat(app.getCompany()).isEqualTo(developer.getCompany());
        assertThat(app.getPackageName()).isEqualTo(SampleDataCreator.PACKAGE_NAME);

        assertThat(app.getName(Locale.ENGLISH)).isEqualTo(APP_TITLE);
        assertThat(app.getName(Locale.GERMAN)).isEqualTo(NAME_GERMAN);
        assertThat(app.getDescription(Locale.ENGLISH)).isEqualTo(DESCRIPTION_ENGLISH);
        assertThat(app.getDescription(Locale.GERMAN)).isEqualTo(DESCRIPTION_GERMAN);
        assertThat(app.getSummary(Locale.ENGLISH)).isEqualTo(SUMMARY_ENGLISH);
        assertThat(app.getSummary(Locale.GERMAN)).isEqualTo(SUMMARY_GERMAN);
        assertThat(app.getProductWebsiteUrl()).isEqualTo(PRODUCT_WEBSITE);
        assertThat(app.getGalleryImages()).containsAll(screenshots);
        assertThat(app.getIcon()).isEqualTo(icon);

        assertThat(app.getPrivacyPolicyUrl()).isEqualTo(TestDataConstants.PRODUCT_CONTAINER.PRIVACY_POLICY);
        assertThat(app.getEula().getType()).isEqualTo(EulaType.STANDARD);

        assertThat(app.getEmailAddress()).isEqualTo(TestDataConstants.PRODUCT_CONTAINER.EMAIL);
        assertThat(app.getSupportPageUrl()).isEqualTo(TestDataConstants.PRODUCT_CONTAINER.SUPPORT_PAGE);
        assertThat(app.getSupportPhoneNumber()).isEqualTo(TestDataConstants.PRODUCT_CONTAINER.SUPPORT_PHONE);

        assertThat(app.getEurope1PriceFactory_PTG()).isEqualTo(ProductTaxGroup.valueOf(EU_FULL_VAT));
        assertThat(app.getUnit()).isEqualTo(unitService.getUnitForCode(PIECES));
        assertThat(app.getApprovalStatus()).isEqualTo(ArticleApprovalStatus.CHECK);
        assertThat(app.getSubmittedBy()).isEqualTo(developer);
        assertThat(app.getPublishDate()).isNotNull();
        assertThat(app.isNewCountryAddingAllowed()).isEqualTo(NEW_COUNTRY_ADDING_CONSENT);
    }

    private void validateSubscriptionAppLicenseFields(AppLicenseModel appLicense, AppModel app) {
        validateAppLicenseFields(appLicense, app);
        assertThat(appLicense.getCode()).endsWith(SUBSCRIPTION_SUFFIX);
        assertThat(appLicense.getEurope1Prices().stream()
            .map(priceRow -> new Tuple(priceRow.getEndTime(), priceRow.getPrice(), priceRow.getCurrency())))
            .containsExactlyInAnyOrder(new Tuple(null, EUR_PRICE, sampleDataCreator.getCurrency(EUR)),
                new Tuple(null, USD_PRICE, sampleDataCreator.getCurrency(USD)));
        assertThat(appLicense.getEurope1Prices().stream()
            .map(PriceRowModel::getStartTime)).doesNotContainNull();
        assertThat(appLicense.getEurope1Prices().stream().filter(priceRow -> priceRow.getStartTime().before(testStartTime))).hasSize(0);
        assertThat(appLicense.getEurope1Prices().stream().filter(priceRow -> priceRow.getStartTime().after(new Date()))).hasSize(0);
        assertThat(appLicense.getSpecifiedPrice()).isCloseTo(EUR_PRICE, Offset.offset(TOLERANCE));
    }

    private void validateFullAppLicenseFields(AppLicenseModel appLicense, AppModel app) {
        validateAppLicenseFields(appLicense, app);
        assertThat(appLicense.getCode()).doesNotEndWith(EVALUATION_SUFFIX);
        assertThat(appLicense.getEurope1Prices()).extracting("endTime", "price", "currency", "ug")
            .containsExactlyInAnyOrder(
                Tuple.tuple(null, EUR_PRICE, sampleDataCreator.getCurrency(EUR), null),
                Tuple.tuple(null, USD_PRICE, sampleDataCreator.getCurrency(USD), null),
                Tuple.tuple(null, 0.0, sampleDataCreator.getCurrency(EUR), app.getCompany().getUserPriceGroup()),
                Tuple.tuple(null, 0.0, sampleDataCreator.getCurrency(USD), app.getCompany().getUserPriceGroup()));
        assertThat(appLicense.getEurope1Prices().stream()
            .map(PriceRowModel::getStartTime)).doesNotContainNull();
        assertThat(appLicense.getEurope1Prices().stream().filter(priceRow -> priceRow.getStartTime().before(testStartTime))).hasSize(0);
        assertThat(appLicense.getEurope1Prices().stream().filter(priceRow -> priceRow.getStartTime().after(new Date()))).hasSize(0);
        assertThat(appLicense.getSpecifiedPrice()).isCloseTo(EUR_PRICE, Offset.offset(TOLERANCE));
    }

    private void validateEvalAppLicenseFields(AppLicenseModel appLicense, AppModel app) {
        validateAppLicenseFields(appLicense, app);
        assertThat(appLicense.getCode()).endsWith(EVALUATION_SUFFIX);
        assertThat(appLicense.getEurope1Prices().stream()
            .map(priceRow -> new Tuple(priceRow.getEndTime(), priceRow.getPrice(), priceRow.getCurrency())))
            .containsExactlyInAnyOrder(new Tuple(null, 0.0, sampleDataCreator.getCurrency(EUR)),
                new Tuple(null, 0.0, sampleDataCreator.getCurrency(USD)));
        assertThat(appLicense.getEurope1Prices().stream()
            .map(PriceRowModel::getStartTime)).doesNotContainNull();
        assertThat(appLicense.getEurope1Prices().stream().filter(priceRow -> priceRow.getStartTime().before(testStartTime))).hasSize(0);
        assertThat(appLicense.getEurope1Prices().stream().filter(priceRow -> priceRow.getStartTime().after(new Date()))).hasSize(0);
    }

    private void validateAppLicenseFields(AppLicenseModel appLicense, AppModel app) {
        assertThat(appLicense).isNotNull();
        assertThat(appLicense.getEnabledCountries()).isEqualTo(ImmutableSet.of(sampleDataCreator.getCountry(GERMANY)));
        assertThat(appLicense.getEurope1PriceFactory_PTG()).isEqualTo(ProductTaxGroup.valueOf(EU_FULL_VAT));
        assertThat(appLicense.getUnit()).isEqualTo(unitService.getUnitForCode(PIECES));
        assertThat(appLicense.getApprovalStatus()).isEqualTo(ArticleApprovalStatus.CHECK);
        assertThat(appLicense.getBaseProduct()).isEqualTo(app);
    }
}
