package com.sast.cis.core.converter.payment;

import com.sast.cis.core.data.CreditCardPaymentInfoData;
import com.sast.cis.core.enums.PaymentMethodType;
import de.hybris.bootstrap.annotations.UnitTest;
import de.hybris.platform.commercefacades.order.data.CardTypeData;
import de.hybris.platform.commercefacades.user.data.AddressData;
import de.hybris.platform.core.PK;
import de.hybris.platform.core.enums.CreditCardType;
import de.hybris.platform.core.model.order.payment.CreditCardPaymentInfoModel;
import de.hybris.platform.core.model.user.AddressModel;
import de.hybris.platform.core.model.user.CustomerModel;
import de.hybris.platform.servicelayer.dto.converter.ConversionException;
import de.hybris.platform.servicelayer.dto.converter.Converter;
import generated.de.hybris.platform.core.model.order.payment.CreditCardPaymentInfoBuilder;
import generated.de.hybris.platform.core.model.user.AddressBuilder;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.when;

@UnitTest
@RunWith(MockitoJUnitRunner.Silent.class)
public class CisCreditCardPaymentInfoPopulatorUnitTest {

    private static final PK PAYMENT_INFO_PK = PK.fromLong(1000001L);
    private static final String CREDIT_CARD_NUMBER = "4444 5555 5555 5555";
    private static final CreditCardType CREDIT_CARD_TYPE = CreditCardType.MASTERCARD_EUROCARD;
    private static final String CREDIT_CARD_OWNER = "Testy McTestFace";
    private static final String CREDIT_CARD_VALID_TO_MONTH = "11";
    private static final String CREDIT_CARD_VALID_TO_YEAR = "21";
    private static final String CREDIT_CARD_VALID_FROM_MONTH = "11";
    private static final String CREDIT_CARD_VALID_FROM_YEAR = "21";
    private static final String SUBSCRIPTION_ID = "nope";
    private static final Integer ISSUE_NUMBER = 11111;
    private static final boolean SAVED = true;

    @Mock
    private Converter<AddressModel, AddressData> addressConverter;

    @Mock
    private Converter<CreditCardType, CardTypeData> cardTypeConverter;

    @InjectMocks
    private CisCreditCardPaymentInfoPopulator populator;

    @Mock
    private CustomerModel customer;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void convert_SourceIsNull_throwsException() {
        CreditCardPaymentInfoData actualTarget = new CreditCardPaymentInfoData();
        assertThatThrownBy(() -> populator.populate(null, actualTarget)).isInstanceOf(ConversionException.class);
    }

    @Test
    public void convert_targetIsNull_throwsException() {
        CreditCardPaymentInfoModel source = CreditCardPaymentInfoBuilder.generate().buildInstance();
        assertThatThrownBy(() -> populator.populate(source, null)).isInstanceOf(ConversionException.class);
    }

    @Test
    public void convert_SourceIsFull_returnsFullEntry() {
        AddressModel billingAddress = AddressBuilder.generate().buildMockInstance();
        CreditCardPaymentInfoModel source = CreditCardPaymentInfoBuilder.generate()
            .withNumber(CREDIT_CARD_NUMBER)
            .withType(CREDIT_CARD_TYPE)
            .withCcOwner(CREDIT_CARD_OWNER)
            .withValidToMonth(CREDIT_CARD_VALID_TO_MONTH)
            .withValidToYear(CREDIT_CARD_VALID_TO_YEAR)
            .withValidFromMonth(CREDIT_CARD_VALID_FROM_MONTH)
            .withValidFromYear(CREDIT_CARD_VALID_FROM_YEAR)
            .withSubscriptionId(SUBSCRIPTION_ID)
            .withBillingAddress(billingAddress)
            .withIssueNumber(ISSUE_NUMBER)
            .withSaved(SAVED)
            .withUser(customer)
            .buildMockInstance();

        when(customer.getDefaultPaymentInfo()).thenReturn(source);

        CardTypeData cardTypeData = new CardTypeData().withCode(CREDIT_CARD_TYPE.getCode()).withName(CREDIT_CARD_TYPE.getType());
        AddressData billingAddressData =  new AddressData();

        when(source.getPk()).thenReturn(PAYMENT_INFO_PK);
        when(cardTypeConverter.convert(CREDIT_CARD_TYPE)).thenReturn(cardTypeData);
        when(addressConverter.convert(billingAddress)).thenReturn(billingAddressData);

        CreditCardPaymentInfoData actualTarget = new CreditCardPaymentInfoData();
        populator.populate(source, actualTarget);

        CreditCardPaymentInfoData expectedTarget = new CreditCardPaymentInfoData();
        expectedTarget.setId(PAYMENT_INFO_PK.toString());
        expectedTarget.setSaved(SAVED);
        expectedTarget.setDefaultPaymentInfo(true);
        expectedTarget.setPaymentMethod(PaymentMethodType.CREDIT_CARD);
        expectedTarget.setCardNumber(CREDIT_CARD_NUMBER);
        expectedTarget.setCardType(CREDIT_CARD_TYPE.getCode());
        expectedTarget.setCardTypeData(cardTypeData);
        expectedTarget.setAccountHolderName(CREDIT_CARD_OWNER);
        expectedTarget.setExpiryMonth(CREDIT_CARD_VALID_TO_MONTH);
        expectedTarget.setExpiryYear(CREDIT_CARD_VALID_TO_YEAR);
        expectedTarget.setStartMonth(CREDIT_CARD_VALID_FROM_MONTH);
        expectedTarget.setStartYear(CREDIT_CARD_VALID_FROM_YEAR);
        expectedTarget.setSubscriptionId(SUBSCRIPTION_ID);
        expectedTarget.setBillingAddress(billingAddressData);
        expectedTarget.setIssueNumber(ISSUE_NUMBER.toString());

        assertThat(actualTarget).isEqualToComparingFieldByFieldRecursively(expectedTarget);
    }

    @Test
    public void convert_SourceIsMissingData_returnsEntry() {
        CreditCardPaymentInfoModel source = CreditCardPaymentInfoBuilder.generate()
            .withNumber(CREDIT_CARD_NUMBER)
            .withType(null)
            .withCcOwner(CREDIT_CARD_OWNER)
            .withValidToMonth(CREDIT_CARD_VALID_TO_MONTH)
            .withValidToYear(CREDIT_CARD_VALID_TO_YEAR)
            .withValidFromMonth(CREDIT_CARD_VALID_FROM_MONTH)
            .withValidFromYear(CREDIT_CARD_VALID_FROM_YEAR)
            .withSubscriptionId(SUBSCRIPTION_ID)
            .withBillingAddress(null)
            .withIssueNumber(null)
            .withSaved(SAVED)
            .withUser(customer)
            .buildMockInstance();

        when(source.getPk()).thenReturn(PAYMENT_INFO_PK);

        CreditCardPaymentInfoData actualTarget = new CreditCardPaymentInfoData();
        populator.populate(source, actualTarget);

        CreditCardPaymentInfoData expectedTarget = new CreditCardPaymentInfoData();
        expectedTarget.setId(PAYMENT_INFO_PK.toString());
        expectedTarget.setSaved(SAVED);
        expectedTarget.setDefaultPaymentInfo(false);
        expectedTarget.setPaymentMethod(PaymentMethodType.CREDIT_CARD);
        expectedTarget.setCardNumber(CREDIT_CARD_NUMBER);
        expectedTarget.setCardType(null);
        expectedTarget.setCardTypeData(null);
        expectedTarget.setAccountHolderName(CREDIT_CARD_OWNER);
        expectedTarget.setExpiryMonth(CREDIT_CARD_VALID_TO_MONTH);
        expectedTarget.setExpiryYear(CREDIT_CARD_VALID_TO_YEAR);
        expectedTarget.setStartMonth(CREDIT_CARD_VALID_FROM_MONTH);
        expectedTarget.setStartYear(CREDIT_CARD_VALID_FROM_YEAR);
        expectedTarget.setSubscriptionId(SUBSCRIPTION_ID);
        expectedTarget.setBillingAddress(null);
        expectedTarget.setIssueNumber(null);

        assertThat(actualTarget).isEqualToComparingFieldByFieldRecursively(expectedTarget);
    }

}
