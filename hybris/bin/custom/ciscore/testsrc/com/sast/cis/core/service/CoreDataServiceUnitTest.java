package com.sast.cis.core.service;

import com.sast.cis.core.constants.CiscoreConstants;
import com.sast.cis.core.data.CoreData;
import com.sast.cis.core.data.Idp;
import com.sast.cis.core.data.IotCompanyData;
import com.sast.cis.core.facade.NavigationItemFacade;
import com.sast.cis.core.model.IoTCompanyModel;
import com.sast.cis.core.service.company.IotCompanyService;
import com.sast.cis.core.service.country.CountryService;
import com.sast.cis.core.service.security.SpringSecurityAdapter;
import com.sast.cis.core.service.security.UserPermissionService;
import com.sast.cis.core.service.security.UserPrincipal;
import de.hybris.bootstrap.annotations.UnitTest;
import de.hybris.platform.commercefacades.storesession.StoreSessionFacade;
import de.hybris.platform.commercefacades.storesession.data.CurrencyData;
import de.hybris.platform.commercefacades.storesession.data.LanguageData;
import de.hybris.platform.commercefacades.user.data.CountryData;
import de.hybris.platform.commerceservices.i18n.CommerceCommonI18NService;
import de.hybris.platform.core.model.c2l.CountryModel;
import de.hybris.platform.core.model.c2l.CurrencyModel;
import de.hybris.platform.core.model.c2l.LanguageModel;
import de.hybris.platform.core.model.user.CustomerModel;
import de.hybris.platform.core.model.user.UserModel;
import de.hybris.platform.servicelayer.config.ConfigurationService;
import de.hybris.platform.servicelayer.dto.converter.Converter;
import de.hybris.platform.servicelayer.user.UserService;
import de.hybris.platform.store.BaseStoreModel;
import de.hybris.platform.store.services.BaseStoreService;
import org.apache.commons.configuration.Configuration;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Answers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.security.web.csrf.CsrfToken;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@UnitTest
@RunWith(MockitoJUnitRunner.Silent.class)
public class CoreDataServiceUnitTest {
    private static final String USER_NAME = "Heinz Herbert Tester";
    private static final String CSRF_TOKEN_SESSION_ATTRIBUTE = "org.springframework.security.web.csrf.HttpSessionCsrfTokenRepository.CSRF_TOKEN";
    private static final String ACCOUNTS_URL = "https://accounts.ourdomain.com";
    private static final String INFO_URL = "https://info.ourdomain.com";
    private static final String CORPORATE_HOME = "https://ourdomain.com";
    private static final String AA_STORE = "aastore";

    @Mock
    private FeatureToggleService featureToggleService;

    @Mock
    private StoreSessionFacade storeSessionFacade;

    @Mock
    private UserService userService;

    @Mock
    private BaseStoreService baseStoreService;

    @Mock
    protected CommerceCommonI18NService commerceCommonI18NService;

    @Mock
    private Converter<LanguageModel, LanguageData> languageConverter;

    @Mock
    private Converter<CurrencyModel, CurrencyData> currencyConverter;

    @Mock
    private Converter<CountryModel, CountryData> countryConverter;

    @Mock
    private ConfigurationService configurationService;

    @Mock
    private NavigationItemFacade navigationItemFacade;

    @Mock
    private IotCompanyService iotCompanyService;

    @Mock
    private CountryService countryService;

    @Mock
    private UserPermissionService userPermissionService;

    @Mock
    private SpringSecurityAdapter springSecurityAdapter;

    @Mock
    private Converter<IoTCompanyModel, IotCompanyData> cisIotCompanyDataConverter;

    @InjectMocks
    private CoreDataService coreDataService;

    @Mock
    private CurrencyData currencyEur;

    @Mock
    private CurrencyData currencyUsd;

    @Mock
    private LanguageData languageDe;

    @Mock
    private LanguageData languageEn;

    @Mock
    private CountryData countryDe;

    @Mock
    private CountryData countryNz;

    @Mock
    private CustomerModel user;

    @Mock
    private LanguageModel languageModelDe;

    @Mock
    private CurrencyModel currencyModelEur;

    @Mock
    private CurrencyModel currencyModelUsd;

    @Mock
    private CountryModel countryModelDe;

    @Mock
    private CountryModel countryModelNz;

    @Mock
    private Configuration configuration;

    @Mock
    private HttpServletRequest httpServletRequest;

    @Mock
    private HttpServletResponse httpServletResponse;

    @Mock
    private HttpSession httpSession;

    @Mock
    private CsrfToken csrfToken;

    @Mock
    private UserModel currentUser;

    @Mock
    private BaseStoreModel baseStore;

    @Mock
    private IoTCompanyModel company;

    @Mock
    private IotCompanyData companyData;

    @Mock
    private UserPrincipal userPrincipal;

    @Before
    public void setUp() {
        when(featureToggleService.getActiveFeatures()).thenReturn(Set.of("FEATURE_ENABLE_STUFF", "FEATURE_ALLOW_NOTHING", "IMPROPER_NAME"));
        when(storeSessionFacade.getAllCurrencies()).thenReturn(List.of(currencyUsd, currencyEur));
        when(currentUser.getDisplayName()).thenReturn(USER_NAME);
        when(currentUser.getUid()).thenReturn("");
        when(userService.getCurrentUser()).thenReturn(currentUser);
        when(baseStoreService.getCurrentBaseStore()).thenReturn(baseStore);
        when(baseStore.getUid()).thenReturn(AA_STORE);
        when(commerceCommonI18NService.getDefaultLanguage()).thenReturn(languageModelDe);
        when(commerceCommonI18NService.getCurrentCurrency()).thenReturn(currencyModelUsd);
        when(languageConverter.convert(languageModelDe)).thenReturn(languageDe);
        when(currencyConverter.convert(currencyModelEur)).thenReturn(currencyEur);
        when(currencyConverter.convert(currencyModelUsd)).thenReturn(currencyUsd);
        when(configurationService.getConfiguration()).thenReturn(configuration);
        when(configurationService.getConfiguration().getString(CiscoreConstants.ACCOUNTS_URL_PROPERTY)).thenReturn(ACCOUNTS_URL);
        when(configurationService.getConfiguration().getString(CiscoreConstants.INFO_URL_PROPERTY)).thenReturn(INFO_URL);
        when(configurationService.getConfiguration().getString(CiscoreConstants.CORPORATE_HOME_PROPERTY)).thenReturn(CORPORATE_HOME);
        when(navigationItemFacade.getNavigationUrl("globalSupport", baseStore)).thenReturn("/support");

        when(iotCompanyService.getCurrentCompany()).thenReturn(Optional.of(company));
        when(cisIotCompanyDataConverter.convert(company)).thenReturn(companyData);

        when(currencyEur.getIsocode()).thenReturn("EUR");
        when(currencyEur.getSymbol()).thenReturn("€");
        when(currencyUsd.getIsocode()).thenReturn("USD");
        when(currencyUsd.getSymbol()).thenReturn("$");

        when(currencyModelEur.getIsocode()).thenReturn("EUR");
        when(currencyModelUsd.getIsocode()).thenReturn("USD");
        when(languageModelDe.getIsocode()).thenReturn("de");

        when(countryConverter.convert(countryModelDe)).thenReturn(countryDe);
        when(countryConverter.convert(countryModelNz)).thenReturn(countryNz);
        when(countryConverter.convertAll(List.of(countryModelDe, countryModelNz))).thenReturn(List.of(countryDe, countryNz));
        when(countryService.getActiveCountries()).thenReturn(List.of(countryModelDe, countryModelNz));

        when(currentUser.getName()).thenReturn(USER_NAME);

        when(languageDe.getIsocode()).thenReturn("de");
        when(languageEn.getIsocode()).thenReturn("en");
        when(configuration.getString("website.domain")).thenReturn("best-website-ever.com");

        when(company.getCountry()).thenReturn(countryModelDe);

        when(httpServletRequest.getContextPath()).thenReturn("/context/path");
        when(httpServletRequest.getSession(false)).thenReturn(httpSession);

        when(httpSession.getAttribute(CSRF_TOKEN_SESSION_ATTRIBUTE)).thenReturn(csrfToken);
        when(csrfToken.getToken()).thenReturn("awesome-token");

        when(httpServletResponse.getStatus()).thenReturn(200);

        when(userPermissionService.isAuthenticated()).thenReturn(true);

        when(springSecurityAdapter.getCurrentUser()).thenReturn(userPrincipal);
    }

    @Test
    public void populate() {
        CoreData actualCoreData = new CoreData();
        coreDataService.populateCommonCoreData(actualCoreData, httpServletRequest, httpServletResponse);

        assertThat(actualCoreData).isNotNull();

        assertThat(actualCoreData).isEqualToComparingFieldByField(getExpectedCoreData());
    }

    @Test
    public void populate_userServiceThrowsRuntimeException_noNamePopulated() {
        CoreData expectedCoreData = getExpectedCoreData().withUserName(null);
        when(userService.getCurrentUser()).thenReturn(null);

        CoreData actualCoreData = new CoreData();
        coreDataService.populateCommonCoreData(actualCoreData, httpServletRequest, httpServletResponse);

        assertThat(actualCoreData).isEqualToComparingFieldByField(expectedCoreData);
    }

    @Test
    public void populate_baseStoreIsNull_noBasestorePopulated() {
        CoreData expectedCoreData = getExpectedCoreData().withBasestore(null).withSupportUrl(null);
        when(baseStoreService.getCurrentBaseStore()).thenReturn(null);

        CoreData actualCoreData = new CoreData();
        coreDataService.populateCommonCoreData(actualCoreData, httpServletRequest, httpServletResponse);

        assertThat(actualCoreData).isEqualToComparingFieldByField(expectedCoreData);
    }

    @Test
    public void populate_sessionDoesNotExist_noCsrfTokenPopulated() {
        CoreData expectedCoreData = getExpectedCoreData().withCsrfToken(null);
        when(httpServletRequest.getSession(false)).thenReturn(null);

        CoreData actualCoreData = new CoreData();
        coreDataService.populateCommonCoreData(actualCoreData, httpServletRequest, httpServletResponse);

        assertThat(actualCoreData).isEqualToComparingFieldByField(expectedCoreData);
    }

    @Test
    public void populate_sessionHasNoCsrfToken_noCsrfTokenPopulated() {
        CoreData expectedCoreData = getExpectedCoreData().withCsrfToken(null);
        when(httpSession.getAttribute(CSRF_TOKEN_SESSION_ATTRIBUTE)).thenReturn(null);

        CoreData actualCoreData = new CoreData();
        coreDataService.populateCommonCoreData(actualCoreData, httpServletRequest, httpServletResponse);

        assertThat(actualCoreData).isEqualToComparingFieldByField(expectedCoreData);
    }

    @Test
    public void populate_csrfSessionAttributeHasIncorrectType_noCsrfTokenPopulated() {
        CoreData expectedCoreData = getExpectedCoreData().withCsrfToken(null);
        when(httpSession.getAttribute(CSRF_TOKEN_SESSION_ATTRIBUTE)).thenReturn("not a token");

        CoreData actualCoreData = new CoreData();
        coreDataService.populateCommonCoreData(actualCoreData, httpServletRequest, httpServletResponse);

        assertThat(actualCoreData).isEqualToComparingFieldByField(expectedCoreData);
    }

    @Test
    public void populate_noCurrentCompany_currentCountryNotPopulated() {
        CoreData expectedCoreData = getExpectedCoreData()
            .withCurrentCountry(null)
            .withCurrentCompany(null);
        when(iotCompanyService.getCurrentCompany()).thenReturn(Optional.empty());

        CoreData actualCoreData = new CoreData();
        coreDataService.populateCommonCoreData(actualCoreData, httpServletRequest, httpServletResponse);

        assertThat(actualCoreData).isEqualToComparingFieldByField(expectedCoreData);
    }

    @Test
    public void populate_currentUserIsAnonymous_displayNameIsNull() {
        when(currentUser.getUid()).thenReturn("anonymous");
        when(iotCompanyService.getCurrentCompany()).thenReturn(Optional.empty());

        CoreData actualCoreData = new CoreData();
        coreDataService.populateCommonCoreData(actualCoreData, httpServletRequest, httpServletResponse);

        assertThat(actualCoreData.getUserName()).isNull();
    }

    @Test
    public void givenUserIsNotAuthenticated_whenPopulate_thenSetActiveCountriesToEmpty() {
        when(userPermissionService.isAuthenticated()).thenReturn(false);

        CoreData actualCoreData = new CoreData();
        coreDataService.populateCommonCoreData(actualCoreData, httpServletRequest, httpServletResponse);

        assertThat(actualCoreData.getActiveCountries()).isEmpty();
    }

    @Test
    public void givenUserAuthenticatedWithoutSkid_whenPopulate_thenIdpIsNull() {
        CoreData actualCoreData = new CoreData();
        coreDataService.populateCommonCoreData(actualCoreData, httpServletRequest, httpServletResponse);

        assertThat(actualCoreData.getIdp()).isNull();
    }

    @Test
    public void givenUserAuthenticatedWithSkid_whenPopulate_thenIdpIsPopulated() {
        when(userPrincipal.getLoginIdp())
            .thenReturn("test-idp");
        when(userPrincipal.getLoginIdpAccountUrl())
            .thenReturn("http://example.com/account");

        CoreData actualCoreData = new CoreData();
        coreDataService.populateCommonCoreData(actualCoreData, httpServletRequest, httpServletResponse);

        assertThat(actualCoreData.getIdp())
            .isEqualToComparingFieldByField(new Idp()
                .withLoginIdp("test-idp")
                .withLoginIdpAccountUrl("http://example.com/account"));
    }

    private CoreData getExpectedCoreData() {
        return new CoreData()
            .withCurrentCountry(countryDe)
            .withBaseUrl("/context/path/")
            .withBasestore("aastore")
            .withCsrfToken("awesome-token")
            .withDefaultLanguage("de")
            .withCurrencySymbols(Map.of("EUR", "€", "USD", "$"))
            .withCurrentCurrency("USD")
            .withDomainUrl("best-website-ever.com")
            .withSupportUrl("/support")
            .withInfoUrl(INFO_URL)
            .withAccountsUrl(ACCOUNTS_URL)
            .withCorporateHome(CORPORATE_HOME)
            .withHttpStatus(200)
            .withModuleConfig(Map.of("ENABLE_STUFF", true, "ALLOW_NOTHING", true, "IMPROPER_NAME", true))
            .withActiveCountries(List.of(countryDe, countryNz))
            .withCurrentCompany(companyData)
            .withUserName(USER_NAME);
    }
}
