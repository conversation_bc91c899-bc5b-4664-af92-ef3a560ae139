package com.sast.cis.core.service.singlesignon;

import com.sast.cis.core.config.ump.UmpApiConfig;
import com.sast.cis.core.config.ump.UmpApiConfigResolver;
import com.sast.cis.core.constants.TenantEnum;
import com.sast.cis.core.data.UmpCompanyData;
import com.sast.cis.core.data.UmpDistributorData;
import com.sast.cis.core.data.UmpUserData;
import com.sast.cis.core.exceptions.CompanyNotFoundException;
import com.sast.cis.core.exceptions.IncompleteCompanyDataException;
import com.sast.cis.core.model.IoTCompanyModel;
import com.sast.cis.core.service.account.AccountService;
import com.sast.cis.core.validator.UmpDataValidator;
import de.hybris.bootstrap.annotations.UnitTest;
import de.hybris.platform.store.BaseStoreModel;
import de.hybris.platform.store.services.BaseStoreService;
import generated.com.sast.cis.core.model.IoTCompanyBuilder;
import generated.de.hybris.platform.store.BaseStoreBuilder;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.test.web.client.ExpectedCount;
import org.springframework.test.web.client.MockRestServiceServer;
import org.springframework.test.web.client.response.MockRestResponseCreators;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.RestTemplate;

import java.util.List;
import java.util.Set;

import static com.sast.cis.core.constants.BaseStoreEnum.AA;
import static com.sast.cis.test.utils.Country.AUSTRIA;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.http.MediaType.APPLICATION_JSON;
import static org.springframework.test.web.client.match.MockRestRequestMatchers.header;
import static org.springframework.test.web.client.match.MockRestRequestMatchers.requestTo;

@RunWith(MockitoJUnitRunner.Silent.class)
@UnitTest
public class UserDataQueryServiceUnitTest {
    private static final String COMPANY_UUID = "company-uuid";
    private static final String COMPANY_NAME = "axSolutions";
    private static final String LAST_NAME = "Installer";
    private static final String FIRST_NAME = "Tom";
    private static final String EMAIL = "<EMAIL>";
    private static final String UMP_BASE_URI = "https://sso-portal";
    private static final String USER_NAME = "some-uuid-1234-ffff";
    private static final String BPMD_ID = "BPMDID";
    public static final String X_TENANT_HEADER = "X-Tenant";
    public static final String AUTHORIZATION_TOKEN = "Basic Og==";

    private static final String USER_BODY = "{\n" +
        "    \"userName\": \"" + USER_NAME + "\",\n" +
        "    \"email\": \"" + EMAIL + "\",\n" +
        "    \"firstName\": \"" + FIRST_NAME + "\",\n" +
        "    \"lastName\": \"" + LAST_NAME + "\",\n" +
        "    \"companyName\": \"" + COMPANY_NAME + "\",\n" +
        "    \"companyId\": \"" + COMPANY_UUID + "\",\n" +
        "    \"activated\": true,\n" +
        "    \"isHardwarePartner\": true\n" +
        "}";

    private static final String MANAGERS_BODY = "[" + USER_BODY + "]";

    @Mock
    private UmpDataValidator umpDataValidator;

    @Mock
    private UmpApiConfigResolver umpApiConfigResolver;

    @Mock
    private BaseStoreService baseStoreService;

    @Mock
    private AccountService accountService;

    @InjectMocks
    private UserDataQueryService userDataQueryService;

    private MockRestServiceServer mockRestServiceServer;

    private IoTCompanyModel company;

    @Before
    public void setUp() {
        BaseStoreModel aaStore = BaseStoreBuilder.generate().withUid(AA.getBaseStoreUid()).buildMockInstance();
        company = IoTCompanyBuilder.generate().withUid(COMPANY_UUID).withName(COMPANY_NAME).withStore(aaStore).withBpmdId(BPMD_ID)
            .buildMockInstance();

        UmpApiConfig umpApiConfig = new UmpApiConfig(UMP_BASE_URI, "", "");
        when(umpApiConfigResolver.getConfigForBaseStore(AA.getBaseStoreUid())).thenReturn(umpApiConfig);

        when(baseStoreService.getCurrentBaseStore()).thenReturn(aaStore);
        when(baseStoreService.getBaseStoreForUid(AA.getBaseStoreUid())).thenReturn(aaStore);

        RestTemplate restTemplate = new RestTemplate();
        userDataQueryService.cisRestTemplate = restTemplate;
        mockRestServiceServer = MockRestServiceServer.createServer(restTemplate);
    }

    @Test
    public void requestCompanyManagers_queriesCorrectUrlAndConvertsCorrectly() {
        mockRestServiceServer.expect(ExpectedCount.once(), requestTo(UMP_BASE_URI + "/companies/" + COMPANY_UUID + "/managers"))
            .andRespond(MockRestResponseCreators.withSuccess(MANAGERS_BODY, APPLICATION_JSON));

        Set<UmpUserData> managers = userDataQueryService.requestCompanyManagers(company);
        assertThat(managers).hasSize(1);
        for (UmpUserData manager : managers) {
            assertThat(manager.getUserName()).isEqualTo(USER_NAME);
            assertThat(manager.getEmail()).isEqualTo(EMAIL);
            assertThat(manager.getFirstName()).isEqualTo(FIRST_NAME);
            assertThat(manager.getLastName()).isEqualTo(LAST_NAME);
            assertThat(manager.getCompanyId()).isEqualTo(COMPANY_UUID);
            assertThat(manager.getCompanyName()).isEqualTo(COMPANY_NAME);
            assertThat(manager.isActivated()).isTrue();
            assertThat(manager.isIsHardwarePartner()).isTrue();
        }
    }

    @Test
    public void requestCompanyManagers_managersNotFound() {
        mockRestServiceServer.expect(ExpectedCount.once(), requestTo(UMP_BASE_URI + "/companies/" + COMPANY_UUID + "/managers"))
            .andRespond(MockRestResponseCreators.withStatus(HttpStatus.INTERNAL_SERVER_ERROR));

        assertThatThrownBy(() -> userDataQueryService.requestCompanyManagers(company))
            .isInstanceOf(HttpServerErrorException.class);
    }

    @Test
    public void requestCompanyManagers_returns500() {
        mockRestServiceServer.expect(ExpectedCount.once(), requestTo(UMP_BASE_URI + "/companies/" + COMPANY_UUID + "/managers"))
            .andRespond(MockRestResponseCreators.withStatus(HttpStatus.INTERNAL_SERVER_ERROR));

        assertThatThrownBy(() -> userDataQueryService.requestCompanyManagers(company))
            .isInstanceOf(HttpServerErrorException.class);
    }

    @Test
    public void requestCompanyData_queriesCorrectUrlAndConvertsCorrectly() {
        mockRestServiceServer.expect(ExpectedCount.once(), requestTo(UMP_BASE_URI + "/companies/" + COMPANY_UUID))
            .andExpect(header(X_TENANT_HEADER, TenantEnum.TENANT_MA.getName()))
            .andExpect(header(HttpHeaders.AUTHORIZATION, AUTHORIZATION_TOKEN))
            .andRespond(MockRestResponseCreators.withSuccess(getCompanyBody(), APPLICATION_JSON));

        UmpCompanyData userData = userDataQueryService.requestCompanyData(company);

        assertThat(userData.getCompanyId()).isEqualTo(COMPANY_UUID);
        assertThat(userData.getCompanyName()).isEqualTo(COMPANY_NAME);
        assertThat(userData.isIsHardwarePartner()).isFalse();
    }

    @Test
    public void requestCompanyData_throwsIncompleteDataExceptionForEmptyResponse() {
        mockRestServiceServer.expect(ExpectedCount.once(), requestTo(UMP_BASE_URI + "/companies/" + COMPANY_UUID))
            .andRespond(MockRestResponseCreators.withSuccess("", APPLICATION_JSON));

        assertThatThrownBy(() -> userDataQueryService.requestCompanyData(company))
            .isInstanceOf(IncompleteCompanyDataException.class)
            .hasFieldOrPropertyWithValue("companyId", COMPANY_UUID);
    }

    @Test
    public void requestCompanyData_throwsCompanyNotFoundExceptionForStatus404() {
        mockRestServiceServer.expect(ExpectedCount.once(), requestTo(UMP_BASE_URI + "/companies/" + COMPANY_UUID))
            .andRespond(MockRestResponseCreators.withStatus(HttpStatus.NOT_FOUND));

        assertThatThrownBy(() -> userDataQueryService.requestCompanyData(company))
            .isInstanceOf(CompanyNotFoundException.class)
            .hasFieldOrPropertyWithValue("companyId", COMPANY_UUID);
        verify(accountService).deactivateCompany(COMPANY_UUID);
    }

    @Test
    public void requestCompanyData_throwsCompanyNotFoundExceptionForStatus500() {
        mockRestServiceServer.expect(ExpectedCount.once(), requestTo(UMP_BASE_URI + "/companies/" + COMPANY_UUID))
            .andRespond(MockRestResponseCreators.withStatus(HttpStatus.INTERNAL_SERVER_ERROR));

        assertThatThrownBy(() -> userDataQueryService.requestCompanyData(company))
            .isInstanceOf(HttpServerErrorException.class);
    }

    @Test
    public void whenRequestDistributorsByCountry_thenQueryEndpointAndConvert() {
        mockRestServiceServer
            .expect(ExpectedCount.once(), requestTo(UMP_BASE_URI + "/distributors/countries/" + AUSTRIA.getIsocode()))
            .andRespond(MockRestResponseCreators.withSuccess(getDistributorsResponse(), APPLICATION_JSON));

        final List<UmpDistributorData> distributors = userDataQueryService.requestDistributorByCountry(AUSTRIA.getIsocode());
        assertThat(distributors).hasSize(2);

        assertThat(distributors).usingFieldByFieldElementComparator().containsExactlyInAnyOrder(
            new UmpDistributorData()
                .withId("12f25634-4bdf-42c9-8cbd-e97c9b26cce7")
                .withExternalCustomerId("25965721")
                .withName("Test Distributor"),
            new UmpDistributorData()
                .withId("69aaafc9-04a7-4f66-83c9-016a5e2a8994")
                .withExternalCustomerId("25585740")
                .withName("Austria Distributor 1")
        );
    }

    @Test
    public void givenDistributorsWithNoExternalCustomerId_whenRequestDistributorsByCountry_thenConvert() {
        mockRestServiceServer
            .expect(ExpectedCount.once(), requestTo(UMP_BASE_URI + "/distributors/countries/" + AUSTRIA.getIsocode()))
            .andRespond(MockRestResponseCreators.withSuccess(getDistributorsWithNoExternalCustomerIdResponse(), APPLICATION_JSON));

        final List<UmpDistributorData> distributors = userDataQueryService.requestDistributorByCountry(AUSTRIA.getIsocode());
        assertThat(distributors).hasSize(2);

        assertThat(distributors).usingFieldByFieldElementComparator().containsExactlyInAnyOrder(
            new UmpDistributorData().withId("eee8383a-efe4-4966-8bc0-0f13c8d87692").withName("Austria Distributor 2"),
            new UmpDistributorData().withId("96c12c0b-9296-43a2-ab78-257e94913063").withName("Austria Distributor 3")
        );
    }

    private String getCompanyBody() {
        return "{\n" +
            "    \"companyId\": \"" + COMPANY_UUID + "\",\n" +
            "    \"companyName\": \"" + COMPANY_NAME + "\",\n" +
            "    \"companyPhone\": null,\n" +
            "    \"bpmdId\": \"" + BPMD_ID + "\",\n" +
            "    \"isHardwarePartner\": false\n" +
            "}";
    }

    private String getDistributorsResponse() {
        return """
             [
                 {
                     "id": "12f25634-4bdf-42c9-8cbd-e97c9b26cce7",
                     "name": "Test Distributor",
                     "externalCustomerId": "25965721"
                   },
                   {
                     "id": "69aaafc9-04a7-4f66-83c9-016a5e2a8994",
                     "name": "Austria Distributor 1",
                     "externalCustomerId": "25585740"
                   }
            ]
             """;
    }

    private String getDistributorsWithNoExternalCustomerIdResponse() {
        return """
             [
                   {
                     "id": "eee8383a-efe4-4966-8bc0-0f13c8d87692",
                     "name": "Austria Distributor 2",
                     "externalCustomerId": null
                   },
                   {
                     "id": "96c12c0b-9296-43a2-ab78-257e94913063",
                     "name": "Austria Distributor 3"
                   }
            ]
             """;
    }
}
