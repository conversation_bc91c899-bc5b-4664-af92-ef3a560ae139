package com.sast.cis.test.utils;

import de.hybris.platform.util.logging.HybrisLogListener;
import de.hybris.platform.util.logging.HybrisLoggingEvent;
import org.apache.log4j.Level;

import java.util.ArrayList;
import java.util.List;
import java.util.Queue;
import java.util.concurrent.ConcurrentLinkedQueue;

public class TestLogListener implements HybrisLogListener {
    private final Queue<HybrisLoggingEvent> log = new ConcurrentLinkedQueue<>();

    @Override
    public boolean isEnabledFor(Level level) {
        return true;
    }

    @Override
    public void log(HybrisLoggingEvent hybrisLoggingEvent) {
        log.add(hybrisLoggingEvent);
    }

    public List<HybrisLoggingEvent> getLog() {
        return new ArrayList<>(log);
    }
}

