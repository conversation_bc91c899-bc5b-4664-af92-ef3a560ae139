package com.sast.cis.core.privateoffer.populator;

import com.sast.cis.core.data.PrivateOfferProjectRegistrationData;
import com.sast.cis.core.data.PrivateOfferRequestData;
import com.sast.cis.core.data.PrivateOfferRequestItemData;
import com.sast.cis.core.model.AppModel;
import com.sast.cis.core.model.IoTCompanyModel;
import com.sast.cis.core.model.PrivateOfferProjectRegistrationModel;
import com.sast.cis.core.model.PrivateOfferRequestItemModel;
import com.sast.cis.core.model.PrivateOfferRequestModel;
import com.sast.cis.core.service.AppService;
import com.sast.cis.core.service.company.IotCompanyService;
import de.hybris.bootstrap.annotations.UnitTest;
import de.hybris.platform.servicelayer.dto.converter.Converter;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.when;

@UnitTest
@RunWith(MockitoJUnitRunner.Silent.class)
public class PrivateOfferRequestModelPopulatorUnitTest {

    private static final String ANY_APP_CODE = "A_1XX1100";
    @Mock
    private AppService appService;

    @Mock
    private IotCompanyService iotCompanyService;

    @Mock
    private Converter<PrivateOfferRequestItemData, PrivateOfferRequestItemModel> privateOfferRequestItemDataToModelConverter;

    @Mock
    private Converter<PrivateOfferProjectRegistrationData, PrivateOfferProjectRegistrationModel> privateOfferProjectRegistrationDataToModelConverter;

    private PrivateOfferRequestModelPopulator populator;

    @Mock
    private PrivateOfferProjectRegistrationModel registrationModel;

    @Mock
    private AppModel appModel;

    @Mock
    private IoTCompanyModel companyModel;

    @Mock
    private PrivateOfferRequestData privateOfferRequestData;

    @Mock
    private PrivateOfferProjectRegistrationData projectRegistrationData;

    private final PrivateOfferRequestModel privateOfferRequestModel = new PrivateOfferRequestModel();

    @Before
    public void setUp() {
        populator = new PrivateOfferRequestModelPopulator(
            appService,
            iotCompanyService,
            privateOfferRequestItemDataToModelConverter,
            privateOfferProjectRegistrationDataToModelConverter
        );
        when(privateOfferRequestData.getAppCode()).thenReturn(ANY_APP_CODE);
        when(appService.getOnlineAppForCodeOrThrow(anyString())).thenReturn(appModel);
        when(iotCompanyService.getCurrentCompanyOrThrow()).thenReturn(companyModel);

        when(privateOfferProjectRegistrationDataToModelConverter.convert(projectRegistrationData)).thenReturn(registrationModel);

        when(privateOfferRequestData.getProjectRegistration()).thenReturn(projectRegistrationData);
        when(privateOfferRequestData.isRegisterProject()).thenReturn(true);
    }

    @Test
    public void givenRegisterProjectFalse_whenPopulate_thenDoNotPopulateRegistrationModel() {
        when(privateOfferRequestData.isRegisterProject()).thenReturn(false);

        populator.populate(privateOfferRequestData, privateOfferRequestModel);

        assertThat(privateOfferRequestModel.getProjectRegistration()).isNull();
    }

    @Test
    public void givenEmptyProjectRegistrationData_whenPopulate_thenPopulateRegistrationModel() {
        populator.populate(privateOfferRequestData, privateOfferRequestModel);

        assertThat(privateOfferRequestModel.getProjectRegistration()).isEqualTo(registrationModel);
    }

    @Test
    public void givenNullProjectRegistrationData_whenPopulate_thenPopulateRegistrationModel() {
        when(privateOfferProjectRegistrationDataToModelConverter.convert(null)).thenReturn(registrationModel);
        when(privateOfferRequestData.getProjectRegistration()).thenReturn(null);

        populator.populate(privateOfferRequestData, privateOfferRequestModel);

        assertThat(privateOfferRequestModel.getProjectRegistration()).isEqualTo(registrationModel);
    }
}