package com.sast.cis.core.paymentintegration.impl

import de.hybris.bootstrap.annotations.UnitTest
import de.hybris.platform.core.model.order.AbstractOrderModel
import de.hybris.platform.core.model.order.payment.PaymentInfoModel
import de.hybris.platform.payment.dto.TransactionStatus
import de.hybris.platform.payment.enums.PaymentTransactionType
import de.hybris.platform.payment.model.PaymentTransactionEntryModel
import de.hybris.platform.payment.model.PaymentTransactionModel
import org.junit.Test
import de.hybris.platform.testframework.JUnitPlatformSpecification
import spock.lang.Unroll

import java.time.ZonedDateTime

@UnitTest
class PaymentTransactionServiceImplUnitSpec extends JUnitPlatformSpecification {
    private static final Date OLDER_DATE = Date.from(ZonedDateTime.now().minusMinutes(2L).toInstant())
    private static final Date NEWER_DATE = Date.from(ZonedDateTime.now().minusMinutes(1L).toInstant())

    PaymentTransactionServiceImpl paymentTransactionService

    private AbstractOrderModel order = Mock(AbstractOrderModel)
    private PaymentInfoModel paymentInfo = Mock(PaymentInfoModel)

    private PaymentTransactionModel authorizedPaymentTransaction = Mock(PaymentTransactionModel)
    private PaymentTransactionModel otherPaymentTransaction = Mock(PaymentTransactionModel)
    private PaymentTransactionModel transferTransaction = Mock(PaymentTransactionModel)

    private PaymentTransactionEntryModel authorizationEntry = Mock(PaymentTransactionEntryModel)
    private PaymentTransactionEntryModel cancellationEntry = Mock(PaymentTransactionEntryModel)

    private PaymentTransactionEntryModel otherAuthorizationEntry = Mock(PaymentTransactionEntryModel)

    private PaymentTransactionEntryModel transferEntry = Mock(PaymentTransactionEntryModel)

    def setup() {
        paymentTransactionService = new PaymentTransactionServiceImpl()

        order.getPaymentInfo() >> paymentInfo
        paymentInfo.getCode() >> 'paymentInfoCode'

        authorizedPaymentTransaction.getType() >> PaymentTransactionType.AUTHORIZATION
        authorizedPaymentTransaction.getEntries() >> List.of(authorizationEntry)
        authorizedPaymentTransaction.getInfo() >> paymentInfo
        authorizedPaymentTransaction.getModifiedtime() >> NEWER_DATE
        authorizationEntry.getType() >> PaymentTransactionType.AUTHORIZATION
        authorizationEntry.getTransactionStatus() >> TransactionStatus.ACCEPTED.name()


        otherPaymentTransaction.getType() >> PaymentTransactionType.AUTHORIZATION
        otherPaymentTransaction.getEntries() >> List.of(otherAuthorizationEntry)
        otherPaymentTransaction.getInfo() >> paymentInfo
        otherPaymentTransaction.getModifiedtime() >> OLDER_DATE
        otherAuthorizationEntry.getType() >> PaymentTransactionType.AUTHORIZATION
        otherAuthorizationEntry.getTransactionStatus() >> TransactionStatus.ACCEPTED.name()


        cancellationEntry.getType() >> PaymentTransactionType.CANCEL

        transferTransaction.getType() >> PaymentTransactionType.TRANSFER
        transferTransaction.getEntries() >> List.of(transferEntry)
        transferEntry.getType() >> PaymentTransactionType.TRANSFER
    }

    @Test
    def 'getAuthorizedTransactionForCurrentPaymentInfo retrieves latest authorized payment transaction'() {
        given:
        order.getPaymentTransactions() >> List.of(otherPaymentTransaction, authorizedPaymentTransaction, transferTransaction)

        when:
        def actualTransaction = paymentTransactionService.getAuthorizedTransactionForCurrentPaymentInfo(order)

        then:
        actualTransaction == Optional.of(authorizedPaymentTransaction)
    }

    @Test
    def 'getAuthorizedTransactionForCurrentPaymentInfo returns empty if payment info does not match successfully authorized transaction'() {
        given:
        def differentPaymentInfo = Mock(PaymentInfoModel)
        differentPaymentInfo.getCode() >> 'somethingElse'
        order.getPaymentTransactions() >> List.of(otherPaymentTransaction, authorizedPaymentTransaction, transferTransaction)

        when:
        def actualTransaction = paymentTransactionService.getAuthorizedTransactionForCurrentPaymentInfo(order)

        then:
        order.getPaymentInfo() >> differentPaymentInfo
        actualTransaction == Optional.empty()
    }

    @Test
    @Unroll
    def 'getAuthorizedTransactionForCurrentPaymentInfo treats transaction status #givenTransactionStatus as unsuccessful'() {
        given:
        order.getPaymentTransactions() >> List.of(otherPaymentTransaction, authorizedPaymentTransaction, transferTransaction)

        when:
        def actualTransaction = paymentTransactionService.getAuthorizedTransactionForCurrentPaymentInfo(order)

        then:
        authorizationEntry.getTransactionStatus() >> givenTransactionStatus.name()
        actualTransaction == Optional.of(otherPaymentTransaction)

        where:
        givenTransactionStatus << [TransactionStatus.ERROR, TransactionStatus.REJECTED, TransactionStatus.PENDING_REVOCATION,
                                   TransactionStatus.REVOKED, TransactionStatus.REVIEW]
    }

    @Test
    def 'getAuthorizedTransactionForCurrentPaymentInfo returns empty if all payment transactions are cancelled'() {
        given:
        order.getPaymentTransactions() >> List.of(otherPaymentTransaction, authorizedPaymentTransaction, transferTransaction)

        when:
        def actualTransaction = paymentTransactionService.getAuthorizedTransactionForCurrentPaymentInfo(order)

        then:
        authorizedPaymentTransaction.getEntries() >> List.of(authorizationEntry, cancellationEntry)
        otherPaymentTransaction.getEntries() >> List.of(otherAuthorizationEntry, cancellationEntry)
        actualTransaction == Optional.empty()
    }

    @Test
    def 'getAuthorizedTransactionForCurrentPaymentInfo ignores cancelled transactions'() {
        given:
        order.getPaymentTransactions() >> List.of(otherPaymentTransaction, authorizedPaymentTransaction, transferTransaction)

        when:
        def actualTransaction = paymentTransactionService.getAuthorizedTransactionForCurrentPaymentInfo(order)

        then:
        authorizedPaymentTransaction.getEntries() >> List.of(authorizationEntry, cancellationEntry)
        otherPaymentTransaction.getEntries() >> List.of(otherAuthorizationEntry)
        actualTransaction == Optional.of(otherPaymentTransaction)
    }

    @Test
    def 'getAuthorizedTransactionForCurrentPaymentInfo handles orders without transaction gracefully'() {
        given:
        order.getPaymentTransactions() >> List.of()

        when:
        def actualTransaction = paymentTransactionService.getAuthorizedTransactionForCurrentPaymentInfo(order)

        then:
        actualTransaction == Optional.empty()
    }

    @Test
    def 'getAuthorizedTransactionForCurrentPaymentInfo throws exception for order without paymentInfo'() {
        when:
        paymentTransactionService.getAuthorizedTransactionForCurrentPaymentInfo(order)

        then:
        order.getPaymentInfo() >> null
        thrown(IllegalArgumentException)
    }

    @Test
    def 'getAuthorizedTransactionForCurrentPaymentInfo throws exception null order'() {
        when:
        paymentTransactionService.getAuthorizedTransactionForCurrentPaymentInfo(null)

        then:
        thrown(IllegalArgumentException)
    }
}
