package com.sast.cis.core.billingintegration.events;

import com.sast.cis.core.billingintegration.dto.ProductExportData;
import de.hybris.bootstrap.annotations.UnitTest;
import de.hybris.platform.servicelayer.event.PublishEventContext;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;

import static org.assertj.core.api.Assertions.assertThat;

@UnitTest
public class ProductCreateResponseEventUnitTest {

    @Rule
    public ExpectedException exceptionRule = ExpectedException.none();

    private ProductExportData exportData = ProductExportData.builder().build();

    @Test
    public void canPublish_contextHasNotBeenProvided_throwsException() {
        ProductCreateResponseEvent event = new ProductCreateResponseEvent(exportData);

        exceptionRule.expect(NullPointerException.class);

        event.canPublish(null);
    }

    @Test
    public void canPublish_contextHasSameSourceAndTargetNodes_returnsTrue() {
        ProductCreateResponseEvent event = new ProductCreateResponseEvent(exportData);
        PublishEventContext context = PublishEventContext.newBuilder().withSourceNodeId(1).withTargetNodeId(1).build();

        assertThat(event.canPublish(context)).isTrue();
    }

    @Test
    public void canPublish_contextHasDifferentSourceAndTargetNodes_returnsFalse() {
        ProductCreateResponseEvent event = new ProductCreateResponseEvent(exportData);
        PublishEventContext context = PublishEventContext.newBuilder().withSourceNodeId(1).withTargetNodeId(2).build();

        assertThat(event.canPublish(context)).isFalse();
    }

}