package com.sast.cis.core.converter.company;

import com.sast.cis.companyprofile.enums.CompanyProfileStatus;
import com.sast.cis.companyprofile.model.CompanyProfileModel;
import com.sast.cis.core.data.IotCompanyData;
import com.sast.cis.core.model.AaDistributorCompanyModel;
import com.sast.cis.core.model.IoTCompanyModel;
import com.sast.cis.core.resolver.CompanyProfileUrlResolver;
import de.hybris.bootstrap.annotations.UnitTest;
import de.hybris.platform.core.model.c2l.CountryModel;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static com.sast.cis.core.enums.CompanyApprovalStatus.APPROVED_COMMERCIAL;
import static com.sast.cis.core.enums.CompanyApprovalStatus.APPROVED_NON_COMMERCIAL;
import static com.sast.cis.core.enums.CompanyApprovalStatus.UNAPPROVED;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@UnitTest
@RunWith(MockitoJUnitRunner.Silent.class)
public class CisIotCompanyDataPopulatorUnitTest {

    @Mock
    private CompanyProfileUrlResolver companyProfileUrlResolver;

    @InjectMocks
    private CisIotCompanyDataPopulator companyDataPopulator;

    @Mock
    private IoTCompanyModel company;

    @Mock
    private CompanyProfileModel companyProfile;

    @Mock
    private CountryModel companyCountry;

    @Mock
    private AaDistributorCompanyModel aaDistributorCompany;

    private final String companyUid = "uid";
    private final String companyName = "company_name";
    private final String companyFriendlyName = "friendly_name";
    private final String companyCountryIso = "company_country";
    private final String companyProfileUrl = "companyProfileUrl";
    private static final String DISTRIBUTOR_ID = "foobarbaz1234";
    private static final String DISTRIBUTOR_NAME = "Super duper company name GmbH";

    @Before
    public void setUp() {
        when(company.getUid()).thenReturn(companyUid);
        when(company.getName()).thenReturn(companyName);
        when(company.getFriendlyName()).thenReturn(companyFriendlyName);
        when(company.getCompanyProfile()).thenReturn(companyProfile);
        when(company.getCountry()).thenReturn(companyCountry);
        when(company.getApprovalStatus()).thenReturn(APPROVED_COMMERCIAL);

        when(companyCountry.getIsocode()).thenReturn(companyCountryIso);

        when(companyProfile.getStatus()).thenReturn(CompanyProfileStatus.PUBLISHED);

        when(companyProfileUrlResolver.resolve(companyProfile)).thenReturn(companyProfileUrl);

        when(aaDistributorCompany.getUmpId()).thenReturn(DISTRIBUTOR_ID);
        when(aaDistributorCompany.getCompanyName()).thenReturn(DISTRIBUTOR_NAME);
        when(company.getDefaultAaDistributor()).thenReturn(aaDistributorCompany);
    }

    @Test
    public void testPopulate() {
        final IotCompanyData companyData = new IotCompanyData();

        companyDataPopulator.populate(company, companyData);

        assertThat(companyData.getName()).isEqualTo(companyName);
        assertThat(companyData.getCompanyUid()).isEqualTo(companyUid);
        assertThat(companyData.getFriendlyName()).isEqualTo(companyFriendlyName);
        assertThat(companyData.getProfileUrl()).isEqualTo(companyProfileUrl);
        assertThat(companyData.getCompanyCountry()).isEqualTo(companyCountryIso);
        assertThat(companyData.isCompanyApproved()).isTrue();
        assertThat(companyData.isManagedAccount()).isFalse();
    }

    @Test
    public void givenCompanyHasNoProfile_whenPopulate_thenSetHasPublishedProfileToFalse() {
        when(company.getCompanyProfile()).thenReturn(null);

        final IotCompanyData companyData = new IotCompanyData();
        companyDataPopulator.populate(company, companyData);

        assertThat(companyData.isHasPublishedProfile()).isFalse();
    }

    @Test
    public void givenCompanyHasDraftProfile_whenPopulate_thenSetHasPublishedProfileToFalse() {
        when(companyProfile.getStatus()).thenReturn(CompanyProfileStatus.DRAFT);

        final IotCompanyData companyData = new IotCompanyData();
        companyDataPopulator.populate(company, companyData);

        assertThat(companyData.isHasPublishedProfile()).isFalse();
    }

    @Test
    public void givenCompanyHasPublishedProfile_whenPopulate_thenSetHasPublishedProfileToTrue() {
        when(companyProfile.getStatus()).thenReturn(CompanyProfileStatus.PUBLISHED);

        final IotCompanyData companyData = new IotCompanyData();
        companyDataPopulator.populate(company, companyData);

        assertThat(companyData.isHasPublishedProfile()).isTrue();
    }

    @Test
    public void givenCompanyWithApprovalStatusUnapproved_whenPopulate_thenSetApprovedToFalse() {
        when(company.getApprovalStatus()).thenReturn(UNAPPROVED);

        final IotCompanyData companyData = new IotCompanyData();
        companyDataPopulator.populate(company, companyData);

        assertThat(companyData.isCompanyApproved()).isFalse();
    }

    @Test
    public void givenCompanyWithApprovalStatusApprovedNonCommercial_whenPopulate_thenSetApprovedToFalse() {
        when(company.getApprovalStatus()).thenReturn(APPROVED_NON_COMMERCIAL);

        final IotCompanyData companyData = new IotCompanyData();
        companyDataPopulator.populate(company, companyData);

        assertThat(companyData.isCompanyApproved()).isFalse();
    }

    @Test
    public void populate_givenCompanyWithNoDistributor_doesnotPopulateDistributor() {
        when(company.getDefaultAaDistributor()).thenReturn(null);

        final IotCompanyData companyData = new IotCompanyData();
        companyDataPopulator.populate(company, companyData);

        assertThat(companyData.getDistributor()).isNull();
    }

    @Test
    public void populate_givenCompanyWithDistributor_populatesDistributor() {
        final IotCompanyData companyData = new IotCompanyData();
        companyDataPopulator.populate(company, companyData);

        assertThat(companyData.getDistributor()).isNotNull();
        assertThat(companyData.getDistributor().getId()).isEqualTo(DISTRIBUTOR_ID);
        assertThat(companyData.getDistributor().getName()).isEqualTo(DISTRIBUTOR_NAME);
    }
}
