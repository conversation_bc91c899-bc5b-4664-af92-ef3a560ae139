package com.sast.cis.test.utils;

import de.hybris.platform.servicelayer.event.events.AbstractEvent;
import de.hybris.platform.servicelayer.event.impl.AbstractEventListener;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.CompletableFuture;

@Slf4j
public abstract class
SingleHybrisEventTestListener<T extends AbstractEvent> extends AbstractEventListener<T> {
    private CompletableFuture<T> receivedEvent;

    public SingleHybrisEventTestListener() {
        receivedEvent = new CompletableFuture<>().newIncompleteFuture();
    }

    public void reset() {
        if(!receivedEvent.isDone()) {
            receivedEvent.cancel(true);
        }
        receivedEvent = new CompletableFuture<>().newIncompleteFuture();
    }

    public CompletableFuture<T> fetchEvent() {
        return receivedEvent;
    }

    @Override
    protected void onEvent(T event) {
        LOG.info(event.toString());
        receivedEvent.complete(event);
    }
}
