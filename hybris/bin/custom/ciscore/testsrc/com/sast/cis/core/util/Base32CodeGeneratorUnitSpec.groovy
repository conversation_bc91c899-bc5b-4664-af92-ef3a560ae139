package com.sast.cis.core.util

import de.hybris.bootstrap.annotations.UnitTest
import de.hybris.platform.testframework.JUnitPlatformSpecification
import org.junit.Test
import spock.lang.Unroll


@UnitTest
class Base32CodeGeneratorUnitSpec extends JUnitPlatformSpecification {
    @Test
    void 'code with given prefix and expected length is generated'() {
        given:
        def givenPrefix = 'HERBERT'

        when:

        def actualCode = Base32CodeGenerator.generateCode(givenPrefix)

        then:
        actualCode ==~ /${givenPrefix}[A-Z0-9]{24}/
    }

    @Test
    @Unroll
    void 'generateCode() throws if given prefix is #givenPrefix'() {
        when:
        Base32CodeGenerator.generateCode(givenPrefix)

        then:
        thrown(IllegalArgumentException)

        where:
        givenPrefix << [null, '', ' ', '\t\n', 'jkasd\nkjhds', 'öäü']
    }

    @Test
    @Unroll
    void 'isValidPrefix() for given prefix="#givenPrefix" returns #expectedValid'() {
        when:
        def actualValid = Base32CodeGenerator.isValidPrefix(givenPrefix)

        then:
        actualValid == expectedValid

        where:
        givenPrefix  || expectedValid
        null         || false
        ''           || false
        ' '          || false
        'a\t\nf'     || false
        'öäü'        || false
        'HERBERT123' || true
    }
}
