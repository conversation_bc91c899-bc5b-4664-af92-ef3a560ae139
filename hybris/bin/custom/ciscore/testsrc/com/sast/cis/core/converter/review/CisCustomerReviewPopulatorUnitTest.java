package com.sast.cis.core.converter.review;

import com.sast.cis.core.model.IntegratorModel;
import com.sast.cis.core.model.IoTCompanyModel;
import de.hybris.bootstrap.annotations.UnitTest;
import de.hybris.platform.commercefacades.product.data.ReviewData;
import de.hybris.platform.customerreview.model.CustomerReviewModel;
import generated.com.sast.cis.core.model.IntegratorBuilder;
import generated.com.sast.cis.core.model.IoTCompanyBuilder;
import generated.de.hybris.platform.customerreview.model.CustomerReviewBuilder;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import java.sql.Date;
import java.time.LocalDate;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@UnitTest
@RunWith(MockitoJUnitRunner.Silent.class)
public class CisCustomerReviewPopulatorUnitTest {

    private static final double RATING = 4.0;
    private static final Date DATE = Date.valueOf(LocalDate.of(2019, 1, 1));
    private static final String COMPANY_NAME = "Test company";
    private static final String INTEGRATOR_NAME = "Test name";

    @InjectMocks
    private CisCustomerReviewPopulator cisCustomerReviewPopulator;

    private IntegratorModel integrator;

    @Before
    public void setUp() {
        IoTCompanyModel company = IoTCompanyBuilder.generate()
            .withName(COMPANY_NAME)
            .buildMockInstance();

        integrator = IntegratorBuilder.generate()
            .withCompany(company)
            .withName(INTEGRATOR_NAME)
            .buildMockInstance();
    }

    @Test
    public void test_populate_withAlias_aliasIsSet() {
        when(integrator.getDisplayName()).thenReturn(INTEGRATOR_NAME);
        CustomerReviewModel review = CustomerReviewBuilder.generate()
            .withRating(RATING)
            .withCreationtime(DATE)
            .withUser(integrator)
            .withShowCompany(true)
            .withShowName(true)
            .buildMockInstance();

        ReviewData reviewData = new ReviewData();
        cisCustomerReviewPopulator.populate(review, reviewData);

        assertThat(reviewData.getRating()).isEqualTo(RATING);
        assertThat(reviewData.getDate()).isEqualTo(DATE);
        assertThat(reviewData.isShowCompany()).isTrue();
        assertThat(reviewData.isShowName()).isTrue();
        assertThat(reviewData.getCompany()).isEqualTo(COMPANY_NAME);
        assertThat(reviewData.getAlias()).isEqualTo(INTEGRATOR_NAME);
    }

    @Test
    public void test_populate_withNoUser_aliasAndCompanyAreNotSet() {
        CustomerReviewModel review = CustomerReviewBuilder.generate()
            .withRating(RATING)
            .withCreationtime(DATE)
            .withShowCompany(true)
            .withShowName(true)
            .buildMockInstance();

        ReviewData reviewData = new ReviewData();
        cisCustomerReviewPopulator.populate(review, reviewData);

        assertThat(reviewData.getRating()).isEqualTo(RATING);
        assertThat(reviewData.getDate()).isEqualTo(DATE);
        assertThat(reviewData.isShowCompany()).isFalse();
        assertThat(reviewData.isShowName()).isFalse();
        assertThat(reviewData.getCompany()).isNull();
        assertThat(reviewData.getAlias()).isNull();
    }
}
