package com.sast.cis.core.validator.review;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableSet;
import com.sast.cis.core.enums.LicenseType;
import com.sast.cis.core.model.AppModel;
import com.sast.cis.core.model.IntegratorModel;
import com.sast.cis.core.model.IoTCompanyModel;
import com.sast.cis.core.service.AppLicenseService;
import com.sast.cis.core.service.AppService;
import com.sast.cis.core.service.company.IotCompanyService;
import de.hybris.bootstrap.annotations.UnitTest;
import de.hybris.platform.customerreview.model.CustomerReviewModel;
import de.hybris.platform.product.ProductService;
import de.hybris.platform.servicelayer.user.UserService;
import de.hybris.platform.variants.model.VariantProductModel;
import generated.com.sast.cis.core.model.AppBuilder;
import generated.com.sast.cis.core.model.IntegratorBuilder;
import generated.com.sast.cis.core.model.IoTCompanyBuilder;
import generated.de.hybris.platform.customerreview.model.CustomerReviewBuilder;
import generated.de.hybris.platform.variants.model.VariantProductBuilder;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.AbstractMap.SimpleImmutableEntry;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Optional;
import java.util.Set;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Matchers.any;
import static org.mockito.Mockito.when;

@UnitTest
@RunWith(MockitoJUnitRunner.Silent.class)
public class ReviewAllowedValidatorUnitTest {

    private static final Entry<String, String> APP_NOT_PURCHASED_ERROR = new SimpleImmutableEntry<>("review", "review.appNotOwned");
    private static final Entry<String, String> APP_ALREADY_REVIEWED = new SimpleImmutableEntry<>("review", "review.exists");
    private static final String INTEGRATOR_UID = "someUid";
    private static final String SEPARATE_INTEGRATOR_UID = "anotherUid";

    @Mock
    private ProductService productService;

    @Mock
    private IotCompanyService iotCompanyService;

    @Mock
    private AppLicenseService appLicenseService;

    @Mock
    private AppService appService;

    @Mock
    private UserService userService;

    @InjectMocks
    private ReviewAllowedValidator reviewAllowedValidator;

    private IntegratorModel integrator;

    @Before
    public void setUp() {
        integrator = IntegratorBuilder.generate()
            .withUid(INTEGRATOR_UID)
            .buildMockInstance();
        when(userService.getCurrentUser()).thenReturn(integrator);
    }

    @Test
    public void companyDidNotPurchaseApp_validationFails() {
        prepareProductService();

        IoTCompanyModel company = IoTCompanyBuilder.generate().buildMockInstance();
        when(iotCompanyService.getCurrentCompany()).thenReturn(Optional.of(company));
        when(iotCompanyService.getCurrentCompanyOrThrow()).thenReturn(company);

        Map<String, String> errors = reviewAllowedValidator.validate("productCode");
        assertThat(errors).as("Expected errors").containsExactly(APP_NOT_PURCHASED_ERROR);
    }

    @Test
    public void companyOnlyTrialPurchaseApp_validationFails() {
        prepareProductService();

        IoTCompanyModel company = IoTCompanyBuilder.generate().buildMockInstance();
        when(iotCompanyService.getCurrentCompany()).thenReturn(Optional.of(company));
        when(iotCompanyService.getCurrentCompanyOrThrow()).thenReturn(company);
        when(appLicenseService.getPurchasedLicensesTypes(any(), any())).thenReturn(Set.of(LicenseType.EVALUATION));

        Map<String, String> errors = reviewAllowedValidator.validate("productCode");
        assertThat(errors).as("Expected errors").containsExactly(APP_NOT_PURCHASED_ERROR);
    }

    @Test
    public void notIotCustomer_validationFails() {
        prepareProductService();

        when(iotCompanyService.getCurrentCompany()).thenReturn(Optional.empty());

        Map<String, String> errors = reviewAllowedValidator.validate("productCode");
        assertThat(errors).as("Expected errors").containsExactly(APP_NOT_PURCHASED_ERROR);
    }

    @Test
    public void companyPurchasedApp_anotherUserAlreadyReviewed_validationSucceeds() {
        AppModel app = prepareProductService();

        IoTCompanyModel company = IoTCompanyBuilder.generate()
            .withPurchasedApps(ImmutableSet.of(app))
            .buildMockInstance();
        when(appLicenseService.getPurchasedLicensesTypes(any(), any())).thenReturn(Set.of(LicenseType.FULL));
        when(iotCompanyService.getCurrentCompany()).thenReturn(Optional.of(company));
        when(iotCompanyService.getCurrentCompanyOrThrow()).thenReturn(company);

        IntegratorModel separateIntegrator = IntegratorBuilder.generate()
            .withUid(SEPARATE_INTEGRATOR_UID)
            .buildMockInstance();
        CustomerReviewModel customerReview = CustomerReviewBuilder.generate()
            .withUser(separateIntegrator)
            .withProduct(app)
            .buildMockInstance();

        when(app.getProductReviews()).thenReturn(ImmutableList.of(customerReview));

        Map<String, String> errors = reviewAllowedValidator.validate("productCode");
        assertThat(errors).hasSize(0);
    }

    @Test
    public void companyPurchasedApp_userAlreadyReviewed_validationFails() {
        AppModel app = prepareProductService();

        IoTCompanyModel company = IoTCompanyBuilder.generate()
            .withPurchasedApps(ImmutableSet.of(app))
            .buildMockInstance();
        when(appLicenseService.getPurchasedLicensesTypes(any(), any())).thenReturn(Set.of(LicenseType.FULL));
        when(iotCompanyService.getCurrentCompany()).thenReturn(Optional.of(company));
        when(iotCompanyService.getCurrentCompanyOrThrow()).thenReturn(company);
        CustomerReviewModel customerReview = CustomerReviewBuilder.generate()
            .withUser(integrator)
            .withProduct(app)
            .buildMockInstance();

        when(integrator.getCustomerReviews()).thenReturn(ImmutableList.of(customerReview));

        Map<String, String> errors = reviewAllowedValidator.validate("productCode");
        assertThat(errors).containsExactly(APP_ALREADY_REVIEWED);
    }

    private AppModel prepareProductService() {
        AppModel app = AppBuilder.generate()
            .withCode("baseProductCode")
            .buildMockInstance();
        VariantProductModel appVersion = VariantProductBuilder.generate()
            .withBaseProduct(app)
            .withCode("productCode")
            .buildMockInstance();
        when(productService.getProductForCode(any())).thenReturn(appVersion);
        when(appService.getOnlineAppForCode("baseProductCode")).thenReturn(Optional.of(app));
        return app;
    }
}
