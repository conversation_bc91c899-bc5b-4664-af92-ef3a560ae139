package com.sast.cis.core.validator.storecontent;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.sast.cis.core.constants.CiscoreConstants;
import com.sast.cis.core.constants.devcon.DevconErrorCode;
import com.sast.cis.core.constants.devcon.DevconInputField;
import com.sast.cis.core.dao.IndustryDao;
import com.sast.cis.core.data.*;
import com.sast.cis.core.dto.DevconErrorMessage;
import com.sast.cis.core.enums.EulaType;
import com.sast.cis.core.model.AppDraftModel;
import com.sast.cis.core.model.IndustryModel;
import com.sast.cis.core.model.ProductContainerModel;
import com.sast.cis.core.model.UseCaseModel;
import com.sast.cis.core.service.ErrorMessageService;
import com.sast.cis.core.service.HtmlSanitizingService;
import com.sast.cis.core.service.ProductContainerService;
import com.sast.cis.core.service.UseCaseService;
import com.tngtech.java.junit.dataprovider.DataProvider;
import com.tngtech.java.junit.dataprovider.DataProviderRunner;
import de.hybris.bootstrap.annotations.UnitTest;
import de.hybris.platform.catalog.enums.ArticleApprovalStatus;
import de.hybris.platform.core.PK;
import de.hybris.platform.servicelayer.config.ConfigurationService;
import generated.com.sast.cis.core.model.AppDraftBuilder;
import generated.com.sast.cis.core.model.IndustryBuilder;
import generated.com.sast.cis.core.model.ProductContainerBuilder;
import generated.com.sast.cis.core.model.UseCaseBuilder;
import org.apache.commons.configuration.Configuration;
import org.apache.commons.lang3.StringUtils;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnit;
import org.mockito.junit.MockitoRule;

import java.util.*;

import static com.sast.cis.core.constants.devcon.DevconErrorCode.*;
import static com.sast.cis.test.utils.TestDataConstants.basicStoreContent;
import static de.hybris.platform.catalog.enums.ArticleApprovalStatus.DRAFT;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Matchers.*;
import static org.mockito.Mockito.*;

@UnitTest
@RunWith(DataProviderRunner.class)
public class DraftStoreContentValidatorUnitTest {
    private static final String LONG_STRING = StringUtils.repeat('x', 256);
    private static final String ALPHANUMERIC_LONG_STRING = "Alphanumeric1234Alphanumeric1234Alphanumeric1234";
    private static final String VERY_LONG_STRING = StringUtils.repeat('x', 65536);
    private static final String INVALID_EMAIL = "testmail.com";
    private static final String NON_EXISTENT_CONTAINER = "nonExistentContainer";
    private static final int LESS_THAN_MAX_LENGTH_CHANGELOG = 1999;

    @Rule
    public MockitoRule mockito = MockitoJUnit.rule();

    @Mock
    private ProductContainerService productContainerService;

    @Mock
    private ConfigurationService configurationService;

    @Mock
    private Configuration configuration;

    private ErrorMessageService errorMessageService = new ErrorMessageService();

    @Mock
    private IndustryDao industryDao;

    @Mock
    private UseCaseService useCaseService;

    @Mock
    private AppNameValidator appNameValidator;

    @Mock
    private AppVideoUrlValidator appVideoUrlValidator;

    @Mock
    private HtmlSanitizingService htmlSanitizingService;

    private DraftStoreContentValidator validator;
    private StoreContentData storeContentData;
    private ProductContainerModel productContainer;

    @Before
    public void prepareMocks() {
        storeContentData = basicStoreContent();
        productContainer = createProductContainer();
        IndustryModel industry = IndustryBuilder.generate().withName("Other").buildMockInstance();
        when(industry.getPk()).thenReturn(PK.fromLong(1L));

        UseCaseModel useCaseModel = UseCaseBuilder.generate().withName("Other").withEnabled(true).buildMockInstance();
        when(useCaseModel.getPk()).thenReturn(PK.fromLong(1L));

        when(configurationService.getConfiguration()).thenReturn(configuration);
        when(configuration.getInt(anyString(), anyInt())).thenReturn(2000);
        when(appNameValidator.validateAppName(any())).thenReturn(Collections.emptySet());
        when(productContainerService.getProductContainerForCode(storeContentData.getProductContainerCode()))
            .thenReturn(Optional.of(productContainer));
        when(industryDao.getAllEnabledIndustries()).thenReturn(ImmutableList.of(industry));
        when(useCaseService.getAllEnabledUseCases()).thenReturn(ImmutableList.of(useCaseModel));
        when(htmlSanitizingService.sanitizeInput(anyString())).thenAnswer(answer -> answer.getArguments()[0]);

        validator = new DraftStoreContentValidator(null,
            configurationService,
            errorMessageService,
            appNameValidator,
            productContainerService,
            industryDao,
            useCaseService,
            htmlSanitizingService,
            appVideoUrlValidator);
    }

    private ProductContainerModel createProductContainer() {
        AppDraftModel app = AppDraftBuilder.generate().withApprovalStatus(DRAFT).buildMockInstance();
        ProductContainerModel productContainer = ProductContainerBuilder.generate().withAppDraft(app).buildMockInstance();
        return productContainer;
    }

    @Test
    @DataProvider(value = {
        "null       | true",
        "DRAFT      | true",
        "UNAPPROVED | true",
        "CHECK      | false"
    }, splitBy = "\\|", trimValues = true)
    public void validate_shouldReturnErrorForWrongApprovalStatus(ArticleApprovalStatus approvalStatus, boolean valid) {
        AppDraftModel app = AppDraftBuilder.generate().withApprovalStatus(approvalStatus).buildMockInstance();
        when(productContainer.getAppDraft()).thenReturn(app);

        Set<DevconErrorMessage> response = validator.validate(storeContentData);

        assertThat(response).hasSize(valid ? 0 : 1);
        if (!valid) {
            DevconErrorMessage expectedError = new DevconErrorMessage()
                .withCode(DevconErrorCode.APP_VERSION_IN_APPROVAL);
            assertThat(response).contains(expectedError);
        }
    }

    @Test
    public void validate_shouldReturnValidationErrorForUnsupportedIndustry() {
        IndustryData industry = new IndustryData().withId(123L).withName("test");
        storeContentData.withIndustries(ImmutableList.of(industry));

        Set<DevconErrorMessage> response = validator.validate(storeContentData);

        assertThat(response).hasSize(1);
        DevconErrorMessage expectedError = new DevconErrorMessage()
            .withCode(DevconErrorCode.STORE_CONTENT_INDUSTRIES_NOT_SUPPORTED)
            .withField(DevconInputField.INDUSTRY)
            .withAdditionalProperty("test");
        assertThat(response).contains(expectedError);
    }

    @Test
    public void validate_shouldReturnValidationErrorForUnsupportedUseCase() {
        UseCaseData useCaseData = new UseCaseData().withId(100l).withName("Dummy");
        storeContentData.withUseCases(ImmutableList.of(useCaseData));

        Set<DevconErrorMessage> response = validator.validate(storeContentData);

        assertThat(response).hasSize(1);
        DevconErrorMessage expectedError = new DevconErrorMessage()
            .withCode(DevconErrorCode.STORE_CONTENT_USE_CASES_NOT_SUPPORTED)
            .withField(DevconInputField.USE_CASE)
            .withAdditionalProperty("Dummy");
        assertThat(response).contains(expectedError);
    }

    @Test
    public void validate_shouldReturnNoValidationErrorWhenUseCaseIsNotSpecified() {
        storeContentData.withUseCases(null);
        Set<DevconErrorMessage> response = validator.validate(storeContentData);

        assertThat(response).isEmpty();
    }

    @Test
    public void validate_validData_noErrorsReported() {
        Set<DevconErrorMessage> result = validator.validate(storeContentData);

        assertThat(result).isEmpty();
    }

    @Test
    public void validate_validDescription_noErrorsReported() {
        String longValidDescription = StringUtils.repeat('a', LESS_THAN_MAX_LENGTH_CHANGELOG) + "<strong>a</strong>";
        storeContentData.withDescriptionByIsocode(new HashMap<>() {{
            put("de", longValidDescription);
            put("en", longValidDescription);
        }});

        assertThat(validator.validate(storeContentData)).isEmpty();
    }

    @Test
    public void validate_tooLongData_errorsReported() {
        when(configuration.getInt(anyString(), anyInt())).thenReturn(0);
        storeContentData
            .withDescriptionByIsocode(ImmutableMap.of("de", VERY_LONG_STRING, "en", VERY_LONG_STRING))
            .withSummaryByIsocode(ImmutableMap.of("de", LONG_STRING, "en", LONG_STRING))
            .withNameByIsocode(ImmutableMap.of("de", "ALPHANUMERIC_LONG_STRING", "en", ALPHANUMERIC_LONG_STRING))
            .withEula(new EulaData().withType(EulaType.CUSTOM).withCustomUrl(LONG_STRING));

        Set<DevconErrorMessage> result = validator.validate(storeContentData);

        Set<DevconErrorMessage> expectedResult = new HashSet<>();
        expectedResult.add(new DevconErrorMessage().withCode(URL_TOO_LONG).withField(DevconInputField.PRODUCT_WEBSITE_URL));
        expectedResult.add(new DevconErrorMessage().withCode(URL_TOO_LONG).withField(DevconInputField.PRIVACY_POLICY_URL));
        expectedResult.add(new DevconErrorMessage().withCode(URL_TOO_LONG).withField(DevconInputField.TERMS_OF_USE_URL));
        expectedResult.add(new DevconErrorMessage().withCode(URL_TOO_LONG).withField(DevconInputField.SUPPORT_PAGE_URL));
        expectedResult.add(new DevconErrorMessage().withCode(DevconErrorCode.EMAIL_TOO_LONG).withField(DevconInputField.EMAIL_KEY));
        expectedResult.add(new DevconErrorMessage().withCode(DevconErrorCode.PHONE_NUMBER_TOO_LONG).withField(DevconInputField.PHONE));
        expectedResult.add(new DevconErrorMessage().withCode(DevconErrorCode.DESCRIPTION_TOO_LONG).withField(DevconInputField.DESCRIPTION));
        expectedResult.add(new DevconErrorMessage().withCode(DevconErrorCode.SUMMARY_TOO_LONG).withField(DevconInputField.SUMMARY));

        assertThat(result).containsOnlyElementsOf(expectedResult);
    }

    @Test
    public void validate_emptyDefaultName_shouldInvokeAppNameValidator() {
        storeContentData
            .withNameByIsocode(ImmutableMap.of("de", "", "en", ""));

        validator.validate(storeContentData);
        verify(appNameValidator, times(1)).validateAppName(storeContentData.getNameByIsocode());
    }

    @Test
    public void validate_invalidMailAddress_errorReported() {
        storeContentData.setEmailAddress(INVALID_EMAIL);

        Set<DevconErrorMessage> result = validator.validate(storeContentData);

        Set<DevconErrorMessage> expectedResult = new HashSet<>();
        expectedResult.add(new DevconErrorMessage().withCode(EMAIL_NOT_VALID).withField(DevconInputField.EMAIL_KEY));

        assertThat(result).containsAll(expectedResult);
    }

    @Test
    public void validate_fancyNewTld_isValid() {
        storeContentData.setEmailAddress("<EMAIL>");

        Set<DevconErrorMessage> result = validator.validate(storeContentData);

        assertThat(result).isEmpty();
    }

    @Test
    public void validate_wrongProductContainer_errorReported() {
        when(productContainerService.getProductContainerForCode(NON_EXISTENT_CONTAINER)).thenReturn(Optional.empty());
        storeContentData.setProductContainerCode(NON_EXISTENT_CONTAINER);

        assertThat(validator.validate(storeContentData)).contains(new DevconErrorMessage().withCode(PRODUCTCONTAINER_NOT_FOUND));
    }

    @Test
    public void validate_documentationFileNull_errorsReported() {
        storeContentData.withDocumentationFiles(null);

        Set<DevconErrorMessage> result = validator.validate(storeContentData);

        Set<DevconErrorMessage> expectedResult = new HashSet<>();
        expectedResult
            .add(new DevconErrorMessage().withCode(DevconErrorCode.DOCUMENTS_INTERNAL_ERROR).withField(DevconInputField.DOCUMENTS));

        assertThat(result).containsAll(expectedResult);
    }

    @Test
    public void validate_duplicateDocumentationFileName_errorsReported() {
        PdfData pdf1 = new PdfData().withCode("1").withDisplayName("name");
        PdfData pdf2 = new PdfData().withCode("2").withDisplayName("name");

        storeContentData.withDocumentationFiles(ImmutableList.of(pdf1, pdf2));

        Set<DevconErrorMessage> result = validator.validate(storeContentData);

        Set<DevconErrorMessage> expectedResult = new HashSet<>();
        expectedResult
            .add(new DevconErrorMessage().withCode(DevconErrorCode.DOCUMENTS_NAMES_NOT_UNIQUE).withField(DevconInputField.DOCUMENTS));

        assertThat(result).containsAll(expectedResult);
    }

    @Test
    public void validate_tooManyDocumentationFiles_errorsReported() {
        when(configuration.getInt(CiscoreConstants.NUMBER_OF_PDF_DOCUMENTS, 15)).thenReturn(2);
        PdfData pdf1 = new PdfData().withCode("1").withDisplayName("name1");
        PdfData pdf2 = new PdfData().withCode("2").withDisplayName("name2");
        PdfData pdf3 = new PdfData().withCode("3").withDisplayName("name3");

        storeContentData.withDocumentationFiles(ImmutableList.of(pdf1, pdf2, pdf3));

        Set<DevconErrorMessage> result = validator.validate(storeContentData);

        Set<DevconErrorMessage> expectedResult = new HashSet<>();
        expectedResult
            .add(new DevconErrorMessage().withCode(DevconErrorCode.DOCUMENTS_NUMBER_EXCEEDED).withField(DevconInputField.DOCUMENTS));

        assertThat(result).containsAll(expectedResult);
        configurationService.getConfiguration().setProperty(CiscoreConstants.NUMBER_OF_PDF_DOCUMENTS, 15);
    }

    @Test
    public void validate_shortDescriptionWithValidCharacters_returnsNoErrors() {
        storeContentData.setSummaryByIsocode(ImmutableMap.of("DE",
            "FLOW Traffic' turns your ca$#mera se%nsor* with built-i+=n deep/test video analytics. "
                + "Get the traffic statistics: (specific traffic events) for real' applications\"."));
        Set<DevconErrorMessage> result = validator.validate(storeContentData);
        assertThat(result.isEmpty()).isTrue();
    }
}
