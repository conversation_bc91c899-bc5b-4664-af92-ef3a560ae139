package com.sast.cis.core.validator.storecontent;

import com.sast.cis.core.constants.devcon.DevconErrorCode;
import com.sast.cis.core.constants.devcon.DevconInputField;
import com.sast.cis.core.dto.DevconErrorMessage;
import com.sast.cis.core.dto.youtube.*;
import com.sast.cis.core.exceptions.devcon.AppVideoParseException;
import com.sast.cis.core.model.AppVideoModel;
import com.sast.cis.core.service.AppVideoService;
import com.sast.cis.core.service.ErrorMessageService;
import com.sast.cis.core.service.YoutubeClientService;
import com.tngtech.java.junit.dataprovider.DataProviderRunner;
import de.hybris.bootstrap.annotations.UnitTest;
import generated.com.sast.cis.core.model.AppVideoBuilder;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnit;
import org.mockito.junit.MockitoRule;

import java.util.ArrayList;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Matchers.any;
import static org.mockito.Mockito.when;

@UnitTest
@RunWith(DataProviderRunner.class)
public class AppVideoUrlValidatorUnitTest {

    @Rule
    public MockitoRule mockito = MockitoJUnit.rule();

    @Mock
    private ErrorMessageService errorMessageService;

    @Mock
    private YoutubeClientService youtubeClientService;

    @Mock
    private AppVideoService appVideoService;

    private AppVideoUrlValidator appVideoUrlValidator;

    @Before
    public void prepareMocks() {
        when(errorMessageService.createErrorMessage(any(DevconErrorCode.class)))
            .thenAnswer(answer -> new DevconErrorMessage().withCode(answer.getArgument(0, DevconErrorCode.class)));
        when(errorMessageService.createErrorMessage(any(DevconErrorCode.class), any(DevconInputField.class)))
            .thenAnswer(answer -> new DevconErrorMessage()
                .withCode(answer.getArgument(0, DevconErrorCode.class))
                .withField(answer.getArgument(1, DevconInputField.class)));

        appVideoUrlValidator = new AppVideoUrlValidator(appVideoService, errorMessageService, youtubeClientService);
    }

    @Test
    public void validate_WrongUrl_fails() throws AppVideoParseException {
        when(appVideoService.parseToAppVideo(any())).thenThrow(new AppVideoParseException("boo"));

        assertThat(appVideoUrlValidator.validateAppVideoUrl("BLANK"))
            .contains(
                new DevconErrorMessage().withCode(DevconErrorCode.STORE_CONTENT_VIDEO_URL_FORMAT).withField(DevconInputField.VIDEO_URL));
    }

    @Test
    public void validate_notExistingVideo_fails() throws AppVideoParseException {
        final AppVideoModel blankVideo = AppVideoBuilder.generate()
            .withExternalId("123")
            .buildInstance();

        final YoutubeResponse response = getYoutubeResponse(0, null, null);

        when(appVideoService.parseToAppVideo(any())).thenReturn(blankVideo);
        when(youtubeClientService.getVideoById(any())).thenReturn(response);

        assertThat(appVideoUrlValidator.validateAppVideoUrl("BLANK"))
            .contains(new DevconErrorMessage().withCode(DevconErrorCode.STORE_CONTENT_INVALID_VIDEO).withField(DevconInputField.VIDEO_URL));
    }

    @Test
    public void validate_validUrl_success() throws AppVideoParseException {
        String videoId = "123";
        final AppVideoModel blankVideo = AppVideoBuilder.generate()
            .withExternalId(videoId)
            .buildInstance();

        final YoutubeResponse response = getYoutubeResponse(1, videoId, YoutubePrivacyStatus.PUBLIC);

        when(appVideoService.parseToAppVideo(any())).thenReturn(blankVideo);
        when(youtubeClientService.getVideoById(any())).thenReturn(response);

        assertThat(appVideoUrlValidator.validateAppVideoUrl(videoId)).isEmpty();
    }

    @Test
    public void validate_validUrl_privateVideo() throws AppVideoParseException {
        String videoId = "123";
        final AppVideoModel blankVideo = AppVideoBuilder.generate()
            .withExternalId(videoId)
            .buildInstance();

        final YoutubeResponse response = getYoutubeResponse(1, videoId, YoutubePrivacyStatus.PRIVATE);

        when(appVideoService.parseToAppVideo(any())).thenReturn(blankVideo);
        when(youtubeClientService.getVideoById(any())).thenReturn(response);
        assertThat(appVideoUrlValidator.validateAppVideoUrl(videoId))
        .contains(new DevconErrorMessage().withCode(DevconErrorCode.STORE_CONTENT_INVALID_VIDEO)
            .withField(DevconInputField.VIDEO_URL));
    }



    private YoutubeResponse getYoutubeResponse(int numberOfResults, String videoId, YoutubePrivacyStatus status) {
        final var response = new YoutubeResponse();
        response.setItems(new ArrayList<>());
        final var page = new YoutubePage();
        if (numberOfResults > 0) {
            YoutubeItem youtubeItem = new YoutubeItem();
            youtubeItem.setId(videoId);
            youtubeItem.setStatus(new YoutubeVideoStatus());
            youtubeItem.getStatus().setPrivacyStatus(status);
            response.getItems().add(youtubeItem);
        }
        page.setTotalResults(numberOfResults);
        response.setPageInfo(page);

        return response;
    }
}
