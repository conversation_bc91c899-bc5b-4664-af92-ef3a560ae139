package com.sast.cis.core.converter.appediting.availability;

import com.google.common.collect.ImmutableSet;
import com.sast.cis.core.data.LicenseAvailabilityData;
import com.sast.cis.core.enums.LicenseAvailabilityStatus;
import com.sast.cis.core.enums.LicenseType;
import com.sast.cis.core.model.AppLicenseDraftModel;
import com.sast.cis.core.model.CountriesAndPricesDraftModel;
import de.hybris.bootstrap.annotations.UnitTest;
import de.hybris.platform.core.model.c2l.CountryModel;
import de.hybris.platform.servicelayer.dto.converter.ConversionException;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.rules.ExpectedException.none;
import static org.mockito.Matchers.any;
import static org.mockito.Mockito.when;

@UnitTest
@RunWith(MockitoJUnitRunner.Silent.class)
public class AppLicenseDraft2LicenseAvailabilityDataPopulatorUnitTest {
    private static final Double PRICE_FULL_LICENSE = 200d;
    private static final Double PRICE_SUBSCRIPTION_LICENSE = 59d;

    @InjectMocks
    private AppLicenseDraft2LicenseAvailabilityDataPopulator appLicenseDraft2LicenseAvailabilityDataPopulator;

    @Rule
    public ExpectedException expectedException = none();

    @Mock
    private AppLicenseDraftModel appLicenseDraft;

    @Mock
    private CountriesAndPricesDraftModel countriesAndPricesDraft;

    @Mock
    private CountryModel country_de;

    @Mock
    private CountryModel country_us;

    private final LicenseAvailabilityData licenseAvailabilityData = new LicenseAvailabilityData();

    @Before
    public void setup() {
        when(appLicenseDraft.getLicenseType()).thenReturn(LicenseType.FULL);
        when(appLicenseDraft.getAvailabilityStatus()).thenReturn(LicenseAvailabilityStatus.PUBLISHED);
        when(appLicenseDraft.getEnabledCountries()).thenReturn(ImmutableSet.of(country_de, country_us));
        when(appLicenseDraft.getCountriesAndPricesDraft()).thenReturn(countriesAndPricesDraft);
        when(appLicenseDraft.getSpecifiedPrice()).thenReturn(PRICE_FULL_LICENSE);
    }

    @Test
    public void populate_when_no_licensedraft_given_throws_exception() {
        expectedException.expect(ConversionException.class);
        appLicenseDraft2LicenseAvailabilityDataPopulator.populate(null, licenseAvailabilityData);
    }

    @Test
    public void populate_when_no_licenseavailabilitydata_given_throws_exception() {
        expectedException.expect(ConversionException.class);
        appLicenseDraft2LicenseAvailabilityDataPopulator.populate(appLicenseDraft, null);
    }

    @Test
    public void populate_works_as_expected() {
        appLicenseDraft2LicenseAvailabilityDataPopulator.populate(appLicenseDraft, licenseAvailabilityData);
        assertThat(licenseAvailabilityData.getType()).isEqualTo(LicenseType.FULL);
        assertThat(licenseAvailabilityData.isEditable()).isTrue();
        assertThat(licenseAvailabilityData.isEnabled()).isTrue();
    }

    @Test
    public void givenSubscriptionLicenseWithPrice_whenPopulate_thenIsEditable() {
        when(appLicenseDraft.getLicenseType()).thenReturn(LicenseType.SUBSCRIPTION);
        when(appLicenseDraft.getSpecifiedPrice()).thenReturn(PRICE_SUBSCRIPTION_LICENSE);

        appLicenseDraft2LicenseAvailabilityDataPopulator.populate(appLicenseDraft, licenseAvailabilityData);

        assertThat(licenseAvailabilityData.isEditable()).isTrue();
    }
}