package com.sast.cis.core.category.populator;

import com.sast.cis.aa.core.category.data.MaterialDTO;
import com.sast.cis.aa.core.data.MaterialData;
import com.sast.cis.core.category.data.PriceDTO;
import com.sast.cis.core.category.data.ProductDTO;
import com.sast.cis.core.store.authz.StoreUserAuthorizationService;
import de.hybris.bootstrap.annotations.UnitTest;
import de.hybris.platform.commercefacades.product.data.PriceData;
import de.hybris.platform.commercefacades.product.data.ProductData;
import de.hybris.platform.servicelayer.dto.converter.Converter;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static java.util.List.of;
import static org.mockito.Mockito.*;

@UnitTest
@RunWith(MockitoJUnitRunner.class)
public class ProductDtoPopulatorUnitTest {
    private static final String CODE = "CODE";
    private static final String NAME = "NAME";
    private static final String DESCRIPTION = "DESCRIPTION";
    private static final String SUMMARY = "SUMMARY";
    private static final String PRODUCT_NOTE = "I am a note to a product";
    private static final int PRODUCT_ORDER = 1;

    @Mock
    private StoreUserAuthorizationService storeUserAuthorizationService;

    @Mock
    private Converter<PriceData, PriceDTO> priceDtoConverter;

    @Mock
    private Converter<MaterialData, MaterialDTO> materialDtoConverter;

    private ProductDtoPopulator productDtoPopulator;

    @Mock
    private ProductData productData;

    @Mock
    private ProductDTO productDto;

    @Mock
    private PriceData priceData;

    @Mock
    private PriceDTO priceDto;

    @Mock
    private MaterialData materialData;

    @Mock
    private MaterialDTO materialDto;

    @Before
    public void setUp() {
        productDtoPopulator = new ProductDtoPopulator(storeUserAuthorizationService, priceDtoConverter, materialDtoConverter);

        when(productData.getCode()).thenReturn(CODE);
        when(productData.getName()).thenReturn(NAME);
        when(productData.getSummary()).thenReturn(SUMMARY);
        when(productData.getDescription()).thenReturn(DESCRIPTION);
        when(productData.getPrice()).thenReturn(priceData);
        when(productData.getOrder()).thenReturn(PRODUCT_ORDER);
        when(productData.getProductNote()).thenReturn(PRODUCT_NOTE);
        when(storeUserAuthorizationService.currentUserHasAccessToPrices()).thenReturn(true);
        when(priceDtoConverter.convert(priceData)).thenReturn(priceDto);
        when(materialDtoConverter.convertAll(of(materialData))).thenReturn(of(materialDto));
    }

    @Test
    public void populate_userNotAuthenticated_priceNotSet() {
        when(storeUserAuthorizationService.currentUserHasAccessToPrices()).thenReturn(false);

        productDtoPopulator.populate(productData, productDto);

        verifyProductData();
        verifyNoInteractions(priceDtoConverter);
    }

    @Test
    public void populate_userAuthenticated_priceSetWithOtherProductData() {
        productDtoPopulator.populate(productData, productDto);

        verifyProductData();
        verify(productDto).setMinPrice(priceDto);
    }

    @Test
    public void populate_userAuthenticatedButHasNoPrice_priceNotSet() {
        when(productData.getPrice()).thenReturn(null);

        productDtoPopulator.populate(productData, productDto);

        verifyProductData();
        verifyNoMoreInteractions(priceDtoConverter);
        verify(productDto, never()).setMinPrice(priceDto);
    }

    @Test
    public void givenProductWithNoBoMs_whenPopulate_thenSetBoMsInResultToEmpty() {
        when(productData.getBillOfMaterials()).thenReturn(null);

        productDtoPopulator.populate(productData, productDto);

        verifyProductData();
        verify(productDto).setBillsOfMaterials(of());
    }

    @Test
    public void givenProductWithBoMs_whenPopulate_thenSetBoMsInResult() {
        when(productData.getBillOfMaterials()).thenReturn(of(materialData));

        productDtoPopulator.populate(productData, productDto);

        verifyProductData();
        verify(productDto).setBillsOfMaterials(of(materialDto));
    }

    @Test
    public void givenProductWithSpecialOffer_whenPopulate_thenSetSpecialOfferInTheResult() {
        when(productData.isSpecialOffer()).thenReturn(true);

        productDtoPopulator.populate(productData, productDto);

        verifyProductData();
        verify(productDto).setSpecialOffer(true);
    }

    private void verifyProductData() {
        verify(productDto).setCode(CODE);
        verify(productDto).setName(NAME);
        verify(productDto).setSummary(SUMMARY);
        verify(productDto).setDescription(DESCRIPTION);
        verify(productDto).setOrder(PRODUCT_ORDER);
        verify(productDto).setProductNote(PRODUCT_NOTE);
    }
}
