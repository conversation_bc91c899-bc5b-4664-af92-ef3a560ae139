package com.sast.cis.core.service;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableSet;
import com.sast.cis.core.dao.UseCaseDao;
import com.sast.cis.core.model.UseCaseModel;
import de.hybris.bootstrap.annotations.UnitTest;
import de.hybris.platform.core.PK;
import generated.com.sast.cis.core.model.UseCaseBuilder;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Collection;
import java.util.Set;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.*;

@UnitTest
public class UseCaseServiceUnitTest {

    public static final String OTHER = "Other";
    @Mock
    private UseCaseDao useCaseDao;

    private UseCaseService useCaseService;

    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);
        UseCaseModel useCaseModel = UseCaseBuilder.generate().withName(OTHER).withIndex(1).buildMockInstance();
        when(useCaseModel.getPk()).thenReturn(PK.fromLong(1l));
        useCaseService = new UseCaseService(useCaseDao);
        when(useCaseDao.findAllEnabledUseCases()).thenReturn(ImmutableList.of(useCaseModel));
        Set<UseCaseModel> useCaseModels = ImmutableSet.of(useCaseModel);
        when(useCaseDao.findEnabledUseCasesByIds(any())).thenReturn(useCaseModels);
    }

    @Test
    public void testAllEnabledUseCases() {
        Collection<UseCaseModel> allEnabledUseCases = useCaseService.getAllEnabledUseCases();
        assertThat(allEnabledUseCases).hasSize(1);
        verify(useCaseDao, times(1)).findAllEnabledUseCases();
    }

    @Test
    public void testFindEnabledUseCasesByIds() {
        ImmutableSet<Long> ids = ImmutableSet.of(1L);
        Set<UseCaseModel> useCasesByIds = useCaseService.getUseCasesByIds(ids);
        assertThat(useCasesByIds).hasSize(1);
        assertThat(useCasesByIds.iterator().next().getName()).isEqualTo(OTHER);
    }

    @Test
    public void testFindEnabledUseCasesByIdsReturnsEmptyListWithEmptyInput() {
        ImmutableSet<Long> ids = ImmutableSet.of();
        Set<UseCaseModel> useCasesByIds = useCaseService.getUseCasesByIds(ids);
        assertThat(useCasesByIds).hasSize(0);
    }
}