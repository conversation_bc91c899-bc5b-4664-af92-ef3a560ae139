package com.sast.cis.core.converter.appversion;

import com.google.common.collect.ImmutableSet;
import com.sast.cis.core.converter.appediting.AppVersionsOverviewPopulator;
import com.sast.cis.core.data.AppVersionOverviewData;
import com.sast.cis.core.data.VersionsOverviewData;
import com.sast.cis.core.model.*;
import de.hybris.bootstrap.annotations.UnitTest;
import de.hybris.platform.catalog.enums.ArticleApprovalStatus;
import de.hybris.platform.servicelayer.config.ConfigurationService;
import generated.com.sast.cis.core.model.*;
import org.apache.commons.configuration.Configuration;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Collections;
import java.util.Date;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@UnitTest
@RunWith(MockitoJUnitRunner.Silent.class)
public class AppVersionsOverviewPopulatorUnitTest {

    private static final String VERSION_NAME = "TestAppVersionName";
    private static final String VERSION_CODE = "TestAppVersionCode";
    private static final String OLD_VERSION_CODE = "OldTestAppVersionCode";
    private static final String PACKAGE_NAME = "packageName";
    private static final Date VERSION_DATE = new Date(10000);

    @Mock
    private ConfigurationService configurationService;

    @InjectMocks
    private AppVersionsOverviewPopulator appVersionsOverviewPopulator;

    private AppLicenseModel appLicense;

    private AppVersionModel appVersion;
    private AppVersionModel oldAppVersion;
    private AppVersionDraftModel appVersionDraft;
    private AppVersionDraftModel unapprovedAppVersionDraft;
    private AppVersionDraftModel appVersionDraftInApproval;

    @Before
    public void setUp() {
        ApkMediaModel apk = ApkMediaBuilder.generate().withVersionName(VERSION_NAME).withPackageName(PACKAGE_NAME)
            .withVersionCode(1L).buildMockInstance();
        ApkMediaModel oldApk = ApkMediaBuilder.generate().withVersionName(VERSION_NAME).withVersionCode(0L).buildMockInstance();
        var configuration = mock(Configuration.class);
        when(configuration.getString("apk.max.upload.size.bytes")).thenReturn("1024");
        when(configurationService.getConfiguration()).thenReturn(configuration);

        appLicense = AppLicenseBuilder.generate()
            .withApprovalStatus(ArticleApprovalStatus.APPROVED)
            .withCode(VERSION_CODE)
            .buildMockInstance();

        appVersion = AppVersionBuilder.generate()
            .withApk(apk)
            .withCode(VERSION_CODE)
            .withModifiedtime(VERSION_DATE)
            .buildMockInstance();

        oldAppVersion = AppVersionBuilder.generate()
            .withApk(oldApk)
            .withCode(OLD_VERSION_CODE)
            .withModifiedtime(VERSION_DATE)
            .buildMockInstance();

        appVersionDraft = AppVersionDraftBuilder.generate()
            .withApk(apk)
            .withModifiedtime(VERSION_DATE)
            .withCode(VERSION_CODE)
            .withApprovalStatus(ArticleApprovalStatus.DRAFT)
            .buildMockInstance();

        unapprovedAppVersionDraft = AppVersionDraftBuilder.generate()
            .withApk(apk)
            .withApprovalStatus(ArticleApprovalStatus.UNAPPROVED)
            .withCode(VERSION_CODE)
            .withModifiedtime(VERSION_DATE)
            .buildMockInstance();

        appVersionDraftInApproval = AppVersionDraftBuilder.generate()
            .withApk(apk)
            .withApprovalStatus(ArticleApprovalStatus.CHECK)
            .withCode(VERSION_CODE)
            .withModifiedtime(VERSION_DATE)
            .buildMockInstance();
    }

    @Test
    public void givenContainerWithAppAndVersion_returnsVersionsOverviewData() {
        AppModel app = AppBuilder.generate()
            .withVersions(Collections.singleton(appVersion))
            .buildMockInstance();
        ProductContainerModel container = ProductContainerBuilder.generate()
            .withApp(app)
            .buildMockInstance();
        VersionsOverviewData versionsOverview = new VersionsOverviewData();
        appVersionsOverviewPopulator.populate(container, versionsOverview);

        assertThat(versionsOverview.getVersions()).hasSize(1);
        AppVersionOverviewData version = versionsOverview.getVersions().get(0);
        assertThat(version.getVersionName()).isEqualTo(VERSION_NAME);
        assertThat(version.getApprovalStatus()).isEqualTo(ArticleApprovalStatus.APPROVED);
        assertThat(version.getCode()).isEqualTo(VERSION_CODE);
        assertThat(version.getPackageName()).isEqualTo(PACKAGE_NAME);
        assertThat(version.getLastModified()).isEqualTo(VERSION_DATE);
    }

    @Test
    public void givenContainerWithUnapprovedVersion_returnsVersionsOverviewData() {
        AppModel app = AppBuilder.generate()
            .withVersions(Collections.emptySet())
            .buildMockInstance();
        ProductContainerModel container = ProductContainerBuilder.generate()
            .withApp(app)
            .withAppVersionDraft(unapprovedAppVersionDraft)
            .buildMockInstance();
        VersionsOverviewData versionsOverview = new VersionsOverviewData();
        appVersionsOverviewPopulator.populate(container, versionsOverview);

        assertThat(versionsOverview.getVersions()).hasSize(1);
        AppVersionOverviewData version = versionsOverview.getVersions().get(0);
        assertThat(version.getVersionName()).isEqualTo(VERSION_NAME);
        assertThat(version.getApprovalStatus()).isEqualTo(ArticleApprovalStatus.UNAPPROVED);
        assertThat(version.getCode()).isEqualTo(VERSION_CODE);
        assertThat(version.getLastModified()).isEqualTo(VERSION_DATE);
    }

    @Test
    public void givenContainerWithVersionDraftInApproval_returnsVersionsOverviewData() {
        AppModel app = AppBuilder.generate()
            .withVersions(Collections.emptySet())
            .buildMockInstance();
        ProductContainerModel container = ProductContainerBuilder.generate()
            .withApp(app)
            .withAppVersionDraft(appVersionDraftInApproval)
            .buildMockInstance();
        VersionsOverviewData versionsOverview = new VersionsOverviewData();
        appVersionsOverviewPopulator.populate(container, versionsOverview);

        assertThat(versionsOverview.getVersions()).hasSize(1);
        assertThat(versionsOverview.getApkMaxDisplaySize()).isEqualTo("1 KB");
        AppVersionOverviewData version = versionsOverview.getVersions().get(0);
        assertThat(version.getVersionName()).isEqualTo(VERSION_NAME);
        assertThat(version.getApprovalStatus()).isEqualTo(ArticleApprovalStatus.CHECK);
        assertThat(version.getCode()).isEqualTo(VERSION_CODE);
        assertThat(version.getLastModified()).isEqualTo(VERSION_DATE);
    }

    @Test
    public void givenContainerWithMultipleVersions_returnsVersionsOverviewDataInCorrectOrder() {
        AppModel app = AppBuilder.generate()
            .withVersions(ImmutableSet.of(appVersion, oldAppVersion))
            .buildMockInstance();
        ProductContainerModel container = ProductContainerBuilder.generate()
            .withApp(app)
            .buildMockInstance();
        VersionsOverviewData versionsOverview = new VersionsOverviewData();
        appVersionsOverviewPopulator.populate(container, versionsOverview);

        assertThat(versionsOverview.getVersions()).hasSize(2);
        assertThat(versionsOverview.getVersions().get(0).getCode()).isEqualTo(VERSION_CODE);
        assertThat(versionsOverview.getVersions().get(1).getCode()).isEqualTo(OLD_VERSION_CODE);
    }

    @Test
    public void populate_givenContainerWithAppAndVersion_returnsVersionsOverviewData() {
        AppModel app = AppBuilder.generate()
            .withVariants(Collections.singleton(appLicense))
            .buildMockInstance();
        ProductContainerModel container = ProductContainerBuilder.generate()
            .withApp(app)
            .buildMockInstance();
        VersionsOverviewData versionsOverview = new VersionsOverviewData();
        appVersionsOverviewPopulator.populate(container, versionsOverview);

        assertThat(versionsOverview.getVersions()).isEmpty();
    }

    @Test
    public void populate_givenContainerWithAppVersionDraft_returnsVersionsOverviewData() {
        AppDraftModel appDraft = AppDraftBuilder.generate()
            .buildMockInstance();
        ProductContainerModel container = ProductContainerBuilder.generate()
            .withAppVersionDraft(appVersionDraft)
            .withAppDraft(appDraft)
            .buildMockInstance();
        VersionsOverviewData versionsOverview = new VersionsOverviewData();
        appVersionsOverviewPopulator.populate(container, versionsOverview);

        assertThat(versionsOverview.getVersions()).hasSize(1);
        AppVersionOverviewData version = versionsOverview.getVersions().get(0);
        assertThat(version.getVersionName()).isEqualTo(VERSION_NAME);
        assertThat(version.getApprovalStatus()).isEqualTo(appVersionDraft.getApprovalStatus());
        assertThat(version.getCode()).isEqualTo(VERSION_CODE);
        assertThat(version.getLastModified()).isEqualTo(VERSION_DATE);
    }

    @Test
    public void populate_givenContainerWithEmptyAppDraft_returnsEmptyVersionsOverviewData() {
        AppDraftModel appDraft = AppDraftBuilder.generate()
            .buildMockInstance();
        ProductContainerModel container = ProductContainerBuilder.generate()
            .withAppDraft(appDraft)
            .buildMockInstance();
        VersionsOverviewData versionsOverview = new VersionsOverviewData();
        appVersionsOverviewPopulator.populate(container, versionsOverview);

        assertThat(versionsOverview.getVersions()).isEmpty();
        assertThat(versionsOverview.getRedirectUrl()).isNullOrEmpty();
    }
}
