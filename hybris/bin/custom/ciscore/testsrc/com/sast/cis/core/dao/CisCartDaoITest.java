package com.sast.cis.core.dao;

import com.google.common.collect.ImmutableList;
import com.sast.cis.core.enums.LicenseType;
import com.sast.cis.core.model.*;
import com.sast.cis.core.service.customer.developer.DeveloperService;
import com.sast.cis.core.service.customer.integrator.IntegratorService;
import com.sast.cis.test.utils.SampleDataCreator;
import com.sast.cis.test.utils.SessionCatalogRule;
import de.hybris.bootstrap.annotations.IntegrationTest;
import de.hybris.platform.commerceservices.order.CommerceCartModificationException;
import de.hybris.platform.core.model.order.AbstractOrderEntryModel;
import de.hybris.platform.core.model.order.CartEntryModel;
import de.hybris.platform.core.model.order.CartModel;
import de.hybris.platform.order.CartService;
import de.hybris.platform.product.UnitService;
import de.hybris.platform.servicelayer.ServicelayerTransactionalTest;
import de.hybris.platform.servicelayer.model.ModelService;
import de.hybris.platform.servicelayer.user.UserService;
import generated.de.hybris.platform.core.model.order.CartEntryBuilder;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static com.sast.cis.test.utils.TestDataConstants.*;
import static org.assertj.core.api.Assertions.assertThat;

@IntegrationTest
public class CisCartDaoITest extends ServicelayerTransactionalTest {
    private final SampleDataCreator sampleDataCreator = new SampleDataCreator();

    @Resource
    private UnitService unitService;

    @Resource
    private IntegratorService integratorService;

    @Resource
    private DeveloperService developerService;

    @Resource
    private CisCartDao cisCartDao;

    @Resource
    private ModelService modelService;

    @Resource
    private CartService cartService;

    @Resource
    private UserService userService;

    @Rule
    public SessionCatalogRule sessionCatalogRule = SessionCatalogRule.onlineCatalog();

    private IntegratorModel integrator;
    private DeveloperModel developer;
    private IoTCompanyModel developerCompany;
    private CartModel cart;

    @Before
    public void setUp() throws CommerceCartModificationException {
        integrator = integratorService.getIntegratorByInternalUserId(SAMPLE_DATA_INTEGRATOR_A1_UID);
        developer = developerService.getDeveloperByInternalUserId(SAMPLE_DATA_DEVELOPER_A1_UID);
        developerCompany = developer.getCompany();
        cart = createCartWithApp(integrator, developer, "1");
        userService.setCurrentUser(integrator);
    }

    @Test
    public void getCartsForCompany() {
        List<CartModel> carts = cisCartDao.getCartsForCompany(integrator.getCompany());
        assertThat(carts.size()).isEqualTo(1);
        assertThat(carts).containsExactly(cart);
    }

    @Test
    public void getCartByUserAndDeveloperCompany_usersOfDifferentIntegratorCompany() throws CommerceCartModificationException {
        IntegratorModel anotherIntegrator = integratorService.getIntegratorByInternalUserId(SAMPLE_DATA_INTEGRATOR_B1_UID);
        CartModel anotherCart = createCartWithApp(anotherIntegrator, developer, "2");
        Optional<CartModel> resultCart = cisCartDao.getCartByIntegratorDeveloperCompanyAndLicenseTypes(integrator, developerCompany,
            ImmutableList.of(LicenseType.FULL, LicenseType.EVALUATION));
        assertCart(resultCart);
    }

    @Test
    public void getCartByUserAndDeveloperCompany_usersOfSameIntegratorCompany() throws CommerceCartModificationException {
        IntegratorModel anotherUserOfSameIntegratorCompany = integratorService.getIntegratorByInternalUserId(SAMPLE_DATA_INTEGRATOR_A2_UID);
        CartModel anotherCart = createCartWithApp(anotherUserOfSameIntegratorCompany, developer, "2");
        Optional<CartModel> resultCart = cisCartDao.getCartByIntegratorDeveloperCompanyAndLicenseTypes(integrator, developerCompany,
            ImmutableList.of(LicenseType.FULL, LicenseType.EVALUATION));
        assertCart(resultCart);
    }

    private void assertCart(Optional<CartModel> resultCart) throws CommerceCartModificationException {
        assertThat(resultCart).isPresent();

        CartModel cartModel = resultCart.get();
        assertThat(cartModel.getEntries()).isNotEmpty();
        assertThat(cartModel.getEntries()).hasSize(2);
        assertThat(cartModel.getCompany()).isEqualTo(integrator.getCompany());
        cartModel.getEntries().forEach(entry -> assertThat(getCompanyFromCartEntry(entry)).isEqualTo(developerCompany));
    }

    private IoTCompanyModel getCompanyFromCartEntry(AbstractOrderEntryModel entry) {
        AppLicenseModel appLicense = (AppLicenseModel) entry.getProduct();
        return ((AppModel) appLicense.getBaseProduct()).getCompany();
    }

    private CartModel createCartWithApp(IntegratorModel integrator, DeveloperModel developer, String id)
        throws CommerceCartModificationException {
        CartModel anotherCart = sampleDataCreator.createCart();
        anotherCart.setUser(integrator);
        anotherCart.setCompany(integrator.getCompany());
        AppModel app = sampleDataCreator.createApp("code" + id, "title" + id, developer, CatalogVersion.ONLINE);
        AppLicenseModel fullLicenseApp = sampleDataCreator.createFullAppLicense(app);
        AppLicenseModel evalLicense = sampleDataCreator.createEvalForFullAppLicense(fullLicenseApp);

        ArrayList<AbstractOrderEntryModel> cartEntries = new ArrayList<>();
        cartEntries.add(createEntry(anotherCart, 5, fullLicenseApp, 0));
        cartEntries.add(createEntry(anotherCart, 5, evalLicense, 1));
        anotherCart.setEntries(cartEntries);
        modelService.save(anotherCart);

        return anotherCart;
    }

    private CartEntryModel createEntry(CartModel cart, long quantity, AppLicenseModel license, int entryNumber) {
        CartEntryModel cartEntry = CartEntryBuilder.generate()
            .withOrder(cart)
            .withEntryNumber(entryNumber)
            .withQuantity(quantity)
            .withProduct(license)
            .withUnit(unitService.getUnitForCode("pieces"))
            .buildIntegrationInstance();
        modelService.save(cartEntry);
        return cartEntry;
    }
}
