package com.sast.cis.core.service;

import com.sast.cis.test.utils.SampleDataCreator;
import de.hybris.bootstrap.annotations.IntegrationTest;
import de.hybris.platform.servicelayer.ServicelayerTest;
import de.hybris.platform.testframework.HybrisJUnit4ClassRunner;
import de.hybris.platform.testframework.RunListeners;
import de.hybris.platform.testframework.runlistener.ItemCreationListener;
import org.junit.Test;
import org.junit.runner.RunWith;

import javax.annotation.Resource;

import static org.assertj.core.api.Assertions.assertThat;

@IntegrationTest
@RunWith(HybrisJUnit4ClassRunner.class)
@RunListeners({ ItemCreationListener.class })
public class ShopMediaUrlServiceITest extends ServicelayerTest {
    private final SampleDataCreator sampleDataCreator = new SampleDataCreator();
    private static final String DEFAULT_FILE = "test.txt";
    private static final String DEFAULT_S3_FILE = "http://localhost:4566/cbsiotstore-localdev-apks/" + DEFAULT_FILE;
    private static final String DEFAULT_MEDIA_FILE = "/media/logo";

    @Resource
    private ShopMediaUrlService shopMediaUrlService;

    @Test
    public void getApkUrlForS3_blankMedia_returnsS3Url() {
        final var apkMedia = sampleDataCreator.getOrCreateApkMedia("apk", "", 1L, "");
        final var apkUrlForS3 = shopMediaUrlService.getApkUrlForS3(apkMedia);
        assertThat(apkUrlForS3).isNotNull().isEqualTo(DEFAULT_S3_FILE);
    }

    @Test
    public void getUrl_blankMedia_returnsAbsoluteLocalMediaLink() {
        final var simpleMedia = sampleDataCreator.getOrCreateCatalogUnawareMedia("logo");
        final var mediaUrl = shopMediaUrlService.getUrl(simpleMedia);
        assertThat(mediaUrl).isNotNull().startsWith("https://").contains(DEFAULT_MEDIA_FILE);
    }
}
