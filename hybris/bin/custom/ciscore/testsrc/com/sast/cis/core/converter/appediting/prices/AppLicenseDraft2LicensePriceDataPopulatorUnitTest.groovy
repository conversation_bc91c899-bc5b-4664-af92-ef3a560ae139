package com.sast.cis.core.converter.appediting.prices

import com.sast.cis.core.data.LicensePriceData
import com.sast.cis.core.data.VolumeDiscountData
import com.sast.cis.core.enums.LicenseAvailabilityStatus
import com.sast.cis.core.enums.LicenseType
import com.sast.cis.core.model.AppLicenseDraftModel
import com.sast.cis.core.model.CountriesAndPricesDraftModel
import com.sast.cis.core.model.VolumeDiscountDraftModel
import com.sast.cis.core.model.VolumeDiscountModel
import com.sast.cis.core.util.PriceUtil
import de.hybris.bootstrap.annotations.UnitTest
import de.hybris.platform.servicelayer.dto.converter.ConversionException
import de.hybris.platform.servicelayer.dto.converter.Converter
import org.junit.Test
import de.hybris.platform.testframework.JUnitPlatformSpecification

@UnitTest
class AppLicenseDraft2LicensePriceDataPopulatorUnitTest extends JUnitPlatformSpecification {
    private static final FULL_PRICE = 100d
    private static final SUBSCRIPTION_PRICE = 200d

    PriceUtil priceUtil = Mock(PriceUtil)
    Converter<VolumeDiscountDraftModel, VolumeDiscountData> volumeDiscountDraftDataConverter = Mock(Converter)

    AppLicenseDraft2LicensePriceDataPopulator appLicenseDraft2LicensePriceDataPopulator

    AppLicenseDraftModel appLicenseDraft = Mock(AppLicenseDraftModel)
    CountriesAndPricesDraftModel countriesAndPricesDraft = Mock(CountriesAndPricesDraftModel)
    private List<VolumeDiscountData> volumeDiscounts = Mock(List)

    def setup() {
        appLicenseDraft2LicensePriceDataPopulator = new AppLicenseDraft2LicensePriceDataPopulator(priceUtil,volumeDiscountDraftDataConverter)
        appLicenseDraft.countriesAndPricesDraft >> countriesAndPricesDraft
        appLicenseDraft.licenseType >> LicenseType.FULL
        appLicenseDraft.availabilityStatus >> LicenseAvailabilityStatus.PUBLISHED
        appLicenseDraft.specifiedPrice >> FULL_PRICE
        priceUtil.formatAmount(0, FULL_PRICE) >> '100'
        priceUtil.formatAmount(0, SUBSCRIPTION_PRICE) >> '200'
        volumeDiscountDraftDataConverter.convertAll(_ as Collection<VolumeDiscountModel>) >> volumeDiscounts;
    }

    @Test
    def 'given full license draft, all values are populated as expected'() {
        given:
        def actualData = new LicensePriceData()

        when:
        appLicenseDraft2LicensePriceDataPopulator.populate(appLicenseDraft, actualData)

        then:
        with(actualData) {
            verifyAll {
                actualData.editable
                actualData.enabled
                actualData.type == LicenseType.FULL
                actualData.specifiedPrice == '100'
            }
        }
    }

    @Test
    def 'given subscription license draft, all values are populated as expected'() {
        given:
        def actualData = new LicensePriceData()

        when:
        appLicenseDraft2LicensePriceDataPopulator.populate(appLicenseDraft, actualData)

        then:
        appLicenseDraft.licenseType >> LicenseType.SUBSCRIPTION
        appLicenseDraft.specifiedPrice >> SUBSCRIPTION_PRICE
        appLicenseDraft.availabilityStatus >> LicenseAvailabilityStatus.UNPUBLISHED
        with(actualData) {
            verifyAll {
                actualData.editable
                !actualData.enabled
                actualData.type == LicenseType.SUBSCRIPTION
                actualData.specifiedPrice == '200'
            }
        }
    }

    @Test
    def 'given evaluation license draft, all values are populated as expected'() {
        given:
        def actualData = new LicensePriceData()

        when:
        appLicenseDraft2LicensePriceDataPopulator.populate(appLicenseDraft, actualData)

        then:
        appLicenseDraft.licenseType >> LicenseType.EVALUATION
        with(actualData) {
            verifyAll {
                actualData.editable
                actualData.type == LicenseType.EVALUATION
                actualData.specifiedPrice == null
            }
        }
    }

    @Test
    def 'given app license draft is null, exception is thrown'() {
        given:
        def actualData = Mock(LicensePriceData)

        when:
        appLicenseDraft2LicensePriceDataPopulator.populate(null as AppLicenseDraftModel, actualData)

        then:
        thrown(ConversionException)
        0 * actualData._
    }

    @Test
    def 'given data is null, exception is thrown'() {
        when:
        appLicenseDraft2LicensePriceDataPopulator.populate(appLicenseDraft, null)

        then:
        thrown(ConversionException)
    }
    @Test
    def 'full license draft with volume discounts, volume discounts are populated correctly'() {
        given:
        def actualData = new LicensePriceData()
        when:
        appLicenseDraft2LicensePriceDataPopulator.populate(appLicenseDraft, actualData)

        then:
        appLicenseDraft.volumeDiscounts >> volumeDiscounts
        with(actualData) {
            verifyAll {
                actualData.type == LicenseType.FULL
                actualData.volumeDiscounts == volumeDiscounts
            }
        }
    }
}
