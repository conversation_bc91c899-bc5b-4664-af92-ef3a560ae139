package com.sast.cis.test.utils.buildergenerator;

import de.hybris.platform.servicelayer.model.AbstractItemModel;

import java.io.File;
import java.lang.reflect.Method;
import java.util.Locale;
import java.util.stream.Collectors;

import static java.lang.String.format;
import static java.lang.reflect.Modifier.isPublic;
import static java.util.Arrays.stream;
import static javax.tools.JavaFileObject.Kind.SOURCE;
import static org.apache.commons.lang3.StringUtils.uncapitalize;

public class BuilderTemplate {
    private final Class<?> modelClass;

    BuilderTemplate(Class<?> modelClass) {
        this.modelClass = modelClass;
    }

    String javaFilename() {
        return modelToBuilderName(builderPackage() + "." + builderName()).replaceAll("\\.", File.separator) + SOURCE.extension;
    }

    private String modelToBuilderName(String modelName) {
        return modelName.replaceFirst("Model$", "Builder");
    }

    private String builderPackage() {
        return "generated." + modelClass.getPackage().getName();
    }

    private String builderName() {
        return modelToBuilderName(modelClass.getSimpleName());
    }

    String getBuilderClassJavacode() {
        return getBuilderClassHeader() + getBuilderBody() + "\n}";
    }

    private String getBuilderClassHeader() {
        return format("package %1$s;\n\n"
                + "@SuppressWarnings({ \"rawtypes\", \"serial\", \"unchecked\" })\n"
                + "public class %2$s\n"
                + "  <BUILDER extends %2$s<BUILDER, MODEL>,\n"
                + "   MODEL extends %3$s>\n"
                + "  extends\n"
                + "   %4$s<BUILDER, MODEL> {\n"
                + "    protected %2$s(Class<MODEL> modelClass) { super(modelClass); }\n"
                + "    public static %2$s<?,?> generate() { return new %2$s(%3$s.class); }\n",
            builderPackage(), builderName(), modelFqcn(), superBuilderFqcn());
    }

    private String modelFqcn() {
        return modelClass.getCanonicalName();
    }

    private String superBuilderFqcn() {
        if (AbstractItemModel.class.equals(modelClass.getSuperclass())) {
            return AbstractItemBuilder.class.getCanonicalName();
        }
        return modelToBuilderName("generated." + modelClass.getSuperclass().getCanonicalName());
    }

    private String getBuilderBody() {
        return stream(modelClass.getDeclaredMethods()).filter(this::isSetter)
            .map(this::generateWithMethod)
            .collect(Collectors.joining("\n"))
                + "\n"
                + stream(modelClass.getDeclaredMethods()).filter(this::isLocalizedSetter)
                    .map(this::generateLocalizedWithMethod)
                    .collect(Collectors.joining("\n"));
    }

    private boolean isSetter(Method method) {
        return isPublic(method.getModifiers()) && method.getName().startsWith("set") && method.getParameterCount() == 1;
    }

    private boolean isLocalizedSetter(Method method) {
        return isPublic(method.getModifiers())
                && method.getName().startsWith("set")
                && method.getParameterCount() == 2
                && method.getParameterTypes()[1].equals(Locale.class);
    }

    private String generateWithMethod(Method setter) {
        return String.format("    public BUILDER with%1$s(%2$s arg) { return with(\"%3$s\", arg); }",
            capitalizedFieldName(setter), withMethodArgument(setter), uncapitalize(capitalizedFieldName(setter)));
    }

    private String generateLocalizedWithMethod(Method setter) {
        String methodArgument = withUnparameterizedMethodArgument(setter);
        return String.format(
            "    public BUILDER with%1$s(%2$s arg, java.util.Locale locale) { return withLocale(\"%3$s\", arg, locale, %4$s.class); }",
            capitalizedFieldName(setter), methodArgument, uncapitalize(capitalizedFieldName(setter)), methodArgument);
    }

    private String capitalizedFieldName(Method setter) {
        return setter.getName().substring(3);
    }

    private String withMethodArgument(Method setter) {
        return setter.getParameters()[0].getParameterizedType().getTypeName();
    }

    private String withUnparameterizedMethodArgument(Method setter) {
        return setter.getParameters()[0].getType().getTypeName();
    }
}
