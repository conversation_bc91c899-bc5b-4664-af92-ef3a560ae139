package com.sast.cis.core.converter.licenseactivation;

import com.sast.cis.core.data.LicenseActivationMessage;
import com.sast.cis.core.enums.LicenseActivationStatus;
import com.sast.cis.core.enums.LicenseType;
import com.sast.cis.core.model.*;
import de.hybris.bootstrap.annotations.UnitTest;
import org.apache.commons.lang3.time.DateUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Date;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@UnitTest
@RunWith(MockitoJUnitRunner.Silent.class)
public class LicenseActivationToLicenseActivationMsgPopulatorTest {

    private LicenseActivationToLicenseActivationMsgPopulator populator;

    @Mock
    private LicenseActivationModel licenseActivation;

    @Mock
    private IoTCompanyModel company;

    @Mock
    private AppLicenseModel appLicense;

    @Mock
    private AppModel app;

    @Mock
    private AppVersionModel appVersion;

    @Mock
    private ApkMediaModel apk;

    private final String companyUid = "company_uid";
    private final String packageName = "com.snst";
    private final LicenseType licenseType = LicenseType.TOOL;
    private final String versionName = "latest_version_name";
    private final long versionCode = 10L;
    private final LicenseActivationStatus activationStatus = LicenseActivationStatus.ACTIVE;
    private final Date creationTime = new Date();
    private final Date modifiedTime = DateUtils.addDays(creationTime, 1);

    @Before
    public void setUp() {
        populator = new LicenseActivationToLicenseActivationMsgPopulator();

        when(licenseActivation.getCompany()).thenReturn(company);
        when(licenseActivation.getAppLicense()).thenReturn(appLicense);
        when(licenseActivation.getActivationStatus()).thenReturn(activationStatus);
        when(licenseActivation.getCreationtime()).thenReturn(creationTime);
        when(licenseActivation.getModifiedtime()).thenReturn(modifiedTime);

        when(company.getUid()).thenReturn(companyUid);

        when(appLicense.getLicenseType()).thenReturn(licenseType);
        when(appLicense.getBaseProduct()).thenReturn(app);

        when(app.getPackageName()).thenReturn(packageName);
        when(app.getLatestVersion()).thenReturn(appVersion);

        when(appVersion.getApk()).thenReturn(apk);

        when(apk.getVersionName()).thenReturn(versionName);
        when(apk.getVersionCode()).thenReturn(versionCode);
    }

    @Test
    public void givenLicenseActivation_whenPopulate_thenPopulateLicenseActivationMessage() {
        final LicenseActivationMessage target = new LicenseActivationMessage();
        populator.populate(licenseActivation, target);

        assertThat(target.getTransactionId()).isNotEmpty();
        assertThat(target.getCompanyAccountId()).isEqualTo(companyUid);
        assertThat(target.getCreationTimestamp()).isEqualTo(modifiedTime);
        assertThat(target.getApplication().getApplicationId()).isEqualTo(packageName);
        assertThat(target.getApplication().getLicenseType()).isEqualTo(licenseType.getCode());
        assertThat(target.getApplication().getVersionName()).isEqualTo(versionName);
        assertThat(target.getApplication().getVersionCode()).isEqualTo(versionCode);
    }

    @Test
    public void givenLicenseActivationWithNoModifiedTime_whenPopulate_thenPopulateWithCreationTime() {
        when(licenseActivation.getModifiedtime()).thenReturn(null);

        final LicenseActivationMessage target = new LicenseActivationMessage();
        populator.populate(licenseActivation, target);

        assertThat(target.getCreationTimestamp()).isEqualTo(creationTime);
    }

    @Test
    public void givenLicenseActivationWithActiveStatus_whenPopulate_thenPopulateEnabledTrue() {
        when(licenseActivation.getActivationStatus()).thenReturn(LicenseActivationStatus.ACTIVE);

        final LicenseActivationMessage target = new LicenseActivationMessage();
        populator.populate(licenseActivation, target);

        assertThat(target.getApplication().isEnabled()).isTrue();
    }

    @Test
    public void givenLicenseActivationWithInactiveStatus_whenPopulate_thenPopulateEnabledFalse() {
        when(licenseActivation.getActivationStatus()).thenReturn(LicenseActivationStatus.INACTIVE);

        final LicenseActivationMessage target = new LicenseActivationMessage();
        populator.populate(licenseActivation, target);

        assertThat(target.getApplication().isEnabled()).isFalse();
    }
}