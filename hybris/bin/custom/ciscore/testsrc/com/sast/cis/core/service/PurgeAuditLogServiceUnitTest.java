package com.sast.cis.core.service;

import de.hybris.bootstrap.annotations.UnitTest;
import de.hybris.platform.core.model.type.TypeModel;
import de.hybris.platform.servicelayer.config.ConfigurationService;
import de.hybris.platform.servicelayer.type.TypeService;
import org.apache.commons.configuration.Configuration;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.Matchers.anyInt;
import static org.mockito.Matchers.anyString;
import static org.mockito.Mockito.*;

@UnitTest
@RunWith(MockitoJUnitRunner.Silent.class)
public class PurgeAuditLogServiceUnitTest {

    private List<String> auditPropertyList = new ArrayList<>(
        List.of("audit.countriesandpricesdraft.enabled",
            "audit.iotcustomer2iotcompany.enabled",
            "audit.product.enabled"));

    @Mock
    private ConfigurationService configurationService;

    @Mock
    private AuditLogRemovalService auditLogRemovalService;

    @Mock
    private TypeService typeService;

    @InjectMocks
    private PurgeAuditLogService purgeAuditLogService;

    @Before
    public void setup() throws Exception {
        when(configurationService.getConfiguration()).thenReturn(mock(Configuration.class));
        when(configurationService.getConfiguration().getInt(anyString())).thenReturn(90);
        when(configurationService.getConfiguration().getBoolean(anyString())).thenReturn(true);
        when(configurationService.getConfiguration().getKeys()).thenReturn(auditPropertyList.iterator());
        when(typeService.getTypeForCode("countriesandpricesdraft")).thenReturn(mock(TypeModel.class));
        when(typeService.getTypeForCode("iotcustomer2iotcompany")).thenReturn(mock(TypeModel.class));
        when(typeService.getTypeForCode("product")).thenReturn(mock(TypeModel.class));
    }

    @Test
    public void test_allEnabledAuditType_executesAll() {
        purgeAuditLogService.purgeAuditLogForAllAuditTypes();
        verify(auditLogRemovalService, times(3)).removeAuditLog(anyString(), anyInt());
    }

    @Test
    public void test_someEnabledAuditTypeForLog_skipNotEnabledAuditType() {
        when(configurationService.getConfiguration().getBoolean("audit.product.enabled")).thenReturn(false);
        purgeAuditLogService.purgeAuditLogForAllAuditTypes();
        verify(auditLogRemovalService, times(2)).removeAuditLog(anyString(), anyInt());
    }

    @Test
    public void test_typeModelDoesNotExists_skipNotFoundTypeModel() {
        auditPropertyList = new ArrayList<>(
            List.of("audit.countriesandpricesdraft.enabled",
                "audit.iotcustomer2iotcompany.enabled",
                "audit.user.enabled",
                "audit.product.enabled"));
        purgeAuditLogService.purgeAuditLogForAllAuditTypes();
        verify(auditLogRemovalService, times(3)).removeAuditLog(anyString(), anyInt());
    }

    @Test
    public void test_invalidAuditTypePropertyKey_SkipItForExecution() {
        auditPropertyList = new ArrayList<>(
            List.of("audit.countriesandpricesdraft.enabled",
                "audit.iotcustomer2iotcompany.enabled",
                "audit.conditional.session-cart.enabled",
                "audit.product.enabled"));
        purgeAuditLogService.purgeAuditLogForAllAuditTypes();
        verify(auditLogRemovalService, times(3)).removeAuditLog(anyString(), anyInt());
    }
}