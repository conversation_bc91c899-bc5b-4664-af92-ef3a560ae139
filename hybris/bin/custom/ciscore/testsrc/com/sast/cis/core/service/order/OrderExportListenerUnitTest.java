package com.sast.cis.core.service.order;

import com.sast.cis.core.billingintegration.dto.OrderExportData;
import com.sast.cis.core.billingintegration.dto.OrderExportResult;
import com.sast.cis.core.billingintegration.events.OrderResponseEvent;
import com.sast.cis.core.dao.CisOrderDao;
import com.sast.cis.core.service.contract.ContractService;
import com.sast.cis.core.service.order.export.DefaultOrderExportResponseHandler;
import com.sast.cis.core.service.order.export.InvalidOrderExportDataException;
import com.sast.cis.core.service.order.export.MigrationOrderExportResponseHandler;
import com.sast.cis.core.service.order.export.OrderExportDataValidator;
import com.sast.cis.core.service.order.export.RecreatedOrderExportResponseHandler;
import de.hybris.bootstrap.annotations.UnitTest;
import de.hybris.platform.core.enums.OrderStatus;
import de.hybris.platform.core.model.order.OrderModel;
import de.hybris.platform.servicelayer.model.ModelService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.*;

@UnitTest
@RunWith(MockitoJUnitRunner.class)
public class OrderExportListenerUnitTest {

    @Mock
    private ModelService modelService;

    @Mock
    private CisOrderDao cisOrderDao;

    @Mock
    private ContractService contractService;

    @Mock
    private OrderExportDataValidator orderExportDataValidator;

    @Mock
    private DefaultOrderExportResponseHandler defaultOrderExportResponseHandler;

    @Mock
    private MigrationOrderExportResponseHandler migrationOrderExportResponseHandler;

    @Mock
    private RecreatedOrderExportResponseHandler recreatedOrderExportResponseHandler;

    @InjectMocks
    private OrderExportListener orderExportListener;

    @Mock
    private OrderModel order;

    @Test
    public void onEvent_event_is_null_throws_exception() {
        assertThatThrownBy(() -> orderExportListener.onEvent(null))
            .isInstanceOf(IllegalArgumentException.class)
            .hasMessageContaining("orderResponseEvent is marked non-null but is null");
    }

    @Test
    public void onEvent_orderId_not_exists_throws_exception() {
        OrderExportData orderExportData = OrderExportData.builder().id("invalidId").orderExportResult(OrderExportResult.COMPLETED).build();
        when(cisOrderDao.findOrderForCode(anyString())).thenReturn(Optional.empty());

        assertThatThrownBy(() -> orderExportListener.onEvent(new OrderResponseEvent(orderExportData)))
            .isInstanceOf(IllegalStateException.class)
            .hasMessageContaining("Order with code 'invalidId' does not exist");
    }

    @Test
    public void onEvent_migrationOrder_is_updated_and_handled() {
        final OrderExportData orderExportData = OrderExportData.builder()
            .id("testId")
            .billingOrderId("externalTestId")
            .contractItems(List.of())
            .orderExportResult(OrderExportResult.COMPLETED)
            .build();
        when(order.isMigrationOrder()).thenReturn(true);
        when(cisOrderDao.findOrderForCode(anyString())).thenReturn(Optional.of(order));

        orderExportListener.onEvent(new OrderResponseEvent(orderExportData));

        verify(modelService).save(order);
        verify(modelService).refresh(order);
        verify(contractService).updateContractsAfterOrderExport(order, orderExportData);
        verify(migrationOrderExportResponseHandler).handleOrder(order, orderExportData);

        // handler is responsible for triggering the event in case of migration order
        verify(defaultOrderExportResponseHandler, never()).handleOrder(any(OrderModel.class), any(OrderExportData.class));

    }

    @Test
    public void givenResponseWithOpenStatusForMigrationOrder_whenHandle_thenDelegateToAppropriateHandler() {
        final OrderExportData orderExportData = OrderExportData.builder()
            .id("testId")
            .billingOrderId("externalTestId")
            .contractItems(List.of())
            .orderExportResult(OrderExportResult.OPEN)
            .build();
        when(order.isMigrationOrder()).thenReturn(true);
        when(cisOrderDao.findOrderForCode(anyString())).thenReturn(Optional.of(order));

        orderExportListener.onEvent(new OrderResponseEvent(orderExportData));

        verify(modelService).save(order);
        verify(modelService).refresh(order);
        verify(contractService).updateContractsAfterOrderExport(order, orderExportData);
        verify(migrationOrderExportResponseHandler).handleOrder(order, orderExportData);
        verify(defaultOrderExportResponseHandler, never()).handleOrder(any(OrderModel.class), any(OrderExportData.class));
    }

    @Test
    public void onEvent_recreatedOrder_is_handled() {
        final OrderExportData orderExportData = OrderExportData.builder()
            .id("testId")
            .billingOrderId("externalTestId")
            .contractItems(List.of())
            .orderExportResult(OrderExportResult.COMPLETED)
            .build();
        when(cisOrderDao.findOrderForCode(anyString())).thenReturn(Optional.of(order));
        when(order.getStatus()).thenReturn(OrderStatus.REJECTED);
        when(order.isOrderRecreated()).thenReturn(true);

        orderExportListener.onEvent(new OrderResponseEvent(orderExportData));

        verify(modelService).save(order);
        verify(modelService).refresh(order);
        verify(recreatedOrderExportResponseHandler).handleOrder(order, orderExportData);
        verify(defaultOrderExportResponseHandler, never()).handleOrder(any(OrderModel.class), any(OrderExportData.class));
    }

    @Test
    public void givenInvalidOrderExportData_whenHandle_thenPropagateException() {
        final var orderExportData = OrderExportData.builder().build();
        final var validationException = InvalidOrderExportDataException.forOrderExportData(orderExportData, "Exception");
        doThrow(validationException).when(orderExportDataValidator).validateOrderExportData(orderExportData);

        assertThatThrownBy(() -> orderExportListener.onEvent(new OrderResponseEvent(orderExportData))).isEqualTo(validationException);

        verify(modelService, never()).save(any(OrderModel.class));
        verify(defaultOrderExportResponseHandler, never()).handleOrder(any(OrderModel.class), any(OrderExportData.class));
    }
}
