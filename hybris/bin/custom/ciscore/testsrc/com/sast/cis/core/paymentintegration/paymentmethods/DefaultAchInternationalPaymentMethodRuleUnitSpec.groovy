package com.sast.cis.core.paymentintegration.paymentmethods

import com.sast.cis.core.data.UmpCompanyData
import com.sast.cis.core.enums.LicenseType
import com.sast.cis.core.model.AppLicenseModel
import com.sast.cis.core.model.IoTCompanyModel
import com.sast.cis.core.service.AppLicenseService
import com.sast.cis.core.service.company.IotCompanyService
import com.sast.cis.core.service.singlesignon.UserDataQueryService
import de.hybris.bootstrap.annotations.UnitTest
import de.hybris.platform.core.model.c2l.CountryModel
import de.hybris.platform.core.model.order.CartModel
import de.hybris.platform.core.model.order.OrderModel
import de.hybris.platform.testframework.JUnitPlatformSpecification
import org.junit.Test
import spock.lang.Unroll

import static com.sast.cis.core.enums.PaymentMethodType.ACH_INTERNATIONAL
import static com.sast.cis.core.enums.PaymentMethodType.CREDIT_CARD

@UnitTest
class DefaultAchInternationalPaymentMethodRuleUnitSpec extends JUnitPlatformSpecification {
    private UserDataQueryService userDataQueryService = Mock(UserDataQueryService)
    private IotCompanyService iotCompanyService = Mock(IotCompanyService)
    private AppLicenseService appLicenseService = Mock(AppLicenseService)

    private DefaultAchInternationalPaymentMethodRule paymentMethodRule

    private IoTCompanyModel buyerCompany = Mock(IoTCompanyModel)
    private IoTCompanyModel sellerCompany = Mock(IoTCompanyModel)
    private CartModel cart = Mock(CartModel)
    private AppLicenseModel appLicense = Mock(AppLicenseModel)
    private CountryModel buyerCountry = Mock(CountryModel)
    private CountryModel sellerCountry = Mock(CountryModel)
    private UmpCompanyData umpCompanyData = Mock(UmpCompanyData)

    def setup() {
        paymentMethodRule = new DefaultAchInternationalPaymentMethodRule(userDataQueryService, iotCompanyService, appLicenseService)
        userDataQueryService.requestCompanyData() >> umpCompanyData
        cart.getCompany() >> buyerCompany
        sellerCompany.getCountry() >> sellerCountry
        appLicenseService.getCompany(appLicense) >> sellerCompany
        buyerCompany.getCountry() >> buyerCountry
    }

    @Test
    @Unroll
    def 'licenseType=#givenLicenseType, methodInCountry=#givenMethodInCountry, positiveCreditLimit=#givenPositiveCreditLimit then supportsPurchase=#expectedSupportsPurchase'() {
        given:
        def givenUmpCompanyData = new UmpCompanyData()

        when:
        def actualSupportsPurchase = paymentMethodRule.supportsPurchase(appLicense, buyerCompany)

        then:
        if (givenMethodInCountry) {
            buyerCountry.getSupportedPaymentMethods() >> Set.of(CREDIT_CARD, ACH_INTERNATIONAL)
        } else {
            buyerCountry.getSupportedPaymentMethods() >> Set.of(CREDIT_CARD)
        }
        buyerCountry.getIsocode() >> "XX"
        sellerCountry.getIsocode() >> "XX"
        appLicense.getLicenseType() >> givenLicenseType
        userDataQueryService.requestCompanyData(buyerCompany) >> givenUmpCompanyData
        iotCompanyService.hasPositiveCreditLimit(givenUmpCompanyData) >> givenPositiveCreditLimit

        expect:
        actualSupportsPurchase == expectedSupportsPurchase

        where:
        givenLicenseType         | givenMethodInCountry | givenPositiveCreditLimit || expectedSupportsPurchase
        LicenseType.EVALUATION   | true                 | false                    || false
        LicenseType.FULL         | true                 | false                    || false
        LicenseType.SUBSCRIPTION | true                 | false                    || false
        LicenseType.EVALUATION   | true                 | true                     || true
        LicenseType.FULL         | true                 | true                     || true
        LicenseType.SUBSCRIPTION | true                 | true                     || true
        LicenseType.EVALUATION   | false                | true                     || false
        LicenseType.FULL         | false                | true                     || false
        LicenseType.SUBSCRIPTION | false                | true                     || false
        LicenseType.EVALUATION   | false                | false                    || false
        LicenseType.FULL         | false                | false                    || false
        LicenseType.SUBSCRIPTION | false                | false                    || false
    }

    @Test
    @Unroll
    def 'supportsPurchase=#expectedSupportsPurchase for seller in #sellerCountryIso and buyer in #buyerCountryIso'() {
        when:
        def actualSupportsPurchase = paymentMethodRule.supportsPurchase(appLicense, buyerCompany)

        then:
        iotCompanyService.hasPositiveCreditLimit(umpCompanyData) >> true
        userDataQueryService.requestCompanyData(buyerCompany) >> umpCompanyData
        sellerCountry.getIsocode() >> sellerCountryIso
        buyerCountry.getIsocode() >> buyerCountryIso
        buyerCountry.getSupportedPaymentMethods() >> Set.of(ACH_INTERNATIONAL)
        appLicense.getLicenseType() >> LicenseType.FULL

        expect:
        actualSupportsPurchase == expectedSupportsPurchase

        where:
        buyerCountryIso | sellerCountryIso || expectedSupportsPurchase
        "XX"            | "XX"             || true
        "KR"            | "KR"             || false
        "KR"            | "XX"             || true
        "XX"            | "KR"             || true
    }

    @Test
    @Unroll
    def 'cart is support is \'#expectedSupportsCart\' if cart value is \'#givenCartPrice\' and buyerHasCredit=\'#givenBuyerHasCredit\''() {
        when:
        def actualSupportsCart = paymentMethodRule.supportsCart(cart)

        then:
        cart.getTotalPrice() >> givenCartPrice
        iotCompanyService.hasBiggerCreditLimitThan(buyerCompany, _ as BigDecimal) >> givenBuyerHasCredit

        expect:
        actualSupportsCart == expectedSupportsCart

        where:
        givenCartPrice | givenBuyerHasCredit || expectedSupportsCart
        0.00           | false               || false
        0.00           | true                || false
        0.01           | false               || false
        0.01           | true                || true
    }

    @Test
    def 'supportsPurchase throws exception when given product is null'() {
        when:
        paymentMethodRule.supportsPurchase(null, buyerCompany)

        then:
        thrown(IllegalArgumentException)
    }

    @Test
    def 'supportsPurchase throws exception when given buyer is null'() {
        when:
        paymentMethodRule.supportsPurchase(appLicense, null)

        then:
        thrown(IllegalArgumentException)
    }

    @Test
    def 'supportsSale throws exception when given company is null'() {
        when:
        paymentMethodRule.supportsSale(LicenseType.FULL, null)

        then:
        thrown(IllegalArgumentException)
    }

    @Test
    def 'returns payment method ACH_INTERNATIONAL'() {
        when:
        def actualPaymentMethod = paymentMethodRule.getType()

        then:
        actualPaymentMethod == ACH_INTERNATIONAL
    }

    @Test
    def 'payment method is not supported for migration orders'() {
        given:
        def migrationOrder = Mock(OrderModel)
        migrationOrder.isMigrationOrder() >> true

        when:
        def supportsMigrationOrder = paymentMethodRule.supportsMigrationOrder(migrationOrder)

        then:
        !supportsMigrationOrder
    }
}
