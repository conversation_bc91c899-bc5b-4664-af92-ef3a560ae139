package com.sast.cis.core.facade

import com.sast.cis.core.SpringContextServiceMockHelper
import com.sast.cis.core.config.BaseStoreConfigService
import com.sast.cis.core.config.keycloak.SiteUmpAdapterConfigResolutionService
import com.sast.cis.core.constants.BaseStoreEnum
import com.sast.cis.core.data.CreditCardPaymentInfoData
import com.sast.cis.core.data.OrderPaymentData
import com.sast.cis.core.enums.*
import com.sast.cis.core.exceptions.cart.CartUnpayableException
import com.sast.cis.core.factory.CisCartFactory
import com.sast.cis.core.model.*
import com.sast.cis.core.paymentintegration.data.AuthorizationStatus
import com.sast.cis.core.service.SpringContextService
import com.sast.cis.core.service.TranslationService
import com.sast.cis.core.service.company.IotCompanyService
import com.sast.cis.core.service.customer.developer.DeveloperService
import com.sast.cis.core.util.Base58UUIDCodeGenerator
import com.sast.cis.payment.dpg.model.DpgCreditCardPaymentInfoModel
import com.sast.cis.test.utils.FeatureToggleRule
import com.sast.cis.test.utils.LoginUtil
import com.sast.cis.test.utils.SampleDataCreator
import com.sast.cis.test.utils.UmpWireMockRule
import de.hybris.bootstrap.annotations.IntegrationTest
import de.hybris.platform.catalog.CatalogVersionService
import de.hybris.platform.catalog.model.CatalogVersionModel
import de.hybris.platform.core.model.order.CartModel
import de.hybris.platform.core.model.user.UserGroupModel
import de.hybris.platform.core.model.user.UserModel
import de.hybris.platform.order.CartService
import de.hybris.platform.payment.dto.TransactionStatus
import de.hybris.platform.payment.dto.TransactionStatusDetails
import de.hybris.platform.payment.enums.PaymentTransactionType
import de.hybris.platform.product.UnitService
import de.hybris.platform.servicelayer.ServicelayerTransactionalSpockSpecification
import de.hybris.platform.servicelayer.config.ConfigurationService
import de.hybris.platform.servicelayer.i18n.CommonI18NService
import de.hybris.platform.servicelayer.model.ModelService
import de.hybris.platform.servicelayer.user.UserService
import de.hybris.platform.servicelayer.user.daos.UserGroupDao
import de.hybris.platform.site.BaseSiteService
import de.hybris.platform.store.services.BaseStoreService
import org.apache.commons.collections4.CollectionUtils
import org.junit.Rule
import org.junit.Test
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.test.util.ReflectionTestUtils
import org.springframework.web.client.RestTemplate
import spock.lang.Narrative
import spock.lang.Subject

import javax.annotation.Resource

import static com.sast.cis.core.constants.CiscoreConstants.CIS_PRODUCT_CATALOG
import static com.sast.cis.core.constants.CiscoreConstants.IOT_STORE_BASE_SITE_UID
import static com.sast.cis.core.dao.CatalogVersion.ONLINE
import static com.sast.cis.core.enums.CompanyApprovalStatus.APPROVED_COMMERCIAL
import static com.sast.cis.test.utils.TestDataConstants.*
import static com.sast.cis.test.utils.UmpWireMockRule.UMP_BASE_URL_KEY

@IntegrationTest
@Narrative("""
This test is basically 'store checkout in a nutshell'. 

If payment-related business rules change, this test *should* fail and *should* require modification.
""")
class CisCheckoutFacadeISpec extends ServicelayerTransactionalSpockSpecification {
    private static final String DPG_TOKEN = Base58UUIDCodeGenerator.generateCode("dpgToken")

    private SpringContextService springContextService
    private SampleDataCreator sampleDataCreator = new SampleDataCreator()

    @Rule
    public UmpWireMockRule umpWireMockRule = new UmpWireMockRule()

    @Rule
    public FeatureToggleRule featureToggleRule = new FeatureToggleRule()

    @Resource
    @Subject
    private CisCheckoutFacade cisCheckoutFacade

    @Resource
    private ModelService modelService

    @Resource
    private ConfigurationService configurationService

    @Resource
    private IotCompanyService iotCompanyService

    @Resource
    private UserService userService

    @Resource
    private UserGroupDao userGroupDao

    @Resource
    private TranslationService translationService

    @Resource
    private UnitService unitService

    @Resource
    private BaseStoreService baseStoreService

    @Resource
    private CartService cartService

    @Resource
    private CisCartFactory cisCartFactory

    @Resource
    private CisCartFacade cisCartFacade

    @Resource
    private RestTemplate cisRestTemplate

    @Resource
    private BaseSiteService baseSiteService

    @Resource
    private CatalogVersionService catalogVersionService

    @Resource
    private DeveloperService developerService

    @Resource
    private BaseStoreConfigService baseStoreConfigService

    @Resource
    private CommonI18NService commonI18NService

    @Resource
    private SiteUmpAdapterConfigResolutionService siteUmpAdapterConfigResolutionService

    private PspSellerAccountModel boschTransferPspSellerAccount

    private IoTCompanyModel sellerCompany
    private IoTCompanyModel boschTransferSellerCompany

    private IoTCompanyModel buyerCompany
    private AppLicenseModel evalLicense
    private AppLicenseModel fullLicense
    private AppLicenseModel boschTransferFullLicense
    private AppLicenseModel subscriptionLicense
    private AppModel app
    private AppModel boschTransferApp
    private UserModel user
    private CartModel cart
    private CatalogVersionModel catalogVersion
    private StripeCreditCardPaymentInfoModel existingCreditCardPaymentInfo

    def setup() {
        featureToggleRule.enable(Feature.FEATURE_ENABLE_DPG_CC)
        springContextService = new SpringContextServiceMockHelper().newSpringContextServiceMock()
        String configPrefixForBaseStore = baseStoreConfigService.getConfigPrefixForBaseStore(BaseStoreEnum.AZENA)
        String key = String.format(UMP_BASE_URL_KEY, configPrefixForBaseStore)
        configurationService.getConfiguration().setProperty(key, umpWireMockRule.getUmpUrl())

        buyerCompany = iotCompanyService.getCompanyByUid(DEMO_COMPANY_UID).orElseThrow()
        user = userService.getUserForUID(DEMO_COMPANY_INTEGRATOR_UID)

        app = sampleDataCreator.createApp('App_code', 'com.testapp', ONLINE)
        fullLicense = sampleDataCreator.createFullAppLicense(app)
        evalLicense = sampleDataCreator.createEvalForFullAppLicense(fullLicense)
        subscriptionLicense = sampleDataCreator.createSubscriptionAppLicense(app)
        sellerCompany = app.getCompany()

        buyerCompany = prepareCompany(buyerCompany, APPROVED_COMMERCIAL, BigDecimal.valueOf(10000))
        sellerCompany = prepareCompany(sellerCompany, APPROVED_COMMERCIAL, BigDecimal.ZERO)
        sampleDataCreator.createDpgSellerAccount(sellerCompany)

        LoginUtil loginUtil = new LoginUtil(siteUmpAdapterConfigResolutionService, cisRestTemplate)
        SecurityContextHolder.getContext().setAuthentication(loginUtil.generateKeycloakAuthenticationObject())
        userService.setCurrentUser(userService.getUserForUID(DEMO_COMPANY_INTEGRATOR_UID))
        baseSiteService.setCurrentBaseSite(baseSiteService.getBaseSiteForUID(IOT_STORE_BASE_SITE_UID), false)
        catalogVersion = catalogVersionService.getCatalogVersion(CIS_PRODUCT_CATALOG, ONLINE.getVersionName())
        catalogVersionService.setSessionCatalogVersions(Collections.singletonList(catalogVersion))

        ReflectionTestUtils.setField(translationService, "springContextService", springContextService)

        umpWireMockRule.prepareGetUserDataResponse(DEMO_COMPANY_USER_SSOID, DEMO_COMPANY_UID)
        userService.setCurrentUser(user)

        // bosch transfer
        def austrianDeveloper = developerService.getDeveloperByInternalUserId(AA_AUSTRIA1_COMPANY_DEVELOPER_UID)
        boschTransferApp = sampleDataCreator.createApp('BoschTransferTestApp', 'aa.esi.CheckoutFacade.ispec', austrianDeveloper, ONLINE)
        boschTransferFullLicense = sampleDataCreator.createFullAppLicense(boschTransferApp)
        boschTransferSellerCompany = boschTransferApp.getCompany()
        boschTransferSellerCompany = prepareCompany(boschTransferSellerCompany, APPROVED_COMMERCIAL, BigDecimal.valueOf(10000))
        modelService.save(boschTransferSellerCompany)
        boschTransferPspSellerAccount = sampleDataCreator.createBoschSellerAccount(boschTransferSellerCompany)

        boschTransferSellerCompany.getCountry()
    }

    private String selectFirstMatchingPaymentInfo(OrderPaymentData orderPaymentData, PaymentMethodType paymentMethod, PaymentProvider paymentProvider) {
        def matchingCheckoutInfo = CollectionUtils.emptyIfNull(orderPaymentData.checkoutInfos)
                .find { checkoutInfoData ->
                    checkoutInfoData.paymentMethod == paymentMethod &&
                            checkoutInfoData.paymentProvider == paymentProvider
                }
        return matchingCheckoutInfo.paymentInfos.first().id;
    }

    @Test
    def 'checkout is performed with SEPA via DPG'() {
        given:
        cisCartFacade.addToCart(fullLicense.getCode(), 1L)
        cart = cartService.getSessionCart()
        disableSellerAccount(PaymentProvider.BOSCH_TRANSFER)

        when: 'user proceeds to summary page'
        def summaryPageData = cisCheckoutFacade.getCheckoutPaymentSummaryPageData()

        and: 'user proceeds with checkout'
        cisCheckoutFacade.setPaymentInfo(selectFirstMatchingPaymentInfo(summaryPageData, PaymentMethodType.SEPA_CREDIT, PaymentProvider.DPG))
        def actualAuthorizationResult = cisCheckoutFacade.authorize()

        then: 'summary page shows correct information'
        verifyAll(summaryPageData) {
            summaryPageData.paymentMethod == PaymentMethodType.SEPA_CREDIT
            summaryPageData.entries.size() == 1
            summaryPageData.entries*.productCode == [fullLicense.getCode()]
            summaryPageData.totalPrice.symbol == 'EUR'
            summaryPageData.totalPrice.value =~ /93[,.]00/
        }

        and: 'authorization result is successful'
        verifyAll(actualAuthorizationResult) {
            verifyAll {
                authorizationStatus == AuthorizationStatus.SUCCESS
                userActionParameters == Map.of()
            }
        }

        and: 'transaction entry is accepted successful authorization'
        verifyAll(actualAuthorizationResult.paymentTransactionEntry) {
            verifyAll {
                type == PaymentTransactionType.AUTHORIZATION
                transactionStatus == TransactionStatus.ACCEPTED.name()
                transactionStatusDetails == TransactionStatusDetails.SUCCESFULL.name()
            }
        }

        and: 'authorized transaction is for payment provider DPG, consistent with attached payment info and for payment method SEPA'
        verifyAll(actualAuthorizationResult.paymentTransactionEntry.paymentTransaction) {
            verifyAll {
                paymentProvider == PaymentProvider.DPG.name()
                paymentInstrument instanceof SepaTransferPaymentInstrumentModel
                info instanceof SepaCreditTransferPaymentInfoModel
                info.paymentProvider == PaymentProvider.DPG
                info == cart.paymentInfo
            }
        }

        and: 'cart contains the correct products'
        cart.entries.size() == 1
        cart.entries*.product == [fullLicense]
    }

    @Test
    void 'checkout is performed with SEPA via BoschTransfer'() {
        given:
        cisCartFacade.addToCart(boschTransferFullLicense.getCode(), 1L)
        cart = cartService.getSessionCart()

        when: 'user proceeds to summary page'
        def summaryPageData = cisCheckoutFacade.getCheckoutPaymentSummaryPageData()

        and: 'user proceeds with checkout'
        cisCheckoutFacade.setPaymentInfo(selectFirstMatchingPaymentInfo(summaryPageData, PaymentMethodType.SEPA_CREDIT, PaymentProvider.BOSCH_TRANSFER))
        def actualAuthorizationResult = cisCheckoutFacade.authorize()

        then: 'summary page shows correct information'
        verifyAll(summaryPageData) {
            summaryPageData.paymentMethod == PaymentMethodType.SEPA_CREDIT
            summaryPageData.entries.size() == 1
            summaryPageData.entries*.productCode == [boschTransferFullLicense.getCode()]
            summaryPageData.totalPrice.symbol == 'EUR'
            summaryPageData.totalPrice.value =~ /93[,.]00/
        }

        and: 'authorization result is successful'
        verifyAll(actualAuthorizationResult) {
            verifyAll {
                authorizationStatus == AuthorizationStatus.SUCCESS
            }
        }

        and: 'transaction entry is accepted successful authorization'
        verifyAll(actualAuthorizationResult.paymentTransactionEntry) {
            verifyAll {
                type == PaymentTransactionType.AUTHORIZATION
                transactionStatus == TransactionStatus.ACCEPTED.name()
                transactionStatusDetails == TransactionStatusDetails.SUCCESFULL.name()
            }
        }

        and: 'authorized transaction is for payment provider BOSCH_TRANSFER, consistent with attached payment info and for payment method SEPA'
        verifyAll(actualAuthorizationResult.paymentTransactionEntry.paymentTransaction) {
            verifyAll {
                paymentProvider == PaymentProvider.BOSCH_TRANSFER.name()
                paymentInstrument instanceof SepaTransferPaymentInstrumentModel
                info instanceof SepaCreditTransferPaymentInfoModel
                info.paymentProvider == PaymentProvider.BOSCH_TRANSFER
                info == cart.paymentInfo
            }
        }

        and: 'cart contains the correct products'
        cart.entries.size() == 1
        cart.entries*.product == [boschTransferFullLicense]
    }


    @Test
    def 'checkout is performed with new CC via DPG'() {
        given:
        def newCcData = new CreditCardPaymentInfoData()
        newCcData.setSubscriptionId(null)
        newCcData.setAccountHolderName(user.getName())
        newCcData.setReusable(false)
        newCcData.setPaymentProvider(PaymentProvider.DPG)
        newCcData.setPaymentMethod(PaymentMethodType.CREDIT_CARD)
        newCcData.setToken(DPG_TOKEN)
        newCcData.setCardNumber("123456******9876")
        newCcData.setExpiryMonth("12")
        newCcData.setExpiryYear("50")

        cisCartFacade.addToCart(fullLicense.getCode(), 1L)
        cart = cartService.getSessionCart()

        when: 'checkout for session cart is prepared'
        OrderPaymentData summaryPageData = cisCheckoutFacade.getCheckoutPaymentSummaryPageData()

        and: 'user adds new credit card'
        cisCheckoutFacade.createPaymentInfo(newCcData)

        and: 'user proceeds with checkout'
        def actualAuthorizationResult = cisCheckoutFacade.authorize()

        then: 'summary page shows correct information'
        verifyAll(summaryPageData) {
            entries.size() == 1
            entries*.productCode == [fullLicense.getCode()]
            totalPrice.symbol == 'EUR'
            totalPrice.value =~ /93[,.]00/
        }

        and: 'authorization result is successful'
        verifyAll(actualAuthorizationResult) {
            verifyAll {
                authorizationStatus == AuthorizationStatus.SUCCESS
                userActionParameters == Map.of()
            }
        }

        and: 'transaction entry is accepted successful authorization'
        verifyAll(actualAuthorizationResult.paymentTransactionEntry) {
            verifyAll {
                type == PaymentTransactionType.AUTHORIZATION
                transactionStatus == TransactionStatus.ACCEPTED.name()
                transactionStatusDetails == TransactionStatusDetails.SUCCESFULL.name()
            }
        }

        and: 'authorized transaction is for payment provider DPG, consistent with attached payment info and for payment method SEPA'
        verifyAll(actualAuthorizationResult.paymentTransactionEntry.paymentTransaction) {
            verifyAll {
                paymentProvider == PaymentProvider.DPG.name()
                info instanceof DpgCreditCardPaymentInfoModel
                info.paymentProvider == PaymentProvider.DPG
                info == cart.paymentInfo
            }
        }

        and: 'cart contains the correct products'
        cart.entries.size() == 1
        cart.entries*.product == [fullLicense]
    }

    @Test
    def 'checkout attempt of an unpayable cart throws CartUnpayableException'() {
        given:
        prepareCompany(buyerCompany, APPROVED_COMMERCIAL, BigDecimal.valueOf(10))
        cisCartFacade.addToCart(subscriptionLicense.getCode(), 11L)
        def buyerCountry = buyerCompany.country
        buyerCountry.supportedPaymentMethods = [PaymentMethodType.SEPA_CREDIT] as Set
        modelService.save(buyerCountry)

        cart = cartService.getSessionCart()

        when: 'checkout for session cart is prepared'
        cisCheckoutFacade.getCheckoutPaymentSummaryPageData()

        then: 'CartUnpayableException is thrown to be handled by the controller'
        thrown(CartUnpayableException)

        and: 'No payment transactions are created for the current cart'
        modelService.refresh(cart)
        cart.paymentTransactions == []
    }

    @Test
    def 'checkout for a full license is performed with ACH via DPG for US buyer'() {
        given:
        buyerCompany.setCountry(commonI18NService.getCountry('US'))
        modelService.save(buyerCompany)
        cisCartFacade.addToCart(fullLicense.getCode(), 1L)
        cart = cartService.getSessionCart()

        when: 'checkout for session cart is prepared'
        def summaryPageData = cisCheckoutFacade.getCheckoutPaymentSummaryPageData()

        then: 'summary page shows correct information (ACH)'
        with(summaryPageData) {
            summaryPageData.entries.size() == 1
            summaryPageData.entries*.productCode == [fullLicense.getCode()]
            summaryPageData.totalPrice.symbol == 'USD'
            summaryPageData.totalPrice.value =~ /130[,.]00/
        }

        and: 'user proceeds with checkout'
        cisCheckoutFacade.setPaymentInfo(selectFirstMatchingPaymentInfo(summaryPageData, PaymentMethodType.ACH_INTERNATIONAL, PaymentProvider.DPG))
        def actualAuthorizationResult = cisCheckoutFacade.authorize()

        then: 'authorization result is successful'
        with(actualAuthorizationResult) {
            verifyAll {
                authorizationStatus == AuthorizationStatus.SUCCESS
                userActionParameters == Map.of()
            }
        }

        and: 'transaction entry is accepted successful authorization'
        with(actualAuthorizationResult.paymentTransactionEntry) {
            verifyAll {
                type == PaymentTransactionType.AUTHORIZATION
                transactionStatus == TransactionStatus.ACCEPTED.name()
                transactionStatusDetails == TransactionStatusDetails.SUCCESFULL.name()
            }
        }

        and: 'authorized transaction is for payment provider DPG, consistent with attached payment info and for payment method SEPA'
        with(actualAuthorizationResult.paymentTransactionEntry.paymentTransaction) {
            verifyAll {
                paymentProvider == PaymentProvider.DPG.name()
                paymentInstrument instanceof AchTransferPaymentInstrumentModel
                info instanceof AchInternationalCreditTransferPaymentInfoModel
                info.paymentProvider == PaymentProvider.DPG
                info == cart.paymentInfo
            }
        }

        and: 'cart contains the correct products'
        cart.entries.size() == 1
        cart.entries*.product == [fullLicense]
    }

    @Test
    def 'checkout for a subscription license is performed with ACH via DPG for US buyer'() {
        given:
        buyerCompany.setCountry(commonI18NService.getCountry('US'))
        modelService.save(buyerCompany)
        cisCartFacade.addToCart(subscriptionLicense.getCode(), 1L)
        cart = cartService.getSessionCart()

        when: 'checkout for session cart is prepared'
        def summaryPageData = cisCheckoutFacade.getCheckoutPaymentSummaryPageData()

        then: 'summary page shows correct information (ACH)'
        with(summaryPageData) {
            summaryPageData.entries.size() == 1
            summaryPageData.entries*.productCode == [subscriptionLicense.getCode()]
            summaryPageData.totalPrice.symbol == 'USD'
            summaryPageData.totalPrice.value =~ /130[,.]00/
        }

        and: 'user proceeds with checkout'
        cisCheckoutFacade.setPaymentInfo(selectFirstMatchingPaymentInfo(summaryPageData, PaymentMethodType.ACH_INTERNATIONAL, PaymentProvider.DPG))
        def actualAuthorizationResult = cisCheckoutFacade.authorize()

        then: 'authorization result is successful'
        with(actualAuthorizationResult) {
            verifyAll {
                authorizationStatus == AuthorizationStatus.SUCCESS
                userActionParameters == Map.of()
            }
        }

        and: 'transaction entry is accepted successful authorization'
        with(actualAuthorizationResult.paymentTransactionEntry) {
            verifyAll {
                type == PaymentTransactionType.AUTHORIZATION
                transactionStatus == TransactionStatus.ACCEPTED.name()
                transactionStatusDetails == TransactionStatusDetails.SUCCESFULL.name()
            }
        }

        and: 'authorized transaction is for payment provider DPG, consistent with attached payment info and for payment method SEPA'
        with(actualAuthorizationResult.paymentTransactionEntry.paymentTransaction) {
            verifyAll {
                paymentProvider == PaymentProvider.DPG.name()
                paymentInstrument instanceof AchTransferPaymentInstrumentModel
                info instanceof AchInternationalCreditTransferPaymentInfoModel
                info.paymentProvider == PaymentProvider.DPG
                info == cart.paymentInfo
            }
        }

        and: 'cart contains the correct products'
        cart.entries.size() == 1
        cart.entries*.product == [subscriptionLicense]
    }

    @Test
    def 'invalid payment info is removed when entering checkout'() {
        given:
        cisCartFacade.addToCart(boschTransferFullLicense.getCode(), 1L)
        cart = cartService.getSessionCart()
        cart.setPaymentInfo(existingCreditCardPaymentInfo)
        modelService.save(cart)

        when: 'checkout for session cart is prepared'
        cisCheckoutFacade.getCheckoutPaymentSummaryPageData()

        then: 'user does not get payment infos to select, payment info is set on the cart'
        modelService.refresh(cart)
        cart.paymentInfo != existingCreditCardPaymentInfo
    }

    @Test
    def 'eval license is payed with ZERO and does not require payment method selection'() {
        given:
        cisCartFacade.addToCart(evalLicense.getCode(), 1L)
        cart = cartService.getSessionCart()

        when: 'checkout for session cart is prepared'
        def summaryPageData = cisCheckoutFacade.getCheckoutPaymentSummaryPageData()

        then: 'summary page shows correct information'
        with(summaryPageData) {
            summaryPageData.paymentMethod == PaymentMethodType.ZERO
            summaryPageData.entries.size() == 1
            summaryPageData.entries*.productCode == [evalLicense.getCode()]
            summaryPageData.totalPrice.symbol == 'EUR'
            summaryPageData.totalPrice.value =~ /0[,.]00/
        }

        and: 'user proceeds with checkout'
        def actualAuthorizationResult = cisCheckoutFacade.authorize()

        then: 'authorization result is successful'
        with(actualAuthorizationResult) {
            verifyAll {
                authorizationStatus == AuthorizationStatus.SUCCESS
                userActionParameters == Map.of()
            }
        }

        and: 'transaction entry is accepted successful authorization'
        with(actualAuthorizationResult.paymentTransactionEntry) {
            verifyAll {
                type == PaymentTransactionType.AUTHORIZATION
                transactionStatus == TransactionStatus.ACCEPTED.name()
                transactionStatusDetails == TransactionStatusDetails.SUCCESFULL.name()
            }
        }

        and: 'authorized transaction is for payment provider ZERO, consistent with attached payment info and for payment method ZERO'
        with(actualAuthorizationResult.paymentTransactionEntry.paymentTransaction) {
            verifyAll {
                paymentProvider == PaymentProvider.ZERO.name()
                info instanceof ZeroPaymentInfoModel
                info.paymentProvider == PaymentProvider.ZERO
                info == cart.paymentInfo
            }
        }

        and: 'cart contains the correct products'
        cart.entries.size() == 1
        cart.entries*.product == [evalLicense]
    }

    @Test
    def 'full license with zero price is payed with ZERO and does not require payment method selection'() {
        given:
        fullLicense.europe1Prices.each { priceRow ->
            priceRow.setPrice(0.0)
            modelService.save(priceRow)
        }
        cisCartFacade.addToCart(fullLicense.getCode(), 1L)
        cart = cartService.getSessionCart()

        when: 'checkout for session cart is prepared'
        def summaryPageData = cisCheckoutFacade.getCheckoutPaymentSummaryPageData()

        and: 'user proceeds with checkout'
        def actualAuthorizationResult = cisCheckoutFacade.authorize()

        then: 'summary page shows correct information'
        with(summaryPageData) {
            summaryPageData.paymentMethod == PaymentMethodType.ZERO
            summaryPageData.entries.size() == 1
            summaryPageData.entries*.productCode == [fullLicense.getCode()]
            summaryPageData.totalPrice.symbol == 'EUR'
            summaryPageData.totalPrice.value =~ /0[,.]00/
        }

        and: 'authorization result is successful'
        with(actualAuthorizationResult) {
            verifyAll {
                authorizationStatus == AuthorizationStatus.SUCCESS
                userActionParameters == Map.of()
            }
        }

        and: 'transaction entry is accepted successful authorization'
        with(actualAuthorizationResult.paymentTransactionEntry) {
            verifyAll {
                type == PaymentTransactionType.AUTHORIZATION
                transactionStatus == TransactionStatus.ACCEPTED.name()
                transactionStatusDetails == TransactionStatusDetails.SUCCESFULL.name()
            }
        }

        and: 'authorized transaction is for payment provider ZERO, consistent with attached payment info and for payment method ZERO'
        with(actualAuthorizationResult.paymentTransactionEntry.paymentTransaction) {
            verifyAll {
                paymentProvider == PaymentProvider.ZERO.name()
                info instanceof ZeroPaymentInfoModel
                info.paymentProvider == PaymentProvider.ZERO
                info == cart.paymentInfo
            }
        }

        and: 'cart contains the correct products'
        cart.entries.size() == 1
        cart.entries*.product == [fullLicense]
    }

    @Test
    def 'seller has no DPG account, only Bosch Transfer payment is offered'() {
        given:
        sellerCompany.setPspSellerAccounts(List.of())
        modelService.save(sellerCompany)

        var pspSellerAccount = sampleDataCreator.createBoschSellerAccount(sellerCompany)
        sellerCompany.setPspSellerAccounts(List.of(pspSellerAccount))
        modelService.save(sellerCompany)
        var sellerCountry = sellerCompany.getCountry()
        sellerCountry.setSupportedPaymentProviders(Set.of(PaymentProvider.BOSCH_TRANSFER, PaymentProvider.DPG))
        modelService.save(sellerCountry)

        cisCartFacade.addToCart(fullLicense.getCode(), 1L)
        cart = cartService.getSessionCart()

        when: 'checkout for session cart is prepared'
        def orderPaymentData = cisCheckoutFacade.getCheckoutPaymentSummaryPageData()

        then: 'SEPA payment method with BOSCH TRANSFER is chosen'
        orderPaymentData.getPaymentMethod() == PaymentMethodType.SEPA_CREDIT
        verifyAll(orderPaymentData.paymentInfo) {
            paymentProvider == PaymentProvider.BOSCH_TRANSFER
            paymentMethod == PaymentMethodType.SEPA_CREDIT
        }
    }

    private IoTCompanyModel prepareCompany(IoTCompanyModel company, CompanyApprovalStatus companyApprovalStatus, BigDecimal creditLimit) {
        company.setApprovalStatus(companyApprovalStatus)
        company.setAaCustomerGroup(getUsergroup())
        modelService.save(company)
        def data = umpWireMockRule.buildUmpCompanyData(company.getUid(), AZENA_MARKETPLACE_ID)
        data.withCreditLimit(creditLimit)
        umpWireMockRule.prepareGetCompanyDataResponse(company.getUid(), umpWireMockRule.buildResponseDefinition(data))
        return company
    }

    private void disableSellerAccount(PaymentProvider paymentProvider) {
        sellerCompany.getPspSellerAccounts().stream()
                .filter { PspSellerAccountModel pspSellerAccount -> pspSellerAccount.getPaymentProvider() == paymentProvider }
                .each { PspSellerAccountModel matchingSellerAccount ->
                    matchingSellerAccount.setStatus(PspSellerAccountStatus.DISABLED)
                    modelService.save(matchingSellerAccount)
                }

        modelService.refresh(sellerCompany)
    }

    private UserGroupModel getUsergroup() {
        return userGroupDao.findUserGroupByUid("IDW000")
    }
}
