package com.sast.cis.core.converter;

import com.google.common.collect.ImmutableList;
import com.sast.cis.core.constants.MediaFormat;
import com.sast.cis.core.data.ProductContainerOverviewData;
import com.sast.cis.core.enums.StoreAvailabilityMode;
import com.sast.cis.core.model.*;
import com.sast.cis.core.service.AppLicenseService;
import com.sast.cis.core.service.AppService;
import com.sast.cis.core.service.AppVersionService;
import com.sast.cis.core.service.ShopMediaUrlService;
import com.sast.cis.core.service.media.CisMediaContainerService;
import com.sast.cis.test.utils.TestLogListener;
import de.hybris.bootstrap.annotations.UnitTest;
import de.hybris.platform.catalog.enums.ArticleApprovalStatus;
import de.hybris.platform.core.model.media.MediaContainerModel;
import de.hybris.platform.core.model.media.MediaModel;
import de.hybris.platform.util.logging.HybrisLogger;
import generated.com.sast.cis.core.model.*;
import generated.de.hybris.platform.core.model.media.MediaBuilder;
import generated.de.hybris.platform.core.model.media.MediaContainerBuilder;
import org.apache.log4j.Level;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Matchers.anyString;
import static org.mockito.Mockito.verifyZeroInteractions;
import static org.mockito.Mockito.when;

@UnitTest
@RunWith(MockitoJUnitRunner.Silent.class)
public class ProductContainerPopulatorUnitTest {
    private static final String TITLE = "title";
    private static final String ICON_URL_FOR_DRAFT = "testUrlForDraft";
    private static final MediaModel ICON_FOR_DRAFT = MediaBuilder.generate().withURL(ICON_URL_FOR_DRAFT).buildMockInstance();
    private static final String ICON_URL_FOR_APP = "testUrlForApp";
    private static final MediaModel ICON_FOR_APP = MediaBuilder.generate().withURL(ICON_URL_FOR_APP).buildMockInstance();
    private static final String APP_CODE = "testApp";
    private static final Double AVERAGE_RATING = 2.0;
    private static final Date MODIFIED_TIME_CONTAINER = new GregorianCalendar(2018, Calendar.SEPTEMBER, 17).getTime();
    private static final String PRODUCT_CONTAINER_CODE = "product_container";
    private static final String STORE_CONTENT_PATH = "app/storecontent/";

    private final TestLogListener logListener = new TestLogListener();

    private ProductContainerPopulator productContainerPopulator;

    @Mock
    private AppLicenseService appLicenseService;

    @Mock
    private AppVersionService appVersionService;

    @Mock
    private AppService appService;

    @Mock
    private CisMediaContainerService cisMediaContainerService;

    @Mock
    private ShopMediaUrlService urlService;

    private AppModel app;
    private AppDraftModel appDraft;

    @Before
    public void setUp() {
        HybrisLogger.addListener(logListener);

        MediaContainerModel draftLogo = MediaContainerBuilder.generate()
            .withMedias(ImmutableList.of(MediaBuilder.generate().withURL(ICON_URL_FOR_DRAFT).buildMockInstance())).buildMockInstance();
        StoreContentDraftModel storeContentDraft = StoreContentDraftBuilder.generate()
            .withName(TITLE)
            .withIcon(draftLogo)
            .buildMockInstance();

        AppModel onlineApp = AppBuilder.generate()
            .withCode(APP_CODE)
            .withApprovalStatus(ArticleApprovalStatus.APPROVED)
            .buildMockInstance();
        when(onlineApp.getAverageRating()).thenReturn(AVERAGE_RATING);

        AppVersionModel appVersion = AppVersionBuilder.generate()
            .withApk(ApkMediaBuilder.generate().withVersionCode(1L).buildMockInstance())
            .buildMockInstance();

        appDraft = AppDraftBuilder.generate()
            .withStoreContentDraft(storeContentDraft)
            .withApprovalStatus(ArticleApprovalStatus.DRAFT)
            .withStoreAvailabilityMode(StoreAvailabilityMode.PUBLIC)
            .buildMockInstance();

        AppLicenseModel fullLicense = AppLicenseBuilder.generate().buildMockInstance();
        AppLicenseModel trialLicense = AppLicenseBuilder.generate().buildMockInstance();

        MediaContainerModel appLogo = MediaContainerBuilder.generate()
            .withMedias(ImmutableList.of(MediaBuilder.generate().withURL(ICON_URL_FOR_APP).buildMockInstance())).buildMockInstance();
        app = AppBuilder.generate()
            .withCode(APP_CODE)
            .withName(TITLE)
            .withApprovalStatus(ArticleApprovalStatus.UNAPPROVED)
            .withStoreAvailabilityMode(StoreAvailabilityMode.PUBLIC)
            .withVersions(Collections.singleton(appVersion))
            .withIcon(appLogo)
            .buildMockInstance();

        when(appVersionService.getLatestVersion(app)).thenReturn(Optional.of(appVersion));
        when(appService.getOnlineAppForCode(anyString())).thenReturn(Optional.of(onlineApp));
        when(cisMediaContainerService.getMediaForFormat(appLogo, MediaFormat.SMALL_ICON)).thenReturn(ICON_FOR_APP);
        when(cisMediaContainerService.getMediaForFormat(draftLogo, MediaFormat.SMALL_ICON)).thenReturn(ICON_FOR_DRAFT);
        when(appLicenseService.getEvaluationLicense(app)).thenReturn(Optional.of(trialLicense));
        when(appLicenseService.getFullAppLicenseForAzenaTenant(app)).thenReturn(Optional.of(fullLicense));
        when(urlService.getUrl(ICON_FOR_APP)).thenReturn(ICON_URL_FOR_APP);
        when(urlService.getUrl(ICON_FOR_DRAFT)).thenReturn(ICON_URL_FOR_DRAFT);

        productContainerPopulator = new ProductContainerPopulator(appVersionService,
            appService,
            cisMediaContainerService,
            urlService,
            false);
    }

    @After
    public void tearDown() {
        HybrisLogger.removeListener(logListener);
    }


    @Test
    public void onlyAppDraftPresent_getEnabledInStoreValueFromDraft() {
        ProductContainerModel productContainer = createProductContainer(null, appDraft);
        when(appDraft.getStoreAvailabilityMode()).thenReturn(StoreAvailabilityMode.UNAVAILABLE);
        ProductContainerOverviewData productContainerOverviewData = new ProductContainerOverviewData();

        productContainerPopulator.populate(productContainer, productContainerOverviewData);


        assertThat(productContainerOverviewData.isEnabledInStore()).isFalse();
    }

    @Test
    public void onlyAppDraftPresent_getAppIsNotEditableDraft() {
        getExpectedIsAppEditable(false);
    }

    @Test
    public void onlyAppDraftPresent_getAppIsEditableDraft() {
        getExpectedIsAppEditable(true);
    }

    private void getExpectedIsAppEditable(boolean isAppEditable) {
        when(appService.isAppEditable(app)).thenReturn(isAppEditable);
        ProductContainerModel productContainer = createProductContainer(app, appDraft);
        ProductContainerOverviewData productContainerOverviewData = new ProductContainerOverviewData();

        productContainerPopulator.populate(productContainer, productContainerOverviewData);

        assertThat(productContainerOverviewData.isAppEditable()).isEqualTo(isAppEditable);
    }

    @Test
    public void onlyAppDraftPresent_getVersionNameFromVersionDraftApk() {
        ProductContainerModel productContainer = createProductContainer(null, appDraft);
        ApkMediaModel apk = ApkMediaBuilder.generate()
            .withVersionName("an excellent version")
            .buildMockInstance();
        AppVersionDraftModel appVersionDraft = AppVersionDraftBuilder.generate()
            .withApk(apk)
            .buildMockInstance();
        when(productContainer.getAppVersionDraft()).thenReturn(appVersionDraft);
        ProductContainerOverviewData productContainerOverviewData = new ProductContainerOverviewData();

        productContainerPopulator.populate(productContainer, productContainerOverviewData);

        assertThat(productContainerOverviewData.getVersionName()).isEqualTo("an excellent version");
    }

    @Test
    public void appNotPresent_appDraftPresent_imageIsTakenFromDraft_titleIsSetFromContainer() {
        ProductContainerModel productContainer = createProductContainer(null, appDraft);
        ProductContainerOverviewData productContainerOverviewData = new ProductContainerOverviewData();

        productContainerPopulator.populate(productContainer, productContainerOverviewData);

        assertThat(productContainerOverviewData).isEqualToComparingFieldByField(getExpectedProductOverviewData()
            .withEnabledInStore(true)
            .withIconUrl(ICON_URL_FOR_DRAFT)
            .withAppApprovalStatus(ArticleApprovalStatus.DRAFT)
            .withDeletable(true)
            .withAverageRating(null));

        verifyZeroInteractions(appLicenseService);
    }

    private ProductContainerOverviewData getExpectedProductOverviewData() {
        return new ProductContainerOverviewData()
            .withEnabledInStore(true)
            .withName(TITLE)
            .withUpdated(MODIFIED_TIME_CONTAINER)
            .withUrl(STORE_CONTENT_PATH + PRODUCT_CONTAINER_CODE)
            .withCode(PRODUCT_CONTAINER_CODE);
    }

    @Test
    public void appPresent_appDraftNotPresent_imageIsTakenFromApp_titleIsSetFromContainer() {
        ProductContainerModel productContainer = createProductContainer(app, null);
        ProductContainerOverviewData productContainerOverviewData = new ProductContainerOverviewData();

        productContainerPopulator.populate(productContainer, productContainerOverviewData);

        assertThat(productContainerOverviewData).isEqualToComparingFieldByField(getExpectedProductOverviewData()
            .withIconUrl(ICON_URL_FOR_APP)
            .withAppApprovalStatus(ArticleApprovalStatus.UNAPPROVED)
            .withLatestVersionApprovalStatus(ArticleApprovalStatus.APPROVED)
            .withAverageRating(AVERAGE_RATING));
    }

    @Test
    public void appAndAppDraftPresent_imageIsTakenFromApp_titleIsSetFromContainer() {
        ProductContainerModel productContainer = createProductContainer(app, appDraft);
        ProductContainerOverviewData productContainerOverviewData = new ProductContainerOverviewData();

        productContainerPopulator.populate(productContainer, productContainerOverviewData);

        assertThat(productContainerOverviewData).isEqualToComparingFieldByField(getExpectedProductOverviewData()
            .withIconUrl(ICON_URL_FOR_APP)
            .withAppApprovalStatus(ArticleApprovalStatus.UNAPPROVED)
            .withLatestVersionApprovalStatus(ArticleApprovalStatus.APPROVED)
            .withAverageRating(AVERAGE_RATING));
    }

    @Test
    public void appAndAppVersionDraftPresent_latestVersionApprovalStatusIsPopulated() {
        ProductContainerModel productContainer = createProductContainer(app, null);
        ApkMediaModel apk = ApkMediaBuilder.generate().buildMockInstance();
        AppVersionDraftModel appVersionDraft = AppVersionDraftBuilder.generate()
            .withApk(apk)
            .buildMockInstance();
        ProductContainerOverviewData productContainerOverviewData = new ProductContainerOverviewData();

        when(productContainer.getAppVersionDraft()).thenReturn(appVersionDraft);
        when(appVersionDraft.getApprovalStatus()).thenReturn(ArticleApprovalStatus.UNAPPROVED);

        productContainerPopulator.populate(productContainer, productContainerOverviewData);

        assertThat(productContainerOverviewData).isEqualToComparingFieldByField(getExpectedProductOverviewData()
            .withIconUrl(ICON_URL_FOR_APP)
            .withAppApprovalStatus(ArticleApprovalStatus.UNAPPROVED)
            .withLatestVersionApprovalStatus(ArticleApprovalStatus.UNAPPROVED)
            .withAverageRating(AVERAGE_RATING));
    }

    @Test
    public void neitherAppNorAppDraftPresent_populatesFallbackValues() {
        ProductContainerModel productContainer = createProductContainer(null, null);
        ProductContainerOverviewData productContainerOverviewData = new ProductContainerOverviewData();
        productContainerPopulator.populate(productContainer, productContainerOverviewData);

        assertThat(productContainerOverviewData).isEqualToComparingFieldByField(getExpectedProductOverviewData()
            .withName(null)
            .withEnabledInStore(true)
            .withDeletable(true)
            .withAppApprovalStatus(ArticleApprovalStatus.DRAFT));
    }

    @Test
    public void onlineAppIsNotPresent_averageRatingSetToNull() {
        when(appService.getOnlineAppForCode(anyString())).thenReturn(Optional.empty());
        ProductContainerModel productContainer = createProductContainer(app, appDraft);
        ProductContainerOverviewData productContainerOverviewData = new ProductContainerOverviewData();
        productContainerPopulator.populate(productContainer, productContainerOverviewData);

        assertThat(productContainerOverviewData).isEqualToComparingFieldByField(getExpectedProductOverviewData()
            .withIconUrl(ICON_URL_FOR_APP)
            .withAppApprovalStatus(ArticleApprovalStatus.UNAPPROVED)
            .withLatestVersionApprovalStatus(ArticleApprovalStatus.APPROVED)
            .withAverageRating(null));
    }

    @Test
    public void populate_appWithoutIcon_logAlertAndContinue() {
        when(app.getIcon()).thenReturn(null);
        ProductContainerModel productContainer = createProductContainer(app, null);
        ProductContainerOverviewData productContainerOverviewData = new ProductContainerOverviewData();

        productContainerPopulator.populate(productContainer, productContainerOverviewData);

        assertThat(logListener.getLog())
            .filteredOn(hybrisLoggingEvent -> Level.ERROR.equals(hybrisLoggingEvent.getLevel()))
            .hasSize(1)
            .allMatch(hybrisLoggingEvent -> hybrisLoggingEvent.getMessage().toString().startsWith("ALERT"));
        assertThat(productContainerOverviewData).isEqualToComparingFieldByField(getExpectedProductOverviewData()
            .withIconUrl(null)
            .withAppApprovalStatus(ArticleApprovalStatus.UNAPPROVED)
            .withLatestVersionApprovalStatus(ArticleApprovalStatus.APPROVED)
            .withAverageRating(AVERAGE_RATING));
    }

    private ProductContainerModel createProductContainer(AppModel app, AppDraftModel appDraft) {
        return ProductContainerBuilder.generate()
            .withTitle(TITLE)
            .withModifiedtime(MODIFIED_TIME_CONTAINER)
            .withAppDraft(appDraft)
            .withApp(app)
            .withCode(PRODUCT_CONTAINER_CODE)
            .buildMockInstance();
    }
}
