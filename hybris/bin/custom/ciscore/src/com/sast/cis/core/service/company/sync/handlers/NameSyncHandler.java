package com.sast.cis.core.service.company.sync.handlers;

import com.sast.cis.core.data.UmpCompanyData;
import com.sast.cis.core.model.IoTCompanyModel;
import com.sast.cis.core.service.company.sync.CompanySyncHandler;
import de.hybris.platform.servicelayer.model.ModelService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

@Component
@Slf4j
public class NameSyncHandler implements CompanySyncHandler {
    private final ModelService modelService;

    @Autowired
    public NameSyncHandler(ModelService modelService) {
        this.modelService = modelService;
    }

    @Override
    public void syncCompanyData(IoTCompanyModel company, UmpCompanyData umpCompany) {
        updateCompanyName(company, umpCompany);
        updateCompanyFriendlyName(company, umpCompany);
    }

    public void updateCompanyName(IoTCompanyModel company, UmpCompanyData umpCompany) {
        String currentCompanyName = company.getName();
        String newCompanyName = umpCompany.getCompanyName();

        if (!currentCompanyName.equals(newCompanyName)) {

            LOG.info("Updating CompanyUid={} name from {} to {}", company.getUid(), company.getName(), umpCompany.getCompanyName());

            try {
                company.setName(newCompanyName);
                if (company.getBillingAddress() != null) {
                    company.getBillingAddress().setCompany(newCompanyName);
                }

                if (company.getBillingAddress() != null) {
                    modelService.saveAll(company, company.getBillingAddress());
                } else {
                    modelService.save(company);
                }
            } catch (RuntimeException e) {
                LOG.error(e.getMessage(), e);
            } finally {
                modelService.refresh(company);
                if (company.getBillingAddress() != null) {
                    modelService.refresh(company.getBillingAddress());
                }
            }
        }
    }

    public void updateCompanyFriendlyName(IoTCompanyModel company, UmpCompanyData umpCompany) {
        String currentCompanyFriendlyName = company.getFriendlyName();
        String newCompanyFriendlyName = umpCompany.getFriendlyName();

        if (!StringUtils.isEmpty(newCompanyFriendlyName) && !newCompanyFriendlyName.equals(currentCompanyFriendlyName)) {
            company.setFriendlyName(newCompanyFriendlyName);
            modelService.save(company);
        }
    }
}
