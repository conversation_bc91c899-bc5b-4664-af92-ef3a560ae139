package com.sast.cis.core.followapp.populator;

import com.sast.cis.core.data.FollowAppData;
import com.sast.cis.core.followapp.service.FollowAppService;
import com.sast.cis.core.model.AppModel;
import com.sast.cis.core.model.IntegratorModel;
import com.sast.cis.core.service.customer.integrator.IntegratorService;
import com.sast.cis.core.service.security.UserPermissionService;
import de.hybris.platform.commercefacades.product.data.ProductData;
import de.hybris.platform.converters.Populator;
import de.hybris.platform.core.model.product.ProductModel;
import de.hybris.platform.core.model.user.UserModel;
import de.hybris.platform.servicelayer.dto.converter.ConversionException;
import de.hybris.platform.servicelayer.user.UserService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class ProductDataFollowAppPopulator implements Populator<ProductModel, ProductData> {
    private final FollowAppService followAppService;
    private final UserService userService;
    private final IntegratorService integratorService;

    @Override
    public void populate(ProductModel product, ProductData productData) throws ConversionException {
        if (product instanceof AppModel && isCurrentUserAllowedToViewFollowAppInfo()) {
            AppModel appModel = (AppModel) product;
            IntegratorModel currentIntegrator = integratorService.getCurrentIntegrator();
            boolean subscribed = followAppService.isSubscribed(currentIntegrator, appModel);
            productData.setFollowAppData(new FollowAppData().withSubscribed(subscribed));
        }
    }

    private boolean isCurrentUserAllowedToViewFollowAppInfo() {
        UserModel currentUser = userService.getCurrentUser();
        return currentUser instanceof IntegratorModel && !userService.isAnonymousUser(currentUser);
    }
}
