package com.sast.cis.core.converter.appediting.countriesandprices;

import com.sast.cis.core.data.VolumeDiscountData;
import com.sast.cis.core.model.VolumeDiscountDraftModel;
import de.hybris.platform.converters.Populator;
import org.springframework.stereotype.Component;

@Component
public class VolumeDiscountDraftDataPopulator implements Populator<VolumeDiscountDraftModel, VolumeDiscountData> {
    @Override
    public void populate(VolumeDiscountDraftModel source, VolumeDiscountData target) {
        target.setDiscount(source.getDiscount());
        target.setMinQuantity(source.getMinQuantity());
        target.setPk(source.getPk().getLong());
    }
}
