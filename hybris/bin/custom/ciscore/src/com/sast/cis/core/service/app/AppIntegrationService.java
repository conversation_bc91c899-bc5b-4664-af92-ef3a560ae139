package com.sast.cis.core.service.app;

import com.google.common.base.Strings;
import com.sast.cis.core.data.AppIntegrationData;
import com.sast.cis.core.enums.AppIntegrationType;
import com.sast.cis.core.model.AppIntegrationModel;
import com.sast.cis.core.model.AppModel;
import com.sast.cis.core.model.StandardAppIntegrationModel;
import com.sast.cis.core.model.StoreContentDraftModel;
import com.sast.cis.core.service.media.devcon.MediaUpdateService;
import de.hybris.platform.servicelayer.dto.converter.Converter;
import de.hybris.platform.servicelayer.model.ModelService;
import de.hybris.platform.servicelayer.search.FlexibleSearchService;
import groovy.util.logging.Slf4j;
import lombok.AllArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static java.util.stream.Collectors.toSet;
import static org.apache.commons.collections4.ListUtils.emptyIfNull;

@Slf4j
@Service
@AllArgsConstructor
public class AppIntegrationService implements AppIntegration {
    private final FlexibleSearchService flexibleSearchService;
    private final ModelService modelService;
    private final MediaUpdateService mediaUpdateService;

    private final Converter<AppIntegrationData, AppIntegrationModel> appIntegrationData2AppIntegrationConverter;
    private final Converter<AppIntegrationModel, AppIntegrationData> appIntegrationDataConverter;
    private final Converter<StandardAppIntegrationModel, AppIntegrationData> standardAppIntegrationDataConverter;

    @Override
    public void updateIntegrations(AppModel app, List<AppIntegrationData> appIntegrations) {
        if (appIntegrations == null || app == null || app.getAppIntegrations() == null) {
            return;
        }

        app.setAppIntegrations(update(appIntegrations, app.getAppIntegrations()));
    }

    @Override
    public void updateIntegrations(StoreContentDraftModel storeContentDraft, List<AppIntegrationData> appIntegrations) {
        if (storeContentDraft == null || appIntegrations == null) {
            return;
        }
        storeContentDraft.setAppIntegrations(update(appIntegrations, emptyIfNull(storeContentDraft.getAppIntegrations())));
    }

    @Override
    public List<AppIntegrationData> getDevconAppIntegrationData(List<AppIntegrationModel> existingAppIntegrations) {
        Set<String> existingStandardCodes = CollectionUtils.emptyIfNull(existingAppIntegrations).stream()
            .filter(integration -> AppIntegrationType.STANDARD.equals(integration.getType()))
            .map(integration -> integration.getStandardIntegration().getCode())
            .collect(toSet());

        return Stream.concat(
                CollectionUtils.emptyIfNull(existingAppIntegrations)
                    .stream()
                    .map(appIntegrationDataConverter::convert),
                getDefaultStandardAppIntegrations().stream()
                    .filter(standardIntegration -> !existingStandardCodes.contains(standardIntegration.getCode()))
                    .map(standardAppIntegrationDataConverter::convert))
            .sorted(Comparator.comparing(AppIntegrationData::getStandardIntegrationOrder, Comparator.nullsLast(Comparator.naturalOrder())))
            .collect(Collectors.toList());
    }

    private List<AppIntegrationModel> update(List<AppIntegrationData> appIntegrations, List<AppIntegrationModel> appIntegrationsModels) {
        Assert.notNull(appIntegrations, "appIntegrations is null");
        Assert.notNull(appIntegrationsModels, "appIntegrationsModels is null");

        Map<Boolean, List<AppIntegrationData>> newAppIntegrations = appIntegrations
            .stream()
            .collect(Collectors.partitioningBy(item -> Strings.isNullOrEmpty(item.getCode())));

        List<AppIntegrationModel> savedIntegrationsModels = updateSavedIntegrations(appIntegrationsModels, newAppIntegrations.get(false));

        List<AppIntegrationModel> newIntegrationModels = appIntegrationData2AppIntegrationConverter
            .convertAll(newAppIntegrations.get(true));
        modelService.saveAll(newIntegrationModels);

        List<AppIntegrationModel> result = new ArrayList<>();
        result.addAll(savedIntegrationsModels);
        result.addAll(newIntegrationModels);
        return result;
    }

    private List<AppIntegrationModel> updateSavedIntegrations(List<AppIntegrationModel> appIntegrations,
        List<AppIntegrationData> appIntegrationsData) {
        Map<String, AppIntegrationData> integrationsDataMap = CollectionUtils.emptyIfNull(appIntegrationsData)
            .stream()
            .collect(Collectors.toMap(AppIntegrationData::getCode, item -> item));

        ArrayList<AppIntegrationModel> remainingIntegrations = new ArrayList<>();

        for (AppIntegrationModel appIntegration : appIntegrations) {
            AppIntegrationData appIntegrationData = integrationsDataMap.get(appIntegration.getPk().toString());
            if (appIntegrationData != null) {
                appIntegration.setStatus(appIntegrationData.getStatus());
                if (appIntegrationData.getDocumentation() != null && appIntegrationData.getDocumentation().getUrl() != null) {
                    appIntegration
                        .setDocumentation(mediaUpdateService.getPdfMediaWithUpdatedFileName(appIntegrationData.getDocumentation()));
                } else {
                    appIntegration.setDocumentation(null);
                }
                modelService.save(appIntegration);
                if (appIntegration.getApp() != null) {
                    modelService.save(appIntegration.getApp());
                }
                remainingIntegrations.add(appIntegration);
            } else {
                modelService.remove(appIntegration);
            }

        }
        return remainingIntegrations;
    }

    private List<StandardAppIntegrationModel> getDefaultStandardAppIntegrations() {
        StandardAppIntegrationModel standardAppIntegration = new StandardAppIntegrationModel();
        standardAppIntegration.setEnabled(true);

        return emptyIfNull(flexibleSearchService.getModelsByExample(standardAppIntegration));
    }

}
