package com.sast.cis.core.pricegroup.dao;

import com.google.common.base.Preconditions;
import de.hybris.platform.core.model.enumeration.EnumerationValueModel;
import de.hybris.platform.enumeration.EnumerationService;
import de.hybris.platform.europe1.enums.UserPriceGroup;
import de.hybris.platform.jalo.Item;
import de.hybris.platform.jalo.JaloSession;
import de.hybris.platform.persistence.enumeration.EnumerationValueEJBImpl;
import de.hybris.platform.servicelayer.model.ModelService;
import de.hybris.platform.servicelayer.type.TypeService;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
@AllArgsConstructor
public class UserPriceGroupDao {
    private final ModelService modelService;
    private final TypeService typeService;
    private final EnumerationService enumerationService;

    public Optional<UserPriceGroup> getByCode(String code) {
        Preconditions.checkArgument(StringUtils.isNotBlank(code), "Given code %s is invalid", code);
        try {
            return Optional.of((UserPriceGroup) enumerationService.getEnumerationValue("UserPriceGroup", code));
        } catch (Exception e) {
            return Optional.empty();
        }
    }

    public UserPriceGroup getOrCreateUserPriceGroup(String code) {
        Preconditions.checkArgument(StringUtils.isNotBlank(code), "Given code %s is invalid", code);
        return getByCode(code).orElseGet(() -> createForCode(code));

    }

    public void deleteUserPriceGroup(String code) throws Exception {
        Preconditions.checkArgument(StringUtils.isNotBlank(code), "Given code %s is invalid", code);
        UserPriceGroup userPriceGroup = getByCode(code).orElseThrow(() -> UserPriceGroupNotFoundException.forCode(code));
        final EnumerationValueModel enumerationValueModel = typeService.getEnumerationValue(userPriceGroup);
        final Item item = modelService.getSource(enumerationValueModel);
        ((EnumerationValueEJBImpl) item.getImplementation()).remove(JaloSession.getCurrentSession().getSessionContext());
    }

    private UserPriceGroup createForCode(String code) {
        Preconditions.checkArgument(StringUtils.isNotBlank(code), "Given code %s is invalid", code);
        UserPriceGroup userPriceGroup = UserPriceGroup.valueOf(code);
        modelService.save(userPriceGroup);
        return userPriceGroup;
    }
}
