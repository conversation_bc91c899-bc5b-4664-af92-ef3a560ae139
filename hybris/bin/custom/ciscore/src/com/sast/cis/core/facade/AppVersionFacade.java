package com.sast.cis.core.facade;

import com.google.common.collect.ImmutableList;
import com.sast.cis.core.dao.CatalogVersion;
import com.sast.cis.core.data.AppVersionMetaData;
import com.sast.cis.core.exceptions.devcon.DevconErrorException;
import com.sast.cis.core.model.AppVersionModel;
import com.sast.cis.core.model.ProductContainerModel;
import com.sast.cis.core.service.*;
import de.hybris.platform.servicelayer.model.ModelService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;

import java.util.Locale;
import java.util.Objects;
import java.util.Optional;
import java.util.function.BiConsumer;

import static com.sast.cis.core.constants.devcon.DevconErrorCode.APPVERSION_NOT_FOUND;
import static com.sast.cis.core.constants.devcon.DevconErrorCode.BACKEND_ERROR;

@Component
public class AppVersionFacade {
    private static final Logger LOG = LoggerFactory.getLogger(AppVersionFacade.class);

    @Resource
    private AppVersionService appVersionService;

    @Resource
    private ErrorMessageService errorMessageService;

    @Resource
    private ProductContainerService productContainerService;

    @Resource
    private ModelService modelService;

    @Resource
    private AppSyncService appSyncService;

    @Resource
    private HtmlSanitizingService htmlSanitizingService;

    @Resource
    private TransactionTemplate transactionTemplate;


    public void updateAppVersion(AppVersionMetaData appVersionMetaData) {
        String productContainerCode = appVersionMetaData.getProductContainerCode();
        LOG.info("Updating appVersion with code={} for productContainer for code={}", appVersionMetaData.getAppVersionCode(),
            productContainerCode);

        ProductContainerModel productContainer = productContainerService.getProductContainerForCode(productContainerCode)
            .orElseThrow(() -> {
                LOG.error("The AppVersion update cannot be processed due to missing ProductContainer. " +
                   "Searched for ProductContainer with code={}", productContainerCode);
                return new DevconErrorException(errorMessageService.createErrorMessage(BACKEND_ERROR));
            });

        if (productContainer.getApp() == null) {
            LOG.error("The AppVersion update for ProductContainer with code={} cannot be processed due to missing App.",
                productContainerCode);
            throw new DevconErrorException(errorMessageService.createErrorMessage(BACKEND_ERROR));
        }

        Optional<AppVersionModel> appVersion = appVersionService.getVersionForCodeAndCatalogVersion(
            appVersionMetaData.getAppVersionCode(), CatalogVersion.STAGED);

        if (appVersion.isPresent()) {
            updateExistingAppVersionInternal(appVersionMetaData, productContainer, appVersion.get());
        } else {
            LOG.error("No model found to be updated or transferred for ProductContainer.code={} and AppVersion.code={}.",
                productContainer.getCode(), appVersionMetaData.getAppVersionCode());

            throw new DevconErrorException(errorMessageService.createErrorMessage(APPVERSION_NOT_FOUND));
        }
    }

    private void updateExistingAppVersionInternal(AppVersionMetaData appVersionMetaData,
        ProductContainerModel productContainer, AppVersionModel appVersion) {
        if (!Objects.equals(productContainer.getApp().getCode(), appVersion.getApp().getCode())) {
            LOG.error("The received ProductContainer.code={} is not related to the AppLicense with code={} to be updated.",
                productContainer.getCode(), appVersion.getCode());

            throw new DevconErrorException(errorMessageService.createErrorMessage(BACKEND_ERROR));
        }

        transactionTemplate.execute(status -> updateAppVersionAndChangeDate(appVersionMetaData, productContainer, appVersion));
        appSyncService.performSync(ImmutableList.of(appVersion), appVersion.getApp().getCatalogVersion().getCatalog().getId());
    }

    private boolean updateAppVersionAndChangeDate(AppVersionMetaData appVersionMetaData, ProductContainerModel productContainer,
        AppVersionModel appVersion) {
        LOG.debug("Updating app version with code={}.", appVersion.getCode());
        updateAppVersion(appVersion, appVersionMetaData);
        productContainerService.updateProductContainerChangeDate(productContainer);
        LOG.info("Updated app version with code={} with new app version meta data", appVersion.getCode());

        return true;
    }

    private void updateAppVersion(AppVersionModel appVersion, AppVersionMetaData appVersionMetaData) {
        updateChangelogEntries(appVersionMetaData, appVersion::setChangelog);
        modelService.save(appVersion);
    }

    private void updateChangelogEntries(AppVersionMetaData appVersionMetaData, BiConsumer<String, Locale> setter) {
        appVersionMetaData.getChangelogByIsocode().forEach((isoCode, changelog) -> setter.accept(
            StringUtils.isBlank(changelog) ? null : htmlSanitizingService.sanitizeInput(changelog), new Locale(isoCode))
        );
    }
}
