package com.sast.cis.brim.converter.orderexport;

import com.sast.cis.brim.converter.BrimConverter;
import com.sast.cis.core.billingintegration.dto.ContractItemData;
import com.sast.cis.core.billingintegration.dto.OrderExportData;
import com.sast.cis.core.billingintegration.dto.OrderExportResult;
import com.securityandsafetythings.billing.brim.model.ordercontract.v1.ContractItem;
import com.securityandsafetythings.billing.brim.model.ordercontract.v1.Log;
import com.securityandsafetythings.billing.brim.model.ordercontract.v1.ResponseObject;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Nonnull;
import java.util.List;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
public class OrderExportDataConverter implements BrimConverter<ResponseObject, OrderExportData> {

    private final BrimConverter<List<Log>, List<String>> orderLogConverter;
    private final BrimConverter<ContractItem, ContractItemData> contractItemDataConverter;

    @Override
    public OrderExportData convert(@Nonnull ResponseObject responseObject) {
        checkSource(responseObject);

        return OrderExportData.builder()
            .id(responseObject.getExternalOrderReference())
            .billingOrderId(responseObject.getProviderOrderId())
            .errors(orderLogConverter.convert(responseObject.getLog()))
            .orderExportResult(mapOrderStatus(responseObject.getStatus()))
            .contractItems(convertContractItems(responseObject.getContractItems()))
            .build();
    }

    private OrderExportResult mapOrderStatus(com.securityandsafetythings.billing.brim.model.ordercontract.v1.OrderStatus orderStatus) {
        if (orderStatus == null) {
            return null;
        }
        return switch (orderStatus) {
            case OPEN -> OrderExportResult.OPEN;
            case REJECTED -> OrderExportResult.REJECTED;
            case COMPLETED -> OrderExportResult.COMPLETED;
            case ERROR -> OrderExportResult.ERROR;
        };
    }

    private List<ContractItemData> convertContractItems(final List<ContractItem> contractItems) {
        return CollectionUtils.emptyIfNull(contractItems).stream()
            .map(contractItemDataConverter::convert)
            .collect(Collectors.toList());
    }
}
