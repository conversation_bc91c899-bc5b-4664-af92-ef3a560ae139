package com.sast.cis.brim.service

import com.sast.cis.core.billingintegration.request.BuyerContractService
import com.sast.cis.core.dao.CatalogVersion
import com.sast.cis.core.enums.BillingSystemStatus
import com.sast.cis.core.model.AppLicenseModel
import com.sast.cis.core.model.SubscriptionContractModel
import com.sast.cis.test.utils.BrimulatorRule
import com.sast.cis.test.utils.FeatureToggleRule
import com.sast.cis.test.utils.SampleDataCreator
import de.hybris.bootstrap.annotations.IntegrationTest
import de.hybris.platform.core.enums.OrderStatus
import de.hybris.platform.core.model.order.OrderEntryModel
import de.hybris.platform.core.model.order.OrderModel
import de.hybris.platform.servicelayer.ServicelayerSpockSpecification
import de.hybris.platform.servicelayer.model.ModelService
import de.hybris.platform.testframework.RunListeners
import de.hybris.platform.testframework.runlistener.ItemCreationListener
import org.junit.Rule
import org.junit.Test

import javax.annotation.Resource
import java.util.concurrent.TimeUnit
import java.util.concurrent.TimeoutException

@IntegrationTest
@RunListeners([ItemCreationListener])
class BuyerContractServiceISpec extends ServicelayerSpockSpecification {
    private static final APP_CODE = '*********'

    @Rule
    public BrimulatorRule brimulatorRule = new BrimulatorRule()
    @Rule
    public FeatureToggleRule featureToggleRule = new FeatureToggleRule()

    protected final SampleDataCreator sampleDataCreator = new SampleDataCreator()

    @Resource
    private ModelService modelService

    @Resource
    private BuyerContractService buyerContractService

    private AppLicenseModel appSubscriptionLicense
    private Date now
    SubscriptionContractModel subscription

    def setup() {
        now = new Date()
        def app = sampleDataCreator
                .createApp(APP_CODE, 'com.sast.cis.brim.service.BuyerContractServiceISpec' + UUID.randomUUID().toString(), CatalogVersion.STAGED)

        appSubscriptionLicense = sampleDataCreator.createSubscriptionAppLicense(app)
        OrderModel order = sampleDataCreator.createOrder(OrderStatus.COMPLETED)
        OrderEntryModel entry = sampleDataCreator.createOrderEntry(order, appSubscriptionLicense, 1)
        modelService.saveAll(appSubscriptionLicense, order, entry)
        subscription = sampleDataCreator.createSubscription(order.getCode(), entry, UUID.randomUUID().toString(), now)
        modelService.save(subscription)
        modelService.refresh(subscription)
    }

    @Test
    def 'cancels a valid subscription, cancelled on BRIM side and update from successful response'() {
        when:
        buyerContractService.cancelContract(subscription)
        modelService.refresh(appSubscriptionLicense)
        waitForSubscriptionCancellationSynced(subscription, 30L)

        then: 'subscription is cancelled'
        subscription.getEndDate()

        and: 'subscription is set to IN_SYNC'
        subscription.getBillingSystemStatus() == BillingSystemStatus.IN_SYNC
    }

    @Test
    def 'cancel request rejected, status set to REJECTED'() {
        given:
        brimulatorRule.prepareContractCancelErrorResponse(subscription.cancelIdempotencyKey, subscription.code)

        when:
        buyerContractService.cancelContract(subscription)
        modelService.refresh(appSubscriptionLicense)
        waitForSubscriptionCancellationSynced(subscription, 30L)

        then: 'subscription is not cancelled'
        !subscription.getEndDate()

        and: 'subscription billing status is set to REJECTED'
        subscription.getBillingSystemStatus() == BillingSystemStatus.REJECTED
    }


    boolean waitForSubscriptionCancellationSynced(SubscriptionContractModel subscription, long timeout)
            throws InterruptedException, TimeoutException {
        long start = System.currentTimeSeconds()
        while (true) {
            modelService.refresh(subscription)

            if (BillingSystemStatus.IN_SYNC == subscription.billingSystemStatus
                    || BillingSystemStatus.REJECTED == subscription.billingSystemStatus) {
                return true
            }

            if (System.currentTimeSeconds() - start > timeout) {
                throw new TimeoutException()
            } else {
                TimeUnit.SECONDS.sleep(1)
            }
        }
    }
}
