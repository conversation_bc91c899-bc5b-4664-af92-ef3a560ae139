package com.sast.cis.brim.converter

import com.sast.cis.brim.converter.coredto.ProductExportDataConverter
import com.sast.cis.brim.exception.BrimException
import com.sast.cis.brim.exception.BrimProductNotFoundException
import com.sast.cis.brim.service.BrimAppLicenseService
import com.sast.cis.core.billingintegration.dto.Price
import com.sast.cis.core.billingintegration.dto.PriceRecurrence
import com.sast.cis.core.billingintegration.dto.ProductExportData
import com.securityandsafetythings.billing.brim.model.enums.LogCode
import com.securityandsafetythings.billing.brim.model.product.v1.*
import de.hybris.bootstrap.annotations.UnitTest
import de.hybris.platform.servicelayer.i18n.CommonI18NService
import org.junit.Test
import de.hybris.platform.testframework.JUnitPlatformSpecification

import java.time.LocalDate
import java.time.LocalTime
import java.time.Month

@UnitTest
class ProductExportDataConverterUnitSpec extends JUnitPlatformSpecification {
    private static final String CATEGORY_ID = "SAST"
    private static final String PRODUCT_ID = "A123456_full"
    private static final String BRIM_ID = "A123456_FULL"
    private static final String LANGUAGE_EN = "en"
    private static final String LANGUAGE_DE = "de"
    private static final String NAME_EN = "Exzellente App"
    private static final String NAME_DE = "Excellent App"
    private static final String AMOUNT_EUR = "2.00"
    private static final String AMOUNT_USD = "2.38"
    private static final LocalDate VALID_FROM_TOMORROW = LocalDate.of(2020, Month.AUGUST, 05)
    private static final LocalDate VALID_TO_THE_FUTURE = LocalDate.of(9999, Month.DECEMBER, 31)
    private static final Log ERROR_LOG = new Log().setCode(LogCode.ERROR).setDetail("detail")
    private static final Log WARNING_LOG = new Log().setCode(LogCode.WARNING).setDetail("warn")

    CommonI18NService commonI18NService = Mock(CommonI18NService)
    BrimAppLicenseService brimAppLicenseService = Mock(BrimAppLicenseService)
    BrimConverter<ChargeGet, List<Price>> chargeGetPricesConverter = Mock(BrimConverter)
    ProductExportDataConverter productExportDataConverter

    ProductData mockProduct = Mock(ProductData)
    ChargeGet mockCharge = Mock(ChargeGet)
    RateGet mockRate = Mock(RateGet)
    OneTimeGet mockOneTime = Mock(OneTimeGet)
    Price mockPriceEur = Mock(Price)
    Price mockPriceUsd = Mock(Price)

    Name mockNameDe = Mock(Name)
    Name mockNameEn = Mock(Name)
    IsoLanguage mockLanguageDe = Mock(IsoLanguage)
    IsoLanguage mockLanguageEn = Mock(IsoLanguage)

    def setup() {
        commonI18NService.getLocaleForIsoCode(LANGUAGE_DE) >> Locale.GERMAN
        commonI18NService.getLocaleForIsoCode(LANGUAGE_EN) >> Locale.ENGLISH

        brimAppLicenseService.getCodeForBrimId(BRIM_ID) >> PRODUCT_ID
        chargeGetPricesConverter.convert(mockCharge) >> [mockPriceEur, mockPriceUsd]

        productExportDataConverter = new ProductExportDataConverter(commonI18NService, brimAppLicenseService, chargeGetPricesConverter)

        mockProduct.getId() >> BRIM_ID
        mockProduct.getCategoryId() >> CATEGORY_ID
        mockProduct.getName() >> List.of(mockNameDe, mockNameEn)
        mockProduct.getChargePlan() >> mockCharge
        mockProduct.getLog() >> [ERROR_LOG, WARNING_LOG]

        mockCharge.getRate() >> mockRate
        mockRate.getOneTime() >> mockOneTime

        mockNameDe.getLanguage() >> mockLanguageDe
        mockNameDe.getDescription() >> NAME_DE
        mockNameEn.getLanguage() >> mockLanguageEn
        mockNameEn.getDescription() >> NAME_EN

        mockLanguageDe.getCode() >> LANGUAGE_DE
        mockLanguageEn.getCode() >> LANGUAGE_EN

        mockPriceEur.getAmount() >> AMOUNT_EUR
        mockPriceEur.getCurrency() >> Currency.getInstance("EUR")
        mockPriceEur.getValidFrom() >> VALID_FROM_TOMORROW
        mockPriceEur.getValidTo() >> VALID_TO_THE_FUTURE

        mockPriceUsd.getAmount() >> AMOUNT_USD
        mockPriceUsd.getCurrency() >> Currency.getInstance("USD")
        mockPriceUsd.getValidFrom() >> VALID_FROM_TOMORROW
        mockPriceUsd.getValidTo() >> VALID_TO_THE_FUTURE

        mockOneTime.getPrices() >> List.of(mockPriceEur, mockPriceUsd)
    }


    @Test
    def 'null check'() {
        when:
        productExportDataConverter.convert(null)

        then:
        thrown(IllegalArgumentException.class)
    }

    @Test
    def 'when given a valid Product object, returns valid ProductExportData'() {
        when:
        def actualResult = productExportDataConverter.convert(mockProduct)

        then:
        actualResult == expectedFullResult
    }

    @Test
    def 'when given a product without names and prices, empty lists are set'() {
        given:
        def expectedResult = expectedFullResult
        expectedResult.setNames(Map.of())
        expectedResult.setPrices(List.of())

        when: 'prices and names are empty'
        def actualResult = productExportDataConverter.convert(mockProduct)

        then:
        mockProduct.getChargePlan() >> null
        mockProduct.getName() >> List.of()
        actualResult == expectedResult
    }

    @Test
    def 'when given a product with null names and prices, empty lists are set'() {
        given:
        def expectedResult = expectedFullResult
        expectedResult.setNames(Map.of())
        expectedResult.setPrices(List.of())

        when: 'prices and names are null'
        def actualNullResult = productExportDataConverter.convert(mockProduct)

        then:
        mockProduct.getChargePlan() >> null
        mockProduct.getName() >> null
        actualNullResult == expectedResult
    }

    @Test
    def 'when charge plan is null, set empty list of prices'() {
        given:
        def expectedResult = expectedFullResult
        expectedResult.setPrices(List.of())

        when:
        def actualResult = productExportDataConverter.convert(mockProduct)

        then:
        mockProduct.getChargePlan() >> null
        actualResult == expectedResult
    }

    @Test
    def 'when rate is null, set empty list of prices'() {
        given:
        def expectedResult = expectedFullResult
        expectedResult.setPrices(List.of())

        when:
        def actualResult = productExportDataConverter.convert(mockProduct)

        then:
        mockCharge.getRate() >> null
        actualResult == expectedResult
    }

    @Test
    def 'when product is not found by brim id, exception propagates'() {
        given:
        def givenException = new BrimProductNotFoundException("Uahhh")

        when:
        productExportDataConverter.convert(mockProduct)

        then:
        brimAppLicenseService.getCodeForBrimId(BRIM_ID) >> { throw givenException }
        def actualException = thrown(BrimProductNotFoundException)
        actualException.is(givenException)
    }

    @Test
    def 'when product id is invalid, BrimException is thrown'() {
        when:
        productExportDataConverter.convert(mockProduct)

        then:
        mockProduct.getId() >> givenProductId
        thrown(BrimException)

        where:
        givenProductId || _
        null           || _
        ''             || _
        '   '          || _
    }

    def getExpectedFullResult() {
        ProductExportData.builder()
                .id(PRODUCT_ID)
                .categoryId(CATEGORY_ID)
                .names([(Locale.GERMAN): NAME_DE, (Locale.ENGLISH): NAME_EN])
                .prices([mockPriceEur, mockPriceUsd])
                .errors([ERROR_LOG.getDetail()])
                .build()
    }

    def getExpectedEurPrice() {
        Price.builder()
                .amount(new BigDecimal(AMOUNT_EUR))
                .currency(Currency.getInstance("EUR"))
                .recurrence(PriceRecurrence.ONE_TIME)
                .validFrom(VALID_FROM_TOMORROW.atStartOfDay())
                .validTo(VALID_TO_THE_FUTURE.atTime(LocalTime.MAX))
                .build()
    }

    def getExpectedUsdPrice() {
        Price.builder()
                .amount(new BigDecimal(AMOUNT_USD))
                .currency(Currency.getInstance("USD"))
                .recurrence(PriceRecurrence.ONE_TIME)
                .validFrom(VALID_FROM_TOMORROW.atStartOfDay())
                .validTo(VALID_TO_THE_FUTURE.atTime(LocalTime.MAX))
                .build()
    }
}
