package com.sast.cis.brim.converter.orderexport

import com.sast.cis.core.addon.ThlAddon
import com.sast.cis.core.enums.ActivationMode
import com.sast.cis.core.model.AppLicenseModel
import com.sast.cis.core.model.IoTCompanyModel
import com.sast.cis.core.model.RuntimeModel
import com.securityandsafetythings.billing.brim.model.ordercontract.v1.Configuration
import de.hybris.bootstrap.annotations.UnitTest
import de.hybris.platform.core.model.order.OrderEntryModel
import de.hybris.platform.core.model.order.OrderModel
import de.hybris.platform.testframework.JUnitPlatformSpecification
import de.hybris.platform.variants.model.VariantProductModel
import org.apache.commons.lang3.StringUtils
import org.junit.Test

@UnitTest
class ItemConfigurationFactoryUnitSpec extends JUnitPlatformSpecification {
    private static final String PRICE_CODE = 'I_AM_A_PRICE_CODE'
    private ThlAddon thlAddon = Mock()

    private final ItemConfigurationFactory itemConfigurationFactory = new ItemConfigurationFactory(thlAddon)

    private OrderEntryModel orderEntry = Mock()
    private AppLicenseModel variantProduct = Mock()
    private RuntimeModel runtime = Mock()
    private OrderModel order = Mock()
    private IoTCompanyModel company = Mock()

    private long quantity = 5
    private String brimCostCenter = 'brimCostCenter'
    private String brimInvoicingCycle = '3YEAR'

    void setup() {
        runtime.getBrimInvoicingCycle() >> brimInvoicingCycle
        variantProduct.getRuntime() >> runtime

        orderEntry.getOrder() >> order
        orderEntry.getProduct() >> variantProduct
        orderEntry.getQuantity() >> quantity

        order.getCompany() >> company
        company.getBrimCostCenterId() >> brimCostCenter
        thlAddon.isThlDiscountApplicable(orderEntry) >> false
    }

    @Test
    def "given order entry then create item configuration"() {
        when:
        def actualConfiguration = itemConfigurationFactory.createConfigurations(orderEntry)

        then:
        actualConfiguration.size() == 3
        actualConfiguration.containsAll(
                new Configuration().setCharacteristic('QUANTITY').setValue("$quantity"),
                new Configuration().setCharacteristic('COST_CENTER').setValue(brimCostCenter),
                new Configuration().setCharacteristic('INV_CYCLE').setValue(brimInvoicingCycle)
        )
    }

    @Test
    def "given runtime with no inv cycle when create configuration then create without inv cycle config"() {
        when:
        def actualConfiguration = itemConfigurationFactory.createConfigurations(orderEntry)

        then:
        runtime.getBrimInvoicingCycle() >> StringUtils.EMPTY
        actualConfiguration.size() == 2
        actualConfiguration.containsAll(
                new Configuration().setCharacteristic('QUANTITY').setValue("$quantity"),
                new Configuration().setCharacteristic('COST_CENTER').setValue(brimCostCenter)
        )
    }

    @Test
    def "given product with no runtime when create configuration then create without inv cycle config"() {
        when:
        def actualConfiguration = itemConfigurationFactory.createConfigurations(orderEntry)

        then:
        variantProduct.getRuntime() >> null
        actualConfiguration.size() == 2
        actualConfiguration.containsAll(
                new Configuration().setCharacteristic('QUANTITY').setValue("$quantity"),
                new Configuration().setCharacteristic('COST_CENTER').setValue(brimCostCenter)
        )
    }

    @Test
    def "given company with no brim cost center when create configuration then create without cost center cycle config"() {
        when:
        def actualConfiguration = itemConfigurationFactory.createConfigurations(orderEntry)

        then:
        company.getBrimCostCenterId() >> StringUtils.EMPTY
        actualConfiguration.size() == 2
        actualConfiguration.containsAll(
                new Configuration().setCharacteristic('QUANTITY').setValue("$quantity"),
                new Configuration().setCharacteristic('INV_CYCLE').setValue(brimInvoicingCycle)
        )
    }

    @Test
    def "given entry with billing price code, price code is added to configuration"() {
        when:
        def actualConfiguration = itemConfigurationFactory.createConfigurations(orderEntry)

        then:
        orderEntry.getBillingPriceCode() >> PRICE_CODE
        actualConfiguration.size() == 4
        actualConfiguration.containsAll(
                new Configuration().setCharacteristic('QUANTITY').setValue("$quantity"),
                new Configuration().setCharacteristic('INV_CYCLE').setValue(brimInvoicingCycle),
                new Configuration().setCharacteristic('COST_CENTER').setValue(brimCostCenter),
                new Configuration().setCharacteristic('PRICE_CODE_ID').setValue(PRICE_CODE)
        )
    }

    @Test
    def "given order entry with delayed activation reduced price is added to configuration"() {
        when:
        def actualConfiguration = itemConfigurationFactory.createConfigurations(orderEntry)

        then:
        order.getActivationMode() >> ActivationMode.DELAYED
        orderEntry.getBasePrice() >> 100.0
        actualConfiguration.size() == 5
        actualConfiguration.containsAll(
                new Configuration().setCharacteristic('QUANTITY').setValue("$quantity"),
                new Configuration().setCharacteristic('INV_CYCLE_RP_APPLICABLE').setValue('1'),
                new Configuration().setCharacteristic('REDUCED_AMOUNT').setValue('100.0'),
        )
    }

    @Test
    def "given order entry for THL discount then create item configuration"() {
        when:
        def actualConfiguration = itemConfigurationFactory.createConfigurations(orderEntry)

        then:
        order.getActivationMode() >> ActivationMode.DELAYED
        thlAddon.isThlDiscountApplicable(orderEntry) >> true
        orderEntry.getAddOnUg() >> "IDW000"
        orderEntry.getOriginalPrice() >> 100.0
        variantProduct.getThlGroupDiscount() >> Map.of("IDW000","10.5")
        actualConfiguration.size() == 8
        actualConfiguration.containsAll(
                new Configuration().setCharacteristic('DISC_MONTH').setValue("9999"),
                new Configuration().setCharacteristic('DISC_PERCENT').setValue("10.5"),
                new Configuration().setCharacteristic('FREEMIUM_FLAG').setValue("X"),
                new Configuration().setCharacteristic('REDUCED_AMOUNT').setValue('100.0'),
        )
    }

    @Test
    def "given immediate order entry for THL discount then create item configuration without the REDUCED_AMOUNT field"() {
        when:
        def actualConfiguration = itemConfigurationFactory.createConfigurations(orderEntry)

        then:
        order.isMigrationOrder() >> true
        thlAddon.isThlDiscountApplicable(orderEntry) >> true
        orderEntry.getAddOnUg() >> "IDW000"
        orderEntry.getOriginalPrice() >> 100.0
        variantProduct.getThlGroupDiscount() >> Map.of("IDW000","10.5")
        actualConfiguration.size() == 6
        actualConfiguration.containsAll(
                new Configuration().setCharacteristic('DISC_MONTH').setValue("9999"),
                new Configuration().setCharacteristic('DISC_PERCENT').setValue("10.5"),
                new Configuration().setCharacteristic('FREEMIUM_FLAG').setValue("X")
        )
        actualConfiguration.stream().noneMatch { it.getCharacteristic() == 'REDUCED_AMOUNT' }
    }
}
