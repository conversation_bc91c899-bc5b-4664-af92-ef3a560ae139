<!--
 [y] hybris Platform

 Copyright (c) 2018 SAP SE or an SAP affiliate company. All rights reserved.

 This software is the confidential and proprietary information of SAP
 ("Confidential Information"). You shall not disclose such Confidential
 Information and shall use it only in accordance with the terms of the
 license agreement you entered into with SAP.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<groupId>de.hybris.platform.platformwebservices</groupId>
	<artifactId>platformwebservices.web</artifactId>
	<version>6.7.0.0-RC19</version>

	<packaging>jar</packaging>


	<properties>
		<jersey.version>1.13</jersey.version>
	</properties>

	<dependencies>
		<dependency>
		  <groupId>com.sun.jersey</groupId>
		  <artifactId>jersey-core</artifactId>
		  <version>${jersey.version}</version>
		</dependency>
		<dependency>
   			<groupId>com.sun.jersey</groupId>
   			<artifactId>jersey-client</artifactId>
		  <version>${jersey.version}</version>
		</dependency>	
		<dependency>
		  <groupId>com.sun.jersey</groupId>
		  <artifactId>jersey-json</artifactId>
		  <version>${jersey.version}</version>
		</dependency>
		<dependency>
		  <groupId>com.sun.jersey</groupId>
		  <artifactId>jersey-server</artifactId>
		  <version>${jersey.version}</version>
		</dependency>
		<dependency>
		  <groupId>com.sun.jersey</groupId>
		  <artifactId>jersey-servlet</artifactId>
		  <version>${jersey.version}</version>
		</dependency>
		<dependency>
		  <groupId>com.sun.jersey.contribs</groupId>
		  <artifactId>jersey-spring</artifactId>
		  <version>${jersey.version}</version>
		</dependency>
		<dependency>
		   <groupId>com.sun.jersey.jersey-test-framework</groupId>
		   <artifactId>jersey-test-framework-core</artifactId>
		    <version>${jersey.version}</version>
		    <scope>test</scope>
		</dependency>
		<dependency>
			<groupId>javax.ws.rs</groupId>
			<artifactId>jsr311-api</artifactId>
			<version>1.1.1</version>
		</dependency>
		
	</dependencies>
</project>
