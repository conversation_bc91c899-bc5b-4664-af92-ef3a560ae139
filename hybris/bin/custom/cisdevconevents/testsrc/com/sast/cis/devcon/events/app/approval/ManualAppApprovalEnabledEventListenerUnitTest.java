package com.sast.cis.devcon.events.app.approval;

import com.google.common.collect.ImmutableSet;
import com.sast.cis.core.dao.CatalogVersion;
import com.sast.cis.core.enums.StoreAvailabilityMode;
import com.sast.cis.core.event.approval.ManualAppApprovalEnableEventPayload;
import com.sast.cis.core.event.approval.ManualAppApprovalEnabledEvent;
import com.sast.cis.core.model.AppDraftModel;
import com.sast.cis.core.model.AppModel;
import com.sast.cis.core.model.IoTCompanyModel;
import com.sast.cis.core.model.ProductContainerModel;
import com.sast.cis.core.service.AppSyncService;
import com.sast.cis.core.service.ProductCatalogIdentifierService;
import com.sast.cis.core.service.company.IotCompanyService;
import de.hybris.bootstrap.annotations.UnitTest;
import de.hybris.platform.catalog.CatalogVersionService;
import de.hybris.platform.catalog.model.CatalogModel;
import de.hybris.platform.catalog.model.CatalogVersionModel;
import de.hybris.platform.core.model.c2l.CountryModel;
import de.hybris.platform.servicelayer.model.ModelService;
import generated.com.sast.cis.core.model.AppBuilder;
import generated.com.sast.cis.core.model.AppDraftBuilder;
import generated.com.sast.cis.core.model.IoTCompanyBuilder;
import generated.com.sast.cis.core.model.ProductContainerBuilder;
import generated.de.hybris.platform.catalog.model.CatalogBuilder;
import generated.de.hybris.platform.core.model.c2l.CountryBuilder;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Optional;
import java.util.Set;

import static com.sast.cis.core.enums.StoreAvailabilityMode.PUBLIC;
import static com.sast.cis.core.enums.StoreAvailabilityMode.RESTRICTED_BUYER;
import static org.mockito.Matchers.anyString;
import static org.mockito.Mockito.*;

@UnitTest
@RunWith(MockitoJUnitRunner.Silent.class)

public class ManualAppApprovalEnabledEventListenerUnitTest {
    private static final String COMPANY1_ID = "company001";
    private static final String COMPANY2_ID = "company002";
    private static final String SELLER_ID = "seller001";
    private static final String APP1_CODE = "A_0001";
    private static final String APP2_CODE = "A_0002";
    private static final String APP3_CODE = "A_0003";
    private static final String APPDRAFT1_CODE = "AD_0001";
    private static final String APPDRAFT2_CODE = "AD_0002";
    private static final String APPDRAFT3_CODE = "AD_0003";

    private static final String DE = "DE";
    private static final String US = "US";
    private static final String CATALOG_ID = "catalogId";

    @Mock
    private IotCompanyService iotCompanyService;

    @Mock
    private AppSyncService appSyncService;

    @Mock
    private ModelService modelService;
    @Mock
    private ProductCatalogIdentifierService productCatalogIdentifierService;
    @Mock
    private CatalogVersionService catalogVersionService;

    @InjectMocks
    private ManualAppApprovalEnabledEventListener listener;

    @Mock
    private CatalogVersionModel catalogVersion;

    private IoTCompanyModel germanBuyer;

    private ProductContainerModel productContainer1;
    private ProductContainerModel productContainer2;

    private AppModel appWithInvalidBuyer;
    private AppModel appWithInvalidAvailabilityMode;

    private AppDraftModel draftWithInvalidBuyer;
    private AppDraftModel draftWithInvalidAvailabilityMode;

    @Mock
    private ManualAppApprovalEnableEventPayload manualAppApprovalEnableEventPayload;

    @Before
    public void setUp() {
        when(catalogVersion.getVersion()).thenReturn(CatalogVersion.STAGED.getVersionName());
        CatalogModel catalog = CatalogBuilder.generate().withId(CATALOG_ID).buildMockInstance();
        when(productCatalogIdentifierService.getAllBaseStoreCatalogVersions(CatalogVersion.STAGED)).thenReturn(Set.of(catalogVersion));
        doNothing().when(catalogVersionService).setSessionCatalogVersion(CATALOG_ID,CatalogVersion.STAGED.getVersionName());
        when(catalogVersion.getCatalog()).thenReturn(catalog);
        CountryModel germany = CountryBuilder.generate().withIsocode(DE).withInEu(true).buildMockInstance();
        CountryModel usa = CountryBuilder.generate().withIsocode(US).withInEu(false).buildMockInstance();

        germanBuyer = createBuyerCompany(COMPANY1_ID, germany);
        IoTCompanyModel usBuyer = createBuyerCompany(COMPANY2_ID, usa);

        appWithInvalidBuyer = createApp(APP1_CODE, RESTRICTED_BUYER, ImmutableSet.of(germanBuyer, usBuyer));
        appWithInvalidAvailabilityMode = createApp(APP2_CODE, PUBLIC, ImmutableSet.of());
        AppModel appWithoutIssues = createApp(APP3_CODE, RESTRICTED_BUYER, ImmutableSet.of(germanBuyer));

        draftWithInvalidBuyer = createAppDraft(APPDRAFT1_CODE, RESTRICTED_BUYER, ImmutableSet.of(germanBuyer, usBuyer));
        draftWithInvalidAvailabilityMode = createAppDraft(APPDRAFT2_CODE, PUBLIC, ImmutableSet.of());
        AppDraftModel draftWithoutIssues = createAppDraft(APPDRAFT3_CODE, RESTRICTED_BUYER, ImmutableSet.of(germanBuyer));

        productContainer1 = ProductContainerBuilder.generate().withAppDraft(draftWithInvalidBuyer).buildMockInstance();
        productContainer2 = ProductContainerBuilder.generate().withAppDraft(draftWithInvalidAvailabilityMode).buildMockInstance();
        ProductContainerModel productContainer3 = ProductContainerBuilder.generate().withAppDraft(draftWithoutIssues).buildMockInstance();

        IoTCompanyModel sellerCompany = createSellerCompany(SELLER_ID, ImmutableSet.of(appWithInvalidBuyer, appWithInvalidAvailabilityMode,
            appWithoutIssues), ImmutableSet.of(productContainer1, productContainer2, productContainer3), true);

        when(iotCompanyService.getCompanyByUid(anyString())).thenReturn(Optional.of(sellerCompany));
        when(manualAppApprovalEnableEventPayload.getCompanyId()).thenReturn(SELLER_ID);
        when(manualAppApprovalEnableEventPayload.isManualAppApprovalEnabled()).thenReturn(false);
    }

    @Test(expected = IllegalArgumentException.class)
    public void onEvent_when_provided_event_is_null_then_throw_exception() {
        listener.onEvent(null);
    }

    @Test(expected = IllegalStateException.class)
    public void onEvent_when_provided_eventpayload_is_empty_then_throw_exception() {
        ManualAppApprovalEnableEventPayload manualAppApprovalEnableEventPayload = ManualAppApprovalEnableEventPayload.builder()
            .companyId("").build();
        listener.onEvent(new ManualAppApprovalEnabledEvent(manualAppApprovalEnableEventPayload));
    }

    @Test(expected = IllegalStateException.class)
    public void onEvent_when_provided_invalid_company_then_throw_exception() {
        when(iotCompanyService.getCompanyByUid(anyString())).thenThrow(IllegalStateException.class);
        listener.onEvent(new ManualAppApprovalEnabledEvent(manualAppApprovalEnableEventPayload));
    }

    @Test
    public void onEvent_when_manualAppApproval_is_enable_then_do_nothing() {
        when(manualAppApprovalEnableEventPayload.isManualAppApprovalEnabled()).thenReturn(true);
        listener.onEvent(new ManualAppApprovalEnabledEvent(manualAppApprovalEnableEventPayload));
        verify(iotCompanyService, never()).getCompanyByUid(anyString());
        verifyNoMoreInteractions(catalogVersionService);
    }

    @Test
    public void onEvent_process_the_event_removed_nonEUCountry_buyers() {
        listener.onEvent(new ManualAppApprovalEnabledEvent(manualAppApprovalEnableEventPayload));

        verify(appWithInvalidBuyer).setPermittedBuyerCompanies(Set.of(germanBuyer));
        verify(appWithInvalidAvailabilityMode).setStoreAvailabilityMode(StoreAvailabilityMode.UNAVAILABLE);
        verify(draftWithInvalidBuyer).setPermittedBuyerCompanies(Set.of(germanBuyer));
        verify(draftWithInvalidAvailabilityMode).setStoreAvailabilityMode(StoreAvailabilityMode.UNAVAILABLE);
        verify(modelService, times(1)).saveAll(Set.of(appWithInvalidBuyer, appWithInvalidAvailabilityMode));
        verify(modelService).saveAll(Set.of(draftWithInvalidBuyer, draftWithInvalidAvailabilityMode));
        verify(appSyncService, times(1)).performSync(Set.of(appWithInvalidBuyer, appWithInvalidAvailabilityMode), CATALOG_ID);
    }

    @Test
    public void onEvent_with_no_appDraft_process_the_event_removed_nonEUCountry_buyers() {
        when(productContainer1.getAppDraft()).thenReturn(null);
        when(productContainer2.getAppDraft()).thenReturn(null);

        listener.onEvent(new ManualAppApprovalEnabledEvent(manualAppApprovalEnableEventPayload));

        verify(catalogVersionService, times(1)).setSessionCatalogVersions(Set.of(catalogVersion));
        verify(modelService).saveAll(Set.of(appWithInvalidBuyer, appWithInvalidAvailabilityMode));
        verifyNoMoreInteractions(modelService);
        verify(appSyncService, times(1)).performSync(Set.of(appWithInvalidBuyer, appWithInvalidAvailabilityMode), CATALOG_ID);
    }

    @Test
    public void onEvent_noModificationsRequired_noSaveOrSyncPerformed() {
        when(appWithInvalidBuyer.getPermittedBuyerCompanies()).thenReturn(Set.of(germanBuyer));
        when(appWithInvalidAvailabilityMode.getStoreAvailabilityMode()).thenReturn(StoreAvailabilityMode.UNAVAILABLE);
        when(draftWithInvalidBuyer.getPermittedBuyerCompanies()).thenReturn(Set.of(germanBuyer));
        when(draftWithInvalidAvailabilityMode.getStoreAvailabilityMode()).thenReturn(StoreAvailabilityMode.UNAVAILABLE);

        listener.onEvent(new ManualAppApprovalEnabledEvent(manualAppApprovalEnableEventPayload));
        verify(catalogVersionService, times(1)).setSessionCatalogVersions(Set.of(catalogVersion));
        verifyZeroInteractions(modelService);
    }

    private IoTCompanyModel createSellerCompany(String uid, ImmutableSet<AppModel> apps, ImmutableSet<ProductContainerModel> containers,
        boolean manualApprovalEnabled) {
        return IoTCompanyBuilder.generate()
            .withUid(uid)
            .withApps(apps)
            .withProductContainers(containers)
            .withManualAppApprovalEnabled(manualApprovalEnabled)
            .buildMockInstance();
    }

    private AppModel createApp(String code, StoreAvailabilityMode availabilityMode, ImmutableSet<IoTCompanyModel> permittedBuyers) {
        return AppBuilder.generate()
            .withCode(code)
            .withCatalogVersion(catalogVersion)
            .withStoreAvailabilityMode(availabilityMode)
            .withPermittedBuyerCompanies(permittedBuyers)
            .buildMockInstance();
    }

    private AppDraftModel createAppDraft(String code, StoreAvailabilityMode availabilityMode,
        ImmutableSet<IoTCompanyModel> permittedBuyers) {
        return AppDraftBuilder.generate()
            .withCode(code)
            .withStoreAvailabilityMode(availabilityMode)
            .withPermittedBuyerCompanies(permittedBuyers)
            .buildMockInstance();
    }

    private IoTCompanyModel createBuyerCompany(String uid, CountryModel country) {
        return IoTCompanyBuilder.generate()
            .withUid(uid)
            .withCountry(country)
            .buildMockInstance();
    }

}
