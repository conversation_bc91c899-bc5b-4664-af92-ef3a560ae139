<?xml version="1.0" encoding="UTF-8"?>
<!--
 [y] hybris Platform

 Copyright (c) 2017 SAP SE or an SAP affiliate company.  All rights reserved.

 This software is the confidential and proprietary information of SAP
 ("Confidential Information"). You shall not disclose such Confidential
 Information and shall use it only in accordance with the terms of the
 license agreement you entered into with SAP.
-->
<ruleset xmlns="http://pmd.sourceforge.net/ruleset/2.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         name="pmd-eclipse"
         xsi:schemaLocation="http://pmd.sourceforge.net/ruleset/2.0.0 http://pmd.sourceforge.net/ruleset_2_0_0.xsd">
         
	<exclude-pattern>.*/generated-sources/.*</exclude-pattern>
	<exclude-pattern>.*/Generated/.*</exclude-pattern>
	<exclude-pattern>.*/gensrc/.*</exclude-pattern>
	<exclude-pattern>.*/jsp/.*</exclude-pattern>
	<exclude-pattern>.*_jsp.java</exclude-pattern>
	<exclude-pattern>.*/jax-doclets/.*</exclude-pattern>

	<description>Java PMD ruleset for hybris</description>

   <rule ref="rulesets/java/basic.xml/JumbledIncrementer">
        <priority>2</priority>
   </rule>      
   <rule ref="rulesets/java/basic.xml/ForLoopShouldBeWhileLoop"/>
   <rule ref="rulesets/java/basic.xml/OverrideBothEqualsAndHashcode"/>
   <rule ref="rulesets/java/basic.xml/DoubleCheckedLocking"/>
   <rule ref="rulesets/java/basic.xml/ReturnFromFinallyBlock">
           <priority>1</priority>
   </rule>      
   <rule ref="rulesets/java/basic.xml/UnconditionalIfStatement"/>
   <rule ref="rulesets/java/basic.xml/BooleanInstantiation"/>
   <rule ref="rulesets/java/basic.xml/CollapsibleIfStatements"> 
        <priority>2</priority>
   </rule>        
   
   <rule ref="rulesets/java/basic.xml/ClassCastExceptionWithToArray">
           <priority>1</priority>
   </rule>      
   <rule ref="rulesets/java/basic.xml/AvoidDecimalLiteralsInBigDecimalConstructor"/>
   <rule ref="rulesets/java/basic.xml/MisplacedNullCheck">
           <priority>1</priority>
   </rule>      
   <rule ref="rulesets/java/basic.xml/AvoidThreadGroup"/>
   <rule ref="rulesets/java/basic.xml/BrokenNullCheck"/>
   <rule ref="rulesets/java/basic.xml/BigIntegerInstantiation"/>
   <rule ref="rulesets/java/basic.xml/AvoidUsingOctalValues"/>
   <rule ref="rulesets/java/basic.xml/AvoidUsingHardCodedIP"/>
   <rule ref="rulesets/java/basic.xml/CheckResultSet"/>
   <rule ref="rulesets/java/basic.xml/AvoidMultipleUnaryOperators"/>

   <rule ref="rulesets/java/braces.xml/IfStmtsMustUseBraces">
           <priority>2</priority>
   </rule>      
   <rule ref="rulesets/java/braces.xml/WhileLoopsMustUseBraces">
           <priority>2</priority>
   </rule>      
   <rule ref="rulesets/java/braces.xml/IfElseStmtsMustUseBraces">
           <priority>2</priority>
   </rule>      
   <rule ref="rulesets/java/braces.xml/ForLoopsMustUseBraces">
           <priority>2</priority>
   </rule>      

   <rule ref="rulesets/java/clone.xml/ProperCloneImplementation"/>
   <rule ref="rulesets/java/clone.xml/CloneThrowsCloneNotSupportedException"/>
   <rule ref="rulesets/java/clone.xml/CloneMethodMustImplementCloneable"/>
   <rule ref="rulesets/java/codesize.xml/ExcessiveMethodLength"/>

   <rule name="SpaghettiCodeWithComments" ref="rulesets/java/codesize.xml/ExcessiveMethodLength">
   <priority>2</priority>
   <properties>
    <property name="minimum" description="The method size reporting threshold" value="200"/>
   </properties>
   </rule>

   <rule ref="rulesets/java/codesize.xml/ExcessiveParameterList">
     <priority>3</priority>
   <properties>
    <property name="minimum" description="The parameter count reporting threshold" value="7"/>
   </properties>
   </rule>

   <rule name="ExcessiveParameterListOver10" ref="rulesets/java/codesize.xml/ExcessiveParameterList">
        <priority>3</priority>
   <properties>
    <property name="minimum" description="The parameter count reporting threshold" value="10"/>
   </properties>
   </rule>

   <rule ref="rulesets/java/codesize.xml/ExcessiveClassLength">
        <properties>
            <property name="minimum" description="The class size reporting threshold"  value="1500"/>
        </properties>
   </rule>
   
   <rule ref="rulesets/java/codesize.xml/CyclomaticComplexity"/>
   <rule ref="rulesets/java/codesize.xml/TooManyFields"/>
   <rule ref="rulesets/java/codesize.xml/NcssMethodCount">
        <properties>
            <property name="minimum" description="The method NCSS count reporting threshold" value="40"/>
        </properties>
   </rule>
       
   <rule name="SpaghettiCode" ref="rulesets/java/codesize.xml/NcssMethodCount">
      <priority>2</priority>
    <properties>
        <property name="minimum" description="The method NCSS count reporting threshold" value="100"/>
    </properties>
    </rule> 
   <rule ref="rulesets/java/codesize.xml/NcssTypeCount">
        <properties>
            <property name="minimum" description="The type NCSS count reporting threshold" value="1000"/>
        </properties>
   </rule>
   
   
   <rule ref="rulesets/java/codesize.xml/NcssConstructorCount">
        <properties>
            <property name="minimum" description="The constructor NCSS count reporting threshold" value="40"/>
        </properties> 
   </rule>
            
   <rule ref="rulesets/java/codesize.xml/TooManyMethods">
        <priority>4</priority>
        <properties>                
                  <property name="maxmethods"  description="The method count reporting threshold " value="30"/> 
   <property name="violationSuppressXPath">
   	                <value><![CDATA[
//./ClassOrInterfaceDeclaration[(ends-with(@Image, 'Test'))]
	             ]]></value>
	            </property>
        </properties>
   </rule> 
   
      <!-- Custom Rule -->
   <rule name="TooManyPublicMethods" ref="rulesets/java/codesize.xml/TooManyMethods">
        <priority>4</priority>
        <properties>
                <property name="maxmethods"  description="The method count reporting threshold " value="10"/>
                <property name="xpath">
                <value>
                    <![CDATA[
					//ClassOrInterfaceDeclaration/ClassOrInterfaceBody [
						count(descendant::MethodDeclarator[
							not
							(
								starts-with(@Image,'get')
								or
								starts-with(@Image,'set')
								or
								starts-with(@Image,'is')
								or
								starts-with(@Image,'should')
								or
								starts-with(@Image,'test')
							)
							and ( ../@Public = 'true' )
						]) > $maxmethods
					]
                    ]]>
                </value>
            </property>
        </properties>
    </rule>   
   
   <rule ref="rulesets/java/controversial.xml/UnnecessaryConstructor">
        <priority>4</priority>
   </rule>   
   <rule ref="rulesets/java/controversial.xml/DontImportSun">
           <priority>2</priority>
   </rule>   
   
   <rule ref="rulesets/java/controversial.xml/SuspiciousOctalEscape"/>
   <rule ref="rulesets/java/controversial.xml/UnnecessaryParentheses">
           <priority>4</priority>
   </rule>   
   
   <rule ref="rulesets/java/controversial.xml/AvoidAccessibilityAlteration"/>
  <!--  Modified Rule to ignore Performancetests -->  
   <rule ref="rulesets/java/controversial.xml/DoNotCallGarbageCollectionExplicitly">
           <priority>3</priority> 
      <properties>
         <property name="xpath">
            <value>
            <![CDATA[
//ClassOrInterfaceDeclaration[not((starts-with(@Image, 'Performance')))]//Name[
(starts-with(@Image, 'System.') and
(starts-with(@Image, 'System.gc') or
starts-with(@Image, 'System.runFinalization'))) or
(starts-with(@Image,'Runtime.getRuntime') and
../../PrimarySuffix[ends-with(@Image,'gc')])]
 ]]>
</value> 
         </property>
      </properties>
   </rule>

   <rule ref="rulesets/java/coupling.xml/CouplingBetweenObjects"/>
   <rule ref="rulesets/java/coupling.xml/ExcessiveImports">
     <properties>
     <property name="minimum" description="The import count reporting threshold" value="35"/>
   <property name="violationSuppressXPath">
   	                <value><![CDATA[
//./ClassOrInterfaceDeclaration[(ends-with(@Image, 'Test'))]
	             ]]></value>
	            </property>
  
    </properties>
    </rule> 
    
   <rule ref="rulesets/java/coupling.xml/LooseCoupling">
         <priority>2</priority>   
   </rule> 
    
   <rule ref="rulesets/java/design.xml/UseSingleton"/>
   <rule ref="rulesets/java/design.xml/SimplifyBooleanReturns"/>
   <rule ref="rulesets/java/design.xml/SimplifyBooleanExpressions"/>
   <rule ref="rulesets/java/design.xml/SwitchStmtsShouldHaveDefault"/>
   <rule ref="rulesets/java/design.xml/AvoidDeeplyNestedIfStmts"/>
   <rule ref="rulesets/java/design.xml/SwitchDensity"/>
   <rule ref="rulesets/java/design.xml/ConstructorCallsOverridableMethod"/>   
   <rule ref="rulesets/java/design.xml/FinalFieldCouldBeStatic">
            <priority>2</priority>   
   </rule> 
   
   <rule ref="rulesets/java/design.xml/CloseResource">
            <priority>2</priority>   
   </rule> 
   
   <rule ref="rulesets/java/design.xml/NonStaticInitializer">
               <priority>4</priority>   
   </rule> 
   
   <rule ref="rulesets/java/design.xml/DefaultLabelNotLastInSwitchStmt"/>
   <rule ref="rulesets/java/design.xml/NonCaseLabelInSwitchStatement">
               <priority>2</priority>   
   </rule> 
   
   <rule ref="rulesets/java/design.xml/OptimizableToArrayCall"/>
   <rule ref="rulesets/java/design.xml/BadComparison">
               <priority>2</priority>   
   </rule> 
   
   <rule ref="rulesets/java/design.xml/EqualsNull"/>
   <rule ref="rulesets/java/design.xml/InstantiationToGetClass">
               <priority>2</priority>   
   </rule> 
   
   <rule ref="rulesets/java/design.xml/IdempotentOperations">
               <priority>2</priority>   
   </rule> 
   
   <rule ref="rulesets/java/design.xml/ImmutableField"/>
   <rule ref="rulesets/java/design.xml/UseLocaleWithCaseConversions">
               <priority>5</priority>   
   </rule> 
   
   <rule ref="rulesets/java/design.xml/AssignmentToNonFinalStatic">
               <priority>4</priority>   
   </rule> 
   
   <rule ref="rulesets/java/design.xml/MissingStaticMethodInNonInstantiatableClass"/>
   <rule ref="rulesets/java/design.xml/AvoidSynchronizedAtMethodLevel"/>
   <rule ref="rulesets/java/design.xml/MissingBreakInSwitch"/>
   <rule ref="rulesets/java/design.xml/UseNotifyAllInsteadOfNotify"/>
   <rule ref="rulesets/java/design.xml/AvoidInstanceofChecksInCatchClause"/>
   <rule ref="rulesets/java/design.xml/AbstractClassWithoutAbstractMethod"/>
   <rule ref="rulesets/java/design.xml/SimplifyConditional"/>
   <rule ref="rulesets/java/design.xml/CompareObjectsWithEquals"/>
   <rule ref="rulesets/java/design.xml/PositionLiteralsFirstInComparisons"/>
   <rule ref="rulesets/java/design.xml/UnnecessaryLocalBeforeReturn"/>
   <rule ref="rulesets/java/design.xml/NonThreadSafeSingleton"/>   
   <rule ref="rulesets/java/design.xml/UncommentedEmptyConstructor"/>
   <rule ref="rulesets/java/design.xml/AvoidConstantsInterface">
      <priority>4</priority>   
    </rule>   
    
   <rule ref="rulesets/java/design.xml/UnsynchronizedStaticDateFormatter">
      <priority>2</priority>   
   </rule>    
   
   <rule ref="rulesets/java/design.xml/PreserveStackTrace">
       <priority>2</priority>   
   </rule>    
        
   <rule ref="rulesets/java/design.xml/UseCollectionIsEmpty">
         <priority>4</priority>   
   </rule>    
      
   <rule ref="rulesets/java/design.xml/ClassWithOnlyPrivateConstructorsShouldBeFinal"/>

   <rule ref="rulesets/java/design.xml/SingularField">
         <priority>1</priority>   
   </rule>    
   
   <rule ref="rulesets/java/design.xml/ReturnEmptyArrayRatherThanNull">
         <priority>2</priority>   
   </rule>    
      
   <rule ref="rulesets/java/design.xml/AbstractClassWithoutAnyMethod"/>
   <rule ref="rulesets/java/design.xml/TooFewBranchesForASwitchStatement">
         <priority>3</priority>   
   </rule>    
   
   <rule name="UncommentedEmptyBlock"
          language="java"
          since="3.4"
          message="Empty and uncommented block"
          class="net.sourceforge.pmd.lang.rule.XPathRule"
          externalInfoUrl="http://pmd.sourceforge.net/rules/design.html#UncommentedEmptyMethod">
      <description>
By explicitly commenting empty blocks
it is easier to distinguish between intentional (commented) and unintentional
empty block.
      </description>
      <priority>3</priority>
      <properties>
          <property name="xpath">
              <value>
    <![CDATA[
//Block[count(BlockStatement) = 0 and @containsComment = 'false']
 ]]>
             </value>
          </property>
      </properties>
      <example>
  <![CDATA[
public void doSomething() {
}
 ]]>
      </example>
    </rule>
   
<rule name="MemberScope"
            language="java"
             message="All instance variable must be private."
        	class="net.sourceforge.pmd.lang.rule.XPathRule">
         <description>
All instance and class variables must be private. Class constants (which are static and final) can have other scopes.
         </description>
         <priority>2</priority>
         <properties>
             <property name="xpath">
                 <value>
<![CDATA[
//FieldDeclaration[not(@Private='true' or (@Static='true' and @Final='true'))]
 ]]>
                 </value>
             </property>
         </properties>
        <example>
<![CDATA[
public class Bar {
 private int x;
 protected int y;  // all members must be private!
 Bar() {}
}
 ]]>
         </example>
       </rule>
       
   <rule ref="rulesets/java/empty.xml/EmptyCatchBlock">
      <priority>2</priority>   
       <properties> 
            <property name="allowCommentedBlocks" value="true"/>
      </properties> 
   </rule>  
   
   <rule ref="rulesets/java/empty.xml/EmptyIfStmt">
      <priority>2</priority>   
   </rule>  

   <rule ref="rulesets/java/empty.xml/EmptyWhileStmt">
      <priority>2</priority>   
   </rule>  

   <rule ref="rulesets/java/empty.xml/EmptyTryBlock">
       <priority>2</priority>   
   </rule>    
   
   <rule ref="rulesets/java/empty.xml/EmptyFinallyBlock">
         <priority>2</priority>   
   </rule>  
   
   <rule ref="rulesets/java/empty.xml/EmptySwitchStatements">
         <priority>2</priority>   
   </rule>  
   
   <rule ref="rulesets/java/empty.xml/EmptySynchronizedBlock">
          <priority>2</priority>   
   </rule>    
   
   <rule ref="rulesets/java/empty.xml/EmptyStatementNotInLoop">
           <priority>2</priority>   
   </rule>   
    <rule ref="rulesets/java/empty.xml/EmptyInitializer">
           <priority>2</priority>   
   </rule>     
   
   <rule ref="rulesets/java/empty.xml/EmptyStaticInitializer">
           <priority>2</priority>   
   </rule>     
   
   <rule ref="rulesets/java/finalizers.xml/EmptyFinalizer">
           <priority>2</priority>   
   </rule>   
   
   <rule ref="rulesets/java/finalizers.xml/FinalizeOnlyCallsSuperFinalize">
              <priority>2</priority>   
   </rule>   
   
   <rule ref="rulesets/java/finalizers.xml/FinalizeOverloaded">
              <priority>2</priority>   
   </rule>   
   
   <rule ref="rulesets/java/finalizers.xml/FinalizeDoesNotCallSuperFinalize">
              <priority>2</priority>   
   </rule>   
   
   <rule ref="rulesets/java/finalizers.xml/FinalizeShouldBeProtected">
              <priority>2</priority>   
   </rule>   
   
   <rule ref="rulesets/java/finalizers.xml/AvoidCallingFinalize">
              <priority>2</priority>   
   </rule>   
   

   <rule ref="rulesets/java/imports.xml/DuplicateImports"/>
   <rule ref="rulesets/java/imports.xml/DontImportJavaLang"/>
   <rule ref="rulesets/java/imports.xml/UnusedImports"/>
   <rule ref="rulesets/java/imports.xml/ImportFromSamePackage"/>
   
   <rule name="ImportFromChildPackage" 
   language="java"
   class="net.sourceforge.pmd.lang.rule.XPathRule"
   message="You must not import from a child package.">   
       <description>
You must not import from a child package. It usually indicates coupling to a specific implementation rather than referencing the interface of the implementation.
       </description>
       <priority>3</priority>
        <properties>
            <property name="xpath">
                <value><![CDATA[
/ImportDeclaration/Name[starts-with(@Image,concat(/PackageDeclaration/Name/@Image, '.'))]
]]></value>
            </property>
        </properties>
       <example>
<![CDATA[
package com.hybris.cool.stuff;

// OK - importing from parent is totally fine
import com.hybris.cool;

// OK - importing from package on same hierachy level is fine
import com.hybris.cool.muuh;

// ERROR - but you must not import from deeper/child packages
import com.hybris.cool.stuff.impl;

public class MyClass {
// ...
}
]]>
       </example>
     </rule>
   <rule name="DoNotUseImportWildcards" language="java"
   	        since="4.1"
        	class="net.sourceforge.pmd.lang.rule.XPathRule"
        	message="Do not use import wildcards. Keep your code explicit.">
       		<description> Do not use import wildcards. Keep your code explicit.</description>
			<priority>3</priority>
       		<properties>
	            <property name="xpath">
	                <value><![CDATA[
//ImportDeclaration[@ImportedName=@PackageName]
	             ]]></value>
	            </property>
	        </properties>
    	 	<example><![CDATA[
import java.util.*; // Please no!
import java.awt.*; // Which List to use now?
		  ]]></example>
    </rule> 

 
   <rule ref="rulesets/java/javabeans.xml/MissingSerialVersionUID"/>

   <rule ref="rulesets/java/junit.xml/JUnitStaticSuite"/>
   <rule ref="rulesets/java/junit.xml/JUnitSpelling"/>
   <rule ref="rulesets/java/junit.xml/JUnitTestsShouldIncludeAssert">
              <priority>4</priority>   
                 <properties>
   <property name="violationSuppressXPath">
   	                <value><![CDATA[
//ClassOrInterfaceDeclaration
[
@Interface='false'
and (ends-with(@Image, 'Test'))
]
//ClassOrInterfaceBodyDeclaration
[
.//MarkerAnnotation/Name[typeof(@Image, 'org.junit.Test', 'Test')]
and(
.//StatementExpression/PrimaryExpression
[
starts-with(.//PrimaryPrefix/Name/@Image,'assert')
or
starts-with(.//PrimarySuffix/@Image,'assert')
or
starts-with(.//PrimaryPrefix/Name/@Image,'Assert.assert')
or
starts-with(.//PrimaryPrefix/Name/@Image,'Assertions.assert')
or
starts-with(.//PrimarySuffix/@Image,'Assert.assert')
or
starts-with(.//PrimaryPrefix/Name/@Image,'fail')
or
starts-with(.//PrimarySuffix/@Image,'fail')
or
starts-with(.//PrimaryPrefix/Name/@Image,'Assert.fail')
or
starts-with(.//PrimarySuffix/@Image,'Assert.fail')
or
starts-with(.//PrimaryPrefix/Name/@Image,'verify')
or
starts-with(.//PrimarySuffix/@Image,'verify')
or
starts-with(.//PrimaryPrefix/Name/@Image,'Mockito.verify')
or
starts-with(.//PrimarySuffix/@Image,'Mockito.verify')
])]
	             ]]></value>
	            </property>
	        </properties>   
   </rule>   
   
   <rule ref="rulesets/java/junit.xml/TestClassWithoutTestCases"/>
   <rule ref="rulesets/java/junit.xml/UnnecessaryBooleanAssertion"/>
   <rule ref="rulesets/java/junit.xml/UseAssertEqualsInsteadOfAssertTrue"/>
   <rule ref="rulesets/java/junit.xml/UseAssertSameInsteadOfAssertTrue"/>
   <rule ref="rulesets/java/junit.xml/UseAssertNullInsteadOfAssertTrue"/>
   <rule ref="rulesets/java/junit.xml/SimplifyBooleanAssertion"/>

   <rule ref="rulesets/java/logging-jakarta-commons.xml/UseCorrectExceptionLogging">
                 <priority>2</priority>   
   </rule>   
  
   <rule ref="rulesets/java/logging-java.xml/LoggerIsNotStaticFinal"/>
   <rule ref="rulesets/java/logging-java.xml/SystemPrintln"/>
   <rule ref="rulesets/java/logging-java.xml/AvoidPrintStackTrace">
                 <priority>2</priority>   
   </rule>   
   
   <rule ref="rulesets/java/migrating.xml/ReplaceVectorWithList"/>
   <rule ref="rulesets/java/migrating.xml/ReplaceHashtableWithMap"/>
   <rule ref="rulesets/java/migrating.xml/ReplaceEnumerationWithIterator">
                    <priority>4</priority>   
   </rule>   
   
   <rule ref="rulesets/java/migrating.xml/IntegerInstantiation"/>
   <rule ref="rulesets/java/migrating.xml/ByteInstantiation"/>
   <rule ref="rulesets/java/migrating.xml/ShortInstantiation"/>
   <rule ref="rulesets/java/migrating.xml/LongInstantiation"/>
   <rule ref="rulesets/java/migrating.xml/JUnit4TestShouldUseBeforeAnnotation"/>
   <rule ref="rulesets/java/migrating.xml/JUnit4TestShouldUseAfterAnnotation"/>
   <rule ref="rulesets/java/migrating.xml/JUnit4TestShouldUseTestAnnotation"/>
   <rule ref="rulesets/java/migrating.xml/JUnit4SuitesShouldUseSuiteAnnotation"/>
   <rule ref="rulesets/java/migrating.xml/JUnitUseExpected"/>

  <!-- customized to ignore for each loops -->
   <rule ref="rulesets/java/naming.xml/ShortVariable">
                    <priority>5</priority>   
                            <properties>
                <property name="xpath">
                    <value>
                    <![CDATA[
//VariableDeclaratorId[string-length(@Image) < 3]
 [not(ancestor::ForInit)]
 [not((ancestor::FormalParameter) and (ancestor::TryStatement))]
 [not(parent::*/parent::*/parent::ForStatement)]
 [not(@Image= 'id')]
                    ]]>
                </value>
                </property>            
                              </properties>
                              </rule>   
   
   <rule ref="rulesets/java/naming.xml/LongVariable">
                    <priority>5</priority>
   <properties>
         <property name="minimum" description="The variable length reporting threshold" value="36"/>
             
   </properties>
   </rule>   
   
   <rule ref="rulesets/java/naming.xml/ShortMethodName">
                    <priority>5</priority>   
   </rule>   
   
   <rule ref="rulesets/java/naming.xml/VariableNamingConventions">
                    <priority>3</priority>   
   </rule>   
   
   <rule ref="rulesets/java/naming.xml/MethodNamingConventions">
                    <priority>1</priority>   
   </rule>   
   
   <rule ref="rulesets/java/naming.xml/ClassNamingConventions">
                    <priority>2</priority>   
   </rule>   
   
   <rule ref="rulesets/java/naming.xml/AvoidDollarSigns">
                    <priority>1</priority>   
   </rule>   
   
   <rule ref="rulesets/java/naming.xml/MethodWithSameNameAsEnclosingClass">
                    <priority>2</priority>   
   </rule>   
   
   <rule ref="rulesets/java/naming.xml/SuspiciousHashcodeMethodName"/>
   <rule ref="rulesets/java/naming.xml/SuspiciousConstantFieldName"/>
   <rule ref="rulesets/java/naming.xml/SuspiciousEqualsMethodName"/>
   <rule ref="rulesets/java/naming.xml/AvoidFieldNameMatchingTypeName"/>
   <rule ref="rulesets/java/naming.xml/AvoidFieldNameMatchingMethodName"/>
   <rule ref="rulesets/java/naming.xml/NoPackage">
                    <priority>2</priority>   
   </rule>   
   
   <rule ref="rulesets/java/naming.xml/PackageCase">
                    <priority>2</priority>   
   </rule>   
   
   <rule ref="rulesets/java/naming.xml/BooleanGetMethodName">
                    <priority>5</priority>   
   </rule>   
   

   <rule ref="rulesets/java/optimizations.xml/LocalVariableCouldBeFinal"/>
   <rule ref="rulesets/java/optimizations.xml/MethodArgumentCouldBeFinal"/>
   <rule ref="rulesets/java/optimizations.xml/SimplifyStartsWith"/>
   <rule ref="rulesets/java/optimizations.xml/UseStringBufferForStringAppends"/>
   <rule ref="rulesets/java/optimizations.xml/UseArraysAsList"/>
   <rule ref="rulesets/java/optimizations.xml/AvoidArrayLoops"/>
   <rule ref="rulesets/java/optimizations.xml/UnnecessaryWrapperObjectCreation"/>
   <rule ref="rulesets/java/optimizations.xml/AddEmptyString"/>

   <rule ref="rulesets/java/strictexception.xml/AvoidCatchingThrowable">
                    <priority>1</priority>   
   </rule>   
   
   <rule ref="rulesets/java/strictexception.xml/SignatureDeclareThrowsException">
                    <priority>2</priority> 
   <properties>
   <property name="violationSuppressXPath">
   	                <value><![CDATA[
//./ClassOrInterfaceDeclaration[(ends-with(@Image, 'Test'))]
	             ]]></value>
	            </property>
	        </properties>     
   </rule>   
   
   <rule ref="rulesets/java/strictexception.xml/ExceptionAsFlowControl">
                    <priority>2</priority>   
   </rule>   
   
   <rule ref="rulesets/java/strictexception.xml/AvoidCatchingNPE"/>
   <rule ref="rulesets/java/strictexception.xml/AvoidThrowingRawExceptionTypes"/>
   <rule ref="rulesets/java/strictexception.xml/AvoidThrowingNullPointerException"/>
   <rule ref="rulesets/java/strictexception.xml/AvoidRethrowingException">
                    <priority>2</priority>   
   </rule>   
   
   <rule ref="rulesets/java/strictexception.xml/DoNotExtendJavaLangError"/>
   <rule ref="rulesets/java/strictexception.xml/DoNotThrowExceptionInFinally">
                    <priority>2</priority>   
   </rule>   
   
   <rule ref="rulesets/java/strictexception.xml/AvoidThrowingNewInstanceOfSameException"/>
   
   <rule ref="rulesets/java/strings.xml/StringToString"/>
   <rule ref="rulesets/java/strings.xml/InefficientStringBuffering">
                    <priority>2</priority>   
   </rule>   
   
   <rule ref="rulesets/java/strings.xml/UnnecessaryCaseChange"/>
   <rule ref="rulesets/java/strings.xml/UseStringBufferLength"/>
   <rule ref="rulesets/java/strings.xml/AppendCharacterWithChar"/>
   <rule ref="rulesets/java/strings.xml/UseIndexOfChar"/>
   <rule ref="rulesets/java/strings.xml/InefficientEmptyStringCheck">   
                    <priority>4</priority>   
   </rule>   
   
   <rule ref="rulesets/java/strings.xml/InsufficientStringBufferDeclaration">
                    <priority>4</priority>   
   </rule>   
   
   <rule ref="rulesets/java/strings.xml/UselessStringValueOf"/>
   <rule ref="rulesets/java/strings.xml/StringBufferInstantiationWithChar">
                    <priority>2</priority>   
   </rule>   
   
   <rule ref="rulesets/java/strings.xml/UseEqualsToCompareStrings">
                    <priority>2</priority>   
   </rule>   
   
   <rule ref="rulesets/java/strings.xml/AvoidStringBufferField"/>

   <rule ref="rulesets/java/sunsecure.xml/MethodReturnsInternalArray"/>   
   <rule ref="rulesets/java/sunsecure.xml/ArrayIsStoredDirectly"/>     
    
   <rule ref="rulesets/java/unnecessary.xml/UnnecessaryConversionTemporary">
                    <priority>2</priority>   
   </rule>   
   
   <rule ref="rulesets/java/unnecessary.xml/UnnecessaryReturn"/>
   <rule ref="rulesets/java/unnecessary.xml/UnnecessaryFinalModifier"/>
   <rule ref="rulesets/java/unnecessary.xml/UselessOperationOnImmutable">
                    <priority>2</priority>   
   </rule>   
   
   <rule ref="rulesets/java/unnecessary.xml/UselessOverridingMethod">
                    <priority>2</priority>   
   </rule>   
   
   <rule ref="rulesets/java/unnecessary.xml/UnusedNullCheckInEquals">  
                    <priority>2</priority>   
   </rule>   
   
   
   <rule ref="rulesets/java/unusedcode.xml/UnusedPrivateField"> 
                    <priority>2</priority>   
   </rule>   
   
   <rule ref="rulesets/java/unusedcode.xml/UnusedLocalVariable">
                    <priority>2</priority>   
   </rule>   
   
   <rule ref="rulesets/java/unusedcode.xml/UnusedPrivateMethod">
                    <priority>2</priority>   
   </rule>   
   
   <rule ref="rulesets/java/unusedcode.xml/UnusedFormalParameter"/>
   <rule ref="rulesets/java/unusedcode.xml/UnusedModifier"/>    

   <rule ref="rulesets/jsp/basic.xml/NoLongScripts"/>   
   <rule ref="rulesets/jsp/basic.xml/NoScriptlets"/>   
   <rule ref="rulesets/jsp/basic.xml/NoInlineStyleInformation"/>  
   <rule ref="rulesets/jsp/basic.xml/NoClassAttribute"/>   
   <rule ref="rulesets/jsp/basic.xml/NoJspForward"/>   
   <rule ref="rulesets/jsp/basic.xml/IframeMissingSrcAttribute"/>   
   <rule ref="rulesets/jsp/basic.xml/NoHtmlComments"/>   
   <rule ref="rulesets/jsp/basic.xml/DuplicateJspImports"/>   
   <rule ref="rulesets/jsp/basic.xml/JspEncoding"/>     
   <rule ref="rulesets/jsp/basic.xml/NoInlineScript"/>   
   
   <rule ref="rulesets/xml/basic.xml/MistypedCDATASection"/> 
   
</ruleset>   