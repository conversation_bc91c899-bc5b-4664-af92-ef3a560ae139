package com.sast.cis.payment.boschtransfer.methods

import com.sast.cis.core.enums.PaymentMethodType
import com.sast.cis.core.enums.PaymentProvider
import com.sast.cis.core.model.IntegratorModel
import com.sast.cis.core.model.IoTCompanyModel
import com.sast.cis.core.model.SepaCreditTransferPaymentInfoModel
import com.sast.cis.core.model.SepaTransferPaymentInstrumentModel
import com.sast.cis.core.paymentintegration.data.AuthorizationParameter
import com.sast.cis.core.paymentintegration.data.AuthorizationStatus
import com.sast.cis.core.service.order.OrderSellerProvider
import com.sast.cis.payment.boschtransfer.paymentinfo.BoschSepaPaymentInfoService
import com.sast.cis.payment.boschtransfer.selleraccount.BoschSellerAccountService
import com.sast.cis.payment.boschtransfer.util.BoschTransferTransactionService
import de.hybris.bootstrap.annotations.UnitTest
import de.hybris.platform.core.model.order.AbstractOrderModel
import de.hybris.platform.core.model.order.payment.CreditCardPaymentInfoModel
import de.hybris.platform.core.model.user.UserModel
import de.hybris.platform.payment.model.PaymentTransactionEntryModel
import de.hybris.platform.payment.model.PaymentTransactionModel
import de.hybris.platform.testframework.JUnitPlatformSpecification
import org.junit.Test

@UnitTest
class BoschSepaPaymentMethodUnitSpec extends JUnitPlatformSpecification {
    private static final BigDecimal PLANNED_AMOUNT = BigDecimal.valueOf(1.23d)

    private OrderSellerProvider orderSellerProvider = Mock()
    private BoschSepaPaymentInfoService boschSepaPaymentInfoService = Mock()
    private BoschSellerAccountService boschSellerAccountService = Mock()
    private BoschTransferTransactionService boschTransferTransactionService = Mock()

    private BoschSepaPaymentMethod boschSepaPaymentMethod

    private AbstractOrderModel order = Mock()
    private IntegratorModel buyerUser = Mock()
    private IoTCompanyModel sellerCompany = Mock()

    private SepaCreditTransferPaymentInfoModel sepaPaymentInfo = Mock()
    private PaymentTransactionModel authorizationTransaction = Mock()
    private PaymentTransactionEntryModel authorizationEntry = Mock()
    private SepaTransferPaymentInstrumentModel paymentInstrument = Mock()

    private authorizationParameter = new AuthorizationParameter(order, PLANNED_AMOUNT)

    def setup() {
        boschSepaPaymentMethod = new BoschSepaPaymentMethod(
                orderSellerProvider,
                boschSepaPaymentInfoService,
                boschSellerAccountService,
                boschTransferTransactionService
        )

        order.getUser() >> buyerUser
        orderSellerProvider.getSellerForOrderOrThrow(order) >> sellerCompany
        boschSepaPaymentInfoService.createSepaPaymentInfo(buyerUser, sellerCompany) >> sepaPaymentInfo
        authorizationEntry.getPaymentTransaction() >> authorizationTransaction

    }

    @Test
    void 'given a fresh checkout, prepareCheckout creates new payment info and returns it as stored'() {
        given:
        boschSepaPaymentInfoService.getCartPaymentInfo(order) >> Optional.empty()

        when:
        def actualCheckoutInfo = boschSepaPaymentMethod.prepareCheckout(authorizationParameter)

        then:
        verifyAll(actualCheckoutInfo) {
            storedPaymentInfos == [sepaPaymentInfo] as Set
            defaultPaymentInfo == null
            paymentProvider == PaymentProvider.BOSCH_TRANSFER
            paymentMethod == PaymentMethodType.SEPA_CREDIT
            !userCreatable
            !savableForReuse
            cartPaymentInfo == null
            userActionParameters == [:]
        }
    }

    @Test
    void 'given a repeated checkout with this payment method selected, prepareCheckout returns matching cart payment info'() {
        given:
        boschSepaPaymentInfoService.getCartPaymentInfo(order) >> Optional.of(sepaPaymentInfo)

        when:
        def actualCheckoutInfo = boschSepaPaymentMethod.prepareCheckout(authorizationParameter)

        then:
        verifyAll(actualCheckoutInfo) {
            storedPaymentInfos == null
            defaultPaymentInfo == null
            paymentProvider == PaymentProvider.BOSCH_TRANSFER
            paymentMethod == PaymentMethodType.SEPA_CREDIT
            !userCreatable
            !savableForReuse
            cartPaymentInfo == sepaPaymentInfo
            userActionParameters == [:]
        }
        0 * boschSepaPaymentInfoService.createSepaPaymentInfo(_, _)
    }

    @Test
    void 'given a null authorization parameter, prepareCheckout throws IllegalArgumentException'() {
        when:
        boschSepaPaymentMethod.prepareCheckout(null)

        then:
        thrown(IllegalArgumentException)
        0 * boschSepaPaymentInfoService._
    }

    @Test
    void 'given an order without a user, prepareCheckout throws IllegalStateException'() {
        when:
        boschSepaPaymentMethod.prepareCheckout(authorizationParameter)

        then:
        order.getUser() >> null
        thrown(IllegalStateException)
    }

    @Test
    void 'given an order withg a non-integrator user, prepareCheckout throws IllegalStateException'() {
        when:
        boschSepaPaymentMethod.prepareCheckout(authorizationParameter)

        then:
        order.getUser() >> Mock(UserModel)
        thrown(IllegalStateException)
    }

    @Test
    void 'given an order with an appropriate payment info, authorize creates the payment instrument and persists the authorization'() {
        given:
        boschSepaPaymentInfoService.getCartPaymentInfo(order) >> Optional.of(sepaPaymentInfo)

        when:
        def actualTransactionEntry = boschSepaPaymentMethod.authorize(authorizationParameter)

        then:
        1 * boschSepaPaymentInfoService.createPaymentInstrument(sepaPaymentInfo) >> paymentInstrument
        1 * boschTransferTransactionService.persistAuthorization(
                order, sepaPaymentInfo, paymentInstrument, PLANNED_AMOUNT) >> authorizationEntry
        actualTransactionEntry == authorizationEntry
    }

    @Test
    void 'given a null authorization parameter, authorize throws IllegalArgumentException'() {
        when:
        boschSepaPaymentMethod.authorize(null)

        then:
        0 * boschSepaPaymentInfoService._
        0 * boschTransferTransactionService._
        thrown(IllegalArgumentException)
    }

    @Test
    void 'given an order without appropriate payment info, authorize throws IllegalStateException'() {
        given:
        boschSepaPaymentInfoService.getCartPaymentInfo(order) >> Optional.empty()

        when:
        boschSepaPaymentMethod.authorize(authorizationParameter)

        then:
        0 * boschSepaPaymentInfoService.createPaymentInstrument(_)
        0 * boschTransferTransactionService._
        thrown(IllegalStateException)
    }

    @Test
    void 'given an authorization entry with a sepa payment info, getAuthorizationResult returns successful result'() {
        given:
        authorizationTransaction.getInfo() >> sepaPaymentInfo

        when:
        def actualResult = boschSepaPaymentMethod.getAuthorizationResult(authorizationEntry)

        then:
        verifyAll(actualResult) {
            authorizationStatus == AuthorizationStatus.SUCCESS
            paymentTransactionEntry == authorizationEntry
        }
    }

    @Test
    void 'given a null authorization entry, getAuthorizationResult throws IllegalArgumentException'() {
        when:
        boschSepaPaymentMethod.getAuthorizationResult(null)

        then:
        thrown(IllegalArgumentException)
    }

    @Test
    void 'given an authorization entry with an incorrect paymentInfo type, getAuthorizationResult throws IllegalStateException'() {
        when:
        boschSepaPaymentMethod.getAuthorizationResult(authorizationEntry)

        then:
        authorizationTransaction.getInfo() >> Mock(CreditCardPaymentInfoModel)
        thrown(IllegalStateException)
    }

    @Test
    void 'given an authorization entry without paymentInfo, getAuthorizationResult throws IllegalStateException'() {
        when:
        boschSepaPaymentMethod.getAuthorizationResult(authorizationEntry)

        then:
        authorizationTransaction.getInfo() >> null
        thrown(IllegalStateException)
    }

    @Test
    void 'given a company that supports SEPA_CREDIT payment, supportsSeller returns true'() {
        given:
        boschSellerAccountService.supportsPaymentMethod(sellerCompany, PaymentMethodType.SEPA_CREDIT) >> true

        when:
        def actualSupport = boschSepaPaymentMethod.supportsSeller(sellerCompany)

        then:
        actualSupport
    }

    @Test
    void 'given a company that doesnt support SEPA_CREDIT payment, supportsSeller returns false'() {
        given:
        boschSellerAccountService.supportsPaymentMethod(sellerCompany, PaymentMethodType.SEPA_CREDIT) >> false

        when:
        def actualSupport = boschSepaPaymentMethod.supportsSeller(sellerCompany)

        then:
        !actualSupport
    }

    @Test
    void 'given a null company, supportsSeller throws IllegalArgumentException'() {
        when:
        boschSepaPaymentMethod.supportsSeller(null)

        then:
        thrown(IllegalArgumentException)
    }
}
