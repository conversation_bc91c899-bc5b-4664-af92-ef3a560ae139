package com.sast.cis.payment.dpg.transactions

import com.sast.dbpg.api.dto.*
import de.hybris.bootstrap.annotations.UnitTest
import de.hybris.platform.payment.dto.TransactionStatus
import de.hybris.platform.payment.dto.TransactionStatusDetails
import de.hybris.platform.payment.enums.PaymentTransactionType
import de.hybris.platform.payment.model.PaymentTransactionEntryModel
import de.hybris.platform.payment.model.PaymentTransactionModel
import de.hybris.platform.servicelayer.model.ModelService
import de.hybris.platform.testframework.JUnitPlatformSpecification
import org.junit.Test
import spock.lang.Unroll

import java.time.Instant

@UnitTest
class DpgPaymentPersistenceServiceUnitSpec extends JUnitPlatformSpecification {
    private static final String MATCHING_PAYMENT_ID = 'payment-1234'
    private static final String NON_MATCHING_PAYMENT_ID = 'payment-4321'
    private static final BigDecimal AMOUNT = new BigDecimal('31.99')
    private static final Instant NOW = Instant.now()

    private ModelService modelService = Mock()
    private DpgPaymentTransactionFactory dpgPaymentTransactionFactory = Mock()

    private DpgPaymentPersistenceService dpgPaymentEventPersistenceService

    private PaymentTransactionModel paymentTransaction = Mock()
    private PaymentTransactionEntryModel matchingEntry = Mock()
    private PaymentTransactionEntryModel nonMatchingEntry = Mock()
    private PaymentTransactionEntryModel newEntry = Mock()

    def setup() {
        dpgPaymentEventPersistenceService = new DpgPaymentPersistenceService(modelService, dpgPaymentTransactionFactory)

        paymentTransaction.getEntries() >> List.of(matchingEntry, nonMatchingEntry)
        paymentTransaction.getType() >> PaymentTransactionType.INVOICE
        matchingEntry.getRequestToken() >> MATCHING_PAYMENT_ID
        nonMatchingEntry.getRequestToken() >> NON_MATCHING_PAYMENT_ID
    }

    @Test
    @Unroll
    void 'savePaymentAsTransactionEntry with given type #givenType and status #givenStatus updates existing entry with status #expectedStatus'() {
        given:
        def givenDpgPayment = createDpgPayment(givenType, givenStatus)

        when:
        dpgPaymentEventPersistenceService.savePaymentAsTransactionEntry(paymentTransaction, givenDpgPayment)

        then:
        1 * matchingEntry.setTransactionStatus(expectedStatus.toString())
        1 * matchingEntry.setAmount(AMOUNT)
        1 * modelService.save(matchingEntry)
        1 * modelService.refresh(paymentTransaction)
        0 * nonMatchingEntry._
        0 * dpgPaymentTransactionFactory._

        where:
        givenType                      | givenStatus             || expectedStatus
        PaymentType.PREAUTHORIZATION   | PaymentStatus.FAILURE   || TransactionStatus.ERROR
        PaymentType.PREAUTHORIZATION   | PaymentStatus.SUCCESS   || TransactionStatus.ACCEPTED
        PaymentType.PREAUTHORIZATION   | PaymentStatus.PENDING   || TransactionStatus.PENDING
        PaymentType.PREAUTHORIZATION   | PaymentStatus.CREATED   || TransactionStatus.PENDING
        PaymentType.PREAUTHORIZATION   | PaymentStatus.CANCELLED || TransactionStatus.REVOKED

        PaymentType.AUTHORIZATION      | PaymentStatus.FAILURE   || TransactionStatus.ERROR
        PaymentType.AUTHORIZATION      | PaymentStatus.SUCCESS   || TransactionStatus.ACCEPTED
        PaymentType.AUTHORIZATION      | PaymentStatus.PENDING   || TransactionStatus.PENDING
        PaymentType.AUTHORIZATION      | PaymentStatus.CREATED   || TransactionStatus.PENDING
        PaymentType.AUTHORIZATION      | PaymentStatus.CANCELLED || TransactionStatus.REVOKED

        PaymentType.CAPTURE            | PaymentStatus.FAILURE   || TransactionStatus.ERROR
        PaymentType.CAPTURE            | PaymentStatus.SUCCESS   || TransactionStatus.ACCEPTED
        PaymentType.CAPTURE            | PaymentStatus.PENDING   || TransactionStatus.PENDING
        PaymentType.CAPTURE            | PaymentStatus.CREATED   || TransactionStatus.PENDING
        PaymentType.CAPTURE            | PaymentStatus.CANCELLED || TransactionStatus.REVOKED

        PaymentType.CANCELLATION       | PaymentStatus.FAILURE   || TransactionStatus.ERROR
        PaymentType.CANCELLATION       | PaymentStatus.SUCCESS   || TransactionStatus.ACCEPTED
        PaymentType.CANCELLATION       | PaymentStatus.PENDING   || TransactionStatus.PENDING
        PaymentType.CANCELLATION       | PaymentStatus.CREATED   || TransactionStatus.PENDING
        PaymentType.CANCELLATION       | PaymentStatus.CANCELLED || TransactionStatus.REVOKED

        PaymentType.REFUND             | PaymentStatus.FAILURE   || TransactionStatus.ERROR
        PaymentType.REFUND             | PaymentStatus.SUCCESS   || TransactionStatus.ACCEPTED
        PaymentType.REFUND             | PaymentStatus.PENDING   || TransactionStatus.PENDING
        PaymentType.REFUND             | PaymentStatus.CREATED   || TransactionStatus.PENDING
        PaymentType.REFUND             | PaymentStatus.CANCELLED || TransactionStatus.REVOKED

        PaymentType.RETURN             | PaymentStatus.FAILURE   || TransactionStatus.ERROR
        PaymentType.RETURN             | PaymentStatus.SUCCESS   || TransactionStatus.ACCEPTED
        PaymentType.RETURN             | PaymentStatus.PENDING   || TransactionStatus.PENDING
        PaymentType.RETURN             | PaymentStatus.CREATED   || TransactionStatus.PENDING
        PaymentType.RETURN             | PaymentStatus.CANCELLED || TransactionStatus.REVOKED

        PaymentType.CHARGEBACK         | PaymentStatus.FAILURE   || TransactionStatus.ERROR
        PaymentType.CHARGEBACK         | PaymentStatus.SUCCESS   || TransactionStatus.ACCEPTED
        PaymentType.CHARGEBACK         | PaymentStatus.PENDING   || TransactionStatus.PENDING
        PaymentType.CHARGEBACK         | PaymentStatus.CREATED   || TransactionStatus.PENDING
        PaymentType.CHARGEBACK         | PaymentStatus.CANCELLED || TransactionStatus.REVOKED

        PaymentType.CHARGEBACKREVERSAL | PaymentStatus.FAILURE   || TransactionStatus.ERROR
        PaymentType.CHARGEBACKREVERSAL | PaymentStatus.SUCCESS   || TransactionStatus.ACCEPTED
        PaymentType.CHARGEBACKREVERSAL | PaymentStatus.PENDING   || TransactionStatus.PENDING
        PaymentType.CHARGEBACKREVERSAL | PaymentStatus.CREATED   || TransactionStatus.PENDING
        PaymentType.CHARGEBACKREVERSAL | PaymentStatus.CANCELLED || TransactionStatus.REVOKED
    }

    @Test
    @Unroll
    void 'savePaymentAsTransactionEntry with given type #givenType and status #givenStatus creates new entry with status #expectedStatus and type #expectedType'() {
        given:
        def givenDpgPayment = createDpgPayment(givenType, givenStatus)

        when:
        dpgPaymentEventPersistenceService.savePaymentAsTransactionEntry(paymentTransaction, givenDpgPayment)

        then:
        1 * paymentTransaction.getEntries() >> List.of(nonMatchingEntry)
        1 * dpgPaymentTransactionFactory.createTransactionEntry(paymentTransaction, expectedType,
                TransactionStatus.PENDING, TransactionStatusDetails.SUCCESFULL) >> newEntry
        1 * newEntry.setRequestToken(MATCHING_PAYMENT_ID)
        1 * newEntry.setTime(Date.from(NOW))
        1 * newEntry.setTransactionStatus(expectedStatus.toString())
        1 * newEntry.setAmount(AMOUNT)
        1 * modelService.save(newEntry)
        1 * modelService.refresh(paymentTransaction)
        0 * dpgPaymentTransactionFactory._

        where:
        givenType                      | givenStatus             || expectedStatus             | expectedType
        PaymentType.PREAUTHORIZATION   | PaymentStatus.FAILURE   || TransactionStatus.ERROR    | PaymentTransactionType.AUTHORIZATION
        PaymentType.PREAUTHORIZATION   | PaymentStatus.SUCCESS   || TransactionStatus.ACCEPTED | PaymentTransactionType.AUTHORIZATION
        PaymentType.PREAUTHORIZATION   | PaymentStatus.PENDING   || TransactionStatus.PENDING  | PaymentTransactionType.AUTHORIZATION
        PaymentType.PREAUTHORIZATION   | PaymentStatus.CREATED   || TransactionStatus.PENDING  | PaymentTransactionType.AUTHORIZATION
        PaymentType.PREAUTHORIZATION   | PaymentStatus.CANCELLED || TransactionStatus.REVOKED  | PaymentTransactionType.AUTHORIZATION

        PaymentType.AUTHORIZATION      | PaymentStatus.FAILURE   || TransactionStatus.ERROR    | PaymentTransactionType.AUTHORIZATION
        PaymentType.AUTHORIZATION      | PaymentStatus.SUCCESS   || TransactionStatus.ACCEPTED | PaymentTransactionType.AUTHORIZATION
        PaymentType.AUTHORIZATION      | PaymentStatus.PENDING   || TransactionStatus.PENDING  | PaymentTransactionType.AUTHORIZATION
        PaymentType.AUTHORIZATION      | PaymentStatus.CREATED   || TransactionStatus.PENDING  | PaymentTransactionType.AUTHORIZATION
        PaymentType.AUTHORIZATION      | PaymentStatus.CANCELLED || TransactionStatus.REVOKED  | PaymentTransactionType.AUTHORIZATION

        PaymentType.CAPTURE            | PaymentStatus.FAILURE   || TransactionStatus.ERROR    | PaymentTransactionType.CAPTURE
        PaymentType.CAPTURE            | PaymentStatus.SUCCESS   || TransactionStatus.ACCEPTED | PaymentTransactionType.CAPTURE
        PaymentType.CAPTURE            | PaymentStatus.PENDING   || TransactionStatus.PENDING  | PaymentTransactionType.CAPTURE
        PaymentType.CAPTURE            | PaymentStatus.CREATED   || TransactionStatus.PENDING  | PaymentTransactionType.CAPTURE
        PaymentType.CAPTURE            | PaymentStatus.CANCELLED || TransactionStatus.REVOKED  | PaymentTransactionType.CAPTURE

        PaymentType.CANCELLATION       | PaymentStatus.FAILURE   || TransactionStatus.ERROR    | PaymentTransactionType.CANCEL
        PaymentType.CANCELLATION       | PaymentStatus.SUCCESS   || TransactionStatus.ACCEPTED | PaymentTransactionType.CANCEL
        PaymentType.CANCELLATION       | PaymentStatus.PENDING   || TransactionStatus.PENDING  | PaymentTransactionType.CANCEL
        PaymentType.CANCELLATION       | PaymentStatus.CREATED   || TransactionStatus.PENDING  | PaymentTransactionType.CANCEL
        PaymentType.CANCELLATION       | PaymentStatus.CANCELLED || TransactionStatus.REVOKED  | PaymentTransactionType.CANCEL

        PaymentType.REFUND             | PaymentStatus.FAILURE   || TransactionStatus.ERROR    | PaymentTransactionType.REFUND_FOLLOW_ON
        PaymentType.REFUND             | PaymentStatus.SUCCESS   || TransactionStatus.ACCEPTED | PaymentTransactionType.REFUND_FOLLOW_ON
        PaymentType.REFUND             | PaymentStatus.PENDING   || TransactionStatus.PENDING  | PaymentTransactionType.REFUND_FOLLOW_ON
        PaymentType.REFUND             | PaymentStatus.CREATED   || TransactionStatus.PENDING  | PaymentTransactionType.REFUND_FOLLOW_ON
        PaymentType.REFUND             | PaymentStatus.CANCELLED || TransactionStatus.REVOKED  | PaymentTransactionType.REFUND_FOLLOW_ON

        PaymentType.RETURN             | PaymentStatus.FAILURE   || TransactionStatus.ERROR    | PaymentTransactionType.RETURN
        PaymentType.RETURN             | PaymentStatus.SUCCESS   || TransactionStatus.ACCEPTED | PaymentTransactionType.RETURN
        PaymentType.RETURN             | PaymentStatus.PENDING   || TransactionStatus.PENDING  | PaymentTransactionType.RETURN
        PaymentType.RETURN             | PaymentStatus.CREATED   || TransactionStatus.PENDING  | PaymentTransactionType.RETURN
        PaymentType.RETURN             | PaymentStatus.CANCELLED || TransactionStatus.REVOKED  | PaymentTransactionType.RETURN

        PaymentType.CHARGEBACK         | PaymentStatus.FAILURE   || TransactionStatus.ERROR    | PaymentTransactionType.CHARGEBACK
        PaymentType.CHARGEBACK         | PaymentStatus.SUCCESS   || TransactionStatus.ACCEPTED | PaymentTransactionType.CHARGEBACK
        PaymentType.CHARGEBACK         | PaymentStatus.PENDING   || TransactionStatus.PENDING  | PaymentTransactionType.CHARGEBACK
        PaymentType.CHARGEBACK         | PaymentStatus.CREATED   || TransactionStatus.PENDING  | PaymentTransactionType.CHARGEBACK
        PaymentType.CHARGEBACK         | PaymentStatus.CANCELLED || TransactionStatus.REVOKED  | PaymentTransactionType.CHARGEBACK

        PaymentType.CHARGEBACKREVERSAL | PaymentStatus.FAILURE   || TransactionStatus.ERROR    | PaymentTransactionType.CHARGEBACK_REVERSAL
        PaymentType.CHARGEBACKREVERSAL | PaymentStatus.SUCCESS   || TransactionStatus.ACCEPTED | PaymentTransactionType.CHARGEBACK_REVERSAL
        PaymentType.CHARGEBACKREVERSAL | PaymentStatus.PENDING   || TransactionStatus.PENDING  | PaymentTransactionType.CHARGEBACK_REVERSAL
        PaymentType.CHARGEBACKREVERSAL | PaymentStatus.CREATED   || TransactionStatus.PENDING  | PaymentTransactionType.CHARGEBACK_REVERSAL
        PaymentType.CHARGEBACKREVERSAL | PaymentStatus.CANCELLED || TransactionStatus.REVOKED  | PaymentTransactionType.CHARGEBACK_REVERSAL
    }

    @Test
    @Unroll
    void 'savePaymentAsTransactionEntry skips event if given transaction has type AUTHORIZATION and given dpg payment is AUTHORIZATION with status #givenStatus'() {
        given:
        def givenDpgPayment = createDpgPayment(PaymentType.AUTHORIZATION, givenStatus)

        when:
        dpgPaymentEventPersistenceService.savePaymentAsTransactionEntry(paymentTransaction, givenDpgPayment)

        then:
        1 * paymentTransaction.getType() >> PaymentTransactionType.AUTHORIZATION
        0 * paymentTransaction._
        0 * matchingEntry._
        0 * nonMatchingEntry._
        0 * modelService._
        0 * dpgPaymentTransactionFactory._

        where:
        givenStatus << EnumSet.allOf(PaymentStatus)
    }

    @Test
    void 'savePaymentAsTransactionEntry with null transaction throws IllegalArgumentException'() {
        given:
        def givenDpgPayment = createDpgPayment(PaymentType.AUTHORIZATION, PaymentStatus.SUCCESS)

        when:
        dpgPaymentEventPersistenceService.savePaymentAsTransactionEntry(null, givenDpgPayment)

        then:
        thrown(IllegalArgumentException)
        0 * paymentTransaction._
        0 * matchingEntry._
        0 * nonMatchingEntry._
        0 * modelService._
        0 * dpgPaymentTransactionFactory._
    }

    @Test
    void 'savePaymentAsTransactionEntry with a given dpg payment of type UNKNOWN_ENUM_VALUE throws IllegalArgumentException'() {
        given:
        def givenDpgPayment = createDpgPayment(PaymentType.UNKNOWN_ENUM_VALUE, PaymentStatus.SUCCESS)

        when:
        dpgPaymentEventPersistenceService.savePaymentAsTransactionEntry(paymentTransaction, givenDpgPayment)

        then:
        thrown(IllegalArgumentException)
        0 * paymentTransaction._
        0 * matchingEntry._
        0 * nonMatchingEntry._
        0 * modelService._
        0 * dpgPaymentTransactionFactory._
    }

    @Test
    void 'savePaymentAsTransactionEntry with null dpgPayment throws IllegalArgumentException'() {
        when:
        dpgPaymentEventPersistenceService.savePaymentAsTransactionEntry(paymentTransaction, null)

        then:
        thrown(IllegalArgumentException)
        0 * paymentTransaction._
        0 * matchingEntry._
        0 * nonMatchingEntry._
        0 * modelService._
        0 * dpgPaymentTransactionFactory._
    }

    @Test
    void 'savePaymentAsTransactionEntry with payment without type throws IllegalArgumentException'() {
        given:
        def givenDpgPayment = PaymentInfoDto.builder()
                .id(MATCHING_PAYMENT_ID)
                .paymentType(null)
                .paymentStatus(PaymentStatus.SUCCESS)
                .amount(MoneyDto.builder().amount(AMOUNT).currency(CurrencyCode.EUR).build())
                .creationDateTime(NOW)
                .build()

        when:
        dpgPaymentEventPersistenceService.savePaymentAsTransactionEntry(paymentTransaction, givenDpgPayment)

        then:
        thrown(IllegalArgumentException)
        0 * paymentTransaction._
        0 * matchingEntry._
        0 * nonMatchingEntry._
        0 * modelService._
        0 * dpgPaymentTransactionFactory._
    }

    @Test
    void 'savePaymentAsTransactionEntry with payment without status throws IllegalArgumentException'() {
        given:
        def givenDpgPayment = PaymentInfoDto.builder()
                .id(MATCHING_PAYMENT_ID)
                .paymentType(PaymentType.CAPTURE)
                .paymentStatus(null)
                .amount(MoneyDto.builder().amount(AMOUNT).currency(CurrencyCode.EUR).build())
                .creationDateTime(NOW)
                .build()

        when:
        dpgPaymentEventPersistenceService.savePaymentAsTransactionEntry(paymentTransaction, givenDpgPayment)

        then:
        thrown(IllegalArgumentException)
        0 * paymentTransaction._
        0 * matchingEntry._
        0 * nonMatchingEntry._
        0 * modelService._
        0 * dpgPaymentTransactionFactory._
    }

    @Test
    void 'savePaymentAsTransactionEntry with payment without amount throws IllegalArgumentException'() {
        given:
        def givenDpgPayment = PaymentInfoDto.builder()
                .id(MATCHING_PAYMENT_ID)
                .paymentType(PaymentType.CAPTURE)
                .paymentStatus(PaymentStatus.SUCCESS)
                .amount(null)
                .creationDateTime(NOW)
                .build()

        when:
        dpgPaymentEventPersistenceService.savePaymentAsTransactionEntry(paymentTransaction, givenDpgPayment)

        then:
        thrown(IllegalArgumentException)
        0 * paymentTransaction._
        0 * matchingEntry._
        0 * nonMatchingEntry._
        0 * modelService._
        0 * dpgPaymentTransactionFactory._
    }

    @Test
    void 'savePaymentAsTransactionEntry with payment without time throws IllegalArgumentException'() {
        given:
        def givenDpgPayment = PaymentInfoDto.builder()
                .id(MATCHING_PAYMENT_ID)
                .paymentType(PaymentType.CAPTURE)
                .paymentStatus(PaymentStatus.SUCCESS)
                .amount(MoneyDto.builder().amount(AMOUNT).currency(CurrencyCode.EUR).build())
                .creationDateTime(null)
                .build()

        when:
        dpgPaymentEventPersistenceService.savePaymentAsTransactionEntry(paymentTransaction, givenDpgPayment)

        then:
        thrown(IllegalArgumentException)
        0 * paymentTransaction._
        0 * matchingEntry._
        0 * nonMatchingEntry._
        0 * modelService._
        0 * dpgPaymentTransactionFactory._
    }

    private PaymentInfoDto createDpgPayment(PaymentType dpgPaymentType, PaymentStatus dpgPaymentStatus) {
        PaymentInfoDto.builder()
                .id(MATCHING_PAYMENT_ID)
                .paymentType(dpgPaymentType)
                .paymentStatus(dpgPaymentStatus)
                .amount(MoneyDto.builder().amount(AMOUNT).currency(CurrencyCode.EUR).build())
                .creationDateTime(NOW)
                .build()
    }

}
