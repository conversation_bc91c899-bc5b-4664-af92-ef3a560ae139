package com.sast.cis.payment.dpg.email;

import com.sast.cis.core.enums.Feature;
import com.sast.cis.core.service.FeatureToggleService;
import com.sast.cis.email2.constants.EmailType;
import com.sast.cis.payment.dpg.dto.Address;
import com.sast.cis.payment.dpg.dto.DpgSellerOnboarding;
import com.sast.email.client.dto.EmailDto;
import de.hybris.bootstrap.annotations.UnitTest;
import de.hybris.platform.servicelayer.config.ConfigurationService;
import org.apache.commons.configuration.Configuration;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@UnitTest
@RunWith(MockitoJUnitRunner.Silent.class)
public class SellerOnboardingEmailTemplateHandlerUnitTest {
    private static final EmailType EXPECTED_EMAIL_TYPE = EmailType.INITIATE_DPG_SELLER_ONBOARDING;

    @Mock
    private ConfigurationService configurationService;

    @Mock
    private FeatureToggleService featureToggleService;

    @Mock
    private Configuration mockConfiguration;


    @InjectMocks
    private SellerOnboardingEmailHandler sellerOnboardingEmailTemplateHandler;

    @Before
    public void setUpMocks() {
        sellerOnboardingEmailTemplateHandler = new SellerOnboardingEmailHandler(
                configurationService, featureToggleService, "<EMAIL>", "<EMAIL>");

        when(configurationService.getConfiguration())
                .thenReturn(mockConfiguration);
        when(mockConfiguration.getString("azena.email.template." + EXPECTED_EMAIL_TYPE.getTemplate()))
                .thenReturn("100");
        when(featureToggleService.isEnabled(Feature.FEATURE_EMAIL_CLIENT_ENABLED)).thenReturn(false);
    }

    @Test
    public void testReturnsCorrectEmailType() {
        assertThat(sellerOnboardingEmailTemplateHandler.getType()).isEqualTo(EXPECTED_EMAIL_TYPE);
    }

    @Test
    public void getEmailData_successfully() {
        DpgSellerOnboarding givenDpgSellerOnboarding = createEmailData();
        EmailDto actualEmailData = sellerOnboardingEmailTemplateHandler.getEmailDto(givenDpgSellerOnboarding);
        assertThat(actualEmailData.getSubject()).isNotEmpty();
    }

    private DpgSellerOnboarding createEmailData() {
        return DpgSellerOnboarding.builder()
                .businessAddress(createAddress())
                .companyName("TestCompany")
                .conactPersonName("TestName")
                .emailId("<EMAIL>")
                .country("TestCountry")
                .currency("EUR")
                .taxId("TestVatId")
                .build();
    }

    private Address createAddress() {
        return Address.builder()
                .streetName("BusinessStr")
                .houseNumber("333")
                .postCode("99999")
                .city("BusinessCity")
                .build();
    }
}
