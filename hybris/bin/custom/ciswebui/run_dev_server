#!/usr/bin/env python3

import argparse
import os
import pty
import sys

import build_tools

W_CONFIG = 'testing/tests.webpack.config.js'


def main():
    parser = argparse.ArgumentParser(description='SERVER FOR FRONTEND TESTS')
    parser.add_argument('--force-init', action='store_true', help='Delete node_modules folder and run initialize script.')
    parser.add_argument('--aa', action='store_true', help='Run dev server for aa store.')

    args = parser.parse_args()
    print('Running dev server script..')

    status = build_tools.build_typedefs()
    if status != 0:
        sys.exit(status)

    build_tools.init_frontend_environment(args.force_init)
    node_exec = 'node'

    build_tools.build_dto_validators(node_exec)

    webpack_config = W_CONFIG
    if args.aa:
        webpack_config = 'testing/tests.webpack.aa.config.js'

    port = 9010
    if args.aa:
        port = 9011

    os.chdir('iot_store')
    pty.spawn([node_exec, '--max-old-space-size=4096', './node_modules/.bin/webpack', 'serve', '--progress', '--config', webpack_config, '--env', "port={}".format(port), '--env', 'debug'])


if __name__ == '__main__':
    main()
