'use strict';

const path = require('path');
const LicenseWebpackPlugin = require('license-webpack-plugin').LicenseWebpackPlugin;
const CopyWebpackPlugin = require('copy-webpack-plugin')
const fs = require('fs');
const ini = require('ini');
const DEV_MODE = 'development';
const TerserJsPlugin = require('terser-webpack-plugin');
const EslintPlugin = require('eslint-webpack-plugin');
const VueLoaderPlugin = require('vue-loader/lib/plugin');
const glob = require('glob');
const dateFormat = require('dateformat');
const EOL = require('os').EOL;
const fsExtra = require("fs-extra");
const separator = '===============================================================================================';

const distPath = path.join(__dirname, 'dist/js');
const { VuetifyLoaderPlugin } = require('vuetify-loader');
const shopPath = path.join(__dirname, '../../cisshopfrontend/');
const jsAssetsPath = 'web/webroot/_ui/assets/js';
const propertiesPath = 'resources/';
const licenseJsonFile = 'shipped-licenses.json';
const moduleJsonFile = 'included-modules.json';
const watchMode = process.argv.includes('--watch');
const whiteList = ['cisshopfrontend', 'cd-system'];

const updateHashFile = (hashFile, hash) => {
    let hashConfig = {};
    if (fs.existsSync(hashFile)) {
        hashConfig = ini.parse(fs.readFileSync(hashFile, 'utf-8'));
    }
    hashConfig["azena.js.hash"] = hash;
    fs.writeFileSync(hashFile, ini.stringify(hashConfig));
    console.log("Updated config file: ", hashFile);
};

const triggerServerReload = triggerFile => {
    let configRoot = fs.readFileSync(triggerFile, 'utf-8');
    if (!configRoot.includes('modified.timestamp=')) {
        configRoot = configRoot + EOL + EOL + 'modified.timestamp=' + EOL;
    }
    configRoot = configRoot.replace(/^modified\.timestamp=.*$/gm, 'modified.timestamp=updated-by-js-build-script-at-' + dateFormat(new Date(), "HH:MM:ss"));
    fs.writeFileSync(triggerFile, configRoot);
};

const copyToFrontendExtension = (destination, hash, entryMapping) => {
    fsExtra.copy(distPath, destination).catch((e) => console.log('failed to copy webpack output to ' + destination));
};

const isPackageExcluded = (packageName) => {
    return whiteList.includes(packageName);
};

const comparePackages = (a, b) => {
    let nameA = a.name.toUpperCase(); // ignore upper and lowercase
    let nameB = b.name.toUpperCase(); // ignore upper and lowercase
    return nameA.localeCompare(nameB);
};

module.exports = env => {
    let entryPoints = glob.sync(path.join(__dirname, 'src/js/**/*/main.ts'));
    let entryMapping = {};
    for (let i = 0; i < entryPoints.length; ++i) {
        let key = path.dirname(entryPoints[i].split('iot_store/src/js/')[1]);
        entryMapping[key] = entryPoints[i];
    }

    let mode = DEV_MODE;
    let fileName = 'development/[name]/page.js';
    let chunkFilename = 'development/common.js';
    let vendorsFilename = 'development/vendors.js';
    let minimizers = [];
    if (env.mode !== DEV_MODE) {
        mode = 'production';
        fileName = '[fullhash]/[name]/page.js';
        chunkFilename = '[fullhash]/common.js';
        vendorsFilename = '[fullhash]/vendors.js';
        minimizers = [new TerserJsPlugin({
            parallel: true,
            terserOptions: {
                compress: {
                    drop_console: true,
                },
            }
        })];
    }

    console.log('Mode: ', env.mode);

    let config = {
        mode: mode,
        entry: entryMapping,
        output: {
            filename: fileName,
            path: distPath
        },
        resolve: {
            modules: [path.join(__dirname, 'src/js'), path.join(__dirname, "node_modules")],
            alias: {
                'vue$': 'vue/dist/vue.esm.js'
            },
            extensions: ['.js', '.vue', '.ts']
        },
        performance: {
            hints: false
        },
        optimization: {
            minimizer: minimizers,
            splitChunks: {
                chunks: 'all',
                maxInitialRequests: Infinity,
                minSize: 0,
                cacheGroups: {
                    vendor: {
                        test(module) {
                            // `module.resource` contains the absolute path of the file on disk.
                            // Note the usage of `path.sep` instead of / or \, for cross-platform compatibility.
                            const path = require('path');
                            return (
                                module.resource &&
                                module.resource.includes(`${path.sep}node_modules${path.sep}`)
                            );
                        },
                        name: 'vendors',
                        chunks: 'initial',
                        filename: vendorsFilename
                    },
                    commons: {
                        test(module) {
                            // `module.resource` contains the absolute path of the file on disk.
                            // Note the usage of `path.sep` instead of / or \, for cross-platform compatibility.
                            const path = require('path');
                            return (
                                module.resource &&
                                ! module.resource.includes(`${path.sep}node_modules${path.sep}`)
                            );
                        },
                        name: 'common',
                        chunks: 'initial',
                        filename: chunkFilename
                    },
                },
            }
        },
        plugins: [
            new VueLoaderPlugin({
                compiler: require('vue-template-compiler')
            }),
            new VuetifyLoaderPlugin(),
            new EslintPlugin({
                extensions: ['.js', '.vue'],
                overrideConfigFile: path.resolve(__dirname, '.eslintrc.js'),
                formatter: require("eslint-codeframe-formatter"),
                fix: !!env.fix
            }),
            function () {
                this.hooks.done.tap("HashParserPlugin", stats => {
                    let hash = stats.compilation.hash;
                    if (mode === DEV_MODE) {
                        hash = 'development';
                    }

                    copyToFrontendExtension(path.join(shopPath, jsAssetsPath), hash, entryMapping);

                    updateHashFile(path.join(shopPath, propertiesPath, 'generated.properties'), hash);

                    triggerServerReload(path.join(shopPath, propertiesPath, 'customRoot-cisshopfrontend.properties'));
                    console.log("Hash: " + stats.compilation.hash + dateFormat(new Date(), "\t\t(HH:MM:ss)") + "\n");
                });
            }
        ],
        module: {
            rules: [
                {
                    test: /\.svg$/,
                    issuer: /\.(vue|js|ts|svg)$/,
                    use: [
                        'vue-loader',
                        {
                            loader: 'svg-to-vue-component/loader',
                            options: {svgoConfig: false},
                        }
                    ]
                },
                {
                    test: /\.tsx?$/,
                    loader: 'ts-loader',
                    exclude: /node_modules/,
                    options: {
                        appendTsSuffixTo: [/\.vue$/],
                        configFile: path.resolve(__dirname, 'tsconfig.json')
                    }
                },
                {
                    test: /\.vue$/,
                    loader: 'vue-loader'
                },
                {
                    resourceQuery: /blockType=i18n/,
                    use: ['@intlify/vue-i18n-loader', 'yaml-loader']
                },
                {
                    test: /\.css$/,
                    use: [
                        'vue-style-loader',
                        'css-loader'
                    ]
                },
                {
                    test: /\.sass$/,
                    use: [
                        'vue-style-loader',
                        'css-loader',
                        {
                            loader: 'sass-loader',
                            options: {
                                implementation: require('sass'),
                                additionalData: "@import 'cd-system/tokens/scss/variables'"
                            }
                        }
                    ]
                },
                {
                    test: /\.scss$/,
                    use: [
                        'vue-style-loader',
                        'css-loader',
                        {
                            loader: 'sass-loader',
                            options: {
                                implementation: require('sass'),
                                additionalData: `
                                    @import 'cd-system/tokens/scss/mixins';
                                    @import 'cd-system/tokens/scss/common/core';
                                    @import 'cd-system/tokens/scss/azena/overrides';
                                `,
                                sassOptions: {
                                    includePaths: [path.join(__dirname, 'src/scss')]
                                }
                            }
                        }
                    ]
                },
                {
                    test: /\.(woff|woff2|ttf|eot)$/,
                    use: ['url-loader']
                }
            ]
        }
    };

    if (mode === DEV_MODE) {
        config.devtool = 'inline-source-map';
        config.stats = {
            colors: true
        }
    } else {
        config.module.rules.push({
            test: /\.js$/,
            exclude: /(node_modules)/,
            use: {
                loader: 'babel-loader'
            }
        });
    }

    if (!(mode === DEV_MODE && watchMode)) {
        config.plugins.push(function () {
            this.hooks.done.tap("LicenseFormatPlugin", stats => {
                if (watchMode) {
                    return;
                }
                let banner = `\n${separator}\nLIST OF OPEN SOURCE COMPONENTS\n${separator}\n`;
                let formattedLicenses = [banner];

                let licensesJSON = JSON.parse(fs.readFileSync('./dist/' + licenseJsonFile, 'utf-8'));
                formattedLicenses = formattedLicenses.concat(licensesJSON.map(entry => {
                    return `${entry.name} ${entry.version} (${entry.url})\n\nLicense: ${entry.licenseTypeName}\n\n${entry.licenseText}\n\n${separator}\n`;
                }));

                fs.writeFileSync('./dist/js/licenses.txt', formattedLicenses.join('\n\n\n'));
            });
        });

        config.plugins.push(new LicenseWebpackPlugin({
            perChunkOutput: false,
            outputFilename: path.join('..', licenseJsonFile),
            renderLicenses: (modules) => {
                let licenses = modules
                    .map(module => {
                        let version = '';
                        let url = '';
                        if (module.packageJson) {
                            version = module.packageJson.version || '';
                            url = module.packageJson.homepage || '';
                        }
                        return {
                            name: module.name,
                            version,
                            url,
                            licenseTypeName: module.licenseId,
                            licenseText: module.licenseText || ''
                        };
                    });
                licenses.sort(comparePackages);

                return JSON.stringify(licenses);
            },
            excludedPackageTest: isPackageExcluded,
            licenseFileOverrides: {
                isarray: path.join('../..', 'oss-whitelists/MIT-isarray-2.0.5-2019-07-09.txt'),
                '@vue/devtools-api': path.join('../../..', 'oss-whitelists/MIT-vue-devtools-api-6.5.0-2023-04-21.txt')
            },
            handleMissingLicenseText: (packageName, licenseType) => {
                throw new Error("There is no license text for package " + packageName);
            },
        }));

        let packageJSON = JSON.parse(fs.readFileSync('./package.json', 'utf-8'));
        let dependencies = Object.keys(packageJSON.dependencies);
        let devDependencies = Object.keys(packageJSON.devDependencies);
        let explicitDependencies = dependencies.concat(devDependencies).map(dependency => {
            return {'name': dependency, 'directory': path.join(__dirname, 'node_modules', dependency)};
        });

        config.plugins.push(new LicenseWebpackPlugin({
            perChunkOutput: false,
            outputFilename: path.join('..', moduleJsonFile),
            additionalModules: explicitDependencies.filter(module => !isPackageExcluded(module.name)),
            renderLicenses: (modules) => {
                let licenses = modules.map(module => {
                    let version = '';
                    let url = '';
                    if (module.packageJson) {
                        version = module.packageJson.version || '';
                        url = module.packageJson.homepage || '';
                    }
                    return {
                        name: module.name,
                        version,
                        url
                    };
                });
                licenses.sort(comparePackages);

                return JSON.stringify(licenses);
            },
            excludedPackageTest: isPackageExcluded
        }));

    }

    return config;
};
