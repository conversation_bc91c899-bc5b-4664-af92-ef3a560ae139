@import "node_modules/cd-system/tokens/scss/variables";// for cd-system $grid-breakpoints

// TODO: align these values below with cd-system@2.0.0
$breakpoints: (
        'XS': map-get($grid-breakpoints, 'sm'),
        'S':  map-get($grid-breakpoints, 'md'),
        'M':  map-get($grid-breakpoints, 'lg'), // 776px
        'L':  992px, // 992
        'XL': map-get($grid-breakpoints, 'xl') // 1200
) !default;

@mixin respond-to($breakpoint, $max: false) {
  // If the key exists in the map
  @if map-has-key($breakpoints, $breakpoint) {

    // if max-width breakpoint is desired ...
    @if($max) {
      @media(max-width: map-get($breakpoints, $breakpoint)) {
        @content;
      }
    }

    // ... otherwise, default to min-width
    @else {
      @media (min-width: map-get($breakpoints, $breakpoint)) {
        @content;
      }
    }
  }

  // If the key doesn't exist in the map
  @else {
    @warn "Unfortunately, no value could be retrieved from `#{$breakpoint}`. "
        + "Available breakpoints are: #{map-keys($breakpoints)}.";
  }

}