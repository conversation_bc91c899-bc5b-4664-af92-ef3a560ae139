const sepaMandateService = {
  isValidIban(ibanStr: string): boolean {
    // return true if iban is empty or null
    console.log("isValidIban", ibanStr);
    console.log("isValidIban boolean", !ibanStr || ibanStr.trim() === '');
    if (!ibanStr || ibanStr.trim() === '') {
      return true;
    }

    const cleaned = ibanStr.replace(/\s+/g, '');
    if (!/^[A-Z0-9]+$/.test(cleaned)) {
      return false;
    }
    const ibanRegex = /^[A-Z]{2}[0-9]{2}[A-Z0-9]{11,30}$/;
    return ibanRegex.test(cleaned);
  },

  isValidMandateReference(mandateReference: string): boolean {
    const mandateReferenceRegex = /^[A-Z0-9+\?\/\-\:\(\)\.,']{1,35}$/;
    return mandateReferenceRegex.test(mandateReference);
  }
};

export { sepaMandateService };