<template>
  <v-dialog v-bind="$attrs"
            v-on="$listeners"
            :scrollable="scrollable"
            :width="width"
            content-class="white">

    <v-card flat>

      <v-card-title v-if="!isMobile" class="mb-8 mb-md-0 justify-space-between">
        <h3 :class="{ 'text-capitalize' : capitalizetitle === true }" data-id="text-dialog-title">
          <slot name="header"></slot>
        </h3>
        <CDButtonIcon :icon="closeicon" @click="$emit('close')" data-id="button-dialog-close">
        </CDButtonIcon>
      </v-card-title>

      <v-card-title v-else class="header-underline">
        <CDButtonTextIcon
            :icon="closeiconmobile"
            data-id="button-dialog-close-mobile"
            @click.native="$emit('close')">
          <h3 data-id="text-dialog-title-mobile">
            <slot name="header"></slot>
          </h3>
        </CDButtonTextIcon>
      </v-card-title>

      <v-card-text class="black--text mt-6 mt-md-0" :style="[ isScrollable ? { 'height' : scrollheight } : '']">
        <slot></slot>
      </v-card-text>

      <v-card-actions v-if="slotPopulated('actions')"
                      :class="{ 'actions-overline': isMobile, 'pa-8' : isMobile, 'pa-0 ma-8' : !isMobile }">
        <v-row justify="end">
          <slot name="actions"></slot>
        </v-row>
      </v-card-actions>

    </v-card>

  </v-dialog>
</template>

<script lang="ts">
import {Component, Vue, Prop} from 'vue-property-decorator';

@Component
export default class DefaultStoreDialog extends Vue {
  @Prop({ default: 1240 }) width!: number;
  @Prop({ default: false }) scrollable!: boolean;
  @Prop({ default: true }) capitalizetitle!: boolean;
  @Prop({ default: '$close' }) closeicon!: string;
  @Prop({ default: '$close '}) closeiconmobile!: string;
  @Prop({ default: '400px'}) scrollheight!: string; // fixed height needed with 'scrollable' attribute

  get isMobile(): boolean {
    return this.$vuetify.breakpoint.smAndDown;
  }

  get isScrollable(): boolean {
    return this.scrollable;
  }

  slotPopulated(whichSlot: string): boolean {
    return !!(this.$slots && this.$slots[whichSlot]);
  }


}
</script>

<style lang="scss" scoped>
.header-underline {
  border-bottom: 1px solid var(--v-grey-lighten1);
}
.actions-overline {
  border-top: 1px solid var(--v-grey-lighten1);
}
</style>