<template functional>
    <div class="content-divider">
        <span><slot></slot></span>
    </div>
</template>

<script lang="ts">
    import { Component, Vue } from 'vue-property-decorator';

    @Component
    export default class ContentDivider extends Vue {}
</script>

<style scoped lang="scss">
    @import "shop/core/constants";

    .content-divider {
        width: 2px;
        height: 100%;
        border: 1px solid var(--v-grey-lighten1);
        background-color: var(--v-grey-lighten1);

        font-size: 18px;
        color: var(--v-grey-darken2);

        display: flex;
        align-items: center;
        justify-content: center;

        span {
            text-align: center;
            min-width: 30px;
            background-color: var(--v-grey-lighten5);
        }

        // hardcoded breakpoint as component will be deleted
        @media screen and (max-width: 719px) {
            width: 100%;
            height: 2px;
        }
    }
</style>
