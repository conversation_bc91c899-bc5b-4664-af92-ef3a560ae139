<template>
    <div v-if="alertQueue && alertQueue.length > 0" class="message-container">
        <message-component
                v-for="(item, index) in alertQueue"
                v-bind:key="item.message + index"
                v-bind:message="item.message"
                v-bind:error="item.type === 'error'"
                v-bind:warning="item.type === 'warning'"
                v-bind:success="item.type === 'success'"
                v-bind:toast="item.toast"
                v-bind:dismissible="item.dismissible"
                v-bind:timeout="item.timeout"
                v-on:removealert="removeAlert(item)">
        </message-component>
    </div>
</template>

<script lang="ts">
    import {Component, Vue} from 'vue-property-decorator';
    import {Alert, messageService} from 'common/services';
    import MessageComponent from 'common/components/MessageComponent.vue';

    @Component({components: {MessageComponent}})
    export default class AlertDisplay extends Vue {
        alertQueue: Alert[] = [];

        mounted(): void {
            messageService.subscribe(this.alertQueue);
        }

        removeAlert(item: Alert): void {
            const index = this.alertQueue.indexOf(item);
            if (index >= 0) {
                this.alertQueue.splice(index, 1);
            }
        }
    }
</script>

