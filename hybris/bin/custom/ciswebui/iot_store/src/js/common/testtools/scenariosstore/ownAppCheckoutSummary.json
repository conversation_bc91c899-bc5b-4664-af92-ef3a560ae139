{"paymentAddress": {"id": "8796093120535", "title": null, "titleCode": null, "firstName": "<PERSON><PERSON>", "lastName": "User", "companyName": null, "line1": "Sample Street", "line2": "123", "town": "Sample Town", "region": null, "district": null, "postalCode": "12345", "phone": "+1234567890", "cellphone": null, "email": "<EMAIL>", "country": {"isocode": "DE", "name": "Germany", "canBuy": false}, "shippingAddress": false, "billingAddress": true, "defaultAddress": false, "visibleInAddressBook": true, "formattedAddress": "Sample Street, 123, 12345, Sample Town, Germany"}, "totalPrice": {"symbol": "EUR", "value": "0.0"}, "totalTax": {"symbol": "EUR", "value": "0.0"}, "totalPriceWithTax": {"symbol": "EUR", "value": "0.0"}, "entries": [{"productName": "Smoke App", "productCode": "A_00001007_full", "productUrl": "/p/A_00001007", "companyName": "De<PERSON> Apps", "versionName": "1.0.0", "licenseName": "Purchase", "licenseType": "FULL", "logoUrl": "/sample-data/app/app-icon-medium-1.jpeg", "itemPrice": {"symbol": "EUR", "value": "0.0"}, "totalPrice": {"symbol": "EUR", "value": "0.0"}, "quantity": 5, "entryNumber": 1, "scalePrices": [], "countryEulas": []}], "cartHash": "2B57ADF4C143713A5800111DC8FF4A5EE47E91EDD9D53BFD7278615C4144A4D2", "showInvoiceNotes": false, "ownAppsPurchase": true, "invoiceNoteSizeLimit": 50}