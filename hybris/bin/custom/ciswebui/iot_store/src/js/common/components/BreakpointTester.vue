<template>
  <div>
    <h1>{{ currentBreakpoint }}</h1>
    <h2>{{ windowWidth }}</h2>
  </div>
</template>

<script lang="ts">
import { Vue, Component } from 'vue-property-decorator';

@Component
export default class BreakpointTester extends Vue {

  get currentBreakpoint(): string {
    return this.$vuetify.breakpoint.name;
  }

  get windowWidth(): number {
    return window.innerWidth;
  }

}
</script>
