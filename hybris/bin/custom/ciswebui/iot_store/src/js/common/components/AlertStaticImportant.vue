<template>
    <div class="alert">
        <div class="wrapper" v-bind:class="{ 'centered' : centered }">
            <info-icon v-if="!centered"></info-icon>
            <div class="content">
              <slot></slot>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
    import InfoIcon from 'common/images/boxed/icon-24-info-e.svg';

    @Component({
        components: {
            InfoIcon
        }
    })
    export default class AlertStaticImportant extends Vue {
      @Prop() centered!: boolean;

    }
</script>

<style lang="scss" scoped>
    @import "shop/core/constants";

    .alert {
        margin-bottom: 0;
        .wrapper {
            display: grid;
            grid-template-columns: $spacing-l auto;
            max-width: 1200px;
            margin: 0 auto;
            padding: $spacing-xs 0;
            .content {
                display: flex;
                justify-content: space-between;
                p {
                    color: var(--v-grey-lighten5);
                    strong {
                      color: var(--v-grey-lighten5);
                    }
                }
                a {
                    text-transform: uppercase;
                    color:  var(--v-grey-lighten5);
                    &:hover {
                        color:  var(--v-grey-lighten5);
                    }
                }
            }

            &.centered {
              grid-template-columns: none;
              .content {
                justify-content: space-around;
                flex-direction: column;
                align-items: center;
                p {
                  text-align: center;
                }
              }
            }
        }
    }
    .alert.alert-info {
        background-color: var(--v-info-base);
    }
</style>
