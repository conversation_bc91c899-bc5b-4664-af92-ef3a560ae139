<template>
  <div v-if="message" class="root">

    <template v-if="!toast">
      <CDNotificationPersistent
          :type="notificationType"
          :data-id="`container-alert-${notificationType}`"
          dismissible>
        {{ message }}
      </CDNotificationPersistent>
    </template>

    <template v-else>
      <CDNotificationToastWrapper x-position="40" y-position="120">
        <CDNotificationToast
            :value.sync="showToast"
            :type="notificationType"
            :data-id="`container-alert-${notificationType}`"
            :timeout="timeout"
            :dismissible="dismissible">
          {{ message }}
        </CDNotificationToast>
      </CDNotificationToastWrapper>
    </template>

  </div>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';

enum Levels {
    INFO = 'info',
    WARNING = 'warning',
    SUCCESS = 'success',
    ERROR = 'error'
}

@Component
export default class MessageComponent extends Vue {
  @Prop() error!: boolean;
  @Prop() warning!: boolean;
  @Prop() success!: boolean;
  @Prop() message!: string;
  @Prop({ default: false }) dismissible!: boolean;
  @Prop({ default: false }) toast!: boolean;
  @Prop({ default: 4000 }) timeout!: number;

  Levels = Levels;
  showToast = false;

  get notificationType(): string {
      if (this.error) {
          return Levels.ERROR;
      }
      if (this.warning) {
          return Levels.WARNING;
      }
      if (this.success) {
          return Levels.SUCCESS;
      }
      return Levels.INFO;
  }

  mounted(): void {
    if (this.toast) {
        this.showToast = true;
    }
    window.onload = () => setTimeout(() => window.scrollTo(0, 0), 0);
  }
}
</script>
