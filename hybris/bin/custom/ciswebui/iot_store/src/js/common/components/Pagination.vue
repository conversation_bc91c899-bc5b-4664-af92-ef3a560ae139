<template>
    <div v-if="paginationData.hasPrevious || paginationData.hasNext" class="pagination-display">
        <template v-if="paginationData.hasPrevious && paginationData.currentPage < paginationData.numberOfPages">
            <a v-bind:href="linkForPage(paginationData.currentPage - 1)"
              class="arrow"
              data-id="left-arrow-stub">
                <CDIcon color="primary" small>$arrowleft</CDIcon>
            </a>
        </template>

        <div class="link-number" v-for="entryIndex in paginationData.numberOfPages">
            <template v-if="isCurrentPage(entryIndex - 1)">
                <a class="active-page">
                    {{entryIndex}}
                </a>
            </template>
            <template v-else>
                <a v-bind:href="linkForPage(entryIndex - 1)">
                    {{entryIndex}}
                </a>
            </template>
        </div>

        <template v-if="paginationData.hasNext">
            <a v-bind:href="linkForPage(paginationData.currentPage + 1)"
               class="arrow"
               data-id="right-arrow-stub">
                <CDIcon color="primary" small>$arrowright</CDIcon>
            </a>
        </template>
    </div>
</template>

<script lang="ts">
    import {Component, Prop, Vue} from 'vue-property-decorator';
    import {core} from 'common/generated-types/types';
    import PaginationData = core.PaginationData;

    @Component
    export default class Pagination extends Vue {
        @Prop() pageUrl!: string;
        @Prop() paginationData!: PaginationData;

        isCurrentPage(pageNumber: number): boolean {
            return this.paginationData.currentPage === pageNumber;
        }

        linkForPage(pageNumber: number): string {
            return this.pageUrl + '?page=' + pageNumber;
        }
    };
</script>

<style lang="scss" scoped>
    @import "common/design";

    .pagination-display {
        display: flex;
        justify-content: flex-end;
        align-items: center;

        div {
            height: 24px;
            min-width: 17px;
        }

        .link-number a {
            display: inline-block;
            margin: 4px 0 3px;
            padding: 0 5px;
            line-height: 18px;
            text-align: center;

            &.active-page {
                color: var(--v-primary-base) !important;
                font-weight: 600;
            }

            &:not(.active-page):hover {
                color: var(--v-primary-lighten4) !important;
            }
        }

        .arrow {
            margin-top: 1px;
            padding: 0 1px;
            height: 18px;
            display: flex;

            &:hover {
                svg :deep(path) {
                    fill: var(--v-grey-lighten3) !important;
                }
            }
        }
    }
</style>
