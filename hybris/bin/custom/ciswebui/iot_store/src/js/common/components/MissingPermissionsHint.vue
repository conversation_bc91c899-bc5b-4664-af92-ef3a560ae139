<template>
    <div class="permissions-hint">
        {{message}}
    </div>
</template>

<script lang="ts">
    import {Component, Prop, Vue} from 'vue-property-decorator';

    @Component
    export default class MissingPermissionsHint extends Vue {
        @Prop() message!: string;
    }
</script>

<style scoped lang="scss">
    .permissions-hint {
        margin-top: 33px;
        background-color: #EFEFF2;
        width: 100%;
        line-height: 48px;
        border-radius: 8px;
        text-align: center;
        font-size: 14px;
        color: var(--v-grey-darken2);
    }
</style>
