import 'common/testtools/ui_tests_mock';
import {DevconPage, NavigationItemData, NavigationItemGroup, NavigationItemType} from 'common/generated-types/types';

const navigations =
    {
        [NavigationItemType.STORE]: [
            {
                id:"appProductCategory",
                itemCode: "appProductCategory",
                url:"/shop",
                type:"STORE",
                group:"PRODUCT_CATEGORIES",
                text:"Apps",
                description: "",
                target: "_self",
                icon: "",
                index :1,
                entryPage: "",
                customAttributes: {},
            },
            {
                id:"toolProductCategory",
                itemCode: "toolProductCategory",
                url:"/shop/tools",
                type:"STORE",
                group:"PRODUCT_CATEGORIES",
                text:"Azena Tools",
                description: "",
                target: "_self",
                icon: "",
                index:2,
                entryPage: "",
                customAttributes: {},
            },
            {
                id: "deviceManagement",
                itemCode: "deviceManagement",
                url: "http://localhost:8091/page/cameras",
                type: NavigationItemType.STORE,
                group: NavigationItemGroup.HOME_SWITCHER,
                text: "Device Management Portal",
                description: "",
                target: "_blank",
                icon: "",
                index: 10,
                entryPage: "",
                customAttributes: {},
            },
            {
                id: 'storeOrderHistory',
                itemCode: 'storeOrderHistory',
                url: "/shop/my-account/orders",
                type: NavigationItemType.STORE,
                group: NavigationItemGroup.HEADER,
                text: "Order History",
                description: "",
                target: "",
                icon: "user",
                index: 3,
                entryPage: "",
                customAttributes: {},
            } as NavigationItemData,
            {
                id: 'storePaymentDetails',
                itemCode: 'storePaymentDetails',
                url: "/shop/my-account/payment-details",
                type: NavigationItemType.STORE,
                group: NavigationItemGroup.HEADER,
                text: "Payment Details",
                description: "",
                target: "",
                icon: "payment",
                index: 4,
                entryPage: "",
                customAttributes: {},
            } as NavigationItemData,
            {
                id: 'storeSignOut',
                itemCode: 'storeSignOut',
                url: "/shop/logout",
                type: NavigationItemType.STORE,
                group: NavigationItemGroup.HEADER,
                text: "Sign Out",
                description: "",
                target: "",
                icon: "logout",
                index: 6,
                entryPage: "",
                customAttributes: {},
            } as NavigationItemData,
            {
                id: 'storeTermsAndConditions',
                itemCode: 'storeTermsAndConditions',
                url: "https://accounts.azena.com/legal/terms.html",
                type: NavigationItemType.STORE,
                group: NavigationItemGroup.FOOTER,
                text: "Terms and Conditions",
                description: "",
                target: "_blank",
                icon: "",
                index: 1,
                entryPage: "",
                customAttributes: {},
            } as NavigationItemData,
            {
                id: 'storePrivacyPolicy',
                itemCode: 'storePrivacyPolicy',
                url: "https://www.azena.com/privacy-policy",
                type: NavigationItemType.STORE,
                group: NavigationItemGroup.FOOTER,
                text: "Privacy Policy",
                description: "",
                target: "_blank",
                icon: "",
                index: 2,
                entryPage: "",
                customAttributes: {},
            } as NavigationItemData
        ],
        [NavigationItemType.DEVCON]: [
            {
                id: 'devconLogout',
                itemCode: 'devconLogout',
                url: "/logout",
                type: NavigationItemType.DEVCON,
                group: NavigationItemGroup.HEADER,
                text: "Sign Out",
                description: "",
                target: "",
                icon: "logout",
                index: 6,
                entryPage: '',
                customAttributes: {},
            } as NavigationItemData,
            {
                id: 'devconStore',
                itemCode: 'devconStore',
                url: "https://store.dev.local:9002/shop",
                type: NavigationItemType.DEVCON,
                group: NavigationItemGroup.FOOTER,
                text: "Application Store",
                description: "",
                target: "",
                icon: "",
                index: 1,
                entryPage: DevconPage.LANDING,
                customAttributes: {},
            } as NavigationItemData,
            {
                id: 'devconPrivacyPolicy',
                itemCode: 'devconPrivacyPolicy',
                url: "https://www.azena.com/privacy-policy",
                type: NavigationItemType.DEVCON,
                group: NavigationItemGroup.FOOTER,
                text: "Privacy Policy",
                description: "",
                target: "_blank",
                icon: "",
                index: 2,
                entryPage: '',
                customAttributes: {},
            } as NavigationItemData,
            {
                id: 'devconTermsAndConditions',
                itemCode: 'devconTermsAndConditions',
                url: "https://accounts.azena.com/legal/terms.html",
                type: NavigationItemType.DEVCON,
                group: NavigationItemGroup.FOOTER,
                text: "Terms and Conditions",
                description: "",
                target: "_blank",
                icon: "",
                index: 4,
                entryPage: '',
                customAttributes: {},
            } as NavigationItemData,
            {
                id: 'devconApps',
                itemCode: 'devconApps',
                url: "/dc",
                type: NavigationItemType.DEVCON,
                group: NavigationItemGroup.SIDEBAR,
                text: "Apps",
                description: "",
                target: "",
                icon: "apps",
                index: 20,
                entryPage: DevconPage.LANDING,
                customAttributes: {},
            } as NavigationItemData,
            {
                id: 'devconOrderManagement',
                itemCode: 'devconOrderManagement',
                url: "/dc/order-management",
                type: NavigationItemType.DEVCON,
                group: NavigationItemGroup.SIDEBAR,
                text: "Orders",
                description: "",
                target: "",
                icon: "order",
                index: 21,
                entryPage: DevconPage.ORDER_MANAGEMENT,
                customAttributes: {},
            } as NavigationItemData,
            {
                id: 'devconPayment',
                itemCode: 'devconPayment',
                url: "/dc/my-account/payment-services",
                type: NavigationItemType.DEVCON,
                group: NavigationItemGroup.SIDEBAR,
                text: "Payout",
                description: "",
                target: "",
                icon: "coin",
                index: 22,
                entryPage: DevconPage.PAYMENT_SERVICES,
                customAttributes: {},
            } as NavigationItemData,
            {
                id: 'devconDocumentation',
                itemCode: 'devconDocumentation',
                url: "http://localhost:8091/page/developerportal",
                type: NavigationItemType.DEVCON,
                group: NavigationItemGroup.SIDEBAR,
                text: "Documentation",
                description: "",
                target: "_blank",
                icon: "documentation",
                index: 23,
                entryPage: '',
                customAttributes: {},
            } as NavigationItemData,
            {
                id: 'devconHelpDocumentation',
                itemCode: 'devconHelpDocumentation',
                url: "https://docs.azena.com",
                type: NavigationItemType.DEVCON,
                group: NavigationItemGroup.HELP,
                text: "Read developer documentation",
                description: "Find all the information you need to start building your app.",
                target: "_blank",
                icon: "https://static.securityandsafetythings.com/iotstore/images/devcon/dev-documentation.svg",
                index: 0,
                entryPage: '',
                customAttributes: {},
            } as NavigationItemData,
            {
                id: 'devconHelpGetStarted',
                itemCode: 'devconHelpGetStarted',
                url: "https://docs.azena.com/developer/getting_started/setup_the_toolchain",
                type: NavigationItemType.DEVCON,
                group: NavigationItemGroup.HELP,
                text: "Get Started",
                description: "Follow step-by-step tutorials to set up your environment and deploy your first app.",
                target: "_blank",
                icon: "https://static.securityandsafetythings.com/iotstore/images/devcon/get-started.svg",
                index: 1,
                entryPage: '',
                customAttributes: {},
            } as NavigationItemData,
            {
                id: 'devconHelpDownloads',
                itemCode: 'devconHelpDownloads',
                url: "https://docs.dev.securityandsafetythings.com/developer/introduction/downloads",
                type: NavigationItemType.DEVCON,
                group: NavigationItemGroup.HELP,
                text: "Downloads",
                description: "Find development tools, example apps and supporting files.",
                target: "_blank",
                icon: "https://static.securityandsafetythings.com/iotstore/images/devcon/downloads.svg",
                index: 2,
                entryPage: '',
                customAttributes: {},
            } as NavigationItemData,
            {
                id: 'devconHelpDeeplearning',
                itemCode: 'devconHelpDeeplearning',
                url: "https://docs.dev.securityandsafetythings.com/developer/core_topics/computer_vision/getting_started/detecting_common_objects",
                type: NavigationItemType.DEVCON,
                group: NavigationItemGroup.HELP,
                text: "Deep learning on the edge",
                description: "Learn how to get started with running and training your deep learning models.",
                target: "_blank",
                icon: "https://static.securityandsafetythings.com/iotstore/images/devcon/deep-learning.svg",
                index: 3,
                entryPage: '',
                customAttributes: {},
            } as NavigationItemData,
            {
                id: 'devconHelpDistribute',
                itemCode: 'devconHelpDistribute',
                url: "https://docs.azena.com/developer/offer_apps_on_application_store/get_started_as_a_seller/overview",
                type: NavigationItemType.DEVCON,
                group: NavigationItemGroup.HELP,
                text: "Distribute your app",
                description: "Learn how to publish and maintain your apps.",
                target: "_blank",
                icon: "https://static.securityandsafetythings.com/iotstore/images/devcon/distribute-app.svg",
                index: 4,
                entryPage: '',
                customAttributes: {},
            } as NavigationItemData
        ],
        [NavigationItemType.GLOBAL]: [
            {
                id: 'globalMyProfile',
                itemCode: 'globalMyProfile',
                url: "https://www.google.com",
                type: NavigationItemType.GLOBAL,
                group: NavigationItemGroup.HEADER,
                text: "My Profile",
                description: "",
                target: "_blank",
                icon: "user",
                index: 1,
                entryPage: '',
                customAttributes: {},
            } as NavigationItemData,
            {
                id: 'globalMyCompany',
                itemCode: 'globalMyCompany',
                url: "https://duckduckgo.com",
                type: NavigationItemType.GLOBAL,
                group: NavigationItemGroup.HEADER,
                text: "My Company",
                description: "",
                target: "_blank",
                icon: "company",
                index: 2,
                entryPage: '',
                customAttributes: {},
            } as NavigationItemData,
            {
                id: 'globalSupport',
                itemCode: 'globalSupport',
                url: "https://support.azena.com/hc",
                type: NavigationItemType.GLOBAL,
                group: NavigationItemGroup.HEADER,
                text: "Support",
                description: "",
                target: "_blank",
                icon: "support",
                index: 5,
                entryPage: '',
                customAttributes: {},
            } as NavigationItemData,
            {
                id: 'globalLegal',
                itemCode: 'globalLegal',
                url: "https://azena.com/legal-notice",
                type: NavigationItemType.GLOBAL,
                group: NavigationItemGroup.FOOTER,
                text: "Legal",
                description: "",
                target: "_blank",
                icon: "",
                index: 3,
                entryPage: '',
                customAttributes: {},
            } as NavigationItemData,
            {
                id: 'globalImprint',
                itemCode: 'globalImprint',
                url: "https://azena.com/imprint",
                type: NavigationItemType.GLOBAL,
                group: NavigationItemGroup.FOOTER,
                text: "Imprint",
                target: "_blank",
                description: "",
                icon: "",
                index: 5,
                entryPage: '',
                customAttributes: {},
            } as NavigationItemData,
            {
                id: "globalHelpSupport",
                itemCode: "globalHelpSupport",
                url: "https://support.azena.com/hc",
                type: NavigationItemType.GLOBAL,
                group: NavigationItemGroup.HELP,
                text: "Visit Help & Support",
                description: '',
                target: "_blank",
                icon: '',
                index: 0,
                entryPage: '',
                customAttributes: {},
            } as NavigationItemData,
            {
                id: "globalHelpContact",
                itemCode: "globalHelpContact",
                url: "https://support.azena.com/hc/requests/new",
                type: NavigationItemType.GLOBAL,
                group: NavigationItemGroup.HELP,
                text: "Contact Us",
                description: '',
                target: "_blank",
                icon: '',
                index: 1,
                entryPage: '',
                customAttributes: {},
            } as NavigationItemData
        ]
    };
const storeNavigations: NavigationItemData[] = [...navigations[NavigationItemType.STORE], ...navigations[NavigationItemType.GLOBAL]];

function navigationData(active: String)  {
    return {
        "availability": {"subpageUrl": "app/pricing/pc_00001004", "isActivePage": active === 'availability'},
        "pricing": {"subpageUrl": "app/pricing/pc_00001004", "isActivePage": active === 'pricing'},
        "priceAndAvailability": {"subpageUrl": "app/pricing/pc_00001004", "isActivePage": active === 'priceAndAvailability'},
        "publicListing": {"subpageUrl": "app/storecontent/pc_00001004", "isActivePage": active === 'publicListing'},
        "apk": {"subpageUrl": "app/versions/pc_00001004", "isActivePage": active === 'apk'}
    }
};

export {
    storeNavigations,
    navigationData
};
