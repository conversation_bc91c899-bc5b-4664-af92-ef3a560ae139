import Vue from 'vue';
import 'common/test-directive';
import {FocusDirective, OnDocumentDirective, TransformExternalLinks} from 'common/directives';

Vue.directive('focus', FocusDirective);
Vue.directive('onDocument', OnDocumentDirective());
Vue.directive('transformExternalLinks', TransformExternalLinks);

window.frontendData = window.frontendData || {};
window.frontendData.coreData = window.frontendData.coreData || {};
window.frontendData.coreData.currentLanguage = 'en';
Object.defineProperty(window, 'matchMedia', {
    writable: true,
    value: jest.fn().mockImplementation(query => ({
        matches: false,
        media: query,
        onchange: null,
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
        dispatchEvent: jest.fn(),
    }))
});
window.scrollTo = jest.fn();
