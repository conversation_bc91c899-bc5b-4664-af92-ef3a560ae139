import {wrapperComponentFactory} from 'common/testtools/unit-test-utils';
import CompanyApps from 'shop/companyprofile/components/CompanyApps';
import productResource from 'shop/resources/productResource';
import companyAppsData from 'common/testtools/scenariosstore/companyAppsData.json';
import companyAppsDataEmpty from 'common/testtools/scenariosstore/companyAppsDataEmpty.json';
import {cloneDeep} from 'lodash';

jest.mock('shop/resources/productResource');

const companyUid = '90aef59c-48c4-4b18-b587-c1c32a7e544a';

describe('CompanyApps', () => {

    it('displays company apps', async () => {
        productResource.getProducts.mockImplementation(() => Promise.resolve({
            status: 200,
            data: companyAppsData
        }));
        const wrapper = wrapperComponentFactory(CompanyApps, {
            props: { companyUid: companyUid }
        });
        await wrapper.vm.$nextTick();

        expect(wrapper.vm.hasApps).toBeTruthy();
        expect(wrapper.vm.apps.length).toEqual(4);

        await wrapper.vm.$nextTick();

        expect(wrapper.find('[data-id="text-no-apps-found"]').exists()).toBeFalsy();
        expect(wrapper.find('[data-id^="container-company-app-"]').exists()).toBeTruthy();

        expect(productResource.getProducts).toHaveBeenCalledWith(
            {
                companyUid: companyUid,
                page: 0,
                pageSize: 1000
            }
        );
    });

    it('displays message when no apps found', async () => {
        productResource.getProducts.mockImplementation(() => Promise.resolve({
            status: 200,
            data: companyAppsDataEmpty
        }));
        const wrapper = wrapperComponentFactory(CompanyApps, {
            props: { companyUid: companyUid }
        });
        await wrapper.vm.$nextTick();

        expect(wrapper.vm.hasApps).toBeFalsy();

        await wrapper.vm.$nextTick();
        expect(wrapper.find('[data-id="text-no-apps-found"]').exists()).toBeTruthy();
        expect(wrapper.find('[data-id^="container-company-app-"]').exists()).toBeFalsy();

        expect(productResource.getProducts).toHaveBeenCalledWith(
            {
                companyUid: companyUid,
                page: 0,
                pageSize: 1000
            }
        );
    });

    it('App Cards should have company name subs as text', async () => {
        const dataWithPublishedProfiles = cloneDeep(companyAppsData);
        dataWithPublishedProfiles.products.map(product => {
            product.company.hasPublishedProfile = true;
            product.company.profileUrl = '/profile-url/uid';
        });
        productResource.getProducts.mockImplementation(() => Promise.resolve({
            status: 200,
            data: dataWithPublishedProfiles
        }));
        const wrapper = wrapperComponentFactory(CompanyApps, {
            props: { companyUid: companyUid }
        });
        await wrapper.vm.$nextTick();

        expect(wrapper.vm.hasApps).toBeTruthy();
        expect(wrapper.vm.formattedApps).not.toHaveLength(0);
        wrapper.vm.formattedApps.map(app => expect(app.subAsLink).toBeFalsy());
    });
});
