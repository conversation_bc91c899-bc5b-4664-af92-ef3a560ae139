import util from 'common/util';
import { AxiosResponse } from 'axios';
import { SepaMandatePayload } from 'common/types';

let axios = util.axios;

export interface SepaMandateDto {
    mandateReference: string;
    iban?: string;
    accountHolderName?: string;
    signatureDate?: string;
    status: 'DRAFT' | 'FINALIZED';
    creditor?: string;
    creditorIdentifier?: string;
    typeOfPayment?: string;
}

export interface SepaMandateResponse {
    data: SepaMandateDto;
    status: number;
}

const sepaMandateResource = {
    /**
     * Create a draft mandate (automatically generating the reference)
     */
    createDraftMandate(): Promise<AxiosResponse<SepaMandateDto>> {
        const path = '/api/v1/mandates/initialize';
        return axios.post(path);
    },

    /**
     * Activate/finalize a mandate with the provided data
     * If the provided dto contains all required fields, then finalize the draft mandate.
     * Otherwise, update the draft mandate with additional input.
     */
    activateMandate(reference: string, payload?: SepaMandatePayload): Promise<AxiosResponse<SepaMandateDto>> {
        const path = `/api/v1/mandates/${reference}/activate`;
        return axios.post(path, payload);
    },

    /**
     * Get a mandate by reference
     */
    getMandateByReference(reference: string, includeDrafts: boolean = false): Promise<AxiosResponse<SepaMandateDto>> {
        const path = `/api/v1/mandates/${reference}`;
        return axios.get(path, { params: { includeDrafts } });
    },

    /**
     * Get all mandates
     */
    getAllMandates(includeDrafts: boolean = false): Promise<AxiosResponse<SepaMandateDto[]>> {
        const path = '/api/v1/mandates';
        return axios.get(path, { params: { includeDrafts } });
    }
};

export default sepaMandateResource;
