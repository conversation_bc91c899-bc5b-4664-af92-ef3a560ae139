<template>
  <div class="checkout-confirmation-container">
    <v-container :fluid="$vuetify.breakpoint.lgAndDown" class="mt-4 mt-lg-16">

      <div class="checkout-success-container">
        <v-row>
          <v-col cols="12" lg="8">
            <div class="checkout-success-header">
              <h1 class="mb-4">{{ $t('shop.checkout.confirmation.confirmationHeader') }}</h1>
              <h3 class="checkout-success-text">{{ $t('shop.checkout.confirmation.confirmationLine1') }}</h3>
              <h3 class="checkout-success-text">{{ $t('shop.checkout.confirmation.confirmationLine2') }}</h3>
            </div>

            <div class="checkout-success-order">
              <h3 class="checkout-success-text" v-if="ownAppOrder">{{ $t('shop.cartItem.ownApp.info') }}</h3>
              <h3 id="sepaSuccessInfo" class="checkout-success-text"
                  v-if="orders.length === 1 && orders[0].sepaPaymentUsed">
                {{ $t('shop.checkout.confirmation.confirmationLineInvoice') }}
              </h3>
              <h3 class="checkout-success-text" v-if="orders.length === 1">
                {{ $t('shop.checkout.confirmation.confirmationLine3') }}:&nbsp;
                <span><a target="_blank" v-bind:href="orders[0].url">{{ orders[0].code }}</a></span>
              </h3>
              <h3 class="checkout-success-text" v-if="orders.length > 1">
                {{ $t('shop.checkout.confirmation.confirmationLine3plural') }}:
                <a target="_blank" v-bind:href="item.url" v-for="item in orders">{{ item.code }}</a>
              </h3>
            </div>

            <div v-if="isAppToFollowPurchased" class="mt-5">
              <span>{{ $t('shop.checkout.confirmation.followAppNotification') }}</span>
              <a v-for="app in followedApps"
                 :href="app.url"
                 target="_blank">
                {{ app.name }}
              </a>
            </div>
          </v-col>
          <v-col cols="12" lg="4" class="d-flex justify-lg-end">
            <div class="confirmation-button">
              <CDButton
                  :href="shopHome"
                  class="mb-4"
                  data-id="continue-btn">
                {{ $t('shop.checkout.confirmation.continueShopping') }}
              </CDButton>
              <CDButton
                  v-if="coreData.numberOfCartItems > 0"
                  :href="backUrl"
                  class="primary ml-2 mb-4 button-back-to-cart"
                  data-id="button-back-to-cart">
                {{ $t('shop.checkout.backToCart') }}
              </CDButton>
            </div>
          </v-col>
        </v-row>
      </div>

      <v-row>
        <v-col cols="12">
          <div class="my-apps d-flex mt-15 pa-8 pa-lg-16 rounded-lg">
            <div class="confirmation-cameras">
              <integrators></integrators>
            </div>
            <div class="confirmation-content">
              <h3 class="content-paragraph">
                {{ $t('shop.checkout.confirmation.installPrompt') }}
              </h3>
              <div class="content-description d-flex mt-9">
                <div class="content mb-5">
                  <i18n path="shop.checkout.confirmation.installInstructions" tag="p">
                    <template v-slot:linkToPortal>
                      <a :href="`${deviceManagementPortalLink}`">
                        {{ $t("shop.checkout.confirmation.deviceManagementPortal") }}
                      </a>
                    </template>
                  </i18n>
                  <div class="mt-6">
                    <a target="_blank" v-bind:href="supportOnlineLink">
                      {{ $t('shop.checkout.confirmation.learnMore') }}
                    </a>
                  </div>
                </div>
                <div class="content">
                  <content-divider>{{$t('or')}}</content-divider>
                </div>
                <div class="content">
                  <p>{{ $t('shop.checkout.confirmation.toolInstallInstructions') }}</p>
                  <div class="mt-6">
                    <a target="_blank" v-bind:href="supportOfflineLink">
                      {{ $t('shop.checkout.confirmation.learnMore') }}
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </v-col>
      </v-row>

    </v-container>
  </div>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import {
  CheckoutConfirmationData,
  PurchasedProductData,
  ShopCoreData,
  UrlData
} from 'common/generated-types/types';
import {ShopRoute, SupportUrlPaths} from 'common/constants';
import Integrators from 'common/images/boxed/integrators.svg';
import {ContentDivider} from 'common/components';
import {navigationService} from 'common/services';
import { isEmpty } from 'lodash';

@Component({
  components: {
    ContentDivider,
    Integrators
  }
})
export default class CheckoutConfirmation extends Vue {

  @Prop() coreData!: ShopCoreData;
  @Prop() pageData!: CheckoutConfirmationData;
  orders: UrlData[] = [];
  camerasUrl = '';
  ownAppOrder = false;

  supportLink = this.coreData.supportUrl;
  readonly backUrl = ShopRoute.CHECKOUT_PAYMENT_METHOD_BACK;

  deviceManagementPortalLink = this.camerasPurchasedUrl;
  shopHome: string = ShopRoute.HOME;

  created(): void {
    this.setPageTitle();
  }

  private setPageTitle() {
    document.title = this.$t('shop.checkout.pageTitle') as string + this.$t('navigation.storePageTitle') as string;
  }

  mounted(): void {
    this.orders = this.pageData.urlData;
    this.camerasUrl = this.camerasPurchasedUrl;
    this.ownAppOrder = this.orders.some(v => v.ownAppOrder);
    this.supportLink = navigationService.byItemCode('globalSupport').url;
  }

  get supportOfflineLink(): string {
    return `${this.supportLink}${SupportUrlPaths.INSTALL_APPS_OFFLINE}`;
  }

  get supportOnlineLink(): string {
    return `${this.supportLink}${SupportUrlPaths.INSTALL_APPS_ONLINE}`;
  }

  get isAppToFollowPurchased(): boolean {
    return !isEmpty(this.followedApps);
  }

  get followedApps(): PurchasedProductData[] {
    const ALLOWED_LICENSES = ['FULL', 'SUBSCRIPTION'];
    return this.pageData.purchasedProducts.filter(product => ALLOWED_LICENSES.includes(product.licenseType));
  }

  get camerasPurchasedUrl(): string {
    const camerasRoute = '/systems';
    const url = new URL(this.pageData.camerasUrl + camerasRoute);
    const query = new URLSearchParams(url.search);
    query.append('purchased-apps', 'true');
    url.search = query.toString();
    return url.toString();
  }
}

</script>


<style scoped lang="scss">
.checkout-confirmation-container {
    .my-apps {
      border: solid 1px var(--v-grey-darken1);
      background-color: var(--v-shades-white);
      min-height: 300px;
      .confirmation-cameras {
        svg {
          width: 175px;
          height: 190px;
          margin-top: -10px;
        }
        flex: 1;
      }
      .confirmation-content {
        flex: 3;
        .content-paragraph {
          color: var(--v-grey-darken2);
        }
        .content-description {
          // hardcoded breakpoint as component will be deleted
          @media screen and (max-width: 719px) {
            flex-direction: column;
          }
          .content {
            flex: 1 0 auto;
            :deep(p) {
              max-width: 270px;
              // hardcoded breakpoint as component will be deleted
              @media screen and (max-width: 719px) {
                max-width: none;
              }
            }
          }
        }
        // hardcoded breakpoint as component will be deleted
        @media screen and (max-width: 991px) {
          margin-top: 40px;
        }
      }
      // hardcoded breakpoint as component will be deleted
      @media screen and (max-width: 991px) {
        flex-direction: column;
      }

    }
}
</style>
