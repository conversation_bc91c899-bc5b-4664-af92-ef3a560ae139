import util from 'common/util';
import { AxiosResponse } from 'axios';
import { SepaMandatePayload } from 'common/types';

let axios = util.axios;

// Create a specialized axios instance for SEPA mandate service
const sepaMandateAxios = axios.create({
    // The SEPA mandate service expects JWT tokens in Authorization header
    // Since this is a separate microservice, we need to handle authentication differently
    timeout: 30000,
});

// Add request interceptor to include JW<PERSON> token if available
sepaMandateAxios.interceptors.request.use(
    (config) => {
        // Try to get JWT token from various sources
        const token = getJwtToken();
        if (token) {
            config.headers.Authorization = `Bearer ${token}`;
        }
        
        // Ensure we have the required headers
        config.headers['Content-Type'] = 'application/json';
        config.headers['Accept'] = 'application/json';
        
        return config;
    },
    (error) => {
        return Promise.reject(error);
    }
);

// Add response interceptor for error handling
sepaMandateAxios.interceptors.response.use(
    (response) => response,
    (error) => {
        if (error.response && error.response.status === 401) {
            console.warn('SEPA Mandate Service: Unauthorized access, redirecting to login');
            window.location.assign('/logout');
        }
        return Promise.reject(error);
    }
);

/**
 * Attempts to retrieve JWT token from various sources
 * This is a fallback mechanism since the main app uses session-based auth
 */
function getJwtToken(): string | null {
    // Try to get token from window object (if set by server)
    if (window.frontendData?.authData?.jwtToken) {
        return window.frontendData.authData.jwtToken;
    }
    
    // Try to get token from localStorage (if stored by another part of the app)
    const storedToken = localStorage.getItem('jwt_token') || sessionStorage.getItem('jwt_token');
    if (storedToken) {
        return storedToken;
    }
    
    // Try to get token from cookies (if set by server)
    const tokenFromCookie = document.cookie
        .split('; ')
        .find(row => row.startsWith('jwt_token='))
        ?.split('=')[1];
    
    if (tokenFromCookie) {
        return tokenFromCookie;
    }
    
    // If no token found, log warning
    console.warn('SEPA Mandate Service: No JWT token found. API calls may fail.');
    return null;
}

export interface SepaMandateDto {
    mandateReference: string;
    iban?: string;
    accountHolderName?: string;
    signatureDate?: string;
    status: 'DRAFT' | 'FINALIZED';
    creditor?: string;
    creditorIdentifier?: string;
    typeOfPayment?: string;
}

export interface SepaMandateResponse {
    data: SepaMandateDto;
    status: number;
}

const sepaMandateResource = {
    /**
     * Create a draft mandate (automatically generating the reference)
     */
    createDraftMandate(): Promise<AxiosResponse<SepaMandateDto>> {
        const path = '/api/v1/mandates/initialize';
        return sepaMandateAxios.post(path);
    },

    /**
     * Activate/finalize a mandate with the provided data
     * If the provided dto contains all required fields, then finalize the draft mandate.
     * Otherwise, update the draft mandate with additional input.
     */
    activateMandate(reference: string, payload?: SepaMandatePayload): Promise<AxiosResponse<SepaMandateDto>> {
        const path = `/api/v1/mandates/${reference}/activate`;
        return sepaMandateAxios.post(path, payload);
    },

    /**
     * Get a mandate by reference
     */
    getMandateByReference(reference: string, includeDrafts: boolean = false): Promise<AxiosResponse<SepaMandateDto>> {
        const path = `/api/v1/mandates/${reference}`;
        return sepaMandateAxios.get(path, { params: { includeDrafts } });
    },

    /**
     * Get all mandates
     */
    getAllMandates(includeDrafts: boolean = false): Promise<AxiosResponse<SepaMandateDto[]>> {
        const path = '/api/v1/mandates';
        return sepaMandateAxios.get(path, { params: { includeDrafts } });
    }
};

export default sepaMandateResource;
