<template>
  <v-container fluid class="mt-8">
    <v-row>
      <v-col cols="12">
        <h2 class="text-h2 company-apps-title" v-if="!appsLoading">
          {{$t('shop.companyProfile.apps.title')}} ({{totalApps}})
        </h2>
      </v-col>
    </v-row>

    <v-row v-if="!appsLoading && !hasApps">
      <v-col cols="12" data-id="text-no-apps-found">
        {{ $t("shop.companyProfile.apps.noAppsFound") }}
      </v-col>
    </v-row>
    <v-row>
      <v-col cols="12" lg="6" xl="4"
             v-for="app in formattedApps"
             :key="app.code"
             :data-id="`container-company-app-${app.code}`">
        <v-flex fill-height d-flex xs12>
          <app-card :id="app.code" :appCardData="app"></app-card>
        </v-flex>
      </v-col>
    </v-row>
  </v-container>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import {CategoryPageData, SimpleProductData} from 'common/generated-types/types';
import {messageService} from 'common/services';
import {AppCard} from 'common/components';
import {pageSpinner} from 'common/components/spinner';
import {AppCardInterface, SearchQueryParams} from 'common/types';
import {productResource} from 'shop/resources';

@Component({
  components: {
    AppCard
  }
})
export default class CompanyApps extends Vue {
  @Prop() companyUid!: string;

  apps: SimpleProductData[] = [];
  totalApps = 0;
  appsLoading = false;

  pageSpinner = pageSpinner;

  async mounted(): Promise<void> {
    await this.getCompanyApps();
  }

  getCompanyApps(): Promise<void> {
    if (this.appsLoading) {
      return Promise.resolve();
    }
    this.appsLoading = true;
    const queryParams = this.buildQuery();
    return productResource.getProducts(queryParams).then(response => {
      const data = response.data;
      this.populateItems(data);
      this.appsLoading = false;
    }).catch(error => {
      messageService.errorResponse(error.response?.data, this.$i18n);
    }).finally(() => {
      this.appsLoading = false;
      this.pageSpinner.isActive && this.pageSpinner.stop();
    });
  }

  private buildQuery(): SearchQueryParams {
    return {
      companyUid: this.companyUid,
      page: 0,
      pageSize: 1000 // large value in page size to get all company apps
    };
  }

  get formattedApps(): AppCardInterface[] {
    return this.apps.map(app => {
      return {
        title: app.name,
        img: app.logoUrl,
        body: app.shortDescription,
        sub: app.company.name,
        subPrefix: this.$i18n.t('by') as string,
        subAsLink: false,
        url: app.url,
        code: app.code,
        chips: []
      };
    });
  }

  private populateItems(pageData: CategoryPageData): void {
    if (pageData.totalNumberOfResults !== this.totalApps) {
      this.totalApps = pageData.totalNumberOfResults;
    }
    this.apps.push(...pageData.products);
  }

  get hasApps(): boolean {
    return this.apps.length > 0;
  }
}
</script>
<style scoped lang="scss">
@import "common/design";
@import "shop/core/constants";


</style>
