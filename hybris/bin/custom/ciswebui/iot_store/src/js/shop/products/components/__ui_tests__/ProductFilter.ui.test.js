describe("ProductFilter", () => {
    it("looks as expected without private apps", async () => {
        await page.goto(global.__DEV_ADDRESS__ + '/shop-products-components-__ui_tests__-ProductFilter');
        await page.waitFor(600);
        expect(await page.screenshot()).toMatchImageSnapshot("init");
    });

    it("looks as expected with private apps", async () => {
        await page.goto(global.__DEV_ADDRESS__ + '/shop-products-components-__ui_tests__-ProductFilter.private');
        await page.waitFor(600);
        expect(await page.screenshot()).toMatchImageSnapshot("init");
    });
});