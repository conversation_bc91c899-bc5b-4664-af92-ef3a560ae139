import {pageSpinner} from 'common/components/spinner';
import {messageService} from "common/services";
import util from 'common/util';
import {ShopRoute} from 'common/constants';
import {ErrorMessageData} from "common/generated-types/types";


let languageSwitcherService = {
    fallbackToDefaultCompanyStore: function() {
        pageSpinner.start(0);
        messageService.clearAll();
        util.axios.post(ShopRoute.RETURN_TO_COMPANY_STORE_API_RESOURCE)
            .then(() => {
                window.location.reload();
            })
            .catch(error => {
                    const messages = error.response?.data?.map((e: ErrorMessageData) => e.message);
                    messageService.error(messages);
                })
            .finally(pageSpinner.stop);
    }
};

export {languageSwitcherService}
