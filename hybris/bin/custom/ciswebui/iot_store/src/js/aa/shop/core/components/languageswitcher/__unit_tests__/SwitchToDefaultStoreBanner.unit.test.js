import 'common/testtools/unit_tests_mock';
import 'common/test-directive';
import SwitchToDefaultStoreBanner from 'aa/shop/core/components/languageswitcher/SwitchToDefaultStoreBanner.vue';
import {wrapperComponentFactory} from "common/testtools/unit-test-utils";

jest.mock('common/util');

const mountBanner = () => {
    return wrapperComponentFactory(SwitchToDefaultStoreBanner, {
        mocks: {
            $t: key => key
        },
        shallow: false
    });
};


describe('SwitchToDefaultStoreBanner', () => {
    it('renders banner correctly', () => {
        const wrapper = mountBanner();

        expect(wrapper.find('.v-wrapper')).toBeTruthy();
        expect(wrapper.find('.v-banner__text').html().includes('shop.infoBanner.text')).toBeTruthy();
        expect(wrapper.find('[data-id="button-switch-to-default-store"]')).toBeTruthy();
    });
});

