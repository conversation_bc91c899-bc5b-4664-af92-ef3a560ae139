import DistributorDialog from 'aa/shop/checkout/components/DistributorDialog.vue';
import { cloneDeep } from 'lodash';
import { wrapperComponentFactory } from 'common/testtools/unit-test-utils';

const mountDistributorDialog = (distributorsList, isOpen = false) =>
    wrapperComponentFactory(DistributorDialog, {
        props: {
            distributorsList,
            value: isOpen
        },
        shallow: false
    });

describe('AA Distributor Dialog', () => {
    let wrapper;
    let distributorList;

    beforeEach(() => {
        distributorList = [
            { name: 'Alpha' },
            { name: 'Beta' }
        ];
        wrapper = mountDistributorDialog(cloneDeep(distributorList), true);
    });

    it('renders its container when isOpen is true', () => {
        expect(
            wrapper.find('[data-id="container-distributor-switcher-dialog"]').exists()
        ).toBe(true);
    });

    it('sets isOpen to false when close button is clicked', async () => {
        await wrapper.find('[data-id="button-distributor-dialog-close"]').trigger('click');
        await new Promise(process.nextTick);
        expect(wrapper.vm.isOpen.value).toBeFalsy();
    });

    it('emits "distributor-changed" event and closes dialog on confirm', async () => {
        const chosen = distributorList[1];
        wrapper.vm.selectedDistributor = chosen;
        await wrapper.find('[data-id="button-distributor-dialog-submit"]').trigger('click');
        await new Promise(process.nextTick);
        const emitted = wrapper.emitted('distributor-changed');
        expect(emitted).toBeTruthy();
        expect(emitted[0]).toEqual([chosen]);
        expect(wrapper.vm.isOpen.value).toBeFalsy()
    });
});