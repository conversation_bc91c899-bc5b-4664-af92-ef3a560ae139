<template>
  <popup-dialog class="sepa-mandate-dialog"
                data-id="add-sepa-mandatepopup-dialog"
                v-bind="$attrs"
                v-on="$attrs"
                :close-button="{ text: t('cancel') }"
                :submit-button="submitButtonProps"
                @submit="submitForm"
                @close="closeDialog"
                :header-close="true"
  >
    <template #header>
      {{ t('shop.payment.sepaMandate.header') }}
    </template>

    <v-container class="mt-4 px-0">
      <CDBlock :border-bottom="true" :padding-all="false" :padding-bottom="true">
        <v-row>
          <v-col cols="12">
            <payment-method-icons class="mt-n2"
                                  :paymentType="PaymentMethodType.SEPA_DIRECTDEBIT"
            >
            </payment-method-icons>

            <CDInput v-model="accountHolderName"
                     :label="t('shop.payment.sepaMandate.accountHolderName')"
                     required
                     outlined
                     class="sepa-input mb-4 mt-4"
                     data-id="sepa-mandate-account-holder-name"
                     :disabled="isSubmitting"
            >
            </CDInput>

            <CDInput v-model="iban"
                     :label="t('shop.payment.sepaMandate.iban')"
                     :error="Boolean(ibanError)"
                     :error-messages="ibanError"
                     required
                     outlined
                     data-id="sepa-mandate-iban"
                     class="sepa-input"
                     :disabled="isSubmitting"
            >
            </CDInput>

            <v-row>
              <v-col cols="11">
                <CDCheckbox v-model="useAsDefault"
                            :label="t('shop.payment.sepaMandate.defaultPaymentCheckbox')"
                            :color="useAsDefault ? 'primary' : 'grey'"
                            data-id="sepa-mandate-default-checkbox"
                            :disabled="isSubmitting"
                >
                  {{ t('shop.payment.sepaMandate.defaultPaymentCheckbox') }}
                </CDCheckbox>
              </v-col>
              <v-col cols="1" class="d-flex align-center pl-0">
                <v-tooltip bottom>
                  <template v-slot:activator="{ on }">
                    <CDIcon v-on="on" dense>$info</CDIcon>
                  </template>
                  {{ t('shop.payment.sepaMandate.defaultTooltip') }}
                </v-tooltip>
              </v-col>
            </v-row>
          </v-col>
        </v-row>

        <v-row class="mt-4 mb-2">
          <v-col cols="12">
            <div class="mb-4">
              <h5 class="d-inline-block mr-2">
                {{ t('shop.payment.sepaMandate.creditor') }}:
              </h5>
              <span>{{ props.creditor }}</span>
            </div>
            <div class="mb-4">
              <h5 class="d-inline-block mr-2">
                {{ t('shop.payment.sepaMandate.creditorIdentifier') }}:
              </h5>
              <span>{{ props.creditorIdentifier }}</span>
            </div>
            <div class="mb-4">
              <h5 class="d-inline-block mr-2">
                {{ t('shop.payment.sepaMandate.mandateReference') }}:
              </h5>
              <span v-if="currentMandateReference">{{ currentMandateReference }}</span>
              <span v-else class="grey--text">Loading...</span>
            </div>
            <div class="mb-4">
              <h5 class="d-inline-block mr-2">
                {{ t('shop.payment.sepaMandate.typeOfPayment') }}:
              </h5>
              <span>{{ props.typeOfPayment }}</span>
            </div>
          </v-col>
        </v-row>
      </CDBlock>

      <v-row class="mt-4">
        <v-col cols="12">
          <p>
            {{ t('shop.payment.sepaMandate.legalText', {
            paymentProvider: 'Adyen',
            beneficiary: props.creditor
          }) }}
          </p>
        </v-col>
      </v-row>
    </v-container>
  </popup-dialog>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useI18n } from 'vue-i18n-bridge';
import { PaymentMethodType, ShopCoreData } from 'common/generated-types/types';
import { sepaMandateService, messageService } from 'common/services';
import { paymentResource } from 'shop/resources';
import { SepaMandatePayload, MandateStatus } from 'common/types';
import { PopupDialog } from 'common/components/popups';
import { PaymentMethodIcons } from 'common/components';

const props = defineProps<{
  creditor: string;
  creditorIdentifier: string;
  mandateReference: string;
  typeOfPayment: string;
  coreData: ShopCoreData;
}>();

const emit = defineEmits<{
  (e: 'confirm'): void;
  (e: 'cancel'): void;
}>();

const { t } = useI18n();

const accountHolderName = ref('');
const iban = ref('');
const useAsDefault = ref(false);
const isSubmitting = ref(false);
const currentMandateReference = ref('');
const isInitialized = ref(false);
const mandateStatus = ref<MandateStatus | null>(null);


const ibanError = computed(() => {
  if (!iban.value) return '';
  return sepaMandateService.isValidIban(iban.value)
      ? ''
      : (t('shop.payment.sepaMandate.error.invalidIban') as string);
});

const submitButtonProps = computed(() => ({
  text: t('save'),
  isDisabled: isSubmitDisabled.value
}));

const isSubmitDisabled = computed(() => {
  return isSubmitting.value || (mandateStatus.value === MandateStatus.ACTIVE);
});


const initializeMandateIfNeeded = async () => {
  try {
    console.log('companyUid: ' + props.coreData.currentCompany?.companyUid);

    // Check if we already have a mandate reference from props (existing draft)
    if (props.mandateReference && props.mandateReference !== 'PGWMD06AF0J11D1NHPKQHA6L3AUH') {
      currentMandateReference.value = props.mandateReference;

      // Try to load existing draft data
      try {
        const response = await paymentResource.getSepaMandateByReference(props.mandateReference, true);
        const mandateData = response.data;

        if (mandateData && Object.keys(mandateData).length > 0) {
          // Populate form with existing draft data from backend response
          accountHolderName.value = mandateData.accountHolderName || '';
          iban.value = mandateData.iban || '';
          mandateStatus.value = mandateData.status as MandateStatus || null;
          console.log('Populated form with existing mandate data:', mandateData);
        } else {
          // No data from backend, show empty fields
          accountHolderName.value = '';
          iban.value = '';
          mandateStatus.value = null;
          console.log('No existing mandate data found, showing empty fields');
        }
      } catch (loadError) {
        console.log('No existing draft found, will create new one');
        // Pre-populate account holder name with user data if available and no backend data
        accountHolderName.value = props.coreData.userName || '';
        iban.value = '';
        mandateStatus.value = null;
      }
    } else {
      // Initialize a new draft mandate
      const response = await paymentResource.initializeSepaMandateDraft(props.coreData.currentCompany?.companyUid);
      const mandateData = response.data;


      if (mandateData && Object.keys(mandateData).length > 0) {
        currentMandateReference.value = mandateData.mandateReference;

        // Populate form with data from initialization response if available
        accountHolderName.value = mandateData.accountHolderName || props.coreData.userName || '';
        iban.value = mandateData.iban || '';
        mandateStatus.value = mandateData.status as MandateStatus || null;
        console.log('Populated form with initialization data:', mandateData);
      } else {
        // No data from backend, use defaults
        currentMandateReference.value = mandateData?.mandateReference || '';
        accountHolderName.value = props.coreData.userName || '';
        iban.value = '';
        mandateStatus.value = null;
        console.log('No initialization data, using defaults');
      }
    }

    isInitialized.value = true;
  } catch (initError) {
    console.error('Error initializing mandate:', initError);
    messageService.error(t('backendError').toString());
    // On error, show empty fields
    accountHolderName.value = '';
    iban.value = '';
    mandateStatus.value = null;
  }
};

const submitForm = async () => {
  if (isSubmitting.value || !isInitialized.value) return;

  if (!accountHolderName.value) {
    messageService.error(t('shop.payment.sepaMandate.error.accountHolderRequired').toString());
    return;
  }

  const trimmedIban = iban.value.replace(/\s/g, '');

  if (!trimmedIban || !sepaMandateService.isValidIban(trimmedIban)) {
    messageService.error(
        t('shop.payment.sepaMandate.error.ibanRequired').toString()
    );
    return;
  }

  isSubmitting.value = true;

  const payload: SepaMandatePayload = {
    accountHolderName: accountHolderName.value,
    iban: trimmedIban,
    useAsDefault: useAsDefault.value,
    signatureDate: new Date().toISOString().split('T')[0], // Current date in YYYY-MM-DD format
    companyId: props.coreData.currentCompany?.companyUid
  };

  try {
    const response = await paymentResource.updateOrFinalizeSepaMandateByReference(
        currentMandateReference.value,
        payload
    );

    if (!response?.data) {
      throw new Error(`Submission failed with status ${response.status}`);
    }

    // Populate UI with data from backend response if available
    const responseData = response.data;
    if (responseData && Object.keys(responseData).length > 0) {
      // Update form fields with response data
      accountHolderName.value = responseData.accountHolderName || accountHolderName.value;
      iban.value = responseData.iban || iban.value;
      currentMandateReference.value = responseData.mandateReference || currentMandateReference.value;
      mandateStatus.value = responseData.status as MandateStatus || mandateStatus.value;
      console.log('Updated form with response data:', responseData);
    } else {
      console.log('No response data to populate, keeping current values');
    }

    messageService.success(t('shop.payment.sepaMandate.success').toString());
    emit('confirm');
  } catch (submitError: unknown) {
    console.error('Error submitting mandate:', submitError);

    const errorResponse = submitError as { response?: { status?: number; data?: { message?: string } } };


    // Handle specific error cases
    if (errorResponse.response?.data?.message) {
      messageService.error(errorResponse.response.data.message);
    } else if (errorResponse.response?.status === 400) {
      messageService.error(t('shop.payment.sepaMandate.error.invalidInput').toString());
    } else {
      messageService.error(t('shop.payment.sepaMandate.error.submissionFailed').toString());
    }
  } finally {
    isSubmitting.value = false;
  }
};


const closeDialog = () => {
  emit('cancel');
};

onMounted(() => {
  initializeMandateIfNeeded();
});
</script>

<style scoped>
p,
span,
h5 {
  color: var(--v-grey-darken3);
}
.sepa-input :deep(.v-input__slot) {
  background-color: var(--v-grey-lighten3) !important;
  border-radius: unset;
  border-bottom: 1px solid var(--v-grey-darken3);
}
</style>