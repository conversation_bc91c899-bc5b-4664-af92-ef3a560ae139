<template>
    <div class="root">
        <v-app :class="coreData.basestore">
          <new-store-header v-if="improvedNavigationEnabled"></new-store-header>
          <store-header v-else
                        class="header"
                        :cameras-url="coreData.camerasUrl"
                        :my-apps-url="coreData.myAppsUrl"
                        :base-url="home"
                        :user-name="coreData.userName"
                        :number-of-cart-items="coreData.numberOfCartItems">
          </store-header>

          <v-main class="content-wrapper">
              <div class="main-message">
                  <v-container>
                      <v-row>
                          <v-col cols="12">
                              <alert-display></alert-display>
                          </v-col>
                      </v-row>
                  </v-container>
              </div>

              <LoginCtaBanner v-if="showLoginCtaBanner"></LoginCtaBanner>
              <DirectDebitCtaBanner v-if="showDirectDebitCtaBanner"></DirectDebitCtaBanner>
              <PurchaseDisabledBanner v-if="!showDirectDebitCtaBanner && showPurchaseDisabledBanner"></PurchaseDisabledBanner>

              <ManagedAccountBanner v-if="storeRootStore.isManagedAccount"></ManagedAccountBanner>
              <SwitchToDefaultStoreBanner v-else-if="showSwitchBackBanner && selectedCountryDiffersFromCompanyCountry"></SwitchToDefaultStoreBanner>

            <div class="main-container">
              <router-view v-if="coreData.httpStatus === 200"></router-view>
              <slot></slot>
            </div>


              <help-slide-in></help-slide-in>
          </v-main>
          <page-footer></page-footer>


          <portal-target name="portal-store" v-on:change="blurBackground"></portal-target>

          <language-switcher-dialog v-if="isLanguageSwitcherFeatureOn"
                                    v-model="languageSwitcherStore.dialogOpened"
                                    :global-fallback-country="globalFallbackCountry"
                                    :global-default-language="globalDefaultLanguage">
          </language-switcher-dialog>
        </v-app>
    </div>
</template>

<script lang="ts">
    import { Component, Vue } from 'vue-property-decorator';
    import StoreHeader from 'aa/shop/core/components/StoreHeader.vue';
    import PageFooter from 'aa/shop/core/components/PageFooter.vue';
    import {ShopCoreData, GalleryItemData, NavigationItemData, CoreData} from 'common/generated-types/types';
    import { i18nService, messageService, userPermissionService, navigationService } from 'common/services';
    import AlertDisplay from 'common/components/AlertDisplay.vue';
    import { StoreDataProvider } from 'shop/resources';
    import {ShopRoute} from 'common/constants';
    import { HelpSlideIn } from 'aa/shop/core/components/helpslidein';
    import SwitchToDefaultStoreBanner from 'aa/shop/core/components/languageswitcher/SwitchToDefaultStoreBanner.vue';
    import {shopCoreDataProvider} from 'common/provider';
    import LoginCtaBanner from 'aa/shop/core/components/loginctabanner/LoginCtaBanner.vue';
    import DirectDebitCtaBanner from 'aa/shop/core/components/sepactabanner/DirectDebitCtaBanner.vue';
    import ManagedAccountBanner from 'aa/shop/core/components/managedaccountbanner/ManagedAccountBanner.vue';
    import {useStoreRootStore} from 'aa/shop/store/storeRoot';
    import {useCoreDataStore} from 'shop/store/coreData';
    import NewStoreHeader from 'aa/shop/core/components/header/NewStoreHeader.vue';
    import {useCartStore} from 'shop/store/cart';
    import {LanguageSwitcherDialog} from 'aa/shop/core/components/languageswitcher';
    import {useLanguageSwitcherStore} from 'aa/shop/store/languageSwitcher';
    import PurchaseDisabledBanner from 'aa/shop/core/components/purchasedisabledbanner/PurchaseDisabledBanner.vue';

    @Component({
        components: {
            NewStoreHeader,
            ManagedAccountBanner,
            LoginCtaBanner,
            SwitchToDefaultStoreBanner,
            HelpSlideIn,
            StoreHeader,
            PageFooter,
            AlertDisplay,
            LanguageSwitcherDialog,
            DirectDebitCtaBanner,
            PurchaseDisabledBanner
        }
    })
    export default class StoreRoot extends Vue {
        coreData = {} as ShopCoreData;
        galleryItems = {} as GalleryItemData[];
        userPermissionService = userPermissionService;
        navigationService = navigationService;

        storeRootStore = useStoreRootStore();
        coreDataStore = useCoreDataStore();
        cartStore = useCartStore();
        languageSwitcherStore = useLanguageSwitcherStore();

        isLanguageSwitcherFeatureOn = Boolean(shopCoreDataProvider.data.moduleConfig.LANGUAGE_SWITCHER);
        languageSwitcherDialog = false;

        readonly home = ShopRoute.HOME + '/';

        $refs!:{
            mainwrap: HTMLElement;
        };

        created(): void {
            const mergedFrontendData = StoreDataProvider.getMergedFrontendData();
            this.coreData = mergedFrontendData.coreData as ShopCoreData;
            this.galleryItems = mergedFrontendData.galleryItems;
        }

        async mounted() {
            this.storeRootStore.setCoreData<CoreData>(this.coreData);
            if (this.coreData.globalMessages.messages) {
                this.coreData.globalMessages.messages
                    .forEach(message => messageService.message(message, this.$i18n));
            }
            await this.coreDataStore.fetchCoreData();
            this.cartStore.incrementQuantity(this.coreData.numberOfCartItems);
        }

        blurBackground(hasContent: boolean): void {
          this.$nextTick(() => {
            if (hasContent) {
              this.$refs.mainwrap.style.filter = 'blur(3px)';
            } else {
              this.$refs.mainwrap.style.filter = 'blur(0)';
            }
          });
        }

      get showSwitchBackBanner(): boolean {
          return !userPermissionService.isAnonymous() && Boolean(shopCoreDataProvider.data.moduleConfig.LANGUAGE_SWITCHER);
      }

      get showLoginCtaBanner(): boolean {
        return userPermissionService.isAnonymous() && Boolean(shopCoreDataProvider.data.moduleConfig.DIRECT_SALES);
      }

      get globalDefaultLanguage() : string {
        return shopCoreDataProvider.data.globalDefaultLanguage;
      }

      get globalFallbackCountry() : string {
        return shopCoreDataProvider.data.globalFallbackCountry;
      }

      get selectedCountryDiffersFromCompanyCountry(): boolean {
          return i18nService.getUserCountry() !== this.coreData.currentCountry.isocode;
      }

      get privacyPolicyLink(): NavigationItemData {
          return this.navigationService.byItemCode('storePrivacyPolicy');
      }

      get improvedNavigationEnabled(): boolean {
        return Boolean(shopCoreDataProvider.data.moduleConfig.ENABLE_IMPROVED_NAVIGATION);
      }

      get showDirectDebitCtaBanner(): boolean {
	    return !userPermissionService.isAnonymous() && Boolean(shopCoreDataProvider.data.guideCustomerToProvideDirectDebitMandate);
	  }

      get showPurchaseDisabledBanner(): boolean {
        return !userPermissionService.isAnonymous()
          && Boolean(shopCoreDataProvider.data.purchaseDisabledBannerInfo?.showPurchaseDisabledBanner ?? false);
      }
    }

</script>

<style lang="scss">
#vue-app {
  min-height: 100%;
  height: 100%;
  position: relative;
}
</style>

<style scoped lang="scss">
@import "shop/core/constants";

.root {
  position: relative;
  min-height: 100%;

  main {
    width: 100%;
    height: 100%;
    flex-grow: 1;
    flex-shrink: 0;
    flex-basis: auto;

    .footer-container {
      flex-grow: 0;
      flex-shrink: 0;
      flex-basis: auto;
    }

    &.fixed {
      .header {
        position: fixed;
      }

      .content-wrapper {
        margin-top: $header-height;
      }
    }
  }

  .content-wrapper {
    padding-top: $header-height;
    width: 100%;
    flex: 1;
    display: flex;
    flex-direction: column;

    .main-message {
      position: absolute;
      width: 100%;
      z-index: 1;
    }

    .main-container {
      flex: 1;
    }
  }
}

.root :deep(*) {
  box-sizing: border-box;
}
</style>
