import * as Sizes from 'common/testtools/testScreenSizes';
import {countryCookie, languageCookie} from 'common/testtools/testCookies';

describe('language switcher', () => {

    const cookies = [languageCookie('en'), countryCookie('AT')];

    beforeEach(async () => {
        await page.setCookie(...cookies);
    });

    afterEach(async () => {
        await page.deleteCookie(...cookies);
    });

    it('activator looks as expected', async () => {

        await page.goto(global.__DEV_ADDRESS__ + '/aa-shop-core-components-languageswitcher-__ui_tests__-LanguageSwitcher');

        await Sizes.testLargeScreensize(400);
        await Sizes.testSmallScreensize(400);
    });

    it('dialog looks as expected', async () => {
        await page.goto(global.__DEV_ADDRESS__ + '/aa-shop-core-components-languageswitcher-__ui_tests__-LanguageSwitcher');
        await page.waitFor(400);

        const activatorSelector = '[data-id="button-language-switcher-dialog-activator"]';
        await page.waitForSelector(activatorSelector);
        await page.click(activatorSelector);

        await Sizes.testExtraLargeScreensize(1000);
        await Sizes.testLargeScreensize(1000);
        await Sizes.testMediumScreensize(1000);
        await Sizes.testSmallScreensize(1000);
        await Sizes.testExtraSmallScreensize(1000);
    });
});
