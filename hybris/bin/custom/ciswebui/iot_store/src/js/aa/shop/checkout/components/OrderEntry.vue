<template>
  <div class="wrapper">
    <div class="root">
      <div class="product">
        <v-row>
          <v-col cols="12" class="d-flex">
            <div class="image-box">
              <a :href="productSelectionUrl(data)">
                <div ref="logo" class="logo">
                  <div class="rounded-circle" v-bind:style="{'background-image': 'url(' + data.logoUrl + ')'}"></div>
                </div>
              </a>
            </div>

            <v-row no-gutters>

              <v-col cols="12" md="6">
                <div class="product-info ml-4">

                  <h3 class="product-name mr-1" data-id="text-product-name">
                    {{ data.productName }}
                    <CDChip v-if="data.runtime">
                      {{ $t(`shop.runtime.${data.runtime.code}`) }}
                    </CDChip>
                  </h3>

                  <div>{{ $t('quantity') }}: {{ data.quantity }}</div>
                  <div>{{ $t('shop.checkout.byCompany') }} {{ data.companyName }}</div>
                  <div>{{ data.sellerProductId }}</div>

                  <div v-if="!isOwnAppPurchase" data-id="container-item-price">
                    <cart-item-bundle-info :bundle-info="bundleInfo"></cart-item-bundle-info>
                    <span>{{ formatPrice(data.itemPrice) }}{{specialOfferIndicator}} {{ subscriptionDuration }}</span>
                    <special-offer-chip v-if="data.specialOffer"
                                        small
                                        disabled
                                        :data-id="`container-special-price-chip-order-item-${data.appCode}`">
                    </special-offer-chip>
                  </div>

                  <div v-if="!isOwnAppPurchase && futureSubscriptionPrice">
                    <span data-id="text-future-price">
                      {{
                        $t(
                            'shop.checkout.orderEntries.futurePrice',
                            { futurePrice: formatPrice(futureSubscriptionPrice) }
                        )
                      }}
                    </span>
                  </div>

                  <div v-if="isOwnAppPurchase" data-id="container-fee-note">
                    <span> {{ $t('shop.checkout.ownApp.feeNote') }} </span>
                  </div>

                </div>
              </v-col>

              <v-col cols="12" md="6"
                     v-if="!isOwnAppPurchase"
                     data-id="text-total-price"
                     class="d-flex justify-end text-md-right">
                <span class="font-weight-bold ml-4">
                  {{ formatPrice(data.totalPrice) }}{{specialOfferIndicator}}
                </span>
              </v-col>

            </v-row>

          </v-col>
        </v-row>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import util from 'common/util';
import {Component, Prop, Vue, Watch} from 'vue-property-decorator';
import { BundleInfoData, CartItemData, PriceData, ShopCoreData } from 'common/generated-types/types';
import {LicenseType} from 'common/types';
import {CartItemBundleInfo} from 'shop/core/components/cartitembundleinfo';
import { BaseStores, ShopRoute } from 'common/constants';
import { ShopCoreDataProvider } from 'common/provider/coreDataProvider';
import {i18nService} from 'common/services';
import SpecialOfferChip from 'aa/shop/core/components/specialoffer/SpecialOfferChip.vue';

@Component({
  components: {CartItemBundleInfo, SpecialOfferChip}
})
export default class OrderEntry extends Vue {
  @Prop() data!: CartItemData;
  @Prop() isOwnAppPurchase!: boolean;
  @Prop() country!: string;
  @Prop() userGroup!: string;
  LicenseType = LicenseType;

  coreData: ShopCoreData = ShopCoreDataProvider.data;

  $refs!: {
    logo: HTMLElement;
  };

  localeForFormats = '';

  mounted(): void {
    this.localeForFormats = i18nService.getLocaleForFormats();
  }

  get subscriptionDuration(): string {
    return this.data.licenseType === LicenseType.SUBSCRIPTION ? this.$t('shop.cartItem.per12Months').toString() : '';
  }

  get bundleInfo(): BundleInfoData {
    return this.data.bundleInfo;
  }

  get futureSubscriptionPrice(): PriceData | null {
    const productFuturePrices = this.data.productFuturePrices;
    const hasFuturePrices = productFuturePrices?.length;
    return this.isSubscription && hasFuturePrices ? productFuturePrices[0] : null;
  }

  get isSubscription(): boolean {
    return this.data.licenseType === LicenseType.SUBSCRIPTION;
  }

  get isDirectSalesEnabled(): boolean {
    return Boolean(this.coreData.moduleConfig?.DIRECT_SALES) && this.coreData.basestore === BaseStores.AA;
  }

  get isAssociatedThlProduct(): boolean {
    return util.isDACHRegion(this.country) &&
        !(typeof this.data?.addOnThl === 'string' && this.data.addOnThl.trim() !== '') &&
        (typeof this.data?.addOnUg === 'string' && this.data.addOnUg.trim() !== '');
  }

  productSelectionUrl(data: CartItemData): string {
    if(this.isDirectSalesEnabled) {
      return ShopRoute.PRODUCT_SELECTION(data.appCode);
    } else {
      return data.productUrl;
    }
  }

  formatPrice(price: PriceData) : string {
    return `${price.symbol} ${this.$n(i18nService.stringToDefaultNumericFormat(price.value), 'price', this.localeForFormats)}`;
  }

  @Watch('item', {immediate: true})
  onItemChange(): void {
    this.$nextTick(() => {
      util.loadLogoImage([{logoUrl: this.data.logoUrl, logoElement: this.$refs.logo}]);
    });
  }

  get specialOfferIndicator(): string {
      return this.data.specialOffer ? '*' : '';
  }
}
</script>

<style lang="scss" scoped>
@import "shop/cartpage/components/cartlistgridsystem";
</style>
