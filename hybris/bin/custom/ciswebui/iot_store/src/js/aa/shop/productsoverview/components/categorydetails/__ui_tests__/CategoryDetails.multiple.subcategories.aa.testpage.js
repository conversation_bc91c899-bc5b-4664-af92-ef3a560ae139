import 'common/testtools/ui_tests_mock';
import Vue from 'vue';
import VueI18n from 'vue-i18n';
import {i18n} from 'aa/shop/i18n';
import vuetify from 'common/plugins/brands/bosch/vuetify';
import pinia from 'shop/store';
import {useStoreRootStore} from 'aa/shop/store/storeRoot';
import coreDataDefault from 'common/testtools/scenariosstore/aa/coreDataDefault.json';
import TestPageRoot from 'common/testtools/TestPageRoot.vue';
import CategoryDetails from 'aa/shop/productsoverview/components/categorydetails/CategoryDetails.vue';
import cat101Category from 'common/testtools/scenariosstore/aa/cat101Category.json';

Vue.use(VueI18n);
Vue.use(pinia);

window.frontendData.coreData = coreDataDefault;

const storeRootStore = useStoreRootStore();
storeRootStore.coreData = coreDataDefault;

new Vue({
    el: '#vue-app',
    i18n,
    vuetify,
    pinia,
    components: {
        TestPageRoot,
        CategoryDetails
    },
    data: {
        category: cat101Category,
        categoryGroupName: 'Steuergeräte-Diagnose'
    },
    template: `
      <test-page-root>
        <div ref="mainwrap">
          <category-details
              :category="category" :category-group-name="categoryGroupName" >
          </category-details>
        </div>
      </test-page-root>
    `
});
