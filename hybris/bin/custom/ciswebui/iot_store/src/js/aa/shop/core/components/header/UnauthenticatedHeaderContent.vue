<template>
  <v-row align="center"
         class="content-row"
         data-id="container-unauthenticated-header-content">
    <div v-if="isLanguageSwitcherFeatureOn">
      <language-switcher-activator @click="languageSwitcherStore.dialogOpened = true">
      </language-switcher-activator>
    </div>
    <template v-if="getInTouchNav">
      <CDButtonIcon v-if="isMobile"
                    :href="getInTouchNav.url"
                    icon="$letter"
                    data-id="button-header-support">

      </CDButtonIcon>
      <CDButtonTextIcon v-else
                        :href="getInTouchNav.url"
                        class="ml-4"
                        icon="$letter"
                        data-id="button-header-support">
        {{ translateNavigation(getInTouchNav) }}
      </CDButtonTextIcon>
    </template>
    <template v-if="registerNav">
      <CDButton v-if="!isMobile"
                class="ml-4"
                :href="registerNav.url"
                data-id="button-header-register">
        {{ translateNavigation(registerNav) }}
      </CDButton>
    </template>
    <template v-if="loginNav">
      <CDButtonTextIcon v-if="isMobile"
                        :href="loginNav.url"
                        icon="$login"
                        color="primary"
                        id="login-header-link"
                        data-id="button-header-login">
        {{ translateNavigation(loginNav) }}
      </CDButtonTextIcon>
      <CDButton v-else
                :href="loginNav.url"
                class="ml-4"
                color="primary"
                id="login-header-link"
                data-id="button-header-login">
        {{ translateNavigation(loginNav) }}
      </CDButton>
    </template>
  </v-row>
</template>

<script setup lang="ts">
import {computed} from 'vue';
import {navigationService} from 'common/services';
import {NavigationItemData} from 'common/generated-types/types';
import {useI18n} from 'vue-i18n-bridge';
import {shopCoreDataProvider} from 'common/provider';
import {LanguageSwitcherActivator} from 'aa/shop/core/components/languageswitcher';
import {useVuetify} from 'aa/shop/composables/usevuetify';
import {useLanguageSwitcherStore} from 'aa/shop/store/languageSwitcher';

const {t} = useI18n();
const vuetify = useVuetify();
const languageSwitcherStore = useLanguageSwitcherStore();

const isMobile = computed(() => vuetify?.breakpoint.mobile);

const isLanguageSwitcherFeatureOn = computed(() => Boolean(shopCoreDataProvider.data.moduleConfig?.LANGUAGE_SWITCHER));

const loginNav = computed(() => navigationService.byItemCode('storeLogin'));
const registerNav = computed(() => navigationService.byItemCode('storeRegister'));
const getInTouchNav = computed(() => navigationService.byItemCode('getInTouch'));

function translateNavigation(navigationItem: NavigationItemData): string {
  const navTranslationKey = `navigation.items.${navigationItem.itemCode}`;
  return t(navTranslationKey).toString();
}

</script>

<style scoped>
.content-row {
  column-gap: 8px;
}
</style>
