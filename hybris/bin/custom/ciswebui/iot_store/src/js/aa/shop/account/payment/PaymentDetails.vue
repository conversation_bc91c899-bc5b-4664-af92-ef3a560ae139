<template>
  <v-container :fluid="$vuetify.breakpoint.lgAndDown" class="mt-4 mt-lg-16">
    <h1 class="mb-12">{{ $t('shop.payment.paymentDetailsHeader') }}</h1>
    <v-row>
      <v-col cols="12" lg="8" class="mb-8 mb-lg-0">
        <div class="paymentInfo">
          <template v-if="displayPaymentInfos">

            <direct-debit-payment-info-list v-if="displaySepaDDPaymentInfosSection"
                                            :direct-debit-payment-info-list="directDebitPaymentInfos"
                                            @removeSepaDDEntry="removeSepaDDPaymentInfo"
                                            @addSepaMandate="addSepaMandate"
                                            @viewSepaMandate="viewSepaMandate"
                                            :is-sepa-mandate-enabled="isSepaMandateEnabled">
            </direct-debit-payment-info-list>

            <div v-if="invoicePaymentInfos.length > 0"
                 data-id="container-bank-transfer-payment-infos"
                 class="bank-transfer-payment-info mb-16">
              <h2 class="mb-4">{{ $t('shop.payment.bankTransfer') }}</h2>
              <CDBlock class="bank-data mb-8 py-2 pa-md-8"
                       :padding-all="false"
                       :border-bottom="true"
                       v-for="item in invoicePaymentInfos"
                       :key="item.id"
                       data-test="item">

                <payment-method-entry :payment-data="item">
                </payment-method-entry>
              </CDBlock>
            </div>
            <div v-if="ccPaymentInfos.length > 0"
                 data-id="container-cc-payment-infos"
                 class="cc-payment-info mb-16">
              <h2 class="mb-4">{{ $t('shop.payment.creditCard') }}</h2>
              <CDBlock class="card-data py-2 pa-md-8"
                       :padding-all="false"
                       :border-bottom="true"
                       v-for="item in ccPaymentInfos"
                       :key="item.id"
                       data-test="item">
                <v-row class="d-flex align-center">
                  <v-col cols="12" md="2">
                    <payment-method-icons :payment-type="item.cardType"></payment-method-icons>
                  </v-col>
                  <v-col cols="10" sm="8">
                    <v-row class="d-flex justify-space-between">
                      <v-col cols="5" sm="12" md="5" lg="6" class="card-number">
                        {{ item.cardTypeData.name }} {{ item.cardNumber }} &ensp;
                        <CDChip v-if="item.defaultPaymentInfo" data-id="default-payment-chip">
                          {{ $t('preference') }}
                        </CDChip>
                      </v-col>
                      <v-col cols="4" sm="12" md="3" lg="3" class="card-holder text-left break-word">
                        {{ item.accountHolderName }}
                      </v-col>
                      <v-col cols="3" sm="12" md="3" lg="3" class="card-expire">
                        {{ item.expiryMonth }} / {{ item.expiryYear }}
                      </v-col>
                    </v-row>
                  </v-col>
                  <v-col cols="2">
                    <div class="actions d-flex justify-end">
                      <div class="actions-menu mt-n2 actions-for-non-default" v-if="!item.defaultPaymentInfo"
                           data-id="container-actions-menu">
                        <CDFlyoutMenu rounded="b-lg tl-lg tr-0"
                                      menu-icon="$more"
                                      menu-color="black">
                          <CDFlyoutMenuItem :title="setPreferenceText"
                                            class="set-default"
                                            data-id="set-default-payment"
                                            v-on:cdClick="setDefault(item.id)">
                          </CDFlyoutMenuItem>
                          <CDFlyoutMenuItem :title="deletePaymentText"
                                            data-id="delete-non-default-payment"
                                            class="delete ml-auto"
                                            v-on:cdClick="confirmDeletePaymentInfo(item.id),
                                                            findInfos(item.id)">
                          </CDFlyoutMenuItem>
                        </CDFlyoutMenu>
                      </div>
                      <div class="actions-menu mt-n2 actions-for-default" v-else data-id="container-actions-menu">
                        <CDFlyoutMenu rounded="b-lg tl-lg tr-0"
                                      menu-icon="$more"
                                      menu-color="black">
                          <CDFlyoutMenuItem :title="removePreferenceText"
                                            class="set-default"
                                            data-id="set-default-payment"
                                            v-on:cdClick="removeDefault(item.id)">
                          </CDFlyoutMenuItem>
                          <CDFlyoutMenuItem :title="deletePaymentText"
                                            data-id="delete-default-payment"
                                            class="delete ml-auto"
                                            v-on:cdClick="confirmDeletePaymentInfo(item.id), findInfos(item.id)">
                          </CDFlyoutMenuItem>
                        </CDFlyoutMenu>
                      </div>
                    </div>
                  </v-col>
                </v-row>
              </CDBlock>
            </div>
          </template>
          <template v-else>
            <h2>{{ $t('shop.payment.noPaymentDetails') }}</h2>
            <p>{{ $t('shop.payment.noPaymentDetailsInfo') }}</p>
          </template>
        </div>
      </v-col>
      <v-col cols="12" lg="3" offset-lg="1">
        <div class="billingInfo" v-if="ccPaymentInfos.length > 0 || invoicePaymentInfos.length > 0">
          <h2 class="mb-4">{{ $t('shop.payment.billingAddress') }}</h2>
          <i18n path="shop.payment.billingAddressInfo" tag="p" class="grey--text text--darken-2">
            <template v-slot:url>
              <a :href="`${supportUrl}`" target="_blank">{{ $t("shop.payment.contactCustomerSupport") }}</a>
            </template>
          </i18n>
          <v-list dense v-if="billingAddress" class="ma-0 pa-0">
            <v-list-item>
              <v-list-item-content>
                <v-list-item-title>{{ billingAddress.companyName }}</v-list-item-title>
              </v-list-item-content>
            </v-list-item>
            <v-list-item>
              <v-list-item-content>
                <v-list-item-title>{{ billingAddress.line1 }} {{ billingAddress.line2 }}</v-list-item-title>
              </v-list-item-content>
            </v-list-item>
            <v-list-item>
              <v-list-item-content>
                <v-list-item-title>{{ billingAddress.postalCode }} {{ billingAddress.town }}</v-list-item-title>
              </v-list-item-content>
            </v-list-item>
            <v-list-item>
              <v-list-item-content>
                <v-list-item-title>{{ $t(`country.${billingAddress.country.isocode}`) }}</v-list-item-title>
              </v-list-item-content>
            </v-list-item>
          </v-list>
        </div>
      </v-col>
    </v-row>

    <v-dialog width="500" v-model="showConfirmationPopup">
      <v-card class="pa-8">
        <v-row>
          <v-col cols="12">
            <h3>{{ $t('shop.payment.deletePaymentMethod') }}</h3>
          </v-col>
        </v-row>
        <v-row>
          <v-col cols="12">
            <v-list v-if="toDeletePaymentMethodInfos" dense class="mb-4 ma-0 pa-0">
              <v-list-item>
                <v-list-item-content>
                  <v-list-item-title>
                    {{ toDeletePaymentMethodInfos.cardTypeData.name }} {{ toDeletePaymentMethodInfos.cardNumber }}
                  </v-list-item-title>
                </v-list-item-content>
              </v-list-item>
              <v-list-item>
                <v-list-item-content>
                  <v-list-item-title>
                    {{ toDeletePaymentMethodInfos.accountHolderName }}
                  </v-list-item-title>
                </v-list-item-content>
              </v-list-item>
              <v-list-item>
                <v-list-item-content>
                  <v-list-item-title>
                    {{ toDeletePaymentMethodInfos.expiryMonth }} / {{ toDeletePaymentMethodInfos.expiryYear }}
                  </v-list-item-title>
                </v-list-item-content>
              </v-list-item>
            </v-list>
            <p>{{ $t('shop.payment.deletePaymentMethodInfo') }}</p>
          </v-col>
        </v-row>
        <v-row>
          <v-col cols="12" class="text-right">
            <CDButton @click="showConfirmationPopup = false" class="mr-2">Cancel</CDButton>
            <CDButton color="primary" @click="onConfirm">{{ $t('delete') }}</CDButton>
          </v-col>
        </v-row>
      </v-card>
    </v-dialog>

    <add-sepa-mandate-dialog
        :fullscreen="$vuetify.breakpoint.mdAndDown"
        v-if="showAddSepaMandateDialog"
        v-on:cancel="onCancelMandate"
        v-on:confirm="onConfirmMandate"
        :creditor-identifier="'DE12ZZZ0000010300'"
        :mandate-reference="currentMandateReference"
        :creditor="'Robert Bosch GmbH'"
        :type-of-payment="'Recurring payment'"
        :core-data="coreData"
    ></add-sepa-mandate-dialog>

    <view-sepa-mandate-dialog
        :fullscreen="$vuetify.breakpoint.mdAndDown"
        v-if="showSepaMandateDialog"
        v-on:cancel="onCancelMandate"
        v-on:confirm="onConfirmMandate"
        :sepa-direct-debit="selectedDirectDebitPaymentInfo"
        :mandate-reference="selectedMandateReference"
        :creditor="'Robert Bosch GmbH'"
        :type-of-payment="'Recurring payment'"
        :creditor-identifier="'DE12ZZZ0000010299'">
    </view-sepa-mandate-dialog>

    <pending-product-info-draft-poller v-if="pageData.pendingPaymentInfoDrafts?.length > 0"
                                       :pending-payment-info-drafts="pageData.pendingPaymentInfoDrafts">
    </pending-product-info-draft-poller>
  </v-container>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import {PaymentMethodIcons, Tooltip} from 'common/components';
import {PaymentMethodEntry} from 'shop/checkout/components';
import {pageSpinner} from 'common/components/spinner';
import {paymentResource} from 'shop/resources';
import {companyPermissionService, messageService} from 'common/services';
import {
  commercefacades,
  CreditCardPaymentInfoData,
  ErrorMessageData,
  InvoicePaymentInfoData,
  PaymentMethodType,
  PaymentSettingsData,
  SepaMandatePaymentInfoData,
  ShopCoreData,
} from 'common/generated-types/types';
import {DirectDebitPaymentInfoList, PendingProductInfoDraftPoller} from 'shop/account/payment/components';
import {AddSepaMandateDialog, ViewSepaMandateDialog} from 'aa/shop/payment/components';

@Component({
  components: {
    PendingProductInfoDraftPoller,
    DirectDebitPaymentInfoList,
    PaymentMethodIcons,
    Tooltip,
    PaymentMethodEntry,
    ViewSepaMandateDialog,
    AddSepaMandateDialog
  }
})
export default class PaymentDetails extends Vue {
  @Prop() pageData!: PaymentSettingsData;
  @Prop() coreData!: ShopCoreData;

  showConfirmationPopup = false;
  showAddSepaMandateDialog = false;
  showSepaMandateDialog = false;
  toDeletePaymentMethodId = '';
  toDeletePaymentMethodInfos: CreditCardPaymentInfoData = null!;
  mobileMenusOpen = this.pageData.ccPaymentInfos.reduce((acc: { [id: string]: boolean }, e) => {
    acc[e.id] = false;
    return acc;
  }, {});
  ccPaymentInfos = this.pageData.ccPaymentInfos;
  invoicePaymentInfos = this.pageData.invoicePaymentInfos;
  directDebitPaymentInfos = this.pageData.directDebitPaymentInfos;

  selectedDirectDebitPaymentInfo: SepaMandatePaymentInfoData | null = null;
  selectedMandateReference = '';
  currentMandateReference = '';

  created(): void {
    this.setPageTitle();
    document.body.addEventListener('click', () => this.closeMenus());
    this.distinctInvoicePaymentMethods();
  }

  private setPageTitle(): void {
    document.title = this.$t('shop.payment.pageTitle') as string + this.$t('navigation.storePageTitle') as string;
  }

  private closeMenus(skipId: string | null = null): void {
    Object.keys(this.mobileMenusOpen).forEach(k => {
      if (skipId === null || k !== skipId) {
        this.mobileMenusOpen[k] = false;
      }
    });
  }

  private distinctInvoicePaymentMethods() {
    const filterPaymentMethod = (paymentMethod: PaymentMethodType) => {
      return (uniquePaymentInfos: InvoicePaymentInfoData[], paymentInfo: InvoicePaymentInfoData) => {
        if (paymentInfo.paymentMethod === paymentMethod) {
          if (!uniquePaymentInfos.some((p: InvoicePaymentInfoData) => p.paymentMethod === paymentMethod)) {
            uniquePaymentInfos.push(paymentInfo);
          }
        }
        return uniquePaymentInfos;
      };
    };

    const achPaymentInfos = this.invoicePaymentInfos.reduce(filterPaymentMethod(PaymentMethodType.ACH_INTERNATIONAL), []);
    const sepaPaymentInfos = this.invoicePaymentInfos.reduce(filterPaymentMethod(PaymentMethodType.SEPA_CREDIT), []);

    this.invoicePaymentInfos = [...achPaymentInfos, ...sepaPaymentInfos];
  }

  get supportUrl(): string {
    return this.coreData.supportUrl;
  }

  get removePreferenceText(): string {
    return this.$t('shop.payment.removePreference') as string;
  }

  get setPreferenceText(): string {
    return this.$t('shop.payment.setPreference') as string;
  }

  get deletePaymentText(): string {
    return this.$t('shop.payment.delete') as string;
  }

  get billingAddress(): commercefacades.AddressData | null {
    if (this.ccPaymentInfos.length) {
      const defaultPayment = this.ccPaymentInfos.filter(x => x.defaultPaymentInfo)[0];
      if (defaultPayment) {
        return defaultPayment.billingAddress;
      }
    }
    return null;
  }

  get displayPaymentInfos(): boolean {
    return this.ccPaymentInfos.length > 0
        || this.invoicePaymentInfos.length > 0
        || this.displaySepaDDPaymentInfosSection;
  }

  get displaySepaDDPaymentInfosSection(): boolean {
    return this.directDebitPaymentInfos.length > 0 || this.allowCreationOfDDPaymentInfo;
  }

  get allowCreationOfDDPaymentInfo(): boolean {
    return companyPermissionService.allowCurrentCompanyCreationOfSepaDDPaymentInfo();
  }

  get isSepaMandateEnabled(): boolean {
    return Boolean(this.coreData.moduleConfig.ENABLE_SEPA_MANDATE);
  }

  findInfos(paymentMethodId: string): void {
    this.toDeletePaymentMethodInfos = this.ccPaymentInfos.filter(x => x.id === paymentMethodId)[0];
  }

  confirmDeletePaymentInfo(paymentMethodId: string): void {
    this.toDeletePaymentMethodId = paymentMethodId;
    this.showConfirmationPopup = true;
  }

  onConfirm(): void {
    pageSpinner.start();
    messageService.clearAll();
    paymentResource.deletePaymentMethod(this.toDeletePaymentMethodId)
        .then(() => {
          this.ccPaymentInfos = this.ccPaymentInfos.filter(p => p.id !== this.toDeletePaymentMethodId);
          messageService.success(this.$t('shop.payment.removed') as string);
        })
        .catch((error) => {
          const response = error.response;
          if (response && response.data) {
            const data = response.data as ErrorMessageData;
            messageService.error(data.message);
          } else {
            messageService.error(this.$t('backendError') as string);
          }
        })
        .finally(() => {
          pageSpinner.stop();
          this.showConfirmationPopup = false;
          this.toDeletePaymentMethodId = '';
        });
  }

  setDefault(paymentInfoId: string): void {
    pageSpinner.start();
    messageService.clearAll();
    paymentResource.setDefaultPaymentMethod(paymentInfoId)
        .then(() => {
          this.ccPaymentInfos = this.ccPaymentInfos.map((paymentInfo: CreditCardPaymentInfoData) => {
            paymentInfo.defaultPaymentInfo = paymentInfo.id === paymentInfoId;
            return paymentInfo;
          });
          messageService.success(this.$t('shop.payment.setPreferenceSuccess') as string);
        })
        .catch((error) => {
          const response = error.response;
          if (response && response.data) {
            const data = response.data as ErrorMessageData;
            messageService.error(data.message);
          } else {
            messageService.error(this.$t('backendError') as string);
          }
        })
        .finally(() => {
          pageSpinner.stop();
        });
  }

  removeDefault(paymentInfoId: string): void {
    pageSpinner.start();
    messageService.clearAll();
    paymentResource.removeDefaultPaymentMethod(paymentInfoId)
        .then(() => {
          this.ccPaymentInfos = this.ccPaymentInfos.map((paymentInfo: CreditCardPaymentInfoData) => {
            paymentInfo.defaultPaymentInfo = false;
            return paymentInfo;
          });
          messageService.success(this.$t('shop.payment.removePreferenceSuccess') as string);
        })
        .catch((error) => {
          const response = error.response;
          if (response && response.data) {
            const data = response.data as ErrorMessageData;
            messageService.error(data.message);
          } else {
            messageService.error(this.$t('backendError') as string);
          }
        })
        .finally(() => {
          pageSpinner.stop();
        });
  }

  removeSepaDDPaymentInfo(paymentInfoId: string) {
    this.directDebitPaymentInfos = this.directDebitPaymentInfos.filter(p => p.id !== paymentInfoId);
  }

  addSepaMandate(): void {
    if (this.isSepaMandateEnabled) {
      // Clear any previous mandate reference
      this.currentMandateReference = '';
      this.showAddSepaMandateDialog = true;
    }
  }

  viewSepaMandate(paymentInfoId: string): void {
    if (this.isSepaMandateEnabled) {
      const found = this.directDebitPaymentInfos.find(p => p.id === paymentInfoId);
      if (found) {
        this.selectedDirectDebitPaymentInfo = found;
        this.selectedMandateReference = found.mandateReference || '';
      } else {
        this.selectedDirectDebitPaymentInfo = null;
        this.selectedMandateReference = '';
      }
      this.showSepaMandateDialog = true;
    }
  }

  onCancelMandate(): void {
    this.showAddSepaMandateDialog = false;
    this.showSepaMandateDialog = false;
    this.selectedDirectDebitPaymentInfo = null;
    this.selectedMandateReference = '';
    this.currentMandateReference = '';
  }

  onConfirmMandate(): void {
    this.showAddSepaMandateDialog = false;
    this.showSepaMandateDialog = false;
    this.selectedDirectDebitPaymentInfo = null;
    this.selectedMandateReference = '';
    this.currentMandateReference = '';

    // Refresh the page or reload payment data to show the new mandate
    // You might want to call a method to refresh the payment info list
    this.refreshPaymentData();
  }

  private async refreshPaymentData(): Promise<void> {
    try {
      messageService.success(this.$t('shop.payment.success') as string);
    } catch (error) {
      console.error('Error refreshing payment data:', error);
    }
  }
}
</script>