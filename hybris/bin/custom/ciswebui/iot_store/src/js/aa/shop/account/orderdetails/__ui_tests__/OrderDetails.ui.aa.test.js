import {acceptCookieBanner, createInlineUiTestPage, resetMouseCoords} from "common/testtools/inlineTestPageCreator";
import {testMap} from 'common/testtools/scenariosstore';
import orderDetailsWithInvoicesData from 'common/testtools/scenariosstore/aa/orderDetailsWithInvoicesData.json';
import * as Sizes from 'common/testtools/testScreenSizes';
import {cloneDeep} from 'lodash';
import {LicenseType} from 'common/types';
import {PaymentMethodType} from 'common/generated-types/types';
import {cookies_en_AT, coreDataRestHandler} from "common/testtools/testCookies";

describe("order details page", () => {

    let data;

    beforeEach(async () => {
        data = cloneDeep(testMap.OrderDetailsAA);
        await page.setRequestInterception(true);
        page.on('request', coreDataRestHandler);
        await page.setCookie(...cookies_en_AT);
    });

    it("should look as expected for paid apps", async () => {
        await page.setViewport({width: 1199, height: 750});
        await createInlineUiTestPage(data);
        await acceptCookieBanner();
        await page.waitFor(2000);
        expect(await page.screenshot()).toMatchImageSnapshot('');
    });

    it("should not break the layout for long app names, desktop and mobile", async () => {
        await page.setViewport({width: 1199, height: 750});
        await createInlineUiTestPage(data);
        await acceptCookieBanner();
        await page.waitFor(2000);
        expect(await page.screenshot()).toMatchImageSnapshot('');

        await Sizes.testSmallScreensize(2000);
    });

    it("should hide payment method", async () => {
        data.pageData.paymentMethod = PaymentMethodType.ZERO;
        await page.setViewport({width: 1199, height: 750});
        await createInlineUiTestPage(data);
        await acceptCookieBanner();
        await page.waitFor(2000);
        expect(await page.screenshot()).toMatchImageSnapshot('');
    });

    it("subscription order", async () => {
        data.pageData.entries[0].licenseType = LicenseType.SUBSCRIPTION;
        data.pageData.entries[0].licenseName = 'Subscription';
        await page.setViewport({width: 1200, height: 750});
        await createInlineUiTestPage(data);
        await acceptCookieBanner();
        await page.waitFor(2000);
        expect(await page.screenshot()).toMatchImageSnapshot('');
    });
});

describe("order details page with invoices", () => {

    let data;

    beforeEach(async () => {
        data = cloneDeep({
            ...testMap.OrderDetailsAA,
            pageData: orderDetailsWithInvoicesData
        });
    });

    it("should look as expected", async () => {
        await createInlineUiTestPage(data);
        await acceptCookieBanner();
        await resetMouseCoords();

        await Sizes.testExtraSmallScreensize(2500);
        await Sizes.testSmallScreensize(2500);
        await Sizes.testMediumScreensize(2500);
        await Sizes.testLargeScreensize(2500);
        await Sizes.testExtraLargeScreensize(2500);
    });

    it("should look as expected when no invoices", async () => {
        data.pageData.invoices = [];
        await createInlineUiTestPage(data);
        await acceptCookieBanner();

        await Sizes.testExtraSmallScreensize(2500);
        await Sizes.testSmallScreensize(2500);
        await Sizes.testMediumScreensize(2500);
        await Sizes.testLargeScreensize(2500);
        await Sizes.testExtraLargeScreensize(2500);
    });
});

describe("order details with failed payment", () => {

    let data;

    beforeEach(() => {
        data = cloneDeep(testMap.OrderDetailsAA);
    });

    it("should look as expected", async () => {
        data.pageData.failedPayment = {
            cardNumber: "***5556",
            updateUrl: "google.com"
        }
        await createInlineUiTestPage(data);
        await acceptCookieBanner();
        await resetMouseCoords();

        await Sizes.testExtraSmallScreensize(2500);
        await Sizes.testSmallScreensize(2500);
        await Sizes.testMediumScreensize(2500);
        await Sizes.testLargeScreensize(2500);
        await Sizes.testExtraLargeScreensize(2500);
    });
});

describe("order details with account under review", () => {

    let data;

    beforeEach(() => {
        data = cloneDeep(testMap.OrderDetailsAA);
    });


    it("with awaiting_license_activation status should look as expected", async () => {
        data.pageData.invoices[0].invoiceStatus = 'ACTIVATION_PENDING'
        data.coreData.userCompanyUnderReview = false;
        await createInlineUiTestPage(data);
        await acceptCookieBanner();
        await resetMouseCoords();

        await Sizes.testExtraLargeScreensize(2500);
        await Sizes.testLargeScreensize(2500);
        await Sizes.testMediumScreensize(2500);
        await Sizes.testSmallScreensize(2500);
        await Sizes.testExtraSmallScreensize(2500);
    });
});

