import {acceptCookieBanner, createInlineUiTestPage} from 'common/testtools/inlineTestPageCreator';
import {testMap} from 'common/testtools/scenariosstore';
import {cloneDeep} from "lodash";
import * as Sizes from "../../../../common/testtools/testScreenSizes";

describe("PendingCheckoutConfirmation test", () => {
    it("responsiveness test", async () => {
        const data = cloneDeep(testMap.PendingCheckoutConfirmationAA);
        await createInlineUiTestPage(data);
        await acceptCookieBanner();
        await page.waitFor(1000);

        await Sizes.testAllScreenSizes(1400);
    });
});

describe("CheckoutConfirmation test with account under review", () => {
    it("responsiveness test", async () => {
        const data = cloneDeep(testMap.PendingCheckoutConfirmationAA);
        data.coreData.userCompanyUnderReview = true;
        await createInlineUiTestPage(data);
        await acceptCookieBanner();
        await page.waitFor(1000);

        await Sizes.testAllScreenSizes(1400);
    });
});

describe("CheckoutConfirmation test with infobox", () => {
    it("responsiveness test", async () => {
        const data = cloneDeep(testMap.PendingCheckoutConfirmationAA);
        data.coreData.moduleConfig.ORDER_CONFIRMATION_INFOBOX = true;
        await createInlineUiTestPage(data);
        await acceptCookieBanner();
        await page.waitFor(1000);

        await Sizes.testAllScreenSizes(1400);
    });

    it("responsiveness test with company under review note is visible", async () => {
        const data = cloneDeep(testMap.PendingCheckoutConfirmationAA);
        data.coreData.userCompanyUnderReview=true;
        data.coreData.moduleConfig.ORDER_CONFIRMATION_INFOBOX = true;
        await createInlineUiTestPage(data);
        await acceptCookieBanner();
        await page.waitFor(1000);

        await Sizes.testAllScreenSizes(1400);
    });
});
