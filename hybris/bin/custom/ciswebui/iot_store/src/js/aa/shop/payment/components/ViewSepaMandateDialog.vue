<template>
  <popup-dialog
      v-bind="$attrs"
      v-on="$attrs"
      class="sepa-mandate-dialog ma-0"
      data-id="view-sepa-mandatepopup-dialog"
      :header-close="true"
      :close-button="{ text: t('close') }"
      @close="closeDialog"
  >
    <template #header>
      {{ t('shop.payment.sepaMandate.header') }}
    </template>

    <v-container class="mt-4 px-0">
      <v-progress-circular
          v-if="isLoading"
          indeterminate
          color="primary"
          class="d-flex mx-auto my-8"
      ></v-progress-circular>

      <div v-else-if="mandateData">
        <CDBlock :border-bottom="true"
                 :padding-all="false"
                 :padding-bottom="true"
                 class="mb-8 pb-4"
        >
          <p class="mb-4">
            {{ t('shop.payment.sepaMandate.description1', {
            paymentProvider: mandateData?.paymentProvider || 'Adyen',
            beneficiary: creditor
          }) }}
          </p>
          <p class="mb-4">
            {{ t('shop.payment.sepaMandate.description2') }}
          </p>
          <p class="mb-4">
            {{ t('shop.payment.sepaMandate.description3') }}
          </p>
        </CDBlock>

        <CDBlock :border-bottom="true"
                 :padding-all="false"
                 :padding-bottom="true"
                 class="mb-8 pb-4"
        >
          <v-row>
            <v-col cols="12">
              <div class="mb-4">
                <h5 class="d-inline-block mr-2">
                  {{ t('shop.payment.sepaMandate.accountHolderName') }}:
                </h5>
                <span>{{ mandateData?.accountHolderName || '-' }}</span>
              </div>
              <div class="mb-4">
                <h5 class="d-inline-block mr-2">
                  {{ t('shop.payment.sepaMandate.iban') }}:
                </h5>
                <span>{{ maskedIban }}</span>
              </div>
              <div class="mb-4">
                <h5 class="d-inline-block mr-2">
                  {{ t('shop.payment.sepaMandate.mandateReference') }}:
                </h5>
                <span>{{ mandateData?.mandateReference || '-' }}</span>
              </div>
              <v-row>
                <v-col cols="7">
                  <div class="mb-4">
                    <h5 class="d-inline-block mr-2">
                      {{ t('shop.payment.sepaMandate.typeOfPayment') }}:
                    </h5>
                    <span>{{ typeOfPayment }}</span>
                  </div>
                </v-col>
                <v-col cols="5">
                  <div class="mb-4">
                    <h5 class="d-inline-block mr-2">
                      {{ t('date') }}:
                    </h5>
                    <span v-if="mandateData?.signatureDate">
                      {{ formatDate(mandateData.signatureDate) }}
                    </span>
                    <span v-else>-</span>
                  </div>
                </v-col>
              </v-row>
              <div class="mb-4">
                <h5 class="d-inline-block mr-2">
                  Status:
                </h5>
                <span :class="getStatusClass(mandateData?.status)">
                  {{ getStatusText(mandateData?.status) }}
                </span>
              </div>
            </v-col>
          </v-row>
        </CDBlock>

        <CDBlock :border-none="true"
                 :padding-all="false"
                 :padding-bottom="false"
                 class="mb-8"
        >
          <v-row>
            <v-col cols="12">
              <div class="mb-4">
                <h5 class="d-inline-block mr-2">
                  {{ t('shop.payment.sepaMandate.creditor') }}:
                </h5>
                <span>{{ creditor }}</span>
              </div>
              <div class="mb-4">
                <h5 class="d-inline-block mr-2">
                  {{ t('shop.payment.sepaMandate.creditorIdentifier') }}:
                </h5>
                <span>{{ creditorIdentifier }}</span>
              </div>
            </v-col>
          </v-row>
        </CDBlock>
      </div>

      <div v-else-if="error" class="text-center my-8">
        <v-icon large color="error" class="mb-4">$error</v-icon>
        <p class="error--text">{{ error }}</p>
      </div>
    </v-container>
  </popup-dialog>
</template>

<script setup lang="ts">
import { computed, ref, onMounted } from 'vue';
import { useI18n } from 'vue-i18n-bridge';
import { paymentResource } from 'shop/resources';
import { PopupDialog } from 'common/components/popups';
import { SepaMandatePaymentInfoData } from 'common/generated-types/types';

const props = defineProps<{
  sepaDirectDebit?: SepaMandatePaymentInfoData;
  mandateReference?: string;
  creditor: string;
  typeOfPayment: string;
  creditorIdentifier: string;
}>();

const emit = defineEmits<{
  (e: 'cancel'): void;
}>();

const { t, d } = useI18n();

const isLoading = ref(false);
const mandateData = ref<SepaMandatePaymentInfoData | null>(null);
const error = ref('');

function maskIban(iban?: string): string {
  if (!iban || iban?.length <= 4) return iban || '';
  const visible = 4;
  const hiddenPart = '*'.repeat(iban?.length - visible);
  const visiblePart = iban?.slice(-visible);
  return hiddenPart + visiblePart;
}

const maskedIban = computed(() => maskIban(mandateData.value?.iban));

const formatDate = (dateString: string) => {
  try {
    return d(new Date(dateString), 'short');
  } catch (error) {
    return dateString;
  }
};

const getStatusClass = (status: string) => {
  switch (status?.toLowerCase()) {
    case 'active':
    case 'finalized':
      return 'success--text';
    case 'draft':
      return 'warning--text';
    case 'cancelled':
    case 'expired':
      return 'error--text';
    default:
      return 'grey--text';
  }
};


const getStatusText = (status: string) => {
  if (!status) return '-';
  // Try to get localized status text, fallback to the status itself
  const statusKey = `shop.payment.status.${status.toLowerCase()}`;
  const translated = t(statusKey);
  return translated === statusKey ? status : translated;
};

const loadMandateData = async () => {
  isLoading.value = true;
  error.value = '';

  try {
    // If we have sepaDirectDebit prop data, use it directly
    if (props.sepaDirectDebit) {
      mandateData.value = props.sepaDirectDebit;
      return;
    }

    // If we have mandateReference, fetch the data from backend
    if (props.mandateReference) {
      const response = await paymentResource.getSepaMandateByReference(props.mandateReference, false);
      mandateData.value = response.data;
      return;
    }

    // If neither is provided, show error
    throw new Error('No mandate data or reference provided');

  } catch (fetchError: unknown) {
    console.error('Error loading mandate data:', fetchError);

    const errorResponse = fetchError as { response?: { status?: number; data?: { message?: string } } };

    if (errorResponse.response?.status === 404) {
      error.value = t('shop.payment.sepaMandate.error.notFound').toString();
    } else if (errorResponse.response?.data?.message) {
      error.value = errorResponse.response.data.message;
    } else {
      error.value = t('shop.payment.sepaMandate.error.backendError').toString();
    }
  } finally {
    isLoading.value = false;
  }
};

const closeDialog = () => {
  emit('cancel');
};

onMounted(() => {
  loadMandateData();
});
</script>

<style scoped>
p,
span,
h5 {
  color: var(--v-grey-darken3);
}
.success--text {
  color: var(--v-success-base) !important;
}
.warning--text {
  color: var(--v-warning-base) !important;
}
.error--text {
  color: var(--v-error-base) !important;
}
</style>