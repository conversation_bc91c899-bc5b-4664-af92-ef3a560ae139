import VueRouter from 'vue-router';
import {ShopRoute} from 'common/constants';
import {StoreDataProvider, type StoreFrontendData} from 'shop/resources';

import Products from 'shop/products/Products.vue';
import ProductDetails from 'aa/shop/productdetails/ProductDetails.vue';
import CheckoutPaymentPage from 'aa/shop/checkout/CheckoutPaymentPage.vue';
import OrderHistory from 'aa/shop/account/orderhistory/OrderHistory.vue';
import OrderDetails from 'aa/shop/account/orderdetails/OrderDetails.vue';
import PaymentDetails from 'aa/shop/account/payment/PaymentDetails.vue';
import DelayedCheckoutConfirmation from 'aa/shop/checkoutconfirmation/DelayedCheckoutConfirmation.vue';
import PendingCheckoutConfirmation from 'aa/shop/checkoutconfirmation/PendingCheckoutConfirmation.vue';
import CartPage from 'aa/shop/cartpage/CartPage.vue';
import UpdatePaymentMethodPage from 'shop/checkout/UpdatePaymentMethodPage.vue';
import ProductsOverview from 'aa/shop/productsoverview/ProductsOverview.vue';
import ProductSelection from 'aa/shop/productselection/ProductSelection.vue';
import CheckoutConfirmation from "aa/shop/checkoutconfirmation/CheckoutConfirmation.vue";

const props = (): StoreFrontendData => ({ ...StoreDataProvider.getMergedFrontendData() });

const isDirectSalesEnabled = Boolean(props().coreData.moduleConfig?.DIRECT_SALES);

const router = new VueRouter({
    mode: 'history',
    routes: [{
        path: ShopRoute.PRODUCTS_OVERVIEW,
        name: ShopRoute.PRODUCTS_OVERVIEW_NAME,
        component: ProductsOverview,
        props,
        meta: {
            navbarHideOnScroll: true
        },
        ...(isDirectSalesEnabled && {alias: ShopRoute.HOME})
    }, {
        path: ShopRoute.PRODUCTS_SEARCH,
        name: ShopRoute.PRODUCTS_SEARCH_NAME,
        component: Products,
        props,
        meta: {
            appsActive: true
        },
        ...(!isDirectSalesEnabled && {alias: ShopRoute.HOME})
    }, {
        path: ShopRoute.CART,
        name: ShopRoute.CART_NAME,
        component: CartPage,
        props
    }, {
        path: ShopRoute.PRODUCT_DETAIL,
        name: ShopRoute.PRODUCT_DETAIL_NAME,
        component: ProductDetails,
        props
    }, {
        path: ShopRoute.PRODUCT_SELECTION(),
        name: ShopRoute.PRODUCT_SELECTION_NAME,
        component: ProductSelection,
        props: true
    }, {
        path: ShopRoute.CHECKOUT_PAYMENT,
        component: CheckoutPaymentPage,
        props,
        meta: {
            useMinimalHeader: true
        }
    }, {
        path: ShopRoute.ORDER_HISTORY,
        name: ShopRoute.ORDER_HISTORY_NAME,
        component: OrderHistory,
        props
    }, {
        path: ShopRoute.UPDATE_PAYMENT_METHOD,
        name: ShopRoute.UPDATE_PAYMENT_METHOD_NAME,
        component: UpdatePaymentMethodPage,
        props
    }, {
        path: ShopRoute.ORDER_DETAILS,
        name: ShopRoute.ORDER_DETAILS_NAME,
        component: OrderDetails,
        props
    }, {
        path: ShopRoute.PAYMENT_DETAILS,
        name: ShopRoute.PAYMENT_DETAILS_NAME,
        component: PaymentDetails,
        props
    }, {
        path: ShopRoute.CHECKOUT_CONFIRMATION,
        name: ShopRoute.CHECKOUT_CONFIRMATION_NAME,
        component: CheckoutConfirmation,
        props
    }, {
        path: ShopRoute.PENDING_CHECKOUT_CONFIRMATION,
        name: ShopRoute.PENDING_CHECKOUT_CONFIRMATION_NAME,
        component: PendingCheckoutConfirmation,
        props
    }, {
        path: ShopRoute.DELAYED_CHECKOUT_CONFIRMATION,
        name: ShopRoute.DELAYED_CHECKOUT_CONFIRMATION_NAME,
        component: DelayedCheckoutConfirmation,
        props
    }]
});

export default router;
