# This project contains IP governed by two licenses.

This project originated from the Open Source contributions of `<PERSON><PERSON> (<PERSON>) You` & `the Vue.js community`, which is licensed under the `MIT License`. As such, all unmodified code and materials are subject to the terms, conditions, and any other stipulation associated with Vue 2's MIT License.

All other materials, including new files or modifications to existing files (Updates) are original works of HeroDevs, Inc. and licensed under the HeroDevs NES License.

Access to this project, via any medium, is restricted to parties explicitly authorized by HeroDevs, Inc. Contact [<EMAIL>](mailto:<EMAIL>) to learn more.

## HeroDevs License

The HeroDevs NES License

Copyright © 2023-present, HeroDevs, Inc. All Rights Reserved.

Access to this source code is restricted, regardless of medium, to parties authorized by a valid commercial license. Use of this source code, as well as any other associated asset, is also subject to the terms and conditions of the Never-Ending Support program. For more information, visit [https://docs.herodevs.com](https://docs.herodevs.com).

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

---

# Vue.js

The MIT License (MIT)

Copyright (c) 2013-present, Yuxi (Evan) You

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in
all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
THE SOFTWARE.
