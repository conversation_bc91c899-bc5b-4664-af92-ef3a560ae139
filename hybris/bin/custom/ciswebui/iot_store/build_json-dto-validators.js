const path = require("path");
const fs = require("fs");
const TJS = require("typescript-json-schema");
const Ajv = require('ajv');
const ajv = new Ajv({sourceCode: true});
const pack = require('ajv-pack');

const compilerOptions = {esModuleInterop:true};
const GENERATED_TYPES = "src/js/common/generated-types/";

console.log("Generating JSON validator functions...");
const program = TJS.getProgramFromFiles([path.resolve(GENERATED_TYPES + "types.ts")], compilerOptions);
const generator = TJS.buildGenerator(program, {required: true});
const symbols = generator.getMainFileSymbols(program, [path.resolve(GENERATED_TYPES + "types.ts")]);


let errors = [];
for (let idx = 0; idx < symbols.length; ++idx){
    const symbol = symbols[idx];
    console.log(`[${idx + 1}/${symbols.length}] Generating validator for symbol: ${symbol}`);

    let schema;
    try {
        schema = generator.getSchemaForSymbol(symbol);
    }
    catch {
        console.log('Failed!');
        errors.push(symbol);
        continue;
    }

    fs.writeFileSync(GENERATED_TYPES + symbol + ".json", JSON.stringify(schema));

    let validate = ajv.compile(schema);
    let moduleCode = pack(ajv, validate);
    fs.writeFileSync(GENERATED_TYPES + symbol + ".js", moduleCode);
}

if(errors.length > 0) {
    console.log("The following type(s) could not be transpiled to json schema:");
    for(const symbol of errors){
        console.log(symbol);
    }
}







