import os
import shutil
import signal
import subprocess
import sys
from functools import wraps

# Assumptions:
# - module functions can be used from ciswebui folder or iot_store folder
# - within the public module function, we are ALWAYS in ciswebui folder (assured by decorator)

NVM_DIR = os.environ["HOME"] + '/.nvm'
NVM_VERSION = 'v0.39.0'
JS_DIST_DIR = 'iot_store/dist/js'
PACKAGE_JSON = 'iot_store/package.json'
YARN_LOCK = 'iot_store/yarn.lock'
WS_ENDPOINT_FILE = 'iot_store/testing/test_output/wsEndpoint-docker'
DOCKER_IMAGE_NAME = 'docker.sastdev.net/sast/ci/puppeteer-chrome'

CONTENT_ROOT_FILE = \
    """# Auto-generated property file. This file gets updated by the frontend build process.

    include=custom.properties
    include=generated.properties
    """

CONTENT_CUSTOM_FILE = '# Place extension specific properties here.\n'


def assert_ciswebui_folder(f):
    @wraps(f)
    def decorated(*args, **kwargs):
        assert os.path.basename(os.getcwd()) == 'ciswebui', 'Has to be called from the ciswebui directory'
        return f(*args, **kwargs)

    return decorated


def __verify_node_installation():
    assert os.path.isfile('iot_store/node_modules/.bin/check-dependencies')
    status = subprocess.call(['node',
                              './node_modules/.bin/check-dependencies'],
                             cwd=os.path.join(os.getcwd(), 'iot_store'))
    if status == 0:
        print('Verified installed node modules: OK.')
    else:
        print('Verified installed node modules: Failure.')
    return status == 0


def __init_extension(ext_name):
    print('Initializing extension: {}'.format(ext_name))

    assert os.path.isdir(os.path.join('..', ext_name))

    root_file = os.path.join('..', ext_name, 'resources/customRoot-{}.properties'.format(ext_name))
    custom_file = os.path.join('..', ext_name, 'resources/custom.properties')

    with open(root_file, 'w') as f:
        f.write(CONTENT_ROOT_FILE)

    if os.path.isfile(custom_file):
        print('Found existing custom.properties file.')
    else:
        with open(custom_file, 'w') as f:
            f.write(CONTENT_CUSTOM_FILE)
        print('Created custom.properties file.')
    print('Done: Initialized frontend extension {}\n'.format(ext_name))


def __clean_js_dist_dir():
    if os.path.isdir(JS_DIST_DIR) and len(os.listdir(JS_DIST_DIR)) > 0:
        print('Cleaning dist folder..')
        shutil.rmtree(JS_DIST_DIR)
    if not os.path.isdir(JS_DIST_DIR):
        os.makedirs(JS_DIST_DIR)

def __install_yarn():
    print('Installing Yarn', flush=True)
    status = subprocess.call(['bash', '-c', 'source ' + NVM_DIR + '/nvm.sh || nvm use node && '
                              'corepack enable'], cwd=os.path.join(os.getcwd(), 'iot_store'))
    assert status == 0
    print('Install Yarn version (iot_store)', flush=True)
    status = subprocess.call(['yarn', '-v'], cwd=os.path.join(os.getcwd(), 'iot_store'))
    assert status == 0
    print('Done: Yarn installation completed.\n')

def __install_node_modules(force=False):
    print('Installing node modules')

    assert os.path.isfile(PACKAGE_JSON)
    assert os.path.isfile(YARN_LOCK)

    __install_yarn()

    if force and os.path.isdir('iot_store/node_modules'):
        print('Deleted node_modules folder.')
        shutil.rmtree('iot_store/node_modules')
        print('Reinstalling yarn packages..')

    status = subprocess.call(['bash', '-c',
                              'source ' + NVM_DIR + '/nvm.sh || nvm use node && yarn install --immutable'],
                             cwd=os.path.join(os.getcwd(), 'iot_store'))
    assert status == 0
    assert __verify_node_installation()
    print('Done: Installation of node modules completed.\n')

@assert_ciswebui_folder
def init_frontend_environment(force=False, skip_js_cleanup=False):
    # We need to flush stdout, otherwise logging gets messed up in ant.
    if not skip_js_cleanup:
        __clean_js_dist_dir()
    __init_extension('cisshopfrontend')
    sys.stdout.flush()
    if force:
        print('Forced flag: Trigger complete (re)installation.')
        __install_node_modules(force)
        sys.stdout.flush()
        return

    if not os.path.isfile('iot_store/node_modules/.bin/check-dependencies'):
        print('Missing node modules: Trigger installation of node modules.')
        __install_node_modules(True)
    elif not __verify_node_installation():
        print('Inconsistent node modules: Trigger re-installation of node modules.')
        __install_node_modules(True)
    sys.stdout.flush()


@assert_ciswebui_folder
def fetch_docker_container():
    if os.path.isfile(WS_ENDPOINT_FILE):
        os.remove(WS_ENDPOINT_FILE)
    if not os.path.isdir('iot_store/testing/test_output'):
        os.makedirs('iot_store/testing/test_output')
    open(WS_ENDPOINT_FILE, 'a').close()

    print('Trigger docker pull...')
    status = subprocess.call(['docker', 'pull', DOCKER_IMAGE_NAME])
    assert status == 0


@assert_ciswebui_folder
def build_typedefs():
    proc = subprocess.Popen(['bash', '-c', 'source setantenv.sh && ant generateFrontendDtos'],
                            cwd=os.path.join(os.getcwd(), '../../platform'), stdout=subprocess.PIPE)
    output = proc.stdout.read().decode('utf-8')
    proc.communicate()
    if proc.returncode != 0:
        print(output)
    print('Created type definitions: OK.')
    return proc.returncode


@assert_ciswebui_folder
def build_dto_validators(node_exec):
    build_cmd = [node_exec, './build_json-dto-validators']
    process = subprocess.Popen(build_cmd, cwd=os.path.join(os.getcwd(), 'iot_store'))
    try:
        process.communicate()
    except KeyboardInterrupt:
        process.send_signal(signal.SIGINT)
    return process.returncode
