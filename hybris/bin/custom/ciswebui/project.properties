# -----------------------------------------------------------------------
# [y] hybris Platform
#
# Copyright (c) 2018 SAP SE or an SAP affiliate company. All rights reserved.
#
# This software is the confidential and proprietary information of SAP
# ("Confidential Information"). You shall not disclose such Confidential
# Information and shall use it only in accordance with the terms of the
# license agreement you entered into with SAP.
# -----------------------------------------------------------------------

# ciswebui.key=value

# Specifies the location of the spring context file putted automatically to the global platform application context.
ciswebui.application-context=ciswebui-spring.xml

# you can control your logger as follows:
log4j2.logger.helloController.name = com.sast.cis.webui.service.impl.DefaultCiswebuiService
log4j2.logger.helloController.level = DEBUG
log4j2.logger.helloController.appenderRef.stdout.ref = STDOUT

#uncomment, if you want to customize the tld/pluggability scanning. You can extend the whitelists below if there is need for that
#ciswebui.tomcat.tld.scan=displaytag*.jar,jstl-impl*.jar
#ciswebui.tomcat.tld.default.scan.enabled=false
#ciswebui.tomcat.pluggability.scan=displaytag*.jar,jstl-impl*.jar
#ciswebui.tomcat.pluggability.default.scan.enabled=false