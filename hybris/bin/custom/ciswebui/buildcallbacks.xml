<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<project name="ciswebui_buildcallbacks">

    <property value="${ext.ciswebui.path}/iot_store" name="node.project.iot_store"/>
    <property value="${ext.ciswebui.path}" name="node.root"/>
    <property value="${ext.ciscore.path}" name="core.root"/>
    <property value="${node.project.iot_store}/src/js/common/generated-types/types.ts" name="types.location" />
    <property value="/web/webroot/_ui/assets/js" name="azena.js.folder"/>
    <property value="/web/webroot/_ui/assets/aa/js" name="aastore.js.folder"/>
    <property value="----------------------------------------------------------" name="separator"/>
    <property value="true" name="buildFrontend"/>

    <macrodef name="frontend_clean" description="Clean compiled js assets.">
        <attribute name="relativeassetspath"/>
        <attribute name="mode"/>
        <sequential>
            <foreachext>
                <do>
                    <if>
                        <and>
                            <contains string="${ext.@{extname}.path}" substring="hybris/bin/custom"/>
                            <available file="${ext.@{extname}.path}@{relativeassetspath}" type="dir"/>
                        </and>
                        <then>
                            <echo>Cleaning extension: @{extname}</echo>
                            <echo>${ext.@{extname}.path}</echo>
                            <if>
                                <equals arg1="${mode}" arg2="development"/>
                                <then>
                                    <delete dir="${ext.@{extname}.path}@{relativeassetspath}/development"/>
                                </then>
                                <else>
                                    <delete dir="${ext.@{extname}.path}@{relativeassetspath}"/>
                                </else>
                            </if>
                        </then>
                    </if>
                </do>
            </foreachext>
        </sequential>
    </macrodef>

    <macrodef name="frontend_intro">
        <attribute name="message" default=""/>
        <attribute name="mode" default=""/>
        <sequential>
            <echo>${separator}</echo>
            <echo>${separator}</echo>
            <echo></echo>
            <echo>RUNNING FRONTEND BUILD TARGET</echo>
            <echo></echo>
            <if>
                <not>
                    <equals arg1="@{message}" arg2=""/>
                </not>
                <then>
                    <echo>Steps: @{message}.</echo>
                    <if>
                        <not>
                            <equals arg1="@{mode}" arg2=""/>
                        </not>
                        <then>
                            <echo>Mode: @{mode}.</echo>
                        </then>
                    </if>
                    <echo></echo>
                </then>
            </if>
            <echo>${separator}</echo>
            <echo></echo>
        </sequential>
    </macrodef>

    <macrodef name="frontend_js_build">
        <sequential>
            <exec executable="python3" dir="${node.root}" resultproperty="npm.build.result">
                <arg value="build_js"/>
                <arg value="--production"/>
                <arg value="--skip-typedef-creation"/>
            </exec>
            <if>
                <not>
                    <equals arg1="${npm.build.result}" arg2="0"/>
                </not>
                <then>
                    <fail status="1" message="Failed to build JS."/>
                </then>
            </if>
        </sequential>
    </macrodef>

    <macrodef name="frontend_aa_js_build">
        <sequential>
            <exec executable="python3" dir="${node.root}" resultproperty="npm.build.result">
                <arg value="build_js"/>
                <arg value="--production"/>
                <arg value="--build-aa"/>
                <arg value="--skip-typedef-creation"/>
            </exec>
            <if>
                <not>
                    <equals arg1="${npm.build.result}" arg2="0"/>
                </not>
                <then>
                    <fail status="1" message="Failed to build AA JS."/>
                </then>
            </if>
        </sequential>
    </macrodef>

    <macrodef name="build_js_in_dev_mode_to_show_frontend_errors_in_aa_acctests" description="Build frontend in development mode.">
        <sequential>
            <exec executable="python3" dir="${node.root}" resultproperty="npm.build.result">
                <arg value="build_js"/>
                <arg value="--skip-typedef-creation"/>
                <arg value="--build-aa"/>
            </exec>

            <if>
                <not>
                    <equals arg1="${npm.build.result}" arg2="0"/>
                </not>
                <then>
                    <fail status="1" message="Failed to build JS."/>
                </then>
            </if>
        </sequential>
    </macrodef>

    <macrodef name="build_js_in_dev_mode_to_show_frontend_errors_in_acctests" description="Build frontend in development mode.">
        <sequential>
            <exec executable="python3" dir="${node.root}" resultproperty="npm.build.result">
                <arg value="build_js"/>
                <arg value="--skip-typedef-creation"/>
            </exec>

            <if>
                <not>
                    <equals arg1="${npm.build.result}" arg2="0"/>
                </not>
                <then>
                    <fail status="1" message="Failed to build JS."/>
                </then>
            </if>
        </sequential>
    </macrodef>

    <macrodef name="build_js_in_dev_mode_to_show_frontend_errors_in_acctests_all_tenants">
        <sequential>
            <if>
                <equals arg1="${buildFrontend}" arg2="true" casesensitive="false"/>
                <then>
                    <property name="mode" value="development"/>
                    <exec executable="python3" dir="${node.root}">
                        <arg value="iot_store/check_init.py"/>
                    </exec>
                    <parallel>
                        <build_js_in_dev_mode_to_show_frontend_errors_in_acctests/>
                        <build_js_in_dev_mode_to_show_frontend_errors_in_aa_acctests/>
                    </parallel>
                    <echo></echo>
                    <echo>${separator}</echo>
                    <echo>${separator}</echo>
                    <echo></echo>
                </then>
            </if>
        </sequential>
    </macrodef>

    <target name="build_js_in_dev_mode_for_acctests">
        <build_js_in_dev_mode_to_show_frontend_errors_in_acctests_all_tenants/>
    </target>

    <target name="frontend_js_clean" description="Clean compiled js assets.">
        <frontend_clean relativeassetspath="${azena.js.folder}" mode="${mode}"/>
        <frontend_clean relativeassetspath="${aastore.js.folder}" mode="${mode}"/>

        <if>
            <not>
                <isset property="assets-only"/>
            </not>
            <then>
                <delete dir="${node.root}/nvm"/>
            </then>
        </if>
    </target>

    <macrodef name="generateTypesForHybrisDtos">
        <sequential>
            <runGenerator generator="com.sast.cis.test.utils.buildergenerator.FrontendDtoGenerator"
                          classDir="${platformhome}/bootstrap/modelclasses"
                          output="${types.location}"/>
        </sequential>
    </macrodef>

    <macrodef name="generateTypesForDtos">
        <sequential>
            <runGenerator generator="com.sast.cis.test.utils.buildergenerator.FrontendDtoGenerator"
                          classDir="${core.root}"
                          output="${types.location}"/>
        </sequential>
    </macrodef>

    <macrodef name="ciswebui_before_build">
        <sequential>
            <generateTypesForDtos/>
            <generateTypesForHybrisDtos/>
        </sequential>
    </macrodef>

    <target name="generateFrontendDtos" description="Generate TypeScript types for Hybris DTOs ('Data' classes)">
        <gensource/>
        <models_build/>
        <compileModelBuilderGeneratorAndFrontendDtoGenerator/>
<!--        Make sure to always invoke generateTypesForDtos because at that point existing types.ts will be removed in all subsequent steps it will only append-->
        <generateTypesForDtos/>
        <generateTypesForHybrisDtos/>
    </target>

    <macrodef name="ciswebui_after_build">
        <sequential>
            <if>
                <equals arg1="${buildFrontend}" arg2="true" casesensitive="false"/>
                <then>
                    <property name="mode" value="production"/>
                    <frontend_intro message="clean, init, build" mode="${mode}"></frontend_intro>
                    <frontend_clean relativeassetspath="${azena.js.folder}" mode="${mode}"/>
                    <frontend_clean relativeassetspath="${aastore.js.folder}" mode="${mode}"/>
                    <exec executable="python3" dir="${node.root}">
                        <arg value="iot_store/check_init.py"/>
                    </exec>
                    <parallel>
                        <frontend_js_build/>
                        <frontend_aa_js_build/>
                    </parallel>
                    <echo></echo>
                    <echo>${separator}</echo>
                    <echo>${separator}</echo>
                    <echo></echo>
                </then>
            </if>
        </sequential>
    </macrodef>

    <macrodef name="ciswebui_after_clean">
        <sequential>
            <if>
                <equals arg1="${buildFrontend}" arg2="true" casesensitive="false"/>
                <then>
                    <property name="mode" value="production"/>
                    <frontend_clean relativeassetspath="${azena.js.folder}" mode="${mode}"/>
                    <frontend_clean relativeassetspath="${aastore.js.folder}" mode="${mode}"/>
                </then>
            </if>
        </sequential>
    </macrodef>

    <target name="build_async" description="Build frontend async.">
        <property name="build.parallel" value="true"/>
        <build />
    </target>

    <target name="build_no_frontend" description="Build without frontend.">
        <var name="buildFrontend" unset="true"/>
        <property name="buildFrontend" value="false"/>
        <property name="build.parallel" value="true"/>
        <build />
    </target>

    <target name="all_async" description="Ant all with async frontend build.">
        <var name="buildFrontend" unset="true"/>
        <property name="buildFrontend" value="false"/>
        <property name="build.parallel" value="true"/>
        <build />
        <deploy />
        <var name="buildFrontend" unset="true"/>
        <property name="buildFrontend" value="true"/>
        <ciswebui_after_build />
    </target>

    <target name="all_no_frontend" description="Ant all without frontend build.">
        <var name="buildFrontend" unset="true"/>
        <property name="buildFrontend" value="false"/>
        <property name="build.parallel" value="true"/>
        <build />
        <deploy />
    </target>
</project>
