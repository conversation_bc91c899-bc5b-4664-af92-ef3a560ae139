#!/usr/bin/env python3

import argparse
import collections
import configparser
import datetime
import glob
import json
import os
import re
import sys
import unicodedata
from enum import Enum

os.chdir(os.path.dirname(os.path.realpath(__file__)))  # allow that script can be called from everywhere
REGEX = re.compile(r'[\n\r\s]', flags=re.MULTILINE)
LICENSES_FILE = './iot_store/dist/shipped-licenses.json'
MODULES_FILE = './iot_store/dist/included-modules.json'
OSS_DIR = os.path.join(os.getcwd(), 'iot_store/oss-whitelists')
SEPARATOR = '----------------------------------------'
MARKER = '####################'
DATE_REGEX = re.compile(r'.+(?P<date>\d{4}-\d{2}-\d{2})\.txt$')
ACCEPTED = 'accepted'


def print_prologue(module: dict, nr_current: int, nr_total: int):
    print('')
    name = module['name']
    print('[{:2d}/{}] {} {}'.format(nr_current + 1, nr_total, name, SEPARATOR[len(name):]))


class LicenseAction(Enum):
    EXISTED = 1
    REJECTED = 2
    ADDED = 3

    @staticmethod
    def rejected():
        print('')
        print('REJECTED')
        print('')
        return LicenseAction.REJECTED

    @staticmethod
    def exists():
        print('')
        print('OK')
        print('')
        return LicenseAction.EXISTED


def process_license(lic, whitelisted_licenses, update=False) -> LicenseAction:
    if REGEX.sub('', lic['licenseText']) in whitelisted_licenses:
        return LicenseAction.exists()
    if not update:
        print('Detected a non whitelisted license:\n')
        print('Module: {}'.format(lic['name']))
        print('Version: {}'.format(lic['version']))
        print('URL: {}\n'.format(lic['url']))
        print(lic['licenseText'])
        return LicenseAction.rejected()
    display_last_valid_license(lic)
    print(SEPARATOR)
    print('New license text:')
    print(lic['licenseText'])
    print(SEPARATOR)
    print('A new license file will be added.')
    print('Please read the license text and confirm that it can be used.')
    answer = input('[yes/NO] ')
    if answer != 'yes':
        return LicenseAction.rejected()

    file_name = '{}-{}-{}-{}.txt'.format(lic['licenseTypeName'], normalize(lic['name']), lic['version'],
                                         datetime.datetime.now().strftime('%Y-%m-%d'))
    with open(file_name, 'w') as new_lic:
        new_lic.write(lic['licenseText'])
    print('Added new license file to whitelist: {}'.format(file_name))
    print(SEPARATOR)
    return LicenseAction.ADDED


def display_last_valid_license(lic):
    former_module_licenses = glob.glob('*{}*.txt'.format(normalize(lic['name'])))
    if len(former_module_licenses) > 0:
        former_module_licenses.sort(reverse=True, key=lambda cf: DATE_REGEX.match(cf).group('date'))
        print('Last valid license text for this module:')
        with open(former_module_licenses[0], 'r') as f:
            print(f.read())
        print('See {}'.format(former_module_licenses[0]))


def normalize(value: str) -> str:
    value = unicodedata.normalize('NFKD', value).encode('ascii', 'ignore').decode('ascii')
    value = re.sub(r'[^\w\s-]', '', value).strip().lower()
    return re.sub(r'[-\s]+', '-', value)


def sort_config(ini_obj):
    for section in ini_obj.sections():
        ini_obj[section] = collections.OrderedDict(sorted(ini_obj[section].items(), key=lambda t: t[0]))
    ini_obj._sections = collections.OrderedDict(sorted(ini_obj._sections.items(), key=lambda t: t[0]))


def main():
    parser = argparse.ArgumentParser(description='VERIFY FRONTEND LICENSES')
    parser.add_argument('--update', action='store_true', help='Run interactive mode to update whitelist.')
    args = parser.parse_args()

    if not os.path.isfile(LICENSES_FILE) or not os.path.isfile(MODULES_FILE):
        print('Could not open module or license information file.')
        print('Build the frontend in production mode to generate license information.')
        sys.exit(1)

    with open(LICENSES_FILE, 'r') as f:
        licenses = json.load(f)
    with open(MODULES_FILE, 'r') as f:
        modules = json.load(f)

    os.chdir(OSS_DIR)

    whitelisted_licenses = set()
    for fileName in os.listdir('.'):
        with open(fileName, 'r') as f:
            whitelisted_licenses.add(REGEX.sub('', f.read()))

    nr_invalid_licenses = 0
    nr_licenses = len(licenses)

    print('')
    print(MARKER + ' Verifying licenses ' + MARKER)
    print('')
    for idx, lic in enumerate(licenses):
        print_prologue(lic, idx, nr_licenses)
        action = process_license(lic, whitelisted_licenses, args.update)
        if action == LicenseAction.REJECTED:
            nr_invalid_licenses += 1

    print('')
    print(MARKER)
    print('')
    if nr_invalid_licenses > 0:
        print('There are {}/{} non whitelisted licenses.'.format(nr_invalid_licenses, nr_licenses))
        print('Run this script locally (after running ./build_js --production), and use option --update to whitelist them.')
        print('Further info: https://snst.atlassian.net/wiki/spaces/IS/pages/5683976/License+Compliance+and+Quality+Standards+for+External+Dependencies')
        print('')
        print('Status: FAILURE')
        sys.exit(1)
    else:
        print('Checked {} modules for license texts.'.format(nr_licenses))
        print('')
        print('Status: SUCCESS')


if __name__ == '__main__':
    main()
