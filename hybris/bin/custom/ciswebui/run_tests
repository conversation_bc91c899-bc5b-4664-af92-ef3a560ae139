#!/usr/bin/env python3

import argparse
import os
import pty
import socket
import subprocess
import sys
from subprocess import DEVNULL, PIPE

import build_tools
from build_tools import DOCKER_IMAGE_NAME

W_CONFIG = 'testing/tests.webpack.config.js'
DEV_SERVER_PORT = 9010
DEV_SERVER = '**********:{}'.format(DEV_SERVER_PORT)


def is_server_already_running(port):
    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    result = sock.connect_ex(('0.0.0.0', port))
    sock.close()
    return result == 0


def wait_for_dev_server(node_exec, port, config):
    args_dev_server = [node_exec, './node_modules/.bin/webpack', 'serve', '--progress', '--config', config,
                       '--env', 'port={}'.format(port), '--env', 'debug']
    dev_server = subprocess.Popen(args_dev_server, stdout=subprocess.PIPE)
    try:
        compilation_ok = False
        for line in dev_server.stdout:
            sys.stdout.write('WEBPACK > ')
            sys.stdout.buffer.write(line)
            sys.stdout.flush()
            if 'Compiled' in line.decode('utf-8'):
                print('\n\nDev server is running.\n')
                compilation_ok = True
                break
            elif 'Failed to compile' in line.decode('utf-8'):
                compilation_ok = False
                break
        if not compilation_ok:
            dev_server.terminate()
            print('Failed to start dev server.')
            sys.exit(1)
    except KeyboardInterrupt:
        print('Terminated by user input.')
        dev_server.kill()
        sys.exit(1)
    return dev_server


def main():
    parser = argparse.ArgumentParser(description='FRONTEND TESTS HELPER SCRIPT')
    parser.add_argument('--docker-build', action='store_true', help='(Force) build docker container.')
    parser.add_argument('--watch', action='store_true', help='Run interactive watch mode.')
    parser.add_argument('--slow-mo', nargs='?', default=argparse.SUPPRESS, type=int,
                        help='Run in slow motion mode (in milliseconds). Default: 500.')
    group = parser.add_mutually_exclusive_group()
    group.add_argument('--strict', action='store_true', help='No retries, zero tolerance for image snapshot tests.')
    group.add_argument('--percent-tolerance', type=float, help='Set global tolerance for all image snapshot tests in percentage.')
    group.add_argument('--pixel-tolerance', type=int, help='Set global tolerance for all image snapshot tests in percentage.')
    parser.add_argument('--force-init', action='store_true', help='Delete node_modules folder and run initialize script.')
    parser.add_argument('-u', '--update', action='store_true', help='Update snapshots (implicitly sets --strict).')
    parser.add_argument('--aa', action='store_true', help='Run tests for aa store.')

    args = parser.parse_args()

    if hasattr(args, 'slow_mo') and args.slow_mo is None:
        args.slow_mo = 500

    if args.update:
        args.strict = True

    port = DEV_SERVER_PORT
    webpack_config = W_CONFIG
    server = DEV_SERVER
    if args.aa:
        port = 9011
        webpack_config = 'testing/tests.webpack.aa.config.js'
        server = '**********:{}'.format(port)

    build_tools.init_frontend_environment(args.force_init)
    node_exec = 'node'

    p = subprocess.Popen(['docker', 'inspect', '-f', '"{{.State.Running}}"', DOCKER_IMAGE_NAME], stdout=PIPE, stderr=DEVNULL)
    out, _ = p.communicate()
    if out.decode('utf-8').strip().replace('"', '') == 'true':
        status = subprocess.call(['docker', 'stop', DOCKER_IMAGE_NAME], stdout=DEVNULL)
        print('Stopped running docker container.')
        assert status == 0
    build_tools.fetch_docker_container()

    if is_server_already_running(port):
        dev_server = None
        os.chdir('iot_store')
    else:
        status = build_tools.build_typedefs()
        if status != 0:
            sys.exit(status)
        status = build_tools.build_dto_validators(node_exec)
        if status != 0:
            sys.exit(status)
        os.chdir('iot_store')
        dev_server = wait_for_dev_server(node_exec, port, webpack_config)

    for key in vars(args):
        val = getattr(args, key)
        if (val is True) or (type(val) in (int, float)):
            os.environ[key] = str(val)
    os.environ['RUNNING_TESTS_FROM_CMD'] = 'true'

    npm_args = [node_exec, './node_modules/.bin/jest']
    if args.watch:
        npm_args.append('--watch')
    if args.update:
        npm_args.append('--updateSnapshot')
    if hasattr(args, 'slow_mo'):
        npm_args.append('--maxWorkers=1')  # Otherwise multiple tabs will open in firefox.
    else:
        npm_args.append('--maxWorkers=10')
    if args.aa:
        npm_args.append('--theme=aa')

    success = pty.spawn(npm_args)
    if success != 0:
        print ("ERROR: testsuite failure")
        sys.exit(1)
    if args.watch:
        status = subprocess.call(['docker', 'stop', 'puppeteer-chrome'], stdout=DEVNULL)
        assert status == 0

    if dev_server:
        dev_server.terminate()
        print('INFO: Call ./run_dev_server and keep it running to speed up local testing.')


if __name__ == '__main__':
    main()
