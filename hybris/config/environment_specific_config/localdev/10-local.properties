### This is the environment specific local.properties file for a local environment ###
### If the HYBRIS_OPT_CONFIG_DIR environment variable points                       ###
### to the directory of this file, the properties defined in this directory        ###
### will be included in hybris with HIGHEST priority, i.e they will override       ###
### anything defined in <HYBRIS_DIR>/config/local.properties                       ###

spring.profiles.active=test

aws.common.credentialsProvider=ProfileCredentialsProvider
aws.common.credentialsProfile=sast-people
aws.sns.successfulorders.topic=arn:aws:sns:eu-central-1:************:IotStore-successful-order-event
aws.sns.subscription.topic=arn:aws:sns:eu-central-1:************:IotStore-subscription-event
aws.sns.licenseactivation.topic=arn:aws:sns:eu-central-1:************:IotStore-license-activation-event.fifo

aws.sqs.accountupdate.name=IotStore-account_update
aws.sqs.trial_extension-request_events.name=IotStore-trial-license-extension-requests
aws.sqs.aa.accountupdate.name=IotStore-aa_account_update.fifo
aws.sqs.aa.delayed_order_activation.name=IotStore-delayed_order_activation
aws.sqs.aa.contract_migration_tool_events.name=IotStore-migration_tool_events
aws.sqs.aa.contract_migration_tool_events.response.name=IotStore-migration_tool_events_response
# Queue is subscribed to the LMP migration validation response events topic
aws.sqs.aa.contract_migration_response_events.name=IotStore-contract_migration_response_events


aws.sqs.endpoint=http://localhost:4566
aws.sns.endpoint=http://localhost:4566
aws.s3.endpoint=http://127.0.0.1:4566
aws.region=eu-central-1

log4j2.logger.sast.name=com.sast
log4j2.logger.sast.level=debug

log4j2.logger.synchronization.name=de.hybris.platform.catalog.jalo.synchronization
log4j2.logger.synchronization.level=debug

log4j2.logger.mediaconversion.name=de.hybris.platform.mediaconversion
log4j2.logger.mediaconversion.level=debug

log4j2.logger.webappmediafilter.name=de.hybris.platform.servicelayer.web.WebAppMediaFilter
log4j2.logger.webappmediafilter.level=debug

#external URLs
website.domain=.dev.local

com.sast.cis.encryptedAdminPassword=1000:3e402b6d5504a113183aa1f1b2f600b2:8f824507e25835ae13db2ca1ddc2291ab6c920adf5bbb8c022552dd71de21349e1678d05ddeb4dac1756a682d734ec729e77358db63117c377cfb800b4ae4941

developerportal.url=http://localhost:8091/page/developerportal
sast.developer.support.url=https://developer.azena.com/contact
shop.header.cameras.url=http://localhost:8091/page/cameras
shop.header.myapps.url=http://localhost:8091/page/myapps
devcon.header.documentation.url=https://docs.azena.com/#/

azena.dmp.url=http://localhost:8091/page/cameras
aa.dmp.url=http://localhost:8091/page/cameras

usermanagementportal.myprofile.url=http://localhost:8091/page/myprofile
usermanagementportal.mycompany.url=http://localhost:8091/page/mycompany
usermanagementportal.legal.url=https://accounts.azena.com/legal

aa.usermanagementportal.myprofile.url=http://localhost:8091/page/myprofile
aa.usermanagementportal.mycompany.url=http://localhost:8091/page/mycompany
aa.usermanagementportal.legal.url=https://accounts.azena.com/legal

interfaces.portal.user=remoteportal
interfaces.portal.password=********
interfaces.api.user=apiuser
interfaces.api.password=123test123

# ump api configuration for the Azena store
azena.ump.api.username=iotstore-shop
azena.ump.api.password=********
azena.ump.api.baseurl=https://localhost:8190/ump

# ump api configuration for the AA store
aastore.ump.api.username=iotstore-shop-aa
aastore.ump.api.password=********
aastore.ump.api.baseurl=https://localhost:8290/ump

#configure S3 for MediaFolder apks
media.folder.apks.storage.strategy=s3ApkMediaStorageStrategy
media.folder.apks.url.strategy=localMediaWebURLStrategy
media.folder.apks.local.cache.rootCacheFolder=apk-cache
media.folder.apks.endpoint=http://localhost:4566
media.folder.apks.bucketId=cbsiotstore-localdev-apks
media.folder.apks.url.signed=false

# media url strategy for backoffice themes media
media.folder.backofficethemes.url.strategy=localMediaWebURLStrategy
media.folder.backofficelogos.url.strategy=localMediaWebURLStrategy

# media url for AA price updates
media.folder.priceupdates.bucketId=price-updates

# media url for AA price updates
media.folder.productupdates.bucketId=product-updates

# media url for AA technical hotline product configuration
media.folder.thlConfiguration.bucketId=thl-cfg-import

hac.config.init.allowed=true

# 1 mb for testing
apk.max.upload.size.bytes=1048576

# cluster mode
cluster.broadcast.method.jgroups.tcp.bind_addr=127.0.0.1
cluster.node.groups=cf,ncf

# Solrclould Hybrys built-in
# Set cloud.autostart=true to use solrcloud from hybris
solrserver.instances.default.autostart=true
solrserver.instances.default.mode=cloud
solrserver.instances.default.hostname=localhost
solrserver.instances.default.port=8983
solrserver.instances.default.memory=512m
solrserver.instances.default.zk.host=
solrserver.instances.default.zk.upconfig=true
solrserver.instances.default.zk.prop.urlScheme=http

# solrcloud
solr.external.zkendpoint=localhost:9983
solr.client.password=admin123

#Brimulator urls
brimulator.api.url=http://localhost:10080

com.sast.cis.dpg.jslibrary=http://127.0.0.1:8091/dpgjs/js

payment.stripe.url=https://localhost:8090/stripe

aws.s3.timeout=120000

#Clamav scan
clamav.api.url=https://localhost:8088/api
clamav.scan.enabled=true

sso.registration.url=https://www.azena.com/for-integrators
aa.sso.registration.url=https://sso.twbd.io/company-register

# Keycloak configuration
sso.keycloak.configuration.devcon.credentials.secret=78794618-b438-4f1b-8a34-4d43314ec55b
sso.keycloak.configuration.iotstoreapi.credentials.secret=a3907caf-8922-4e0f-bee2-6e6fc24192eb
sso.keycloak.configuration.ssl-required=none
sso.keycloak.configuration.auth-server-url=http://localhost:8080/auth
sso.keycloak.configuration.shop.credentials.secret=78794618-b438-4f1b-8a34-4d43314ec55b

# Keycloak configuration for the Azena store
azena.sso.keycloak.configuration.auth-server-url=http://localhost:8080/auth
azena.sso.keycloak.configuration.shop.credentials.secret=78794618-b438-4f1b-8a34-4d43314ec55b
azena.sso.keycloak.configuration.issuer=http://localhost:8080/auth/realms/sast

# Keycloak configuration for the aa store
aastore.sso.keycloak.configuration.auth-server-url=http://localhost:8080/auth
aastore.sso.keycloak.configuration.shop.credentials.secret=9fab8075-229b-4245-b5ea-018d2bfa201c
aastore.sso.keycloak.configuration.issuer=http://localhost:8080/auth/realms/baam

# Content Security Policy (CSP) - ALLOW ALL FOR DEVELOPMENT
xss.filter.header.Content-Security-Policy=default-src * 'unsafe-inline' 'unsafe-eval' data: blob:; \
  script-src * 'unsafe-inline' 'unsafe-eval'; \
  font-src * data:; \
  style-src * 'unsafe-inline'; \
  img-src * data: blob:; \
  frame-src *; \
  connect-src *

# statistics service url
statistics.url=http://localhost:8081

# brim integration configuration
brim.http.invoiceFile.baseUrl=http://localhost:10080/billing/sales/InvoiceFile/80B9/q/v1.0
brim.http.invoiceFile.apiKey=00000000-0000-0000-0000-************
brim.http.product.update.baseUrl=http://localhost:10080/billing/sales/product/80B9/q/v2
brim.http.product.update.apiKey=00000000-0000-0000-0000-************

country.api.url=http://localhost:8091/rest/private/v3/countries

# When set to 'true', the HTML DOM tree has an additional ytestid attribute. Affects the Backoffice only.
backoffice.cockpit.ytestid.enabled=true

aastore.translation.remoteTranslationsEnabled=true
aastore.translation.baseUrl=http://localhost:7788
aastore.translation.projectId=e-a498a91a573d2f3d0e913b33hc
aastore.translation.cds.projectId=e-12c783af99d2c881ac093b33hc

azena.translation.remoteTranslationsEnabled=false
azena.translation.baseUrl=http://localhost:7788
azena.translation.projectId=ddb7262797dfb7d3a39168e32b3da5bc

brim.productexport.env_id=L

aastore.checkout.baseUrl=https://aa.store.dev.local:9002/shop
azena.checkout.baseUrl=https://store.dev.local:9002/shop

# SAP Commerce 2205.26 changed the default to bcrypt. Migrating to
default.password.encoding=pbkdf2

extract.thl.scanWindowInYears=4

# Task polling interval configuration. Improves the performance of the acceptance tests that involve BPs.
task.polling.interval.min=0
task.polling.interval=2
