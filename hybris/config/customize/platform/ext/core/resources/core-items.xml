<?xml version="1.0" encoding="ISO-8859-1"?>
<!--
 Copyright (c) 2021 SAP SE or an SAP affiliate company. All rights reserved.
-->
<items xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="items.xsd">
    <atomictypes>
        <atomictype class="java.lang.Object" autocreate="true" generate="false"/>
        <atomictype class="java.lang.Number" extends="java.lang.Object" autocreate="true" generate="false"/>
        <atomictype class="java.lang.Integer" extends="java.lang.Number" autocreate="true" generate="false"/>
        <atomictype class="java.lang.Boolean" extends="java.lang.Object" autocreate="true" generate="false"/>
        <atomictype class="java.lang.Byte" extends="java.lang.Number" autocreate="true" generate="false"/>
        <atomictype class="java.lang.Double" extends="java.lang.Number" autocreate="true" generate="false"/>
        <atomictype class="java.lang.Float" extends="java.lang.Number" autocreate="true" generate="false"/>
        <atomictype class="java.lang.Long" extends="java.lang.Number" autocreate="true" generate="false"/>
        <atomictype class="java.lang.Short" extends="java.lang.Number" autocreate="true" generate="false"/>
        <atomictype class="java.lang.String" extends="java.lang.Object" autocreate="true" generate="false"/>
        <atomictype class="java.lang.Character" extends="java.lang.Object" autocreate="true" generate="false"/>
        <atomictype class="java.util.Date" extends="java.lang.Object" autocreate="true" generate="false"/>
        <atomictype class="java.util.Map" extends="java.lang.Object" autocreate="true" generate="false"/>
        <atomictype class="java.lang.Class" extends="java.lang.Object" autocreate="true" generate="false"/>
        <atomictype class="de.hybris.platform.util.ItemPropertyValue" extends="java.lang.Object" autocreate="true"
                    generate="false"/>
        <atomictype class="de.hybris.platform.util.ItemPropertyValueCollection" extends="java.lang.Object"
                    autocreate="true"
                    generate="false"/>
        <atomictype class="java.math.BigInteger" extends="java.lang.Number" autocreate="true" generate="false"/>
        <atomictype class="java.math.BigDecimal" extends="java.lang.Number" autocreate="true" generate="false"/>
        <atomictype class="de.hybris.platform.core.PK" extends="java.lang.Object" autocreate="true" generate="false"/>
        <atomictype class="de.hybris.platform.util.TaxValue" extends="java.lang.Object" autocreate="true"
                    generate="false"/>
        <atomictype class="de.hybris.platform.util.DiscountValue" extends="java.lang.Object" autocreate="true"
                    generate="false"/>
        <atomictype class="de.hybris.platform.util.StandardDateRange" extends="java.lang.Object" autocreate="true"
                    generate="false"/>
        <atomictype class="de.hybris.platform.core.order.EntryGroup" extends="java.lang.Object" autocreate="true"/>
    </atomictypes>

    <collectiontypes>
        <collectiontype code="ExampleCollection" elementtype="Item" autocreate="true" generate="false"/>
        <collectiontype code="ItemCollection" elementtype="Item" autocreate="true" generate="false"/>
        <collectiontype code="StringCollection" elementtype="java.lang.String" autocreate="true" generate="false"/>
        <collectiontype code="ObjectCollection" elementtype="java.lang.Object" autocreate="true" generate="false"/>

        <collectiontype code="MediaCollection" elementtype="Media" autocreate="true" generate="true"/>

        <collectiontype code="AbstractOrderEntryList" elementtype="AbstractOrderEntry" autocreate="true"
                        generate="false"
                        type="list"/>
        <collectiontype code="CartEntryCollection" elementtype="CartEntry" autocreate="true" generate="false"
                        type="list"/>
        <collectiontype code="OrderEntryCollection" elementtype="OrderEntry" autocreate="true" generate="false"
                        type="list"/>
        <collectiontype code="QuoteEntryCollection" elementtype="QuoteEntry" autocreate="true" generate="false"
                        type="list"/>
        <collectiontype code="CartCollection" elementtype="Cart" autocreate="true" generate="false"/>
        <collectiontype code="OrderCollection" elementtype="Order" autocreate="true" generate="false"/>
        <collectiontype code="AbstractOrderCollection" elementtype="AbstractOrder" autocreate="true" generate="false"/>
        <collectiontype code="PaymentModeCollection" elementtype="PaymentMode" autocreate="true" generate="false"/>
        <collectiontype code="PaymentInfoCollection" elementtype="PaymentInfo" autocreate="true" generate="false"/>
        <collectiontype code="DeliveryModeCollection" elementtype="DeliveryMode" autocreate="true" generate="false"/>
        <collectiontype code="DiscountValueCollection" elementtype="de.hybris.platform.util.DiscountValue"
                        autocreate="true"
                        generate="false" type="list"/>
        <collectiontype code="TaxValueCollection" elementtype="de.hybris.platform.util.TaxValue" autocreate="true"
                        generate="false"/>

        <collectiontype code="RegionCollection" elementtype="Region" autocreate="true" generate="false"/>
        <collectiontype code="LanguageCollection" elementtype="Language" autocreate="true" generate="true"/>
        <collectiontype code="LanguageList" elementtype="Language" autocreate="true" generate="true" type="list"/>
        <collectiontype code="LanguageSet" elementtype="Language" autocreate="true" generate="true" type="set"/>

        <collectiontype code="TypeCollection" elementtype="Type" autocreate="true" generate="false"/>
        <collectiontype code="ComposedTypeCollection" elementtype="ComposedType" autocreate="true" generate="false"/>
        <collectiontype code="attributeSet" elementtype="AttributeDescriptor" autocreate="true" generate="false"/>
        <collectiontype code="ViewAttributeList" elementtype="ViewAttributeDescriptor" autocreate="true"
                        generate="false"
                        type="list"/>
        <collectiontype code="ViewAttributeSet" elementtype="ViewAttributeDescriptor" autocreate="true" generate="false"
                        type="set"/>
        <collectiontype code="configAttributeSet" elementtype="ConfigAttributeDescriptor" autocreate="true"
                        generate="false"/>
        <collectiontype code="subTypesSet" elementtype="ComposedType" autocreate="true" generate="false"/>
        <collectiontype code="AtomicTypeSubtypeCollection" elementtype="AtomicType" autocreate="true" generate="false"/>
        <collectiontype code="RestrictionList" elementtype="SearchRestriction" autocreate="true" generate="false"/>

        <collectiontype code="PrincipalGroupCollection" elementtype="PrincipalGroup" autocreate="true"
                        generate="false"/>
        <collectiontype code="PrincipalGroupSet" elementtype="PrincipalGroup" autocreate="true" generate="false"
                        type="set"/>
        <collectiontype code="PrincipalCollection" elementtype="Principal" autocreate="true" generate="false"/>
        <collectiontype code="AddressCollection" elementtype="Address" autocreate="true" generate="false"/>
        <collectiontype code="UserCollection" elementtype="User" autocreate="true" generate="true"/>
        <collectiontype code="TestItemsTypeTwoCollection" elementtype="TestItemType2" autocreate="true"
                        generate="true"/>

        <collectiontype code="ProductCollection" elementtype="Product" autocreate="true" generate="true"/>

        <collectiontype code="InMemoryCartEntryCollection" elementtype="InMemoryCartEntry" autocreate="true"
                        generate="false"/>

        <!-- hmc legacy - SavedValues-->
        <collectiontype code="SavedValuesEntriesCollection" elementtype="SavedValueEntry" autocreate="true"
                        generate="false"
                        type="list"/>
        <collectiontype code="SavedValuesCollection" elementtype="SavedValues" autocreate="true" generate="false"
                        type="list"/>
        <collectiontype code="EntryGroupList" elementtype="de.hybris.platform.core.order.EntryGroup" autocreate="true" type="list"/>
        <collectiontype code="EntryGroupNumbersList" elementtype="java.lang.Integer" autocreate="true" type="set"/>

    </collectiontypes>

    <enumtypes>

        <enumtype code="TestEnum">
            <value code="testValue1"/>
            <value code="testValue2"/>
            <value code="testValue3"/>
            <value code="testValue4"/>
        </enumtype>

        <enumtype code="EncodingEnum" autocreate="true" generate="true" dynamic="true"/>

        <!-- order -->
        <enumtype code="CreditCardType" autocreate="true" generate="true">
            <value code="amex"/>
            <value code="visa"/>
            <value code="master"/>
            <value code="diners"/>
            <value code="unknownType"/>
        </enumtype>

        <enumtype code="OrderStatus" autocreate="true" generate="true" dynamic="true">
            <value code="CREATED"/>
            <value code="ON_VALIDATION"/>
            <value code="COMPLETED"/>
            <value code="CANCELLED"/>
        </enumtype>

        <enumtype code="PaymentStatus" autocreate="true" generate="true" dynamic="true">
            <value code="NOTPAID"/>
            <value code="PARTPAID"/>
            <value code="PAID"/>
        </enumtype>

        <enumtype code="DeliveryStatus" autocreate="true" generate="true" dynamic="true">
            <value code="NOTSHIPPED"/>
            <value code="PARTSHIPPED"/>
            <value code="SHIPPED"/>
        </enumtype>

        <enumtype code="ExportStatus" autocreate="true" generate="true">
            <value code="NOTEXPORTED"/>
            <value code="EXPORTED"/>
        </enumtype>

        <enumtype code="TypeOfCollectionEnum" autocreate="true" generate="true">
            <value code="collection"/>
            <value code="set"/>
            <value code="list"/>
        </enumtype>

        <enumtype code="RelationEndCardinalityEnum" autocreate="true" generate="true">
            <value code="one"/>
            <value code="many"/>
        </enumtype>

        <enumtype code="MediaManagementTypeEnum" autocreate="true" generate="true">
            <value code="FILES"/>
            <value code="SSH"/>
            <value code="FTP"/>
        </enumtype>

        <enumtype code="Gender" autocreate="true" generate="true">
            <value code="MALE"/>
            <value code="FEMALE"/>
        </enumtype>

        <enumtype code="ActionType" autocreate="true" generate="true">
            <model package="de.hybris.platform.servicelayer.enums"/>
            <value code="PLAIN"/>
        </enumtype>

        <!-- hmc legacy - Saved Values -->
        <enumtype code="SavedValueEntryType" autocreate="true" generate="true">
            <value code="created"/>
            <value code="removed"/>
            <value code="changed"/>
        </enumtype>

        <enumtype code="PhoneContactInfoType" generate="true" autocreate="true" dynamic="true">
            <description>Phone type</description>
            <value code="MOBILE"/>
            <value code="WORK"/>
            <value code="HOME"/>
        </enumtype>

        <!-- quote -->
        <enumtype code="QuoteState" autocreate="true" generate="true" dynamic="true">
            <value code="CREATED"/>
            <value code="DRAFT"/>
            <value code="SUBMITTED"/>
            <value code="OFFER"/>
            <value code="ORDERED"/>
            <value code="CANCELLED"/>
            <value code="EXPIRED"/>
        </enumtype>

        <!-- quote -->
        <enumtype code="RetentionState" autocreate="true" generate="true" dynamic="true">
            <value code="PROCESSED"/>
        </enumtype>

        <enumtype code="GroupType" dynamic="true"/>

        <enumtype code="SAPUserVerificationPurpose" autocreate="true" generate="true">
            <value code="LOGIN"/>
<!--            <value code="REGISTRATION"/>-->
        </enumtype>

    </enumtypes>

    <maptypes>
        <maptype code="ExampleMap"
                 argumenttype="Language"
                 returntype="java.math.BigInteger"
                 autocreate="true"
                 generate="false"/>
        <maptype code="localized:java.lang.String"
                 argumenttype="Language"
                 returntype="java.lang.String"
                 autocreate="true"
                 generate="false"/>
        <maptype code="localized:java.lang.Integer"
                 argumenttype="Language"
                 returntype="java.lang.Integer"
                 autocreate="true"
                 generate="false"/>
        <maptype code="localized:java.lang.Boolean"
                 argumenttype="Language"
                 returntype="java.lang.Boolean"
                 autocreate="true"
                 generate="false"/>
        <maptype code="localized:java.lang.Byte"
                 argumenttype="Language"
                 returntype="java.lang.Byte"
                 autocreate="true"
                 generate="false"/>
        <maptype code="localized:java.lang.Double"
                 argumenttype="Language"
                 returntype="java.lang.Double"
                 autocreate="true"
                 generate="false"/>
        <maptype code="localized:java.lang.Float"
                 argumenttype="Language"
                 returntype="java.lang.Float"
                 autocreate="true"
                 generate="false"/>
        <maptype code="localized:java.lang.Long"
                 argumenttype="Language"
                 returntype="java.lang.Long"
                 autocreate="true"
                 generate="false"/>
        <maptype code="localized:java.lang.Short"
                 argumenttype="Language"
                 returntype="java.lang.Short"
                 autocreate="true"
                 generate="false"/>
        <maptype code="localized:java.lang.Character"
                 argumenttype="Language"
                 returntype="java.lang.Character"
                 autocreate="true"
                 generate="false"/>
        <maptype code="localized:java.util.Date"
                 argumenttype="Language"
                 returntype="java.util.Date"
                 autocreate="true"
                 generate="false"/>
        <maptype code="localized:java.math.BigInteger"
                 argumenttype="Language"
                 returntype="java.math.BigInteger"
                 autocreate="true"
                 generate="false"/>
        <maptype code="localized:java.math.BigDecimal"
                 argumenttype="Language"
                 returntype="java.math.BigDecimal"
                 autocreate="true"
                 generate="false"/>
        <maptype code="localized:de.hybris.platform.util.StandardDateRange"
                 argumenttype="Language"
                 returntype="de.hybris.platform.util.StandardDateRange"
                 autocreate="true"
                 generate="false"/>
        <maptype code="ParameterMap"
                 argumenttype="java.lang.String"
                 returntype="Type"
                 autocreate="true"
                 generate="false"/>
        <maptype code="ParamTypeMap"
                 argumenttype="java.lang.String"
                 returntype="Type"
                 autocreate="true"
                 generate="false"/>
        <maptype code="localized:Media"
                 argumenttype="Language"
                 returntype="Media"
                 autocreate="true"
                 generate="false"/>
    </maptypes>

    <relations>


        <relation code="AbstractOrder2AbstractOrderEntry" localized="false" generate="true" autocreate="true">
            <sourceElement type="AbstractOrder" qualifier="order" cardinality="one">
                <modifiers read="true" write="true" search="true" optional="true"/>
                <custom-properties>
                    <property name="ordering.attribute">
                        <value>"entryNumber"</value>
                    </property>
                </custom-properties>
            </sourceElement>
            <targetElement type="AbstractOrderEntry" qualifier="entries" cardinality="many" collectiontype="list"
                           ordered="false">
                <modifiers read="true" write="true" search="true" optional="true" partof="true"/>
                <custom-properties>
                    <property name="query.filter">
                        <value>"{original} is null"</value>
                    </property>
                </custom-properties>
            </targetElement>
        </relation>

        <relation code="PrincipalGroupRelation" autocreate="true" generate="false" localized="false"
                  deployment="de.hybris.platform.persistence.link.PrincipalGroupRelation">
            <sourceElement qualifier="members" type="Principal" collectiontype="set" cardinality="many" ordered="false">
                <modifiers read="true" write="true" search="true" optional="true"/>
            </sourceElement>
            <targetElement qualifier="groups" type="PrincipalGroup" collectiontype="set" cardinality="many"
                           ordered="false">
                <modifiers read="true" write="true" search="true" optional="true"/>
            </targetElement>
        </relation>

        <relation code="OrderDiscountRelation" autocreate="true" generate="false" localized="false"
                  deployment="de.hybris.platform.persistence.link.OrderDiscountRelation">
            <sourceElement qualifier="orders" type="AbstractOrder" cardinality="many" ordered="false">
                <modifiers read="true" write="true" search="true" optional="true"/>
            </sourceElement>
            <targetElement qualifier="discounts" type="Discount" cardinality="many" ordered="true"
                           collectiontype="list">
                <modifiers read="true" write="true" search="true" optional="true"/>
            </targetElement>
        </relation>

        <!-- MediaContainer stuff PLA-5950 -->
        <relation code="MediaContainer2MediaRel" localized="false" generate="true" autocreate="true">
            <sourceElement type="MediaContainer" qualifier="mediaContainer" cardinality="one">
                <modifiers read="true" write="true" search="true" optional="true"/>
            </sourceElement>
            <targetElement type="Media" qualifier="medias" cardinality="many">
                <modifiers read="true" write="true" search="true" optional="true"/>
            </targetElement>
        </relation>
        <relation code="MediaContext2MediaFormatMappingRel" localized="false" generate="true" autocreate="true">
            <sourceElement type="MediaContext" qualifier="mediaContext" cardinality="one">
                <modifiers read="true" write="true" search="true" optional="false" unique="true"/>
            </sourceElement>
            <targetElement type="MediaFormatMapping" qualifier="mappings" cardinality="many">
                <modifiers read="true" write="true" search="true" optional="true" partof="true"/>
            </targetElement>
        </relation>

        <relation code="Media2DerivedMediaRel" localized="false" generate="true" autocreate="true">
            <sourceElement type="Media" qualifier="media" cardinality="one">
                <modifiers read="true" write="true" search="true" optional="false"/>
            </sourceElement>
            <targetElement type="DerivedMedia" qualifier="derivedMedias" cardinality="many">
                <modifiers read="true" write="true" search="true" optional="true" partof="true"/>
            </targetElement>
        </relation>

        <relation code="Country2RegionRelation" generate="true" localized="false" autocreate="true">
            <sourceElement type="Country" qualifier="country" cardinality="one">
                <modifiers read="true" write="true" search="true" optional="false" unique="true"/>
            </sourceElement>
            <targetElement type="Region" qualifier="regions" cardinality="many">
                <modifiers read="true" write="true" search="true" partof="true"/>
            </targetElement>
        </relation>
        <relation code="Principal2SearchRestrictionRelation" generate="true" localized="false" autocreate="true">
            <sourceElement type="Principal" qualifier="principal" cardinality="one">
                <modifiers read="true" write="true" search="true" optional="false" initial="true" unique="true"/>
            </sourceElement>
            <targetElement type="SearchRestriction" qualifier="searchRestrictions" cardinality="many">
                <modifiers read="true" write="false" search="false" optional="true"/>
                <model>
                    <getter name="searchrestrictions" deprecated="true" deprecatedSince="ages"/>
                </model>
            </targetElement>
        </relation>

        <relation code="User2ContactInfos" generate="true" localized="false" autocreate="true">
            <sourceElement type="User" cardinality="one" qualifier="user">
                <modifiers read="true" write="true" search="true" optional="false"/>
            </sourceElement>
            <targetElement type="AbstractContactInfo" cardinality="many" qualifier="contactInfos" ordered="true">
                <modifiers read="true" write="true" search="true" optional="true" partof="true"/>
            </targetElement>
        </relation>

        <relation code="User2Carts" generate="true" localized="false" autocreate="true">
            <sourceElement type="User" cardinality="one" qualifier="user">
                <modifiers read="true" write="true" search="true" optional="false"/>
            </sourceElement>
            <targetElement type="Cart" cardinality="many" qualifier="carts">
                <modifiers read="true" write="true" search="true" optional="true" partof="true"/>
            </targetElement>
        </relation>

        <relation code="User2Quotes" generate="true" localized="false" autocreate="true">
            <sourceElement type="User" cardinality="one" qualifier="user">
                <modifiers read="true" write="true" search="true" optional="false"/>
            </sourceElement>
            <targetElement type="Quote" cardinality="many" qualifier="quotes">
                <modifiers read="true" write="true" search="true" optional="true" partof="true"/>
            </targetElement>
        </relation>

        <relation code="User2Orders" generate="true" localized="false" autocreate="true">
            <sourceElement type="User" cardinality="one" qualifier="user">
                <modifiers read="true" write="true" search="true" optional="false"/>
            </sourceElement>
            <targetElement type="Order" cardinality="many" qualifier="orders">
                <modifiers read="true" write="true" search="true" optional="true" partof="true"/>
            </targetElement>
        </relation>

        <relation code="User2Addresses" generate="true" localized="false" autocreate="true">
            <sourceElement type="User" cardinality="one" qualifier="owner">
                <modifiers read="true" write="false" initial="true" search="true" removable="false" optional="false"/>
            </sourceElement>
            <targetElement type="Address" cardinality="many" qualifier="addresses">
                <modifiers read="true" write="true" search="true" optional="true" partof="true"/>
                <custom-properties>
                    <property name="condition.query">
                        <value>"{original} is null"</value>
                    </property>
                </custom-properties>
            </targetElement>
        </relation>

        <relation code="User2PaymentInfos" generate="true" localized="false" autocreate="true">
            <sourceElement type="User" cardinality="one" qualifier="user">
                <modifiers read="true" write="true" search="true" optional="true" />
            </sourceElement>
            <targetElement type="PaymentInfo" cardinality="many" qualifier="paymentInfos">
                <modifiers read="true" write="true" search="true" optional="true" partof="true"/>
                <custom-properties>
                    <property name="condition.query">
                        <value>"{original} is null"</value>
                    </property>
                </custom-properties>
            </targetElement>
        </relation>

        <!-- hmc legacy - Saved values -->
        <relation code="SavedValueEntriesRelation" autocreate="true" generate="true" localized="false">
            <sourceElement type="SavedValues" qualifier="parent" cardinality="one">
                <modifiers read="true" write="false" initial="true" optional="false" search="true"/>
            </sourceElement>

            <targetElement type="SavedValueEntry" qualifier="savedValuesEntries" cardinality="many"
                           collectiontype="set">
                <modifiers read="true" write="false" optional="true" partof="true"/>
            </targetElement>
        </relation>

        <relation code="ItemSavedValuesRelation" autocreate="true" generate="true" localized="false">
            <sourceElement type="Item" qualifier="modifiedItem" cardinality="one">
                <modifiers read="true" write="false" initial="true" optional="true" search="true"/>
            </sourceElement>

            <targetElement type="SavedValues" qualifier="savedValues" cardinality="many" collectiontype="set"
                           ordered="true">
                <modifiers read="true" write="false" optional="true" partof="false"/>
                <model generate="false"/>
            </targetElement>
        </relation>

    </relations>

    <itemtypes>

        <!--
         modifier defaults

            inital      = false
            read        = true
            write       = true
            optional    = true
            private     = false
            search      = true

   -->

        <!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
        ~~ base item types
        ~~ ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->

        <itemtype code="Item"
                  extends=""
                  jaloclass="de.hybris.platform.jalo.Item"
                  deployment="de.hybris.platform.persistence.Item"
                  autocreate="true"
                  generate="false"
                  abstract="true">
            <attributes>
                <attribute autocreate="true" qualifier="creationtime" type="java.util.Date">
                    <persistence type="cmp" qualifier="creationTimestampInternal"/>
                    <modifiers read="true" write="false" search="true" optional="true" initial="true"/>
                </attribute>
                <attribute autocreate="true" qualifier="modifiedtime" type="java.util.Date">
                    <persistence type="cmp" qualifier="modifiedTimestampInternal"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                </attribute>
                <attribute autocreate="true" qualifier="itemtype" type="ComposedType">
                    <persistence type="cmp" qualifier="typePkString"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                </attribute>
                <attribute autocreate="true" qualifier="owner" type="Item">
                    <persistence type="cmp" qualifier="ownerPkString"/>
                    <modifiers read="true" write="false" search="true" optional="true" private="false" initial="true"/>
                </attribute>
                <attribute autocreate="true" qualifier="pk" type="de.hybris.platform.core.PK">
                    <persistence type="cmp" qualifier="pkString"/>
                    <modifiers read="true" write="false" search="true" optional="false"/>
                </attribute>
                <attribute autocreate="true" qualifier="sealed" type="boolean">
                    <persistence type="property" qualifier="sealed"/>
                    <modifiers read="true" write="false" search="true" optional="true"/>
                </attribute>
            </attributes>
        </itemtype>

        <itemtype code="ExtensibleItem"
                  extends="Item"
                  jaloclass="de.hybris.platform.jalo.ExtensibleItem"
                  deployment="de.hybris.platform.persistence.ExtensibleItem"
                  autocreate="true"
                  generate="false" abstract="true">
        </itemtype>

        <itemtype code="LocalizableItem"
                  extends="ExtensibleItem"
                  jaloclass="de.hybris.platform.jalo.c2l.LocalizableItem"
                  deployment="de.hybris.platform.persistence.c2l.LocalizableItem"
                  autocreate="true"
                  generate="false" abstract="true">
        </itemtype>

        <itemtype code="GenericItem"
                  extends="LocalizableItem"
                  jaloclass="de.hybris.platform.jalo.GenericItem"
                  deployment="de.hybris.platform.persistence.GenericItem"
                  autocreate="true"
                  generate="false">
        </itemtype>

        <!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
        ~~ meta types
        ~~ ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->

        <itemtype code="TypeManagerManaged"
                  extends="LocalizableItem"
                  deployment="de.hybris.platform.persistence.type.TypeManagerManaged"
                  generate="false"
                  autocreate="true"
                  abstract="true"
                  jaloclass="de.hybris.platform.jalo.type.TypeManagerManaged">
            <custom-properties>
                <property name="systemType">
                    <value>java.lang.Boolean.TRUE</value>
                </property>
                <property name="legacyPersistence">
                    <value>java.lang.Boolean.TRUE</value>
                </property>
            </custom-properties>
            <attributes>
                <attribute autocreate="true" qualifier="name" type="localized:java.lang.String">
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                    <custom-properties>
                        <property name="hmcIndexField">
                            <value>"thefield"</value>
                        </property>
                    </custom-properties>
                </attribute>
                <attribute autocreate="true" qualifier="extensionName" type="java.lang.String">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" removable="false" optional="true"/>
                    <custom-properties>
                        <property name="hmcIndexField">
                            <value>"thefield"</value>
                        </property>
                    </custom-properties>
                </attribute>
                <attribute autocreate="true" qualifier="deprecated" type="java.lang.Boolean">
                    <persistence type="property"/>
                    <!-- the 'deprecated' attribute MUST be a dump property since it is set during initialization -->
                    <modifiers read="true" write="false" search="true" removable="false" optional="false"
                               dontOptimize="true"/>
                </attribute>
                <attribute autocreate="true" qualifier="autocreate" type="java.lang.Boolean">
                    <defaultvalue>
                        java.lang.Boolean.TRUE
                    </defaultvalue>
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" removable="false" optional="false"/>
                </attribute>
                <attribute autocreate="true" qualifier="generate" type="java.lang.Boolean">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" removable="false" optional="false"/>
                </attribute>
            </attributes>
        </itemtype>


        <itemtype code="Type"
                  extends="TypeManagerManaged"
                  jaloclass="de.hybris.platform.jalo.type.Type"
                  deployment="de.hybris.platform.persistence.type.Type"
                  autocreate="true"
                  generate="false" abstract="true">
            <attributes>
                <attribute autocreate="true" qualifier="xmldefinition" type="java.lang.String">
                    <persistence type="jalo"/>
                    <modifiers read="true" write="false" removable="true" search="false" optional="true"/>
                </attribute>
                <attribute autocreate="true" qualifier="code" type="java.lang.String">
                    <persistence type="cmp" qualifier="internalCode"/>
                    <modifiers read="true" write="false" search="true" initial="true" optional="false" unique="true"/>
                    <custom-properties>
                        <property name="hmcIndexField">
                            <value>"thefield"</value>
                        </property>
                    </custom-properties>
                </attribute>
                <attribute autocreate="true" qualifier="defaultValue" type="java.lang.Object">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true" private="false"/>
                </attribute>
                <attribute autocreate="true" qualifier="description" type="localized:java.lang.String">
                    <persistence type="property">
                        <columntype>
                            <value>HYBRIS.LONG_STRING</value>
                        </columntype>
                    </persistence>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <custom-properties>
                        <property name="hmcIndexField">
                            <value>"thefield"</value>
                        </property>
                    </custom-properties>
                </attribute>
            </attributes>
        </itemtype>

        <itemtype code="ComposedType"
                  extends="Type"
                  jaloclass="de.hybris.platform.jalo.type.ComposedType"
                  deployment="de.hybris.platform.persistence.type.ComposedType"
                  autocreate="true"
                  generate="false">
            <attributes>
                <attribute autocreate="true" qualifier="abstract" type="java.lang.Boolean">
                    <persistence type="jalo"/>
                    <modifiers read="true" write="false" removable="true" search="false" optional="false"/>
                </attribute>
                <attribute autocreate="true" qualifier="declaredattributedescriptors" type="attributeSet">
                    <persistence type="jalo"/>
                    <modifiers read="true" write="true" partof="true" search="false" optional="true"/>
                </attribute>
                <attribute autocreate="true" qualifier="dumpPropertyTable" type="java.lang.String">
                    <persistence type="jalo"/>
                    <modifiers read="true" write="false" removable="true" search="false" optional="true"/>
                </attribute>
                <attribute autocreate="true" qualifier="attributedescriptors" type="attributeSet">
                    <persistence type="jalo"/>
                    <modifiers read="true" write="false" partof="true" search="false" optional="true" private="true"/>
                </attribute>
                <attribute autocreate="true" qualifier="inheritancePathString" type="java.lang.String">
                    <persistence type="cmp" qualifier="inheritancePathStringInternal"/>
                    <modifiers read="true" write="false" private="true" search="true" optional="true"/>
                </attribute>
                <attribute autocreate="true" qualifier="inheritedattributedescriptors" type="attributeSet">
                    <persistence type="jalo"/>
                    <modifiers read="true" write="false" partof="true" search="false" optional="true"/>
                </attribute>
                <attribute autocreate="true" qualifier="jaloclass" type="java.lang.Class">
                    <persistence type="cmp" qualifier="jaloClassName"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                </attribute>
                <attribute autocreate="true" qualifier="jndiName" type="java.lang.String">
                    <persistence type="cmp" qualifier="itemJNDIName"/>
                    <modifiers read="true" write="false" search="true" removable="true" optional="true"/>
                </attribute>
                <attribute autocreate="true" qualifier="singleton" type="java.lang.Boolean">
                    <persistence type="cmp" qualifier="singletonFlag"/>
                    <modifiers read="true" write="true" search="true" optional="false"/>
                </attribute>
                <attribute autocreate="true" qualifier="jaloonly" type="java.lang.Boolean">
                    <persistence type="property"/>
                    <defaultvalue>java.lang.Boolean.FALSE</defaultvalue>
                    <modifiers read="true" write="true" search="true" optional="false"/>
                </attribute>
                <attribute autocreate="true" qualifier="dynamic" type="java.lang.Boolean">
                    <persistence type="property"/>
                    <defaultvalue>java.lang.Boolean.FALSE</defaultvalue>
                    <modifiers read="true" write="false" initial="false" search="true" optional="false"/>
                </attribute>
                <attribute autocreate="true" qualifier="subtypes" type="subTypesSet">
                    <persistence type="jalo"/>
                    <modifiers read="true" write="false" search="false" optional="true"/>
                </attribute>
                <attribute autocreate="true" qualifier="superType" type="ComposedType">
                    <persistence type="cmp" qualifier="superTypePK"/>
                    <modifiers read="true" write="false" search="true" initial="true" optional="false"/>
                </attribute>
                <attribute autocreate="true" qualifier="table" type="java.lang.String">
                    <persistence type="jalo"/>
                    <modifiers read="true" write="false" search="false" removable="true" optional="true"/>
                </attribute>
                <attribute autocreate="true" qualifier="allSuperTypes" type="ComposedTypeCollection">
                    <persistence type="jalo"/>
                    <modifiers read="true" write="false" search="false" removable="false" optional="true"/>
                </attribute>
                <attribute autocreate="true" qualifier="allSubTypes" type="ComposedTypeCollection">
                    <persistence type="jalo"/>
                    <modifiers read="true" write="false" search="false" removable="false" optional="true"/>
                </attribute>
                <attribute qualifier="legacyPersistence" type="java.lang.Boolean">
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <defaultvalue>Boolean.FALSE</defaultvalue>
                    <persistence type="property"/>
                </attribute>
            </attributes>
            <indexes>
                <index name="ComposedTypeSuperTypePKIDX">
                    <key attribute="superType"/>
                </index>
            </indexes>
        </itemtype>

        <itemtype code="Descriptor"
                  extends="TypeManagerManaged"
                  deployment="de.hybris.platform.persistence.type.Descriptor"
                  generate="false"
                  autocreate="true"
                  jaloclass="de.hybris.platform.jalo.type.Descriptor" abstract="true">
            <attributes>
                <attribute autocreate="true" qualifier="qualifier" type="java.lang.String">
                    <persistence type="cmp" qualifier="qualifierInternal"/>
                    <modifiers read="true" write="false" search="true" optional="false" initial="true" unique="true"/>
                    <custom-properties>
                        <property name="hmcIndexField">
                            <value>"thefield"</value>
                        </property>
                    </custom-properties>
                </attribute>
                <attribute autocreate="true" qualifier="attributeType" type="Type">
                    <persistence type="cmp" qualifier="attributeTypePK"/>
                    <modifiers read="true" write="true" search="true" optional="false" initial="true"/>
                </attribute>
            </attributes>
        </itemtype>

        <itemtype code="CollectionType"
                  extends="Type"
                  jaloclass="de.hybris.platform.jalo.type.CollectionType"
                  deployment="de.hybris.platform.persistence.type.CollectionType"
                  autocreate="true"
                  generate="false">
            <attributes>
                <attribute autocreate="true" qualifier="elementType" type="Type">
                    <persistence type="cmp" qualifier="elementTypePK"/>
                    <modifiers read="true" write="false" search="true" initial="true" optional="false"/>
                </attribute>
                <attribute autocreate="true" qualifier="typeOfCollection" type="TypeOfCollectionEnum">
                    <persistence type="jalo"/>
                    <modifiers read="true" write="false" search="false" initial="true" optional="true"/>
                </attribute>
                <attribute autocreate="true" qualifier="typeOfCollectionInternal" type="java.lang.Integer">
                    <persistence type="cmp" qualifier="typeOfCollection"/>
                    <modifiers read="true" write="false" search="true" initial="false" optional="true" private="true"/>
                </attribute>
            </attributes>
        </itemtype>

        <itemtype code="MapType"
                  extends="Type"
                  jaloclass="de.hybris.platform.jalo.type.MapType"
                  deployment="de.hybris.platform.persistence.type.MapType"
                  autocreate="true"
                  generate="false">
            <attributes>
                <attribute autocreate="true" qualifier="argumentType" type="Type">
                    <persistence type="cmp" qualifier="argumentTypePK"/>
                    <modifiers read="true" write="false" search="false" initial="true" optional="false"/>
                </attribute>
                <attribute autocreate="true" qualifier="returntype" type="Type">
                    <persistence type="cmp" qualifier="returnTypePK"/>
                    <modifiers read="true" write="false" search="false" initial="true" optional="false"/>
                </attribute>
            </attributes>
        </itemtype>

        <itemtype code="AtomicType"
                  extends="Type"
                  jaloclass="de.hybris.platform.jalo.type.AtomicType"
                  deployment="de.hybris.platform.persistence.type.AtomicType"
                  autocreate="true"
                  generate="false">
            <attributes>
                <attribute autocreate="true" redeclare="true" qualifier="code" type="java.lang.String">
                    <persistence type="cmp" qualifier="internalCode"/>
                    <modifiers read="true" write="false" search="true" initial="false" optional="true"/>
                </attribute>
                <attribute autocreate="true" qualifier="inheritancePathString" type="java.lang.String">
                    <persistence type="cmp" qualifier="inheritancePathStringInternal"/>
                    <modifiers read="true" write="true" search="false" private="true" optional="false"/>
                </attribute>
                <attribute autocreate="true" qualifier="javaClass" type="java.lang.Class">
                    <persistence type="cmp" qualifier="javaClassName"/>
                    <modifiers read="true" write="false" search="true" initial="true" optional="false"/>
                </attribute>
                <attribute autocreate="true" qualifier="subtypes" type="AtomicTypeSubtypeCollection">
                    <modifiers read="true" write="false" search="false" optional="false"/>
                    <persistence type="jalo"/>
                </attribute>
                <attribute autocreate="true" qualifier="superType" type="AtomicType">
                    <persistence type="cmp" qualifier="superTypePK"/>
                    <modifiers read="true" write="false" initial="true" search="false" private="false" optional="true"/>
                </attribute>
            </attributes>
        </itemtype>

        <itemtype code="AttributeDescriptor"
                  extends="Descriptor"
                  jaloclass="de.hybris.platform.jalo.type.AttributeDescriptor"
                  deployment="de.hybris.platform.persistence.type.AttributeDescriptor"
                  autocreate="true"
                  generate="false">
            <attributes>
                <attribute qualifier="databaseColumn" type="java.lang.String" autocreate="true">
                    <persistence type="cmp" qualifier="columnNameInternal"/>
                    <modifiers read="true" write="true" search="false" optional="true" removable="true"/>
                </attribute>
                <attribute qualifier="defaultValue" type="java.lang.Object" autocreate="true">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                </attribute>
                <attribute qualifier="defaultValueDefinitionString" type="java.lang.String" autocreate="true">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true" private="true"/>
                </attribute>
                <attribute qualifier="enclosingType" type="ComposedType" autocreate="true">
                    <persistence type="cmp" qualifier="enclosingTypePK"/>
                    <modifiers read="true" write="false" search="true" optional="false" initial="true" unique="true"/>
                </attribute>
                <attribute qualifier="declaringEnclosingType" type="ComposedType" autocreate="true">
                    <persistence type="jalo"/>
                    <modifiers read="true" write="false" search="false" optional="true" initial="false"/>
                </attribute>
                <attribute qualifier="description" type="localized:java.lang.String" autocreate="true">
                    <persistence type="property">
                        <columntype>
                            <value>HYBRIS.LONG_STRING</value>
                        </columntype>
                    </persistence>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <custom-properties>
                        <property name="hmcIndexField">
                            <value>"thefield"</value>
                        </property>
                    </custom-properties>
                </attribute>
                <attribute qualifier="persistenceClass" type="java.lang.Class" autocreate="true">
                    <persistence type="jalo"/>
                    <modifiers read="true" write="false" search="false" optional="true" removable="false"/>
                </attribute>
                <attribute qualifier="persistenceQualifier" type="java.lang.String" autocreate="true">
                    <persistence type="cmp" qualifier="persistenceQualifierInternal"/>
                    <modifiers read="true" write="false" search="true" optional="false" private="true"/>
                </attribute>
                <attribute qualifier="persistenceType" type="AtomicType" autocreate="true">
                    <persistence type="cmp" qualifier="persistenceTypePK"/>
                    <modifiers read="true" write="false" search="true" optional="false" private="true"/>
                </attribute>
                <attribute qualifier="attributeHandler" type="java.lang.String" autocreate="true">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" optional="true"/>
                </attribute>
                <attribute qualifier="selectionOf" type="AttributeDescriptor" autocreate="true">
                    <persistence type="cmp" qualifier="selectionDescriptorPK"/>
                    <modifiers read="true" write="false" search="false" optional="true" private="false"/>
                </attribute>
                <attribute qualifier="proposedDatabaseColumn" type="java.lang.String" autocreate="true">
                    <persistence type="jalo"/>
                    <modifiers read="true" write="false" removable="true" search="false" optional="true"/>
                </attribute>

                <!-- modifiers -->
                <attribute qualifier="modifiers" type="java.lang.Integer" autocreate="true">
                    <persistence type="cmp" qualifier="attributeModifiers"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                </attribute>

                <attribute qualifier="initial" type="java.lang.Boolean" autocreate="true">
                    <persistence type="jalo"/>
                    <defaultvalue>java.lang.Boolean.FALSE</defaultvalue>
                    <modifiers read="true" write="true" optional="false"/>
                </attribute>
                <attribute qualifier="localized" type="java.lang.Boolean" autocreate="true">
                    <persistence type="jalo"/>
                    <modifiers read="true" write="false" initial="false" optional="false"/>
                </attribute>
                <attribute qualifier="optional" type="java.lang.Boolean" autocreate="true">
                    <defaultvalue>java.lang.Boolean.TRUE</defaultvalue>
                    <persistence type="jalo"/>
                    <modifiers read="true" write="true" optional="false"/>
                </attribute>
                <attribute qualifier="partOf" type="java.lang.Boolean" autocreate="true">
                    <persistence type="jalo"/>
                    <modifiers read="true" write="true" optional="false"/>
                </attribute>
                <attribute qualifier="unique" type="java.lang.Boolean" autocreate="true">
                    <persistence type="property"/>
                    <defaultvalue>java.lang.Boolean.FALSE</defaultvalue>
                    <modifiers read="true" write="true" optional="false"/>
                </attribute>
                <attribute qualifier="private" type="java.lang.Boolean" autocreate="true">
                    <persistence type="jalo"/>
                    <defaultvalue>java.lang.Boolean.FALSE</defaultvalue>
                    <modifiers read="true" write="true" optional="false"/>
                </attribute>
                <attribute qualifier="property" type="java.lang.Boolean" autocreate="true">
                    <defaultvalue>java.lang.Boolean.TRUE</defaultvalue>
                    <persistence type="jalo"/>
                    <modifiers read="true" write="false" initial="true" optional="false"/>
                </attribute>
                <attribute qualifier="readable" type="java.lang.Boolean" autocreate="true">
                    <defaultvalue>java.lang.Boolean.TRUE</defaultvalue>
                    <persistence type="jalo"/>
                    <modifiers read="true" write="true" optional="false"/>
                </attribute>
                <attribute qualifier="removable" type="java.lang.Boolean" autocreate="true">
                    <defaultvalue>java.lang.Boolean.TRUE</defaultvalue>
                    <persistence type="jalo"/>
                    <modifiers read="true" write="true" optional="false"/>
                </attribute>
                <attribute qualifier="search" type="java.lang.Boolean" autocreate="true">
                    <defaultvalue>java.lang.Boolean.TRUE</defaultvalue>
                    <persistence type="jalo"/>
                    <modifiers read="true" write="true" optional="false"/>
                </attribute>
                <attribute qualifier="writable" type="java.lang.Boolean" autocreate="true">
                    <defaultvalue>java.lang.Boolean.TRUE</defaultvalue>
                    <persistence type="jalo"/>
                    <modifiers read="true" write="true" optional="false"/>
                </attribute>
                <attribute qualifier="encrypted" type="java.lang.Boolean" autocreate="true">
                    <defaultvalue>java.lang.Boolean.FALSE</defaultvalue>
                    <persistence type="jalo"/>
                    <modifiers read="true" write="true" optional="false"/>
                </attribute>
                <attribute qualifier="primitive" type="java.lang.Boolean" autocreate="true">
                    <defaultvalue>java.lang.Boolean.FALSE</defaultvalue>
                    <persistence type="jalo"/>
                    <modifiers read="true" write="true" optional="false"/>
                </attribute>
                <attribute qualifier="hiddenForUI" type="java.lang.Boolean">
                    <defaultvalue>Boolean.FALSE</defaultvalue>
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="false"/>
                </attribute>
                <attribute qualifier="readOnlyForUI" type="java.lang.Boolean">
                    <defaultvalue>Boolean.FALSE</defaultvalue>
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="false"/>
                </attribute>
            </attributes>
        </itemtype>

        <itemtype code="ConfigAttributeDescriptor"
                  extends="AttributeDescriptor"
                  jaloclass="de.hybris.platform.util.ConfigAttributeDescriptor"
                  autocreate="true"
                  generate="false">
            <attributes>
                <attribute qualifier="externalQualifier" type="java.lang.String">
                    <modifiers search="true" write="true" optional="false" read="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="storeInDatabase" type="java.lang.Boolean">
                    <defaultvalue>Boolean.FALSE</defaultvalue>
                    <modifiers search="true" write="true" optional="false" read="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="needRestart" type="java.lang.Boolean">
                    <defaultvalue>Boolean.FALSE</defaultvalue>
                    <modifiers search="true" write="true" optional="false" read="true"/>
                    <persistence type="property"/>
                </attribute>
            </attributes>
        </itemtype>

        <itemtype code="ViewAttributeDescriptor"
                  extends="AttributeDescriptor"
                  jaloclass="de.hybris.platform.jalo.type.ViewAttributeDescriptor"
                  autocreate="true"
                  generate="false">
            <attributes>
                <attribute qualifier="param" type="java.lang.Boolean">
                    <defaultvalue>Boolean.FALSE</defaultvalue>
                    <modifiers search="true" write="true" optional="false" read="true" initial="true" private="false"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="position" type="java.lang.Integer">
                    <modifiers search="true" write="true" optional="true" read="true" private="true"/>
                    <persistence type="property"/>
                </attribute>
            </attributes>
        </itemtype>

        <itemtype code="ExpressionAttributeDescriptor"
                  extends="AttributeDescriptor"
                  jaloclass="de.hybris.platform.jalo.type.ExpressionAttributeDescriptor"
                  autocreate="true"
                  generate="false">
            <attributes>
                <attribute qualifier="defaultValueExpression" type="java.lang.String">
                    <modifiers search="true" write="true" optional="false" read="true" initial="true" private="true"/>
                    <persistence type="property"/>
                </attribute>
            </attributes>
        </itemtype>

        <itemtype code="ConfigProxyMetaType"
                  extends="ComposedType"
                  autocreate="true"
                  generate="false">
            <attributes>

            </attributes>
        </itemtype>

        <itemtype code="ViewType"
                  extends="ComposedType"
                  jaloclass="de.hybris.platform.jalo.type.ViewType"
                  autocreate="true"
                  generate="false">
            <custom-properties>
                <property name="legacyPersistence">
                    <value>java.lang.Boolean.TRUE</value>
                </property>
            </custom-properties>
            <attributes>
                <attribute qualifier="superType" type="ComposedType" autocreate="true" redeclare="true">
                    <persistence type="cmp" qualifier="superTypePK"/>
                    <modifiers read="false" write="false" search="true" initial="false" optional="true"/>
                </attribute>
                <attribute qualifier="columns" type="ViewAttributeList" autocreate="true">
                    <persistence type="jalo"/>
                    <modifiers read="true" write="true" partof="true" search="false" optional="true"/>
                </attribute>
                <attribute qualifier="params" type="ViewAttributeSet" autocreate="true">
                    <persistence type="jalo"/>
                    <modifiers read="true" write="true" partof="true" search="false" optional="true"/>
                </attribute>
                <attribute qualifier="query" type="java.lang.String" autocreate="true">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="false" dontOptimize="true"/>
                </attribute>
            </attributes>
        </itemtype>

        <itemtype code="RelationTypeUsageView"
                  extends="Item"
                  autocreate="true"
                  generate="false"
                  metatype="ViewType"
                  jaloclass="de.hybris.platform.util.ViewResultItem"
                  jaloonly="true">

            <custom-properties>
                <property name="legacyPersistence">
                    <value>java.lang.Boolean.TRUE</value>
                </property>
                <property name="query">
                    <value>
                        "SELECT DISTINCT {rt:PK}, {rt:code} \n"+
                        "FROM {RelationMetaType AS rt JOIN RelationDescriptor AS ra ON {rt:PK}={ra:relationType} }\n"+
                        "WHERE {ra:attributeType} IN ( {{\n"+
                        " SELECT {PK} FROM {CollectionType}\n"+
                        " WHERE {elementType} IN ( ?composedType, ?composedType.allsupertypes )\n"+
                        "}} ) OR {ra:attributeType} IN ( {{\n"+
                        " SELECT {mt:PK} FROM {MapType AS mt JOIN CollectionType AS ct ON {mt:returnType}={ct:PK} }\n"+
                        " WHERE {ct:elementType} IN ( ?composedType, ?composedType.allsupertypes )\n"+
                        "}} )\n"+
                        "ORDER BY {rt:code} ASC"
                    </value>
                </property>
            </custom-properties>
            <attributes>
                <attribute autocreate="true" qualifier="relationType" type="RelationMetaType"
                           metatype="ViewAttributeDescriptor">
                    <persistence type="jalo"/>
                    <modifiers read="true" write="false" search="false" initial="false" optional="false"/>
                    <custom-properties>
                        <property name="param">
                            <value>Boolean.FALSE</value>
                        </property>
                        <property name="position">
                            <value>Integer.valueOf(0)</value>
                        </property>
                    </custom-properties>
                </attribute>
                <attribute autocreate="true" qualifier="composedType" type="ComposedType"
                           metatype="ViewAttributeDescriptor">
                    <persistence type="jalo"/>
                    <modifiers read="true" write="false" search="true" initial="false" optional="false"/>
                    <custom-properties>
                        <property name="param">
                            <value>Boolean.TRUE</value>
                        </property>
                    </custom-properties>
                </attribute>
            </attributes>
        </itemtype>

        <itemtype code="CustomerOrderOverview" extends="Item" autocreate="true" generate="false" metatype="ViewType"
                  jaloonly="true"
                  jaloclass="de.hybris.platform.util.ViewResultItem">
            <custom-properties>
                <property name="legacyPersistence">
                    <value>java.lang.Boolean.TRUE</value>
                </property>
                <property name="query">
                    <value>
                        "SELECT \n"+
                        " {c:PK} as cust, \n"+
                        " COUNT({o:PK}) as amnt, \n"+
                        " SUM( CASE WHEN {o:totalPrice} IS NULL THEN 0 ELSE {o:totalPrice} END ) AS tot \n"+
                        "FROM \n"+
                        " {Customer AS c LEFT JOIN Order AS o \n"+
                        " ON {c:PK}={o:user} } \n"+
                        "WHERE {o:currency} IS NULL OR {o:currency} = ?currency \n"+
                        "GROUP BY {c:PK}, {c:uid} \n"+
                        "ORDER BY tot DESC, {c:uid} ASC "
                    </value>
                </property>
            </custom-properties>
            <attributes>
                <attribute autocreate="true" qualifier="customer" type="Customer" metatype="ViewAttributeDescriptor">
                    <persistence type="jalo"/>
                    <modifiers read="true" write="false" search="false" initial="false" optional="false"/>
                    <custom-properties>
                        <property name="param">
                            <value>Boolean.FALSE</value>
                        </property>
                        <property name="position">
                            <value>Integer.valueOf(0)</value>
                        </property>
                    </custom-properties>
                </attribute>
                <attribute autocreate="true" qualifier="orderCount" type="java.lang.Integer"
                           metatype="ViewAttributeDescriptor">
                    <persistence type="jalo"/>
                    <modifiers read="true" write="false" search="false" initial="false" optional="false"/>
                    <custom-properties>
                        <property name="param">
                            <value>Boolean.FALSE</value>
                        </property>
                        <property name="position">
                            <value>Integer.valueOf(1)</value>
                        </property>
                    </custom-properties>
                </attribute>
                <attribute autocreate="true" qualifier="orderTotals" type="java.lang.Double"
                           metatype="ViewAttributeDescriptor">
                    <persistence type="jalo"/>
                    <modifiers read="true" write="false" search="false" initial="false" optional="false"/>
                    <custom-properties>
                        <property name="param">
                            <value>Boolean.FALSE</value>
                        </property>
                        <property name="position">
                            <value>Integer.valueOf(2)</value>
                        </property>
                    </custom-properties>
                </attribute>
                <attribute autocreate="true" qualifier="currency" type="Currency" metatype="ViewAttributeDescriptor">
                    <persistence type="jalo"/>
                    <modifiers read="true" write="false" search="true" initial="false" optional="false"/>
                    <custom-properties>
                        <property name="param">
                            <value>Boolean.TRUE</value>
                        </property>
                    </custom-properties>
                </attribute>
            </attributes>
        </itemtype>

        <itemtype code="RelationDescriptor"
                  extends="AttributeDescriptor"
                  jaloclass="de.hybris.platform.jalo.type.RelationDescriptor"
                  autocreate="true"
                  generate="false">
            <attributes>
                <attribute autocreate="true" qualifier="isSource" type="java.lang.Boolean">
                    <modifiers read="true" write="false" search="true" initial="false" optional="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute autocreate="true" qualifier="ordered" type="java.lang.Boolean">
                    <modifiers read="true" write="false" search="true" initial="false" optional="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute autocreate="true" qualifier="relationName" type="java.lang.String">
                    <modifiers read="true" write="false" search="true" initial="false" optional="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute autocreate="true" qualifier="relationType" type="RelationMetaType">
                    <modifiers read="true" write="false" search="true" initial="false" optional="true"/>
                    <persistence type="property"/>
                </attribute>
            </attributes>
        </itemtype>

        <itemtype code="EnumerationMetaType"
                  extends="ComposedType"
                  jaloclass="de.hybris.platform.jalo.enumeration.EnumerationType"
                  autocreate="true"
                  generate="false">
            <attributes>
                <!-- supertype is always set during creation,you cannot choose your own - so we disable this field -->
                <attribute autocreate="true" redeclare="true" qualifier="superType" type="ComposedType">
                    <persistence type="cmp" qualifier="superTypePK"/>
                    <modifiers read="false" write="false" search="true" initial="false" optional="true" private="true"/>
                </attribute>
                <attribute autocreate="true" redeclare="true" qualifier="subtypes" type="subTypesSet">
                    <persistence type="jalo"/>
                    <modifiers read="false" write="false" search="true" initial="false" optional="true" private="true"/>
                </attribute>
                <attribute autocreate="true" qualifier="comparationAttribute" type="AttributeDescriptor">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" initial="false" optional="true"/>
                </attribute>
                <attribute autocreate="true" qualifier="values" type="ItemCollection">
                    <persistence type="jalo"/>
                    <modifiers read="true" write="true" search="false" initial="false" optional="true" partof="true"/>
                </attribute>
                <attribute autocreate="true" qualifier="valueType" type="ComposedType">
                    <persistence type="cmp" qualifier="superTypePK"/>
                    <modifiers read="true" write="false" search="true" initial="true" optional="true"/>
                </attribute>
                <attribute autocreate="true" qualifier="isSorted" type="java.lang.Boolean">
                    <persistence type="jalo"/>
                    <modifiers read="true" write="false" search="false" initial="false" optional="false"/>
                </attribute>
                <attribute autocreate="true" qualifier="isResortable" type="java.lang.Boolean">
                    <persistence type="jalo"/>
                    <modifiers read="true" write="false" search="false" initial="false" optional="false"/>
                </attribute>
            </attributes>
        </itemtype>

        <itemtype code="RelationMetaType"
                  extends="ComposedType"
                  jaloclass="de.hybris.platform.jalo.type.RelationType"
                  autocreate="true"
                  generate="false">
            <attributes>
                <!-- supertype is always set during creation,you cannot choose your own - so we disable this field -->
                <attribute qualifier="superType" autocreate="true" redeclare="true" type="ComposedType">
                    <persistence type="cmp" qualifier="superTypePK"/>
                    <modifiers read="true" write="false" search="true" initial="false" optional="true"/>
                </attribute>
                <attribute qualifier="localized" autocreate="true" type="java.lang.Boolean">
                    <persistence type="property"/>
                    <modifiers read="true" write="false" search="true" initial="true" optional="false"/>
                </attribute>
                <attribute qualifier="sourceAttribute" autocreate="true" type="RelationDescriptor">
                    <persistence type="property"/>
                    <modifiers read="true" write="false" search="true" initial="true" optional="true"/>
                </attribute>
                <attribute qualifier="targetAttribute" autocreate="true" type="RelationDescriptor">
                    <persistence type="property"/>
                    <modifiers read="true" write="false" search="true" initial="true" optional="true"/>
                </attribute>
                <attribute qualifier="sourceType" autocreate="true" type="ComposedType">
                    <persistence type="property"/>
                    <modifiers read="true" write="false" search="false" initial="true" optional="true"/>
                </attribute>
                <attribute qualifier="targetType" autocreate="true" type="ComposedType">
                    <persistence type="property"/>
                    <modifiers read="true" write="false" search="false" initial="true" optional="true"/>
                </attribute>
                <attribute qualifier="sourceTypeRole" autocreate="true" type="java.lang.String">
                    <persistence type="jalo"/>
                    <modifiers read="true" write="false" search="false" initial="true" optional="true"/>
                </attribute>
                <attribute qualifier="targetTypeRole" autocreate="true" type="java.lang.String">
                    <persistence type="jalo"/>
                    <modifiers read="true" write="false" search="false" initial="true" optional="true"/>
                </attribute>
                <attribute qualifier="sourceNavigable" autocreate="true" type="java.lang.Boolean">
                    <persistence type="property"/>
                    <modifiers read="true" write="false" search="false" initial="true" optional="true"/>
                </attribute>
                <attribute qualifier="targetNavigable" autocreate="true" type="java.lang.Boolean">
                    <persistence type="property"/>
                    <modifiers read="true" write="false" search="false" initial="true" optional="true"/>
                </attribute>
                <!-- 1-n specific attributes -->
                <attribute qualifier="sourceTypeCardinality" autocreate="true" type="RelationEndCardinalityEnum">
                    <persistence type="jalo"/>
                    <modifiers read="true" write="false" search="false" initial="true" optional="true"/>
                </attribute>
                <attribute qualifier="targetTypeCardinality" autocreate="true" type="RelationEndCardinalityEnum">
                    <persistence type="jalo"/>
                    <modifiers read="true" write="false" search="false" initial="true" optional="true"/>
                </attribute>
                <attribute qualifier="orderingAttribute" autocreate="true" type="AttributeDescriptor">
                    <persistence type="property"/>
                    <modifiers read="true" write="false" search="true" initial="true" optional="true"/>
                </attribute>
                <attribute qualifier="localizationAttribute" autocreate="true" type="AttributeDescriptor">
                    <persistence type="property"/>
                    <modifiers read="true" write="false" search="true" initial="true" optional="true"/>
                </attribute>
            </attributes>
        </itemtype>

        <itemtype code="EnumerationValue"
                  extends="LocalizableItem"
                  jaloclass="de.hybris.platform.jalo.enumeration.EnumerationValue"
                  deployment="de.hybris.platform.persistence.enumeration.EnumerationValue"
                  autocreate="true"
                  generate="false">
            <custom-properties>
                <property name="systemType">
                    <value>java.lang.Boolean.TRUE</value>
                </property>
            </custom-properties>
            <attributes>
                <attribute autocreate="true" qualifier="code" type="java.lang.String">
                    <persistence type="cmp" qualifier="codeInternal"/>
                    <modifiers read="true" write="false" search="true" optional="false" initial="true" unique="true"/>
                    <custom-properties>
                        <property name="hmcIndexField">
                            <value>"thefield"</value>
                        </property>
                    </custom-properties>
                </attribute>
                <attribute autocreate="true" qualifier="codeLowerCase" type="java.lang.String">
                    <persistence type="cmp" qualifier="codeLowerCase"/>
                    <modifiers read="true" write="false" search="true" initial="true" optional="true"/>
                </attribute>
                <!-- redeclare for setting unique flag -->
                <attribute autocreate="true" qualifier="itemtype" type="ComposedType" redeclare="true">
                    <modifiers read="true" write="true" search="true" optional="true" unique="true"/>
                </attribute>
                <attribute autocreate="true" qualifier="name" type="localized:java.lang.String">
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                    <custom-properties>
                        <property name="hmcIndexField">
                            <value>"thefield"</value>
                        </property>
                    </custom-properties>
                </attribute>
                <attribute autocreate="true" qualifier="sequenceNumber" type="java.lang.Integer">
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="cmp" qualifier="sequenceNumber"/>
                </attribute>
                <attribute autocreate="true" qualifier="deprecated" type="java.lang.Boolean">
                    <persistence type="property"/>
                    <modifiers read="true" write="false" search="true" removable="false" optional="true"
                               dontOptimize="true"/>
                </attribute>
                <attribute autocreate="true" qualifier="extensionName" type="java.lang.String">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" removable="false" optional="true"/>
                    <custom-properties>
                        <property name="hmcIndexField">
                            <value>"thefield"</value>
                        </property>
                    </custom-properties>
                </attribute>
                <attribute autocreate="true" qualifier="icon" type="Media">
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                </attribute>
            </attributes>
            <indexes>
                <index name="uniqueCodeIdx" unique="true">
                    <key attribute="itemtype"/>
                    <key attribute="codeLowerCase"/>
                </index>
            </indexes>
        </itemtype>

        <!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
        ~~ normal item types
        ~~ ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->

        <!-- links ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->

        <itemtype code="Link"
                  extends="ExtensibleItem"
                  jaloclass="de.hybris.platform.jalo.link.Link"
                  deployment="de.hybris.platform.persistence.link.Link"
                  autocreate="true"
                  generate="false">
            <attributes>
                <attribute autocreate="true" qualifier="language" type="Language">
                    <persistence type="property" qualifier="LanguagePK"/>
                    <modifiers read="true" write="true" search="true" optional="true" unique="true"/>
                </attribute>
                <attribute autocreate="true" qualifier="qualifier" type="java.lang.String">
                    <persistence type="cmp" qualifier="qualifierInternal"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                </attribute>
                <attribute autocreate="true" qualifier="source" type="Item">
                    <persistence type="cmp" qualifier="sourcePKInternal"/>
                    <modifiers read="true" write="true" search="true" optional="false" unique="true"/>
                </attribute>
                <attribute autocreate="true" qualifier="target" type="Item">
                    <persistence type="cmp" qualifier="targetPKInternal"/>
                    <modifiers read="true" write="true" search="true" optional="false" unique="true"/>
                </attribute>
                <attribute autocreate="true" qualifier="sequenceNumber" type="java.lang.Integer">
                    <defaultvalue>Integer.valueOf(0)</defaultvalue>
                    <persistence type="cmp" qualifier="sequenceNumberInternal"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                </attribute>
                <attribute autocreate="true" qualifier="reverseSequenceNumber" type="java.lang.Integer">
                    <defaultvalue>Integer.valueOf(0)</defaultvalue>
                    <persistence type="cmp" qualifier="reverseSequenceNumberInternal"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                </attribute>
            </attributes>
        </itemtype>

        <itemtype code="ProductMediaLink"
                  extends="Link"
                  jaloclass="de.hybris.platform.jalo.link.Link"
                  autocreate="true"
                  generate="false">
            <attributes>
                <attribute autocreate="true" redeclare="true" qualifier="source" type="Product">
                    <modifiers read="true" write="true" search="true" removable="false" optional="false" unique="true"/>
                </attribute>
                <attribute autocreate="true" redeclare="true" qualifier="target" type="Media">
                    <modifiers read="true" write="true" search="true" removable="true" optional="false" unique="true"/>
                </attribute>
            </attributes>
        </itemtype>


        <!-- user ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->

        <itemtype code="UserRight"
                  extends="GenericItem"
                  jaloclass="de.hybris.platform.jalo.security.UserRight"
                  autocreate="true"
                  generate="true">
            <deployment table="UserRights" typecode="29"/>
            <attributes>
                <attribute autocreate="true" qualifier="code" type="java.lang.String">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" initial="true" optional="false" unique="true"/>
                    <custom-properties>
                        <property name="hmcIndexField">
                            <value>"thefield"</value>
                        </property>
                    </custom-properties>
                </attribute>
                <attribute autocreate="true" qualifier="name" type="localized:java.lang.String">
                    <modifiers read="true" write="true" search="true" removable="true" optional="true"/>
                    <persistence type="property"/>
                    <custom-properties>
                        <property name="hmcIndexField">
                            <value>"thefield"</value>
                        </property>
                    </custom-properties>
                </attribute>
            </attributes>
        </itemtype>

        <itemtype code="Principal"
                  extends="GenericItem"
                  jaloclass="de.hybris.platform.jalo.security.Principal"
                  autocreate="true"
                  abstract="true"
                  generate="true">
            <attributes>
                <attribute autocreate="true" qualifier="description" type="java.lang.String" generate="true">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <custom-properties>
                        <property name="hmcIndexField">
                            <value>"thefield"</value>
                        </property>
                    </custom-properties>
                </attribute>
                <attribute autocreate="true" qualifier="name" type="java.lang.String" generate="true">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <custom-properties>
                        <property name="hmcIndexField">
                            <value>"thefield"</value>
                        </property>
                    </custom-properties>
                </attribute>
                <attribute autocreate="true" qualifier="displayName" type="localized:java.lang.String">
                    <persistence type="dynamic" attributeHandler="principalDisplayNameLocalizedAttributeHandler"/>
                    <modifiers read="true" write="false" search="false" optional="true"/>
                </attribute>
                <attribute autocreate="true" qualifier="uid" type="java.lang.String" generate="true">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="false" unique="true"/>
                    <custom-properties>
                        <property name="hmcIndexField">
                            <value>"thefield"</value>
                        </property>
                    </custom-properties>
                </attribute>
                <attribute autocreate="true" qualifier="allSearchRestrictions" type="RestrictionList">
                    <persistence type="dynamic"/>
                    <modifiers read="true" write="false" search="false" optional="true"/>
                    <model>
                        <getter name="allsearchrestrictions" deprecated="true" deprecatedSince="ages"/>
                    </model>
                </attribute>
                <attribute autocreate="true" qualifier="allGroups" type="PrincipalGroupSet">
                    <persistence type="dynamic"/>
                    <modifiers read="true" write="false" search="false" optional="true"/>
                    <model>
                        <getter name="allgroups" deprecated="true" deprecatedSince="ages"/>
                    </model>
                </attribute>
            </attributes>
            <indexes>
                <index name="UID" unique="true">
                    <key attribute="uid"/>
                </index>
            </indexes>
        </itemtype>


        <itemtype code="PrincipalGroup"
                  extends="Principal"
                  jaloclass="de.hybris.platform.jalo.security.PrincipalGroup"
                  abstract="true"
                  autocreate="true"
                  generate="true">
            <attributes>
                <attribute autocreate="true" qualifier="locName" type="localized:java.lang.String">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true" private="false"/>
                    <custom-properties>
                        <property name="hmcIndexField">
                            <value>"thefield"</value>
                        </property>
                    </custom-properties>
                    <model>
                        <getter name="locname" deprecated="true" deprecatedSince="ages"/>
                        <setter name="locname" deprecated="true" deprecatedSince="ages"/>
                    </model>
                </attribute>
                <attribute autocreate="true" redeclare="true" qualifier="displayName" type="localized:java.lang.String">
                    <persistence type="dynamic" attributeHandler="principalGroupDisplayNameLocalizedAttributeHandler"/>
                    <modifiers read="true" write="false" search="false" optional="true"/>
                </attribute>
                <attribute autocreate="true" qualifier="maxBruteForceLoginAttempts" type="java.lang.Integer">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                </attribute>
            </attributes>

        </itemtype>

        <itemtype code="User"
                  extends="Principal"
                  jaloclass="de.hybris.platform.jalo.user.User"
                  autocreate="true"
                  generate="true">
            <deployment table="Users" typecode="4" propertytable="UserProps"/>
            <attributes>
                <attribute autocreate="true" qualifier="currentTime" type="java.util.Date">
                    <modifiers read="true" write="false" search="false" optional="false"/>
                    <persistence type="dynamic"/>
                </attribute>
                <attribute autocreate="true" qualifier="currentDate" type="java.util.Date">
                    <modifiers read="true" write="false" search="false" optional="false"/>
                    <persistence type="dynamic"/>
                </attribute>
                <!-- Needs relation with condition -->
                <!--                <attribute autocreate="true" qualifier="addresses" type="AddressCollection">
                                    <modifiers read="true" write="true" search="false" optional="true" partof="true"/>
                                    <persistence type="jalo"/>
                                </attribute>-->
                <attribute autocreate="true" qualifier="defaultPaymentAddress" type="Address" isSelectionOf="addresses">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="false" optional="true"/>
                </attribute>
                <attribute autocreate="true" qualifier="defaultShipmentAddress" type="Address"
                           isSelectionOf="addresses">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="false" optional="true"/>
                </attribute>
                <attribute autocreate="true" qualifier="displayName" redeclare="true" type="localized:java.lang.String">
                    <persistence type="dynamic" attributeHandler="userDisplayNameLocalizedAttributeHandler"/>
                    <modifiers read="true" write="false" search="false" optional="true"/>
                </attribute>
                <attribute autocreate="true" qualifier="password" type="java.lang.String">
                    <persistence type="dynamic"/>
                    <modifiers read="false" write="true" search="true" optional="true"/>
                </attribute>
                <attribute autocreate="true" qualifier="passwordEncoding" type="java.lang.String">
                    <persistence type="property"/>
                    <defaultvalue>de.hybris.platform.persistence.SystemEJB.DEFAULT_ENCODING</defaultvalue>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                </attribute>
                <attribute autocreate="true" qualifier="encodedPassword" type="java.lang.String">
                    <persistence type="property" qualifier="Passwd">
                        <columntype>
                            <value>HYBRIS.LONG_STRING</value>
                        </columntype>
                    </persistence>
                    <modifiers read="true" write="true" search="true" optional="true" encrypted="true"/>
                </attribute>
                <attribute autocreate="true" qualifier="passwordAnswer" type="java.lang.String">
                    <modifiers read="true" write="true" search="true" optional="true" encrypted="true"/>
                    <persistence type="property">
                        <columntype>
                            <value>HYBRIS.LONG_STRING</value>
                        </columntype>
                    </persistence>
                </attribute>
                <attribute autocreate="true" qualifier="passwordQuestion" type="java.lang.String">
                    <modifiers read="true" write="true" search="true" optional="true" encrypted="true"/>
                    <persistence type="property">
                        <columntype>
                            <value>HYBRIS.LONG_STRING</value>
                        </columntype>
                    </persistence>
                </attribute>
                <!-- Needs relation with condition -->
                <!--
                                <attribute autocreate="true" qualifier="paymentInfos" type="PaymentInfoCollection">
                                    <modifiers read="true" write="true" search="false" optional="true" partof="true"/>
                                    <persistence type="jalo"/>
                                </attribute>
                -->
                <attribute autocreate="true" qualifier="sessionLanguage" type="Language">
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute autocreate="true" qualifier="sessionCurrency" type="Currency">
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute autocreate="true" qualifier="loginDisabled" type="boolean">
                    <modifiers read="true" write="true" search="true" optional="false"/>
                    <persistence type="property"/>
                    <description>Determines whether user is allowed to login to system.</description>
                </attribute>
                <attribute autocreate="true" qualifier="lastLogin" type="java.util.Date">
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="hmcLoginDisabled" type="java.lang.Boolean">
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="retentionState" type="RetentionState">
                    <description>User retention state, avoiding being picked up again by cronjob if processed</description>
                    <persistence type="property" />
                </attribute>
                <attribute autocreate="true" qualifier="userprofile" type="UserProfile">
                    <modifiers read="true" write="true" partof="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="deactivationDate" type="java.util.Date">
                    <description>The deactivation date for the user account</description>
                    <persistence type="property" />
                </attribute>
                <attribute autocreate="true" qualifier="randomToken" type="java.lang.String">
                    <modifiers read="true" write="true" search="true" optional="true" encrypted="true"/>
                    <description>Random part of login token</description>
                    <persistence type="property"/>
                </attribute>
            </attributes>
        </itemtype>


        <itemtype code="AbstractUserAudit"
                  extends="GenericItem"
                  jaloclass="de.hybris.platform.jalo.user.AbstractUserAudit"
                  abstract="true"
                  autocreate="true"
                  generate="true">
            <deployment table="UserAudit" typecode="6"/>
            <attributes>
                <attribute autocreate="true" qualifier="uid" type="java.lang.String" generate="true">
                    <persistence type="property"/>
                    <modifiers read="true" initial="true" write="false" search="true" optional="false" unique="false"/>
                </attribute>
                <attribute qualifier="userPK" type="java.lang.Long">
                    <modifiers read="true" initial="true" write="false" search="true" optional="false"/>
                    <persistence type="property"/>
                </attribute>
                <attribute autocreate="true" qualifier="changingUser" type="java.lang.String">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                </attribute>
                <attribute autocreate="true" qualifier="changingApplication" type="java.lang.String">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                </attribute>
                <attribute autocreate="true" qualifier="ipAddress" type="java.lang.String" generate="true">
                    <persistence type="property"/>
                    <modifiers read="true" initial="true" write="false" search="true" optional="true" unique="false"/>
                </attribute>
            </attributes>
            <indexes>
                <index name="UID" unique="false">
                    <key attribute="uid"/>
                </index>
                <index name="userPK" unique="false">
                    <key attribute="userPK"/>
                </index>
            </indexes>
        </itemtype>

        <itemtype code="UserPasswordChangeAudit"
                  extends="AbstractUserAudit"
                  jaloclass="de.hybris.platform.jalo.user.UserPasswordChangeAudit"
                  autocreate="true"
                  generate="true">
            <attributes>
                <attribute autocreate="true" qualifier="encodedPassword" type="java.lang.String">
                    <persistence type="property" qualifier="Passwd">
                        <columntype>
                            <value>HYBRIS.LONG_STRING</value>
                        </columntype>
                    </persistence>
                    <modifiers read="true" initial="true" write="false" search="true" optional="false"
                               encrypted="true"/>
                </attribute>
                <attribute autocreate="true" qualifier="passwordEncoding" type="java.lang.String">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="false"/>
                </attribute>
            </attributes>
        </itemtype>


        <itemtype code="Employee"
                  extends="User"
                  jaloclass="de.hybris.platform.jalo.user.Employee"
                  autocreate="true"
                  generate="true">
        </itemtype>

        <itemtype code="Customer"
                  extends="User"
                  jaloclass="de.hybris.platform.jalo.user.Customer"
                  autocreate="true"
                  generate="true">
            <attributes>
                <!--  auto ID which is generated by NumberSeries -->
                <attribute autocreate="true" qualifier="customerID" type="java.lang.String">
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                </attribute>
            </attributes>
        </itemtype>

        <itemtype code="UserGroup"
                  extends="PrincipalGroup"
                  jaloclass="de.hybris.platform.jalo.user.UserGroup"
                  autocreate="true"
                  generate="true">
            <deployment table="UserGroups" typecode="5" propertytable="UserGroupProps"/>
            <attributes>
                <attribute qualifier="writeableLanguages" type="LanguageCollection">
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="readableLanguages" type="LanguageCollection">
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                </attribute>
                <!-- hmc legacy-->
                <attribute qualifier="hmcXML" type="java.lang.String">
                    <modifiers read="true" write="true" search="false" optional="true" dontOptimize="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="denyWritePermissionForAllLanguages" type="java.lang.Boolean">
                    <modifiers read="true" write="true" search="false" dontOptimize="true"/>
                    <persistence type="property"/>
                    <description>When true and writeableLanguages is empty, deny any write permissions,
                        instead of setting same as readableLanguages.</description>
                </attribute>
            </attributes>
        </itemtype>

        <itemtype code="SAPUserVerificationToken"
                  extends="GenericItem"
                  jaloclass="de.hybris.platform.jalo.user.SAPUserVerificationToken"
                  autocreate="true"
                  generate="true">
            <deployment table="SapUserVerifiTokens" typecode="62"/>
            <attributes>
                <attribute qualifier="hashedTokenId" type="java.lang.String">
                    <persistence type="property" qualifier="tokenId"/>
                    <modifiers read="true" write="false" initial="true" search="true" optional="false" unique="true"/>
                </attribute>
                <attribute qualifier="encodedTokenCode" type="java.lang.String">
                    <persistence type="property" qualifier="tokenCode">
                        <columntype>
                            <value>HYBRIS.LONG_STRING</value>
                        </columntype>
                    </persistence>
                    <modifiers read="true" write="false" initial="true" search="true" optional="false"/>
                </attribute>
                <attribute qualifier="tokenEncoding" type="java.lang.String">
                    <persistence type="property"/>
                    <defaultvalue>de.hybris.platform.persistence.SystemEJB.DEFAULT_ENCODING</defaultvalue>
                    <modifiers read="true" write="false" initial="true" search="true" optional="false"/>
                </attribute>

                <attribute qualifier="uid" type="java.lang.String" generate="true">
                    <persistence type="property"/>
                    <modifiers read="true" write="false" initial="true" search="true" optional="false"/>
                </attribute>

                <attribute qualifier="verificationPurpose" type="SAPUserVerificationPurpose">
                    <description>Verification purpose of the token.</description>
                    <persistence type="property" />
                    <modifiers read="true" write="false" initial="true" search="true" optional="false" unique="true"/>
                </attribute>

                <attribute qualifier="expirationTime" type="java.util.Date">
                    <description>The date/time when the one time token will expire</description>
                    <persistence type="property"/>
                    <modifiers read="true" write="false" initial="true" search="true" optional="false"/>
                </attribute>

                <attribute qualifier="failedVerificationAttempts" type="java.lang.Integer" generate="true">
                    <defaultvalue>Integer.valueOf(0)</defaultvalue>
                    <persistence type="property"/>
                    <modifiers read="true" write="true" optional="true" unique="false"/>
                </attribute>
            </attributes>
            <indexes>
                <index name="id_idx" unique="true">
                    <key attribute="hashedTokenId"/>
                </index>
                <index name="id_purpose_idx">
                    <key attribute="hashedTokenId"/>
                    <key attribute="verificationPurpose"/>
                </index>
                <index name="id_uid_purpose_idx">
                    <key attribute="hashedTokenId"/>
                    <key attribute="uid"/>
                    <key attribute="verificationPurpose"/>
                </index>
                <index name="uid_idx">
                    <key attribute="uid"/>
                </index>
                <index name="expiration_time_idx">
                    <key attribute="expirationTime"/>
                </index>
            </indexes>
        </itemtype>

        <itemtype code="Address"
                  extends="GenericItem"
                  jaloclass="de.hybris.platform.jalo.user.Address"
                  autocreate="true"
                  generate="true">
            <deployment table="Addresses" typecode="23" propertytable="AddressProps"/>
            <attributes>
                <attribute autocreate="true" qualifier="original" type="Address">
                    <modifiers read="true" write="false" initial="true" search="true" optional="true"/>
                    <persistence type="property"/>
                </attribute>
                <!-- this attribute is to distinguish between the 'normal' created addresses and the cloned ones, which are
                   created if you call abstractOrder.setXYAddress() -->
                <attribute qualifier="duplicate" type="java.lang.Boolean">
                    <modifiers read="true" write="true" search="false" optional="true"/>
                    <persistence type="property"/>
                    <model>
                        <getter default="true" name="duplicate">
                            <nullDecorator>Boolean.valueOf(false)</nullDecorator>
                        </getter>
                    </model>
                </attribute>
                <attribute autocreate="true" redeclare="true" qualifier="owner" type="Item">
                    <modifiers read="true" write="false" initial="true" search="true" removable="false" optional="false"
                               partof="false"
                               private="false"/>
                </attribute>
                <attribute autocreate="true" qualifier="appartment" type="java.lang.String">
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                    <custom-properties>
                        <property name="hmcIndexField">
                            <value>"thefield"</value>
                        </property>
                    </custom-properties>
                </attribute>
                <attribute autocreate="true" qualifier="building" type="java.lang.String">
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                    <custom-properties>
                        <property name="hmcIndexField">
                            <value>"thefield"</value>
                        </property>
                    </custom-properties>
                </attribute>
                <attribute autocreate="true" qualifier="cellphone" type="java.lang.String">
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                    <custom-properties>
                        <property name="hmcIndexField">
                            <value>"thefield"</value>
                        </property>
                    </custom-properties>
                </attribute>
                <attribute autocreate="true" qualifier="company" type="java.lang.String">
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                    <custom-properties>
                        <property name="hmcIndexField">
                            <value>"thefield"</value>
                        </property>
                    </custom-properties>
                </attribute>
                <attribute autocreate="true" qualifier="country" type="Country" generate="true">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                </attribute>
                <attribute autocreate="true" qualifier="department" type="java.lang.String">
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                    <custom-properties>
                        <property name="hmcIndexField">
                            <value>"thefield"</value>
                        </property>
                    </custom-properties>
                </attribute>
                <attribute autocreate="true" qualifier="district" type="java.lang.String">
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                    <custom-properties>
                        <property name="hmcIndexField">
                            <value>"thefield"</value>
                        </property>
                    </custom-properties>
                </attribute>
                <attribute autocreate="true" qualifier="email" type="java.lang.String">
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                    <custom-properties>
                        <property name="hmcIndexField">
                            <value>"thefield"</value>
                        </property>
                    </custom-properties>
                </attribute>
                <attribute autocreate="true" qualifier="fax" type="java.lang.String">
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                    <custom-properties>
                        <property name="hmcIndexField">
                            <value>"thefield"</value>
                        </property>
                    </custom-properties>
                </attribute>
                <attribute autocreate="true" qualifier="firstname" type="java.lang.String">
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                    <custom-properties>
                        <property name="hmcIndexField">
                            <value>"thefield"</value>
                        </property>
                    </custom-properties>
                </attribute>
                <attribute autocreate="true" qualifier="lastname" type="java.lang.String">
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                    <custom-properties>
                        <property name="hmcIndexField">
                            <value>"thefield"</value>
                        </property>
                    </custom-properties>
                </attribute>
                <attribute autocreate="true" qualifier="middlename" type="java.lang.String">
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                    <custom-properties>
                        <property name="hmcIndexField">
                            <value>"thefield"</value>
                        </property>
                    </custom-properties>
                </attribute>
                <attribute autocreate="true" qualifier="middlename2" type="java.lang.String">
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                    <custom-properties>
                        <property name="hmcIndexField">
                            <value>"thefield"</value>
                        </property>
                    </custom-properties>
                </attribute>
                <attribute autocreate="true" qualifier="phone1" type="java.lang.String">
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                    <custom-properties>
                        <property name="hmcIndexField">
                            <value>"thefield"</value>
                        </property>
                    </custom-properties>
                </attribute>
                <attribute autocreate="true" qualifier="phone2" type="java.lang.String">
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                    <custom-properties>
                        <property name="hmcIndexField">
                            <value>"thefield"</value>
                        </property>
                    </custom-properties>
                </attribute>
                <attribute autocreate="true" qualifier="pobox" type="java.lang.String">
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                    <custom-properties>
                        <property name="hmcIndexField">
                            <value>"thefield"</value>
                        </property>
                    </custom-properties>
                </attribute>
                <attribute autocreate="true" qualifier="postalcode" type="java.lang.String">
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                    <custom-properties>
                        <property name="hmcIndexField">
                            <value>"thefield"</value>
                        </property>
                    </custom-properties>
                </attribute>
                <attribute autocreate="true" qualifier="region" type="Region" generate="true">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                </attribute>
                <attribute autocreate="true" qualifier="streetname" type="java.lang.String">
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                    <custom-properties>
                        <property name="hmcIndexField">
                            <value>"thefield"</value>
                        </property>
                    </custom-properties>
                </attribute>
                <attribute autocreate="true" qualifier="streetnumber" type="java.lang.String">
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                    <custom-properties>
                        <property name="hmcIndexField">
                            <value>"thefield"</value>
                        </property>
                    </custom-properties>
                </attribute>
                <attribute autocreate="true" qualifier="title" type="Title" generate="true">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                </attribute>
                <attribute autocreate="true" qualifier="town" type="java.lang.String">
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                    <custom-properties>
                        <property name="hmcIndexField">
                            <value>"thefield"</value>
                        </property>
                    </custom-properties>
                </attribute>
                <attribute qualifier="gender" type="Gender" autocreate="true">
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="dateOfBirth" autocreate="true" type="java.util.Date">
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                    <model>
                        <getter name="dateofbirth" deprecated="true" deprecatedSince="ages"/>
                        <setter name="dateofbirth" deprecated="true" deprecatedSince="ages"/>
                    </model>
                </attribute>
            </attributes>
            <indexes>
                <index name="testindex" unique="false">
                    <key attribute="email"/>
                </index>
                <index name="Address_Owner">
                    <key attribute="owner"/>
                </index>
            </indexes>
        </itemtype>

        <itemtype code="AbstractContactInfo" generate="true" autocreate="true"
                  jaloclass="de.hybris.platform.jalo.user.AbstractContactInfo">
            <description>Abstract contact info.</description>
            <deployment table="AbstractContactInfo" typecode="26"/>
            <attributes>
                <attribute qualifier="code" type="java.lang.String">
                    <persistence type="property"/>
                    <modifiers optional="false" initial="true" write="true"/>
                    <description>Specify code for that contact info.</description>
                </attribute>
            </attributes>
        </itemtype>


        <itemtype code="PhoneContactInfo" generate="true" autocreate="true"
                  jaloclass="de.hybris.platform.jalo.user.PhoneContactInfo" extends="AbstractContactInfo">
            <description>Phone contact info.</description>
            <attributes>
                <attribute qualifier="phoneNumber" type="java.lang.String">
                    <persistence type="property"/>
                    <modifiers optional="false" initial="false" write="true"/>
                </attribute>
                <attribute qualifier="type" type="PhoneContactInfoType">
                    <persistence type="property"/>
                    <modifiers optional="false" initial="false" write="true"/>
                </attribute>
            </attributes>
        </itemtype>

        <itemtype code="Title"
                  extends="GenericItem"
                  jaloclass="de.hybris.platform.jalo.user.Title"
                  autocreate="true"
                  generate="true">
            <!-- deployment="de.hybris.platform.persistence.user.Title" -->
            <deployment table="Titles" typecode="24"/>
            <attributes>
                <attribute autocreate="true" qualifier="code" type="java.lang.String">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" initial="true" optional="false" unique="true"/>
                    <custom-properties>
                        <property name="hmcIndexField">
                            <value>"thefield"</value>
                        </property>
                    </custom-properties>
                </attribute>
                <attribute autocreate="true" qualifier="name" type="localized:java.lang.String">
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                    <custom-properties>
                        <property name="hmcIndexField">
                            <value>"thefield"</value>
                        </property>
                    </custom-properties>
                </attribute>
            </attributes>

            <indexes>
                <index name="codeIdx">
                    <key attribute="code"/>
                </index>
            </indexes>
        </itemtype>

        <!-- order ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->

        <itemtype code="AbstractOrder"
                  extends="GenericItem"
                  jaloclass="de.hybris.platform.jalo.order.AbstractOrder"
                  autocreate="true"
                  generate="true"
                  abstract="true">
            <custom-properties>
                <property name="legacyPersistence">
                    <value>java.lang.Boolean.FALSE</value>
                </property>
            </custom-properties>
            <attributes>
                <attribute autocreate="true" qualifier="calculated" type="java.lang.Boolean" generate="true">
                    <custom-properties>
                        <property name="modelPrefetchMode">
                            <value>java.lang.Boolean.TRUE</value>
                        </property>
                    </custom-properties>
                    <defaultvalue>java.lang.Boolean.FALSE</defaultvalue>
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <model>
                        <getter default="true" name="calculated">
                            <nullDecorator>Boolean.valueOf(false)</nullDecorator>
                        </getter>
                    </model>
                </attribute>
                <!--  auto ID which is generated by NumberSeries -->
                <attribute autocreate="true" qualifier="code" type="java.lang.String" generate="true">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true" unique="true"/>
                    <custom-properties>
                        <property name="hmcIndexField">
                            <value>"thefield"</value>
                        </property>
                    </custom-properties>
                </attribute>
                <attribute autocreate="true" qualifier="currency" type="Currency" generate="true">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="false"/>
                </attribute>
                <attribute autocreate="true" qualifier="date" type="java.util.Date" generate="true">
                    <persistence type="property" qualifier="createdTS"/>
                    <modifiers read="true" write="true" search="true" optional="false"/>
                </attribute>
                <attribute autocreate="true" qualifier="deliveryAddress" type="Address" generate="true">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                </attribute>
                <attribute autocreate="true" qualifier="deliveryCost" type="java.lang.Double" generate="true">
                    <persistence type="property">
                        <columntype>
                            <value>java.math.BigDecimal</value>
                        </columntype>
                    </persistence>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <defaultvalue>Double.valueOf(0.0d)</defaultvalue>
                </attribute>
                <attribute autocreate="true" qualifier="deliveryMode" type="DeliveryMode" generate="true">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                </attribute>
                <attribute autocreate="true" qualifier="deliveryStatus" type="DeliveryStatus" generate="true">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                </attribute>
                <attribute qualifier="description" type="java.lang.String">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                </attribute>
                <attribute qualifier="expirationTime" type="java.util.Date">
                    <description>The date/time when the order will expire</description>
                    <persistence type="property"/>
                </attribute>
                <attribute autocreate="true" qualifier="globalDiscountValuesInternal" type="java.lang.String"
                           generate="true">
                    <custom-properties>
                        <property name="hiddenForUI">
                            <value>Boolean.TRUE</value>
                        </property>
                    </custom-properties>
                    <modifiers read="true" write="true" search="false" optional="true"/>
                    <persistence type="property">
                        <columntype database="oracle">
                            <value>CLOB</value>
                        </columntype>
                        <columntype database="sap">
                            <value>NCLOB</value>
                        </columntype>
                        <columntype database="mysql">
                            <value>TEXT</value>
                        </columntype>
                        <columntype database="sqlserver">
                            <value>TEXT</value>
                        </columntype>
                        <columntype>
                            <value>HYBRIS.LONG_STRING</value>
                        </columntype>
                    </persistence>
                </attribute>
                <attribute autocreate="true" qualifier="globalDiscountValues" type="DiscountValueCollection"
                           generate="true">
                    <persistence type="dynamic"/>
                    <modifiers read="true" write="true" search="false" optional="true"/>
                </attribute>
                <attribute qualifier="name" type="java.lang.String">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                </attribute>
                <attribute autocreate="true" qualifier="net" type="java.lang.Boolean" generate="true">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="false"/>
                    <defaultvalue>java.lang.Boolean.FALSE</defaultvalue>
                </attribute>
                <attribute autocreate="true" qualifier="paymentAddress" type="Address" generate="true">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                </attribute>
                <attribute autocreate="true" qualifier="paymentCost" type="java.lang.Double" generate="true">
                    <persistence type="property">
                        <columntype>
                            <value>java.math.BigDecimal</value>
                        </columntype>
                    </persistence>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <defaultvalue>Double.valueOf(0.0d)</defaultvalue>
                </attribute>
                <attribute autocreate="true" qualifier="paymentInfo" type="PaymentInfo" generate="true">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                </attribute>
                <attribute autocreate="true" qualifier="paymentMode" type="PaymentMode" generate="true">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                </attribute>
                <attribute autocreate="true" qualifier="paymentStatus" type="PaymentStatus" generate="true">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                </attribute>
                <attribute autocreate="true" qualifier="status" type="OrderStatus" generate="true">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                </attribute>
                <attribute autocreate="true" qualifier="exportStatus" type="ExportStatus">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                </attribute>
                <attribute autocreate="true" qualifier="statusInfo" type="java.lang.String" generate="true">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <custom-properties>
                        <property name="hmcIndexField">
                            <value>"thefield"</value>
                        </property>
                    </custom-properties>
                </attribute>
                <attribute autocreate="true" qualifier="totalPrice" type="java.lang.Double" generate="true">
                    <persistence type="property">
                        <columntype>
                            <value>java.math.BigDecimal</value>
                        </columntype>
                    </persistence>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <defaultvalue>Double.valueOf(0.0d)</defaultvalue>
                </attribute>
                <attribute autocreate="true" qualifier="totalDiscounts" type="java.lang.Double" generate="true">
                    <persistence type="property">
                        <columntype>
                            <value>java.math.BigDecimal</value>
                        </columntype>
                    </persistence>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <defaultvalue>Double.valueOf(0.0d)</defaultvalue>
                </attribute>
                <attribute autocreate="true" qualifier="totalTax" type="java.lang.Double" generate="true">
                    <persistence type="property">
                        <columntype>
                            <value>java.math.BigDecimal</value>
                        </columntype>
                    </persistence>
                    <modifiers read="true" write="true" search="false" optional="true"/>
                    <defaultvalue>Double.valueOf(0.0d)</defaultvalue>
                </attribute>
                <attribute autocreate="true" qualifier="totalTaxValuesInternal" type="java.lang.String" generate="true">
                    <custom-properties>
                        <property name="hiddenForUI">
                            <value>Boolean.TRUE</value>
                        </property>
                    </custom-properties>
                    <modifiers read="true" write="true" search="false" optional="true"/>
                    <persistence type="property">
                        <columntype database="oracle">
                            <value>CLOB</value>
                        </columntype>
                        <columntype database="sap">
                            <value>NCLOB</value>
                        </columntype>
                        <columntype database="mysql">
                            <value>TEXT</value>
                        </columntype>
                        <columntype>
                            <value>HYBRIS.LONG_STRING</value>
                        </columntype>
                    </persistence>
                </attribute>
                <attribute autocreate="true" qualifier="totalTaxValues" type="TaxValueCollection" generate="true">
                    <persistence type="dynamic"/>
                    <modifiers read="true" write="true" search="false" optional="true"/>
                </attribute>
                <attribute autocreate="true" qualifier="user" type="User" generate="true">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="false"/>
                </attribute>
                <attribute autocreate="true" qualifier="subtotal" type="java.lang.Double" generate="true">
                    <persistence type="property">
                        <columntype>
                            <value>java.math.BigDecimal</value>
                        </columntype>
                    </persistence>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <defaultvalue>Double.valueOf(0.0d)</defaultvalue>
                </attribute>
                <attribute autocreate="true" qualifier="discountsIncludeDeliveryCost" type="boolean">
                    <description>Tells whether delivery costs should be included in discount calculation or not. If this
                        field is true
                        delivery costs are changed the same way as product costs if discount values are set at this
                        order.
                    </description>
                    <modifiers optional="false"/>
                    <persistence type="property"/>
                    <defaultvalue>java.lang.Boolean.FALSE</defaultvalue>
                </attribute>
                <attribute autocreate="true" qualifier="discountsIncludePaymentCost" type="boolean">
                    <description>Tells whether payment costs should be included in discount calculation or not. If this
                        field is true
                        payment costs are changed the same way as product costs if discount values are set at this
                        order.
                    </description>
                    <modifiers optional="false"/>
                    <persistence type="property"/>
                    <defaultvalue>java.lang.Boolean.FALSE</defaultvalue>
                </attribute>
                <attribute type="EntryGroupList" qualifier="entryGroups">
                    <description>List of entry groups for this order.</description>
                    <persistence type="property"/>
                </attribute>
            </attributes>
            <indexes>
                <index name="OrderCode">
                    <key attribute="code"/>
                </index>
                <index name="OrderUser">
                    <key attribute="user"/>
                </index>
            </indexes>
        </itemtype>

        <itemtype code="Cart"
                  extends="AbstractOrder"
                  jaloclass="de.hybris.platform.jalo.order.Cart"
                  autocreate="true"
                  generate="true">
            <deployment table="Carts" typecode="43"/>
            <attributes>
                <attribute autocreate="true" redeclare="true" qualifier="entries" type="CartEntryCollection"/>
                <attribute type="java.lang.String" qualifier="sessionId">
                    <persistence type="property"/>
                    <modifiers read="true" write="true"/>
                </attribute>
                <attribute autocreate="true" qualifier="user" type="User" generate="true" redeclare="true">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="false"/>
                </attribute>
            </attributes>
        </itemtype>

        <itemtype code="Order"
                  extends="AbstractOrder"
                  jaloclass="de.hybris.platform.jalo.order.Order"
                  autocreate="true"
                  generate="true">
            <deployment table="Orders" typecode="45" propertytable="OrderProps"/>
            <attributes>
                <attribute autocreate="true" redeclare="true" qualifier="entries" type="OrderEntryCollection"/>
                <attribute autocreate="true" qualifier="user" type="User" generate="true" redeclare="true">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="false"/>
                </attribute>
            </attributes>

        </itemtype>

        <itemtype code="PaymentInfo"
                  extends="GenericItem"
                  jaloclass="de.hybris.platform.jalo.order.payment.PaymentInfo"
                  autocreate="true"
                  generate="true">
            <deployment table="PaymentInfos" typecode="42"/>
            <attributes>
                <attribute autocreate="true" qualifier="original" type="Item">
                    <modifiers read="true" write="false" initial="true" search="true" optional="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute autocreate="true" qualifier="code" type="java.lang.String">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="false" initial="true"/>
                    <custom-properties>
                        <property name="hmcIndexField">
                            <value>"thefield"</value>
                        </property>
                    </custom-properties>
                </attribute>
                <!-- this attribute is to distinguish between the 'normal' created paymentinfo and the cloned ones, which are
                   created if you call abstractOrder.setPaymentInfo(..) -->
                <attribute qualifier="duplicate" type="java.lang.Boolean">
                    <modifiers read="true" write="true" search="false" optional="true"/>
                    <persistence type="property"/>
                    <defaultvalue>java.lang.Boolean.FALSE</defaultvalue>
                    <model>
                        <getter default="true" name="duplicate">
                            <nullDecorator>Boolean.valueOf(false)</nullDecorator>
                        </getter>
                    </model>
                </attribute>
            </attributes>
            <indexes>
                <index name="PaymentInfo_User">
                    <key attribute="user"/>
                </index>
            </indexes>
        </itemtype>

        <itemtype code="AdvancePaymentInfo"
                  extends="PaymentInfo"
                  jaloclass="de.hybris.platform.jalo.order.payment.PaymentInfo"
                  autocreate="true"
                  generate="false">
        </itemtype>
        <itemtype code="CreditCardPaymentInfo"
                  extends="PaymentInfo"
                  jaloclass="de.hybris.platform.jalo.order.payment.PaymentInfo"
                  autocreate="true"
                  generate="false">
            <attributes>
                <attribute qualifier="ccOwner" autocreate="true" type="java.lang.String">
                    <modifiers read="true" write="true" search="true" optional="false"/>
                    <persistence type="property"/>
                    <custom-properties>
                        <property name="hmcIndexField">
                            <value>"thefield"</value>
                        </property>
                    </custom-properties>
                </attribute>
                <attribute qualifier="number" autocreate="true" type="java.lang.String">
                    <!--  CAUTION: ONLY CHANGE THE VALUE OF ENCRYPTED WHEN YOU KNOW WHAT YOU DO !!! -->
                    <modifiers read="true" write="true" search="false" optional="false" encrypted="true"/>
                    <persistence type="property"/>
                    <custom-properties>
                        <property name="hmcIndexField">
                            <value>"thefield"</value>
                        </property>
                    </custom-properties>
                </attribute>
                <attribute qualifier="type" autocreate="true" type="CreditCardType">
                    <modifiers read="true" write="true" search="true" optional="false"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="validToMonth" autocreate="true" type="java.lang.String">
                    <modifiers read="true" write="true" search="true" optional="false"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="validToYear" autocreate="true" type="java.lang.String">
                    <modifiers read="true" write="true" search="true" optional="false"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="validFromMonth" autocreate="true" type="java.lang.String">
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="validFromYear" autocreate="true" type="java.lang.String">
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                </attribute>
            </attributes>
        </itemtype>

        <itemtype code="DebitPaymentInfo"
                  extends="PaymentInfo"
                  jaloclass="de.hybris.platform.jalo.order.payment.PaymentInfo"
                  autocreate="true"
                  generate="false">
            <attributes>
                <attribute qualifier="bankIDNumber" autocreate="true" type="java.lang.String">
                    <modifiers read="true" write="true" search="true" optional="false"/>
                    <persistence type="property"/>
                    <custom-properties>
                        <property name="hmcIndexField">
                            <value>"thefield"</value>
                        </property>
                    </custom-properties>
                </attribute>
                <attribute qualifier="bank" autocreate="true" type="java.lang.String">
                    <modifiers read="true" write="true" search="true" optional="false"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="accountNumber" autocreate="true" type="java.lang.String">
                    <modifiers read="true" write="true" search="true" optional="false"/>
                    <persistence type="property"/>
                    <custom-properties>
                        <property name="hmcIndexField">
                            <value>"thefield"</value>
                        </property>
                    </custom-properties>
                </attribute>
                <attribute qualifier="baOwner" autocreate="true" type="java.lang.String">
                    <modifiers read="true" write="true" search="true" optional="false"/>
                    <persistence type="property"/>
                    <custom-properties>
                        <property name="hmcIndexField">
                            <value>"thefield"</value>
                        </property>
                    </custom-properties>
                </attribute>
            </attributes>
        </itemtype>

        <itemtype code="InvoicePaymentInfo"
                  extends="PaymentInfo"
                  jaloclass="de.hybris.platform.jalo.order.payment.PaymentInfo"
                  autocreate="true"
                  generate="false">
        </itemtype>


        <itemtype code="AbstractOrderEntry"
                  extends="GenericItem"
                  jaloclass="de.hybris.platform.jalo.order.AbstractOrderEntry"
                  abstract="true"
                  autocreate="true"
                  generate="true">
            <attributes>
                <attribute autocreate="true" qualifier="basePrice" type="java.lang.Double" generate="true">
                    <persistence type="property">
                        <columntype>
                            <value>java.math.BigDecimal</value>
                        </columntype>
                    </persistence>
                    <modifiers read="true" write="true" search="false" optional="true"/>
                    <defaultvalue>Double.valueOf(0.0d)</defaultvalue>
                </attribute>
                <attribute autocreate="true" qualifier="calculated" type="java.lang.Boolean" generate="true">
                    <custom-properties>
                        <property name="modelPrefetchMode">
                            <value>java.lang.Boolean.TRUE</value>
                        </property>
                    </custom-properties>
                    <defaultvalue>Boolean.FALSE</defaultvalue>
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <model>
                        <getter default="true" name="calculated">
                            <nullDecorator>Boolean.valueOf(false)</nullDecorator>
                        </getter>
                    </model>
                </attribute>
                <attribute autocreate="true" qualifier="discountValuesInternal" type="java.lang.String" generate="true">
                    <custom-properties>
                        <property name="hiddenForUI">
                            <value>Boolean.TRUE</value>
                        </property>
                    </custom-properties>
                    <persistence type="property">
                        <columntype database="oracle">
                            <value>CLOB</value>
                        </columntype>
                        <columntype database="sap">
                            <value>NCLOB</value>
                        </columntype>
                        <columntype database="mysql">
                            <value>TEXT</value>
                        </columntype>
                        <columntype>
                            <value>HYBRIS.LONG_STRING</value>
                        </columntype>
                    </persistence>
                    <modifiers read="true" write="true" search="false" optional="true"/>
                </attribute>
                <attribute autocreate="true" qualifier="discountValues" type="DiscountValueCollection" generate="true">
                    <persistence type="dynamic"/>
                    <modifiers read="true" write="true" search="false" optional="true"/>
                </attribute>
                <attribute autocreate="true" qualifier="entryNumber" type="java.lang.Integer" generate="true">
                    <defaultvalue>Integer.valueOf(de.hybris.platform.jalo.order.AbstractOrder.APPEND_AS_LAST)
                    </defaultvalue>
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true" unique="true"/>
                </attribute>
                <attribute autocreate="true" qualifier="info" type="java.lang.String" generate="true">
                    <persistence type="property">
                        <columntype>
                            <value>HYBRIS.LONG_STRING</value>
                        </columntype>
                    </persistence>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                </attribute>
                <attribute autocreate="true" qualifier="product" type="Product" generate="true">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="false"/>
                </attribute>
                <attribute autocreate="true" qualifier="quantity" type="java.lang.Long" generate="true">
                    <persistence type="property">
                        <columntype>
                            <value>java.math.BigDecimal</value>
                        </columntype>
                    </persistence>
                    <modifiers read="true" write="true" search="true" optional="false"/>
                </attribute>
                <attribute autocreate="true" qualifier="taxValues" type="TaxValueCollection" generate="true">
                    <persistence type="dynamic"/>
                    <modifiers read="true" write="true" search="false" optional="true"/>
                </attribute>
                <attribute autocreate="true" qualifier="taxValuesInternal" type="java.lang.String" generate="true">
                    <custom-properties>
                        <property name="hiddenForUI">
                            <value>Boolean.TRUE</value>
                        </property>
                    </custom-properties>
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="false" optional="true"/>
                </attribute>
                <attribute autocreate="true" qualifier="totalPrice" type="java.lang.Double" generate="true">
                    <persistence type="property">
                        <columntype>
                            <value>java.math.BigDecimal</value>
                        </columntype>
                    </persistence>
                    <modifiers read="true" write="true" search="false" optional="true"/>
                    <defaultvalue>Double.valueOf(0.0d)</defaultvalue>
                </attribute>
                <attribute autocreate="true" qualifier="unit" type="Unit" generate="true">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="false"/>
                </attribute>
                <attribute autocreate="true" qualifier="giveAway" type="java.lang.Boolean" generate="true">
                    <defaultvalue>Boolean.FALSE</defaultvalue>
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="false"/>
                    <model>
                        <getter default="true" name="giveAway">
                            <nullDecorator>Boolean.valueOf(false)</nullDecorator>
                        </getter>
                    </model>
                </attribute>
                <attribute autocreate="true" qualifier="rejected" type="java.lang.Boolean" generate="true">
                    <defaultvalue>Boolean.FALSE</defaultvalue>
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="false"/>
                    <model>
                        <getter default="true" name="rejected">
                            <nullDecorator>Boolean.valueOf(false)</nullDecorator>
                        </getter>
                    </model>
                </attribute>
                <attribute qualifier="entryGroupNumbers" type="EntryGroupNumbersList">
                    <description>List of EntryGroup numbers that this order entry belongs to.</description>
                    <persistence type="property"/>
                </attribute>
            </attributes>
            <indexes>
                <index name="oeProd">
                    <key attribute="product"/>
                </index>
                <index name="oeOrd">
                    <key attribute="order"/>
                </index>
            </indexes>
        </itemtype>

        <itemtype code="OrderEntry"
                  extends="AbstractOrderEntry"
                  jaloclass="de.hybris.platform.jalo.order.OrderEntry"
                  autocreate="true"
                  generate="true">
            <deployment table="OrderEntries" typecode="46" propertytable="OrderEntryProps"/>
            <attributes>
                <attribute autocreate="true" redeclare="true" qualifier="order" type="Order">
                    <modifiers read="true" write="false" search="true" removable="true" optional="false" initial="true"
                               unique="true"/>
                </attribute>
            </attributes>
        </itemtype>

        <itemtype code="CartEntry"
                  extends="AbstractOrderEntry"
                  jaloclass="de.hybris.platform.jalo.order.CartEntry"
                  autocreate="true"
                  generate="true">
            <deployment table="CartEntries" typecode="44"/>
            <attributes>
                <attribute autocreate="true" redeclare="true" qualifier="order" type="Cart">
                    <modifiers read="true" write="false" search="true" removable="true" optional="false" initial="true"
                               unique="true"/>
                </attribute>
            </attributes>
        </itemtype>

        <itemtype code="DeliveryMode"
                  extends="GenericItem"
                  jaloclass="de.hybris.platform.jalo.order.delivery.DeliveryMode"
                  autocreate="true"
                  generate="true">
            <deployment table="DeliveryModes" typecode="40"/>
            <attributes>
                <attribute qualifier="active" type="java.lang.Boolean" autocreate="true" generate="true">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="false"/>
                    <defaultvalue>Boolean.FALSE</defaultvalue>
                </attribute>
                <attribute qualifier="code" type="java.lang.String" autocreate="true" generate="true">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="false" unique="true"/>
                    <custom-properties>
                        <property name="hmcIndexField">
                            <value>"thefield"</value>
                        </property>
                    </custom-properties>
                </attribute>
                <attribute autocreate="true" qualifier="description" type="localized:java.lang.String">
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property">
                        <columntype database="oracle">
                            <value>CLOB</value>
                        </columntype>
                        <columntype database="sap">
                            <value>NCLOB</value>
                        </columntype>
                        <columntype>
                            <value>HYBRIS.LONG_STRING</value>
                        </columntype>
                    </persistence>
                    <custom-properties>
                        <property name="hmcIndexField">
                            <value>"thefield"</value>
                        </property>
                    </custom-properties>
                </attribute>
                <attribute autocreate="true" qualifier="name" type="localized:java.lang.String">
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                    <custom-properties>
                        <property name="hmcIndexField">
                            <value>"thefield"</value>
                        </property>
                    </custom-properties>
                </attribute>
                <attribute autocreate="true" qualifier="supportedPaymentModes" type="PaymentModeCollection"
                           generate="true">
                    <persistence type="jalo"/>
                    <modifiers read="true" write="true" search="false" optional="true"/>
                </attribute>
                <attribute autocreate="true" qualifier="supportedPaymentModesInternal" type="java.lang.String"
                           generate="true">
                    <custom-properties>
                        <property name="hiddenForUI">
                            <value>Boolean.TRUE</value>
                        </property>
                    </custom-properties>
                    <persistence type="property"/>
                    <modifiers private="true" read="true" write="true" search="false" optional="true"/>
                </attribute>
            </attributes>
        </itemtype>

        <itemtype code="PaymentMode"
                  extends="GenericItem"
                  jaloclass="de.hybris.platform.jalo.order.payment.PaymentMode"
                  autocreate="true"
                  generate="true">
            <deployment table="PaymentModes" typecode="41"/>
            <attributes>
                <attribute autocreate="true" qualifier="active" type="java.lang.Boolean" generate="true">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="false"/>
                </attribute>
                <attribute autocreate="true" qualifier="code" type="java.lang.String" generate="true">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" initial="false" optional="false" unique="true"/>
                    <custom-properties>
                        <property name="hmcIndexField">
                            <value>"thefield"</value>
                        </property>
                    </custom-properties>
                </attribute>
                <attribute autocreate="true" qualifier="description" type="localized:java.lang.String">
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property">
                        <columntype database="oracle">
                            <value>CLOB</value>
                        </columntype>
                        <columntype database="sap">
                            <value>NCLOB</value>
                        </columntype>
                        <columntype>
                            <value>HYBRIS.LONG_STRING</value>
                        </columntype>
                    </persistence>
                    <custom-properties>
                        <property name="hmcIndexField">
                            <value>"thefield"</value>
                        </property>
                    </custom-properties>
                </attribute>
                <attribute autocreate="true" qualifier="name" type="localized:java.lang.String">
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                    <custom-properties>
                        <property name="hmcIndexField">
                            <value>"thefield"</value>
                        </property>
                    </custom-properties>
                </attribute>
                <attribute autocreate="true" qualifier="paymentInfoType" type="ComposedType" generate="true">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" initial="true" optional="false"/>
                    <model>
                        <getter name="paymentinfotype" deprecated="true" deprecatedSince="ages"/>
                        <setter name="paymentinfotype" deprecated="true" deprecatedSince="ages"/>
                    </model>
                </attribute>
                <attribute autocreate="true" qualifier="supportedDeliveryModes" type="DeliveryModeCollection">
                    <modifiers read="true" write="false" search="false" optional="true"/>
                    <persistence type="jalo"/>
                    <model>
                        <getter name="supporteddeliverymodes" deprecated="true" deprecatedSince="ages"/>
                        <setter name="supporteddeliverymodes" deprecated="true" deprecatedSince="ages"/>
                    </model>
                </attribute>
            </attributes>
        </itemtype>

        <itemtype code="Discount"
                  extends="GenericItem"
                  jaloclass="de.hybris.platform.jalo.order.price.Discount"
                  autocreate="true"
                  generate="true">
            <deployment table="Discounts" typecode="48"/>
            <attributes>
                <attribute autocreate="true" qualifier="absolute" type="java.lang.Boolean">
                    <persistence type="jalo"/>
                    <modifiers read="true" write="false" search="false" optional="false"/>
                </attribute>
                <attribute autocreate="true" qualifier="code" type="java.lang.String" generate="true">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" initial="true" optional="false" unique="true"/>
                    <custom-properties>
                        <property name="hmcIndexField">
                            <value>"thefield"</value>
                        </property>
                    </custom-properties>
                </attribute>
                <attribute autocreate="true" qualifier="currency" type="Currency" generate="true">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                </attribute>
                <attribute autocreate="true" qualifier="global" type="java.lang.Boolean" generate="true">
                    <defaultvalue>Boolean.FALSE</defaultvalue>
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="false"/>
                </attribute>
                <attribute autocreate="true" qualifier="name" type="localized:java.lang.String">
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                    <custom-properties>
                        <property name="hmcIndexField">
                            <value>"thefield"</value>
                        </property>
                    </custom-properties>
                </attribute>
                <attribute autocreate="true" qualifier="priority" type="java.lang.Integer" generate="true">
                    <defaultvalue>Integer.valueOf(1)</defaultvalue>
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="false"/>
                    <model>
                        <getter default="true" name="priority">
                            <nullDecorator>Integer.valueOf(0)</nullDecorator>
                        </getter>
                    </model>
                </attribute>
                <attribute autocreate="true" qualifier="value" type="java.lang.Double" generate="true">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <defaultvalue>Double.valueOf(0.0d)</defaultvalue>
                    <model>
                        <getter default="true" name="value">
                            <nullDecorator>Double.valueOf(0.0d)</nullDecorator>
                        </getter>
                    </model>
                </attribute>
                <attribute autocreate="true" qualifier="discountString" type="java.lang.String">
                    <persistence type="jalo"/>
                    <modifiers read="true" write="false" search="false" optional="true" removable="false"
                               initial="false"/>
                    <model>
                        <getter name="discountstring" deprecated="true" deprecatedSince="ages"/>
                        <setter name="discountstring" deprecated="true" deprecatedSince="ages"/>
                    </model>
                </attribute>

            </attributes>
        </itemtype>

        <itemtype code="Tax"
                  extends="GenericItem"
                  jaloclass="de.hybris.platform.jalo.order.price.Tax"
                  autocreate="true"
                  generate="true">
            <deployment table="Taxes" typecode="47"/>
            <attributes>
                <attribute qualifier="code" type="java.lang.String" autocreate="true" generate="true">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" initial="true" optional="false" unique="true"/>
                    <custom-properties>
                        <property name="hmcIndexField">
                            <value>"thefield"</value>
                        </property>
                    </custom-properties>
                </attribute>
                <attribute qualifier="name" type="localized:java.lang.String" autocreate="true">
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                    <custom-properties>
                        <property name="hmcIndexField">
                            <value>"thefield"</value>
                        </property>
                    </custom-properties>
                </attribute>
                <attribute qualifier="value" type="java.lang.Double" autocreate="true" generate="true">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <defaultvalue>Double.valueOf(0.0d)</defaultvalue>
                </attribute>
                <attribute qualifier="currency" type="Currency" autocreate="true">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                </attribute>
                <attribute qualifier="absolute" type="java.lang.Boolean" autocreate="true">
                    <persistence type="jalo"/>
                    <modifiers read="true" write="false" optional="true"/>
                </attribute>
            </attributes>
        </itemtype>

        <!-- quote ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->

        <itemtype code="Quote"
                  extends="AbstractOrder"
                  jaloclass="de.hybris.platform.jalo.order.Quote"
                  autocreate="true"
                  generate="true">
            <deployment table="Quotes" typecode="60" propertytable="QuoteProps"/>
            <attributes>
                <attribute autocreate="true" qualifier="version" type="java.lang.Integer">
                    <description>The version of the quote. Along with code it makes a quote instance unique.
                    </description>
                    <modifiers initial="true" write="false" optional="false" unique="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute autocreate="true" qualifier="state" type="QuoteState">
                    <description>Current state of the quote</description>
                    <modifiers optional="false"/>
                    <persistence type="property"/>
                </attribute>
                <attribute autocreate="true" redeclare="true" qualifier="entries" type="QuoteEntryCollection"/>
                <attribute autocreate="true" qualifier="user" type="User" generate="true" redeclare="true">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="false"/>
                </attribute>
            </attributes>
            <indexes>
                <index name="version_idx" unique="true">
                    <key attribute="code"/>
                    <key attribute="version"/>
                </index>
            </indexes>
        </itemtype>


        <itemtype code="QuoteEntry"
                  extends="AbstractOrderEntry"
                  jaloclass="de.hybris.platform.jalo.order.QuoteEntry"
                  autocreate="true"
                  generate="true">
            <deployment table="QuoteEntries" typecode="61" propertytable="QuoteEntryProps"/>
            <attributes>
                <attribute autocreate="true" redeclare="true" qualifier="order" type="Quote">
                    <modifiers read="true" write="false" search="true" removable="true" optional="false" initial="true"
                               unique="true"/>
                </attribute>
            </attributes>
        </itemtype>

        <!-- c2l ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->

        <itemtype code="C2LItem"
                  extends="GenericItem"
                  jaloclass="de.hybris.platform.jalo.c2l.C2LItem"
                  autocreate="true"
                  abstract="true"
                  generate="true">
            <attributes>
                <attribute autocreate="true" qualifier="active" type="java.lang.Boolean">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="false"/>
                    <defaultvalue>Boolean.valueOf(false)</defaultvalue>
                    <model>
                        <getter default="true" name="active">
                            <nullDecorator>Boolean.valueOf(false)</nullDecorator>
                        </getter>
                    </model>

                </attribute>
                <attribute autocreate="true" qualifier="isocode" type="java.lang.String">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="false"/>
                    <custom-properties>
                        <property name="hmcIndexField">
                            <value>"thefield"</value>
                        </property>
                    </custom-properties>
                </attribute>
                <attribute autocreate="true" qualifier="name" type="localized:java.lang.String">
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                    <custom-properties>
                        <property name="hmcIndexField">
                            <value>"thefield"</value>
                        </property>
                    </custom-properties>
                </attribute>
            </attributes>
            <indexes>
                <index name="ISOCode">
                    <key attribute="isocode"/>
                </index>
            </indexes>
        </itemtype>

        <itemtype code="Region"
                  extends="C2LItem"
                  jaloclass="de.hybris.platform.jalo.c2l.Region"
                  autocreate="true"
                  generate="true">
            <deployment table="Regions" typecode="35"/>
            <attributes>
                <!-- replaced by Country2RegionRelation relation, but because of compatibility of column name it is defined here too -->
                <attribute autocreate="true" qualifier="country" type="Country">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="false" unique="true"/>
                </attribute>
                <attribute qualifier="isocode" type="java.lang.String" redeclare="true" generate="false">
                    <modifiers read="true" write="true" search="true" optional="false" unique="true"/>
                </attribute>
            </attributes>
            <indexes>
                <index name="Region_Country">
                    <key attribute="country"/>
                </index>
            </indexes>
        </itemtype>

        <itemtype code="Language"
                  extends="C2LItem"
                  jaloclass="de.hybris.platform.jalo.c2l.Language"
                  autocreate="true"
                  generate="true">
            <deployment table="Languages" typecode="32"/>
            <attributes>
                <attribute qualifier="fallbackLanguages" type="LanguageList" autocreate="true">
                    <persistence type="jalo"/>
                    <modifiers read="true" write="true" search="false" optional="true"/>
                    <model>
                        <getter default="true" name="fallbackLanguages">
                            <nullDecorator>java.util.Collections.emptyList()</nullDecorator>
                        </getter>
                    </model>
                </attribute>
                <attribute qualifier="isocode" type="java.lang.String" redeclare="true" generate="false">
                    <modifiers read="true" write="true" search="true" optional="false" unique="true"/>
                </attribute>
            </attributes>
        </itemtype>

        <itemtype code="Country"
                  extends="C2LItem"
                  jaloclass="de.hybris.platform.jalo.c2l.Country"
                  autocreate="true"
                  generate="true">
            <deployment table="Countries" typecode="34"/>
            <attributes>
                <!-- replaced by Country2RegionRelation relation
            <attribute autocreate="true" qualifier="regions" type="RegionCollection">
               <modifiers read="true" write="true" search="false" optional="true" partof="true"/>
               <persistence type="jalo"/>
            </attribute>
            -->
                <attribute qualifier="isocode" type="java.lang.String" redeclare="true" generate="false">
                    <modifiers read="true" write="true" search="true" optional="false" unique="true"/>
                </attribute>
            </attributes>
        </itemtype>

        <itemtype code="Currency"
                  extends="C2LItem"
                  jaloclass="de.hybris.platform.jalo.c2l.Currency"
                  autocreate="true"
                  generate="true">
            <deployment table="Currencies" typecode="33"/>
            <custom-properties>
                <property name="legacyPersistence">
                    <value>java.lang.Boolean.TRUE</value>
                </property>
            </custom-properties>
            <attributes>
                <attribute autocreate="true" qualifier="base" type="java.lang.Boolean">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="false"/>
                    <defaultvalue>Boolean.valueOf(false)</defaultvalue>
                    <model>
                        <getter default="true" name="base">
                            <nullDecorator>Boolean.valueOf(false)</nullDecorator>
                        </getter>
                    </model>
                </attribute>
                <attribute autocreate="true" qualifier="conversion" type="java.lang.Double">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="false"/>
                    <defaultvalue>Double.valueOf(1.0)</defaultvalue>
                    <model>
                        <getter default="true" name="conversion">
                            <nullDecorator>Double.valueOf(0d)</nullDecorator>
                        </getter>
                    </model>
                </attribute>
                <attribute autocreate="true" qualifier="digits" type="java.lang.Integer">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="false"/>
                    <defaultvalue>Integer.valueOf(2)</defaultvalue>
                    <model>
                        <getter default="true" name="digits">
                            <nullDecorator>Integer.valueOf(0)</nullDecorator>
                        </getter>
                    </model>
                </attribute>
                <attribute autocreate="true" qualifier="symbol" type="java.lang.String">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="false"/>
                </attribute>
                <attribute qualifier="isocode" type="java.lang.String" redeclare="true" generate="false">
                    <modifiers read="true" write="true" search="true" optional="false" unique="true"/>
                </attribute>
            </attributes>
        </itemtype>

        <!-- media ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->

        <itemtype code="AbstractMedia"
                  extends="GenericItem"
                  jaloclass="de.hybris.platform.jalo.media.AbstractMedia"
                  autocreate="true"
                  generate="true"
                  abstract="true">
            <attributes>
                <attribute qualifier="mime" type="java.lang.String">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <description>Mime type of referenced data file.</description>
                </attribute>
                <attribute qualifier="size" type="java.lang.Long">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <description>Size of referenced data file.</description>
                </attribute>
                <attribute qualifier="dataPK" type="java.lang.Long">
                    <modifiers read="true" initial="false" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                    <description>PK of the referenced data file.</description>
                </attribute>
                <attribute qualifier="location" type="java.lang.String">
                    <custom-properties>
                        <property name="readOnlyForUI">
                            <value>Boolean.TRUE</value>
                        </property>
                    </custom-properties>
                    <modifiers read="true" write="true" search="false" optional="true"/>
                    <persistence type="property">
                        <columntype>
                            <value>HYBRIS.LONG_STRING</value>
                        </columntype>
                    </persistence>
                    <description>Generated location (by one of Storage Strategies) to media within storage.
                    </description>
                </attribute>
                <attribute qualifier="locationHash" type="java.lang.String">
                    <modifiers read="true" write="true" search="false" optional="true"/>
                    <persistence type="property"/>
                    <description>Computed hash of folder qualifier and location</description>
                </attribute>
                <attribute autocreate="true" qualifier="realFileName" type="java.lang.String" generate="true">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <model>
                        <getter name="realfilename" deprecated="true" deprecatedSince="ages"/>
                        <setter name="realfilename" deprecated="true" deprecatedSince="ages"/>
                    </model>
                    <custom-properties>
                        <property name="hmcIndexField">
                            <value>"thefield"</value>
                        </property>
                    </custom-properties>
                </attribute>
            </attributes>
            <indexes>
                <index name="dataPK_idx">
                    <key attribute="dataPK"/>
                </index>
            </indexes>
        </itemtype>

        <itemtype code="DerivedMedia"
                  extends="AbstractMedia"
                  jaloclass="de.hybris.platform.jalo.media.DerivedMedia"
                  autocreate="true"
                  generate="true">
            <deployment table="DerivedMedias" typecode="31"/>
            <attributes>
                <attribute autocreate="true" qualifier="version" type="java.lang.String" generate="true">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="false" initial="true"/>
                    <description>Version of DerivedMedia (mostly name of format)</description>
                </attribute>
                <attribute qualifier="size" type="java.lang.Long" redeclare="true">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <description>Size of referenced data file.</description>
                </attribute>
                <attribute qualifier="dataPK" type="java.lang.Long" redeclare="true">
                    <modifiers read="true" initial="true" write="true" search="true" optional="false"/>
                    <persistence type="property"/>
                    <description>PK of the referenced data file.</description>
                </attribute>
            </attributes>
            <indexes>
                <index name="version_idx" unique="true">
                    <key attribute="media"/>
                    <key attribute="version"/>
                </index>
            </indexes>
        </itemtype>

        <itemtype code="Media"
                  extends="AbstractMedia"
                  jaloclass="de.hybris.platform.jalo.media.Media"
                  autocreate="true"
                  generate="true">
            <deployment table="Medias" typecode="30" propertytable="MediaProps"/>
            <attributes>
                <attribute autocreate="true" qualifier="code" type="java.lang.String" generate="true">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="false" initial="true" unique="true"/>
                    <description>Code of media</description>
                    <custom-properties>
                        <property name="hmcIndexField">
                            <value>"thefield"</value>
                        </property>
                    </custom-properties>
                </attribute>
                <attribute qualifier="internalURL" type="java.lang.String">
                    <custom-properties>
                        <property name="hiddenForUI">
                            <value>Boolean.TRUE</value>
                        </property>
                    </custom-properties>
                    <modifiers read="true" write="true" search="false" optional="true"/>
                    <persistence type="property">
                        <columntype>
                            <value>HYBRIS.LONG_STRING</value>
                        </columntype>
                    </persistence>
                </attribute>
                <attribute qualifier="URL" type="java.lang.String">
                    <modifiers read="true" write="true" search="false" optional="true"/>
                    <persistence type="dynamic"/>
                    <model>
                        <getter name="url" deprecated="true" deprecatedSince="ages"/>
                        <setter name="url" deprecated="true" deprecatedSince="ages"/>
                    </model>
                </attribute>
                <attribute autocreate="true" qualifier="URL2" type="java.lang.String" generate="false">
                    <custom-properties>
                        <property name="modelPrefetchMode">
                            <value>java.lang.Boolean.FALSE</value>
                        </property>
                    </custom-properties>
                    <persistence type="jalo" qualifier="URL2"/>
                    <modifiers read="true" write="false" search="false" optional="true"/>
                    <model>
                        <getter name="url2" deprecated="true" deprecatedSince="ages"/>
                        <setter name="url2" deprecated="true" deprecatedSince="ages"/>
                    </model>
                </attribute>
                <attribute type="java.lang.String" qualifier="downloadURL">
                    <persistence type="dynamic" attributeHandler="mediaDownloadUrlHandler"/>
                    <modifiers read="true" write="false" removable="true" search="false" optional="true"/>
                    <model>
                        <getter name="downloadurl" deprecated="true" deprecatedSince="ages"/>
                        <setter name="downloadurl" deprecated="true" deprecatedSince="ages"/>
                    </model>
                </attribute>
                <attribute autocreate="true" qualifier="description" type="java.lang.String">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" removable="true" search="true" optional="true"/>
                    <custom-properties>
                        <property name="hmcIndexField">
                            <value>"thefield"</value>
                        </property>
                    </custom-properties>
                </attribute>
                <attribute autocreate="true" qualifier="altText" type="java.lang.String">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true" removable="true" initial="false"
                               unique="false"
                               private="false" partof="false"/>
                    <custom-properties>
                        <property name="hmcIndexField">
                            <value>"thefield"</value>
                        </property>
                    </custom-properties>
                    <model>
                        <getter name="alttext" deprecatedSince="ages"/>
                        <setter name="alttext" deprecatedSince="ages"/>
                    </model>
                </attribute>
                <attribute qualifier="removable" type="java.lang.Boolean">
                    <defaultvalue>Boolean.TRUE</defaultvalue>
                    <modifiers read="true" write="true" initial="false" optional="false" private="false"/>
                    <persistence type="property"/>
                    <model>
                        <getter default="true" name="removable">
                            <nullDecorator>Boolean.valueOf(true)</nullDecorator>
                        </getter>
                    </model>
                </attribute>
                <attribute type="MediaFormat" qualifier="mediaFormat">
                    <modifiers read="true" optional="true"/>
                    <persistence type="property"/>
                    <description>Format of this media</description>
                </attribute>
                <attribute qualifier="folder" type="MediaFolder">
                    <modifiers read="true" initial="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                    <description>Sub folder where this media is stored.</description>
                </attribute>
                <attribute qualifier="subFolderPath" type="java.lang.String">
                    <modifiers read="true" write="true" search="false" optional="true"/>
                    <persistence type="property"/>
                    <description>Generated location (by one of Storage Strategies) to media within storage.
                    </description>
                </attribute>
                <attribute qualifier="foreignDataOwners" type="MediaCollection">
                    <modifiers read="true" write="false" search="true" optional="true"/>
                    <persistence type="dynamic" attributeHandler="mediaForeignDataOwnerHandler"/>
                    <description>List of all medias referencing same data file.</description>
                </attribute>
                <attribute type="PrincipalCollection" qualifier="permittedPrincipals">
                    <persistence type="dynamic" attributeHandler="mediaPermittedPrincipalsHandler"/>
                    <modifiers read="true" write="true" optional="true" unique="false"/>
                </attribute>
                <attribute type="PrincipalCollection" qualifier="deniedPrincipals">
                    <persistence type="dynamic" attributeHandler="mediaDeniedPrincipalsHandler"/>
                    <modifiers read="true" write="true" optional="true" unique="false"/>
                </attribute>
            </attributes>
            <indexes>
                <index name="Media_Code">
                    <key attribute="code"/>
                </index>
            </indexes>
        </itemtype>

        <!-- product ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->

        <itemtype code="Product"
                  extends="GenericItem"
                  jaloclass="de.hybris.platform.jalo.product.Product"
                  autocreate="true"
                  generate="true">
            <deployment table="Products" typecode="1" propertytable="ProductProps"/>
            <attributes>
                <attribute autocreate="true" qualifier="code" type="java.lang.String" generate="true">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" initial="true" optional="false" unique="true"/>
                    <custom-properties>
                        <property name="hmcIndexField">
                            <value>"thefield"</value>
                        </property>
                    </custom-properties>
                </attribute>
                <attribute autocreate="true" qualifier="name" type="localized:java.lang.String">
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                    <custom-properties>
                        <property name="hmcIndexField">
                            <value>"thefield"</value>
                        </property>
                    </custom-properties>
                </attribute>
                <attribute autocreate="true" qualifier="unit" type="Unit" generate="true">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                </attribute>
                <attribute autocreate="true" qualifier="description" type="localized:java.lang.String">
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property">
                        <columntype database="oracle">
                            <value>CLOB</value>
                        </columntype>
                        <columntype database="sap">
                            <value>NCLOB</value>
                        </columntype>
                        <columntype database="mysql">
                            <value>MEDIUMTEXT</value>
                        </columntype>
                        <columntype>
                            <value>HYBRIS.LONG_STRING</value>
                        </columntype>
                    </persistence>
                    <custom-properties>
                        <property name="hmcIndexField">
                            <value>"thefield"</value>
                        </property>
                    </custom-properties>
                </attribute>
                <attribute autocreate="true" qualifier="thumbnail" type="Media">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                </attribute>
                <attribute autocreate="true" qualifier="picture" type="Media">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                </attribute>
            </attributes>
            <indexes>
                <index name="Product_Code">
                    <key attribute="code"/>
                </index>
            </indexes>
        </itemtype>

        <itemtype code="Unit"
                  extends="GenericItem"
                  jaloclass="de.hybris.platform.jalo.product.Unit"
                  autocreate="true"
                  generate="true">
            <deployment table="Units" typecode="10"/>
            <attributes>
                <attribute autocreate="true" qualifier="code" type="java.lang.String" generate="true">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" initial="true" optional="false" unique="true"/>
                    <custom-properties>
                        <property name="hmcIndexField">
                            <value>"thefield"</value>
                        </property>
                    </custom-properties>
                </attribute>
                <attribute autocreate="true" qualifier="conversion" type="java.lang.Double" generate="true">
                    <defaultvalue>java.lang.Double.valueOf(1)</defaultvalue>
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                </attribute>
                <attribute autocreate="true" qualifier="name" type="localized:java.lang.String">
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                    <custom-properties>
                        <property name="hmcIndexField">
                            <value>"thefield"</value>
                        </property>
                    </custom-properties>
                </attribute>
                <attribute autocreate="true" qualifier="unitType" type="java.lang.String" generate="true">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="false"/>
                    <custom-properties>
                        <property name="hmcIndexField">
                            <value>"thefield"</value>
                        </property>
                    </custom-properties>
                </attribute>
            </attributes>
        </itemtype>

        <!-- test ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->

        <itemtype code="TestEmployee"
                  extends="Employee"
                  jaloclass="de.hybris.platform.jalo.test.TestEmployee"
                  autocreate="true"
                  generate="false"/>

        <itemtype code="TestUserGroup"
                  extends="UserGroup"
                  jaloclass="de.hybris.platform.jalo.test.TestUserGroup"
                  autocreate="true"
                  generate="false"/>

        <itemtype code="TestItem"
                  extends="LocalizableItem"
                  jaloclass="de.hybris.platform.jalo.test.TestItem"
                  deployment="de.hybris.platform.persistence.test.TestItem"
                  autocreate="true"
                  generate="false">
            <attributes>
                <attribute autocreate="true" qualifier="a" type="java.lang.String">
                    <persistence type="cmp" qualifier="fieldA"/>
                    <modifiers read="true" write="true" search="false" optional="true"/>
                </attribute>
                <attribute autocreate="true" qualifier="b" type="java.lang.String">
                    <persistence type="cmp" qualifier="fieldB"/>
                    <modifiers read="true" write="true" search="false" optional="true"/>
                </attribute>
                <attribute autocreate="true" qualifier="boolean" type="java.lang.Boolean">
                    <persistence type="cmp" qualifier="fieldBoolean"/>
                    <modifiers read="true" write="true" search="false" optional="true"/>
                </attribute>
                <attribute autocreate="true" qualifier="byte" type="java.lang.Byte">
                    <persistence type="cmp" qualifier="fieldByte"/>
                    <modifiers read="true" write="true" search="false" optional="true"/>
                </attribute>
                <attribute autocreate="true" qualifier="character" type="java.lang.Character">
                    <persistence type="cmp" qualifier="fieldCharacter"/>
                    <modifiers read="true" write="true" search="false" optional="true"/>
                </attribute>
                <attribute autocreate="true" qualifier="date" type="java.util.Date">
                    <persistence type="cmp" qualifier="fieldDate"/>
                    <modifiers read="true" write="true" search="false" optional="true"/>
                </attribute>
                <attribute autocreate="true" qualifier="double" type="java.lang.Double">
                    <persistence type="cmp" qualifier="fieldDouble"/>
                    <modifiers read="true" write="true" search="false" optional="true"/>
                </attribute>
                <attribute autocreate="true" qualifier="float" type="java.lang.Float">
                    <persistence type="cmp" qualifier="fieldFloat"/>
                    <modifiers read="true" write="true" search="false" optional="true"/>
                </attribute>
                <attribute autocreate="true" qualifier="integer" type="java.lang.Integer">
                    <persistence type="cmp" qualifier="fieldInteger"/>
                    <modifiers read="true" write="true" search="false" optional="true"/>
                </attribute>
                <attribute autocreate="true" qualifier="long" type="java.lang.Long">
                    <persistence type="cmp" qualifier="fieldLong"/>
                    <modifiers read="true" write="true" search="false" optional="true"/>
                </attribute>
                <attribute autocreate="true" qualifier="primitiveBoolean" type="java.lang.Boolean">
                    <persistence type="cmp" qualifier="fieldPrimitiveBoolean"/>
                    <modifiers read="true" write="true" search="false" optional="true"/>
                </attribute>
                <attribute autocreate="true" qualifier="primitiveByte" type="java.lang.Byte">
                    <persistence type="cmp" qualifier="fieldPrimitiveByte"/>
                    <modifiers read="true" write="true" search="false" optional="true"/>
                </attribute>
                <attribute autocreate="true" qualifier="primitiveChar" type="java.lang.Character">
                    <persistence type="cmp" qualifier="fieldPrimitiveChar"/>
                    <modifiers read="true" write="true" search="false" optional="true"/>
                </attribute>
                <attribute autocreate="true" qualifier="primitiveDouble" type="java.lang.Double">
                    <persistence type="cmp" qualifier="fieldPrimitiveDouble"/>
                    <modifiers read="true" write="true" search="false" optional="true"/>
                </attribute>
                <attribute autocreate="true" qualifier="primitiveFloat" type="java.lang.Float">
                    <persistence type="cmp" qualifier="fieldPrimitiveFloat"/>
                    <modifiers read="true" write="true" search="false" optional="true"/>
                </attribute>
                <attribute autocreate="true" qualifier="primitiveInteger" type="java.lang.Integer">
                    <persistence type="cmp" qualifier="fieldPrimitiveInteger"/>
                    <modifiers read="true" write="true" search="false" optional="true"/>
                </attribute>
                <attribute autocreate="true" qualifier="primitiveLong" type="java.lang.Long">
                    <defaultvalue>
                        Long.valueOf(12345)
                    </defaultvalue>
                    <persistence type="cmp" qualifier="fieldPrimitiveLong"/>
                    <modifiers read="true" write="true" search="false" optional="true"/>
                </attribute>
                <attribute autocreate="true" qualifier="primitiveShort" type="java.lang.Short">
                    <persistence type="cmp" qualifier="fieldPrimitiveShort"/>
                    <modifiers read="true" write="true" search="false" optional="true"/>
                </attribute>
                <attribute autocreate="true" qualifier="serializable" type="java.lang.Object">
                    <persistence type="cmp" qualifier="fieldSerializable"/>
                    <modifiers read="true" write="true" search="false" optional="true"/>
                </attribute>
                <attribute autocreate="true" qualifier="string" type="java.lang.String">
                    <defaultvalue>
                        "Blah"
                    </defaultvalue>
                    <persistence type="cmp" qualifier="fieldString"/>
                    <modifiers read="true" write="true" search="false" optional="true"/>
                </attribute>
                <attribute autocreate="true" qualifier="longString" type="java.lang.String">
                    <defaultvalue>
                        "LongBlah"
                    </defaultvalue>
                    <persistence type="cmp" qualifier="fieldLongString"/>
                    <modifiers read="true" write="true" search="false" optional="true"/>
                </attribute>
                <attribute autocreate="true" qualifier="testProperty0" type="java.lang.String"
                           metatype="ConfigAttributeDescriptor">
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                    <custom-properties>
                        <property name="externalQualifier">
                            <value>"a.b.c"</value>
                        </property>
                        <property name="storeInDatabase">
                            <value>Boolean.TRUE</value>
                        </property>
                    </custom-properties>
                </attribute>
                <attribute autocreate="true" qualifier="testDumpProperty" type="java.lang.String">
                    <modifiers read="true" write="true" search="true" optional="true" dontOptimize="true"/>
                    <persistence type="property"/>
                </attribute>
            </attributes>
        </itemtype>

        <itemtype code="TestItemType2"
                  extends="TestItem"
                  jaloclass="de.hybris.platform.jalo.test.TestItem"
                  autocreate="true"
                  generate="false">
            <attributes>
                <attribute autocreate="true" qualifier="testProperty1" type="java.lang.Integer">
                    <modifiers read="true" write="true" search="false" optional="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute autocreate="true" qualifier="testProperty2" type="localized:java.lang.String">
                    <modifiers read="true" write="true" search="false" optional="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute autocreate="true" qualifier="foo" type="java.lang.String">
                    <modifiers read="true" write="true" search="false" optional="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute autocreate="true" qualifier="bar" type="java.lang.String">
                    <modifiers read="true" write="true" search="false" optional="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute type="java.lang.String" qualifier="fooBar">
                    <persistence type="dynamic" attributeHandler="dynamicAttributesStringSampleBean"/>
                    <modifiers read="true" write="true" optional="true" unique="false"/>
                </attribute>
                <attribute type="int" qualifier="intBar">
                    <persistence type="dynamic" attributeHandler="dynamicAttributesIntSampleBean"/>
                    <modifiers read="true" write="true" optional="true" unique="false"/>
                </attribute>
                <attribute type="Gender" qualifier="gender">
                    <persistence type="dynamic" attributeHandler="dynamicAttributesEnumSampleBean"/>
                    <modifiers read="true" write="false" optional="true" unique="false"/>
                </attribute>
                <attribute type="localized:java.lang.String" qualifier="localizedFooBar">
                    <persistence type="dynamic" attributeHandler="dynamicLocalizedAttributesStringSampleBean"/>
                    <modifiers read="true" write="true" optional="true" unique="false"/>
                </attribute>
            </attributes>
        </itemtype>

        <itemtype code="TestItemType3" autocreate="true" generate="true" extends="TestItemType2"
                  jaloclass="de.hybris.platform.jalo.test.TestItem3">
            <attributes>
                <attribute type="java.lang.String" qualifier="xxx">
                    <modifiers read="true" write="true" search="false" optional="true"/>
                    <persistence type="property"/>
                </attribute>
                <!-- make sure handlers can be 'redeclared' if required -->
                <attribute type="java.lang.String" qualifier="fooBar" redeclare="true">
                    <persistence type="dynamic" attributeHandler="redeclaredFooBarAttributeHandler"/>
                    <modifiers read="true" write="false" optional="true" unique="false"/>
                </attribute>
                <!-- make sure handlers are 'inherited' otherwise -->
                <attribute type="int" qualifier="intBar" redeclare="true">
                    <modifiers read="true" write="false" optional="true" unique="false"/>
                </attribute>
                <attribute type="java.lang.String" qualifier="prop">
                    <modifiers read="true" write="true" search="false" optional="true" dontOptimize="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute autocreate="true" qualifier="prop2" type="localized:java.lang.String">
                    <modifiers read="true" write="true" search="false" optional="true" dontOptimize="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute autocreate="true" qualifier="prop3" type="java.lang.String">
                    <modifiers read="true" write="true" search="false" optional="true" dontOptimize="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="itemTypeTwo" type="TestItemType2">
                    <persistence type="property"/>
                    <modifiers optional="false"/>
                </attribute>
                <attribute qualifier="itemsTypeTwo" type="TestItemsTypeTwoCollection">
                    <persistence type="property"/>
                    <modifiers optional="false"/>
                </attribute>
            </attributes>
        </itemtype>

        <!-- generic ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->

        <itemtype code="GenericTestItem"
                  deployment="de.hybris.platform.persistence.GenericTestItem"
                  extends="GenericItem"
                  jaloclass="de.hybris.platform.jalo.GenericItem"
                  autocreate="true"
                  generate="false">
        </itemtype>

        <itemtype code="SavedQuery"
                  extends="GenericItem"
                  jaloclass="de.hybris.platform.jalo.flexiblesearch.SavedQuery"
                  autocreate="true"
                  generate="true">
            <deployment table="SavedQueries" typecode="150"/>
            <attributes>
                <attribute autocreate="true" qualifier="code" type="java.lang.String">
                    <modifiers read="true" write="true" search="true" optional="false" removable="true" unique="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute autocreate="true" qualifier="name" type="localized:java.lang.String">
                    <modifiers read="true" write="true" search="true" removable="true" optional="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute autocreate="true" qualifier="description" type="localized:java.lang.String">
                    <modifiers read="true" write="true" search="true" removable="true" optional="true"/>
                    <persistence type="property"/>
                </attribute>
                <!-- deprecated use 'parameterMap' instead -->
                <attribute autocreate="true" qualifier="paramtypes" type="TypeCollection">
                    <modifiers read="true" write="true" search="false" removable="false" optional="true"
                               private="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute autocreate="true" qualifier="params" type="ParamTypeMap">
                    <modifiers read="true" write="true" search="false" removable="false" optional="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute autocreate="true" qualifier="query" type="java.lang.String">
                    <modifiers read="true" write="true" search="true" removable="true" optional="false"/>
                    <persistence type="property">
                        <columntype>
                            <value>HYBRIS.LONG_STRING</value>
                        </columntype>
                    </persistence>
                </attribute>
                <attribute autocreate="true" qualifier="resultType" type="ComposedType">
                    <modifiers read="true" write="true" search="true" removable="true" optional="false"/>
                    <persistence type="property"/>
                </attribute>
            </attributes>
        </itemtype>

        <itemtype code="SearchRestriction"
                  jaloclass="de.hybris.platform.jalo.type.SearchRestriction"
                  extends="TypeManagerManaged"
                  deployment="de.hybris.platform.persistence.type.SearchRestriction"
                  generate="false"
                  autocreate="true">
            <attributes>
                <attribute autocreate="true" qualifier="code" type="java.lang.String">
                    <modifiers read="true" write="true" search="true" optional="false" unique="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute autocreate="true" qualifier="active" type="java.lang.Boolean">
                    <modifiers read="true" write="true" search="true" optional="false"/>
                    <defaultvalue>java.lang.Boolean.TRUE</defaultvalue>
                    <persistence type="property"/>
                </attribute>
                <!-- replaced by Principal2SearchRestrictionRelation relation, but because of CMP it is defined here too -->
                <attribute autocreate="true" qualifier="principal" type="Principal" generate="false">
                    <modifiers read="true" write="true" search="true" optional="false" initial="true" unique="true"/>
                    <persistence type="cmp" qualifier="principalPK"/>
                </attribute>
                <attribute autocreate="true" qualifier="query" type="java.lang.String">
                    <modifiers read="true" write="true" search="true" optional="false"/>
                    <persistence type="cmp" qualifier="query"/>
                </attribute>
                <attribute autocreate="true" qualifier="restrictedType" type="ComposedType">
                    <modifiers read="true" write="true" search="true" optional="false" initial="true" unique="true"/>
                    <persistence type="cmp" qualifier="restrictedTypePK"/>
                </attribute>
            </attributes>
        </itemtype>

        <!-- ConfigItem ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->

        <itemtype code="ConfigProxyItem" metatype="ConfigProxyMetaType"
                  extends="Item"
                  jaloclass="de.hybris.platform.jalo.config.ConfigProxyItem"
                  singleton="true"
                  jaloonly="true"
                  autocreate="true"
                  generate="false">
            <custom-properties>
                <property name="legacyPersistence">
                    <value>java.lang.Boolean.TRUE</value>
                </property>
            </custom-properties>
            <attributes>

                <!-- redeclare not optional item attributes to be optional -->
                <attribute redeclare="true" autocreate="true" qualifier="creationtime" type="java.util.Date">
                    <persistence type="cmp" qualifier="creationTimestampInternal"/>
                    <modifiers read="true" write="false" search="true" optional="true"/>
                </attribute>
                <attribute redeclare="true" autocreate="true" qualifier="modifiedtime" type="java.util.Date">
                    <persistence type="cmp" qualifier="modifiedTimestampInternal"/>
                    <modifiers read="true" write="false" search="true" optional="true"/>
                </attribute>
                <attribute redeclare="true" autocreate="true" qualifier="pk" type="de.hybris.platform.core.PK">
                    <persistence type="cmp" qualifier="pkString"/>
                    <modifiers read="true" write="false" search="true" optional="true"/>
                </attribute>

            </attributes>
            <model generate="false"/>
        </itemtype>

        <!-- MediaContainer stuff PLA-5950 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->

        <itemtype code="MediaContainer"
                  jaloclass="de.hybris.platform.jalo.media.MediaContainer"
                  extends="GenericItem"
                  autocreate="true"
                  generate="true">
            <deployment table="MediaContainer" typecode="50"/>
            <attributes>
                <attribute type="java.lang.String" qualifier="qualifier">
                    <modifiers read="true" initial="true" write="true" optional="false" unique="true"/>
                    <persistence type="property"/>
                    <description>Qualifying name of this container</description>
                </attribute>
                <attribute type="localized:java.lang.String" qualifier="name">
                    <modifiers read="true" initial="true" write="true" optional="true"/>
                    <persistence type="property"/>
                    <description>Name of this container</description>
                </attribute>
            </attributes>
        </itemtype>

        <itemtype code="MediaFormat"
                  jaloclass="de.hybris.platform.jalo.media.MediaFormat"
                  extends="GenericItem"
                  autocreate="true"
                  generate="true">
            <deployment table="MediaFormat" typecode="51"/>
            <attributes>
                <attribute type="java.lang.String" qualifier="qualifier">
                    <modifiers read="true" initial="true" write="true" optional="false" unique="true"/>
                    <persistence type="property"/>
                    <description>Qualifying name of this format</description>
                </attribute>
                <attribute type="localized:java.lang.String" qualifier="name">
                    <modifiers read="true" initial="true" write="true" optional="true"/>
                    <persistence type="property"/>
                    <description>Name of this format</description>
                </attribute>
                <attribute qualifier="externalID" type="java.lang.String">
                    <description>external identifier refering to external objects (e.g. inside MAM systems)
                    </description>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                </attribute>
            </attributes>
            <indexes>
                <index name="qualifierIDX" unique="true">
                    <key attribute="qualifier"/>
                </index>
            </indexes>
        </itemtype>

        <itemtype code="MediaContext"
                  jaloclass="de.hybris.platform.jalo.media.MediaContext"
                  extends="GenericItem"
                  autocreate="true"
                  generate="true">
            <deployment table="MediaContext" typecode="52"/>
            <attributes>
                <attribute type="java.lang.String" qualifier="qualifier">
                    <modifiers read="true" initial="true" write="true" optional="false" unique="true"/>
                    <persistence type="property"/>
                    <description>Qualifying name of this context</description>
                </attribute>
                <attribute type="localized:java.lang.String" qualifier="name">
                    <modifiers read="true" initial="true" write="true" optional="true"/>
                    <persistence type="property"/>
                    <description>Name of this context</description>
                </attribute>
            </attributes>
            <indexes>
                <index name="qualifierIDX" unique="true">
                    <key attribute="qualifier"/>
                </index>
            </indexes>
        </itemtype>

        <itemtype code="MediaFormatMapping"
                  jaloclass="de.hybris.platform.jalo.media.MediaFormatMapping"
                  extends="GenericItem"
                  autocreate="true"
                  generate="true">
            <deployment table="MediaFormatMapping" typecode="53"/>
            <attributes>
                <attribute type="MediaFormat" qualifier="source">
                    <modifiers read="true" initial="true" write="false" optional="false" unique="true"/>
                    <persistence type="property"/>
                    <description>Source format</description>
                </attribute>
                <attribute type="MediaFormat" qualifier="target">
                    <modifiers read="true" initial="true" write="false" optional="false" unique="true"/>
                    <persistence type="property"/>
                    <description>Target format</description>
                </attribute>
            </attributes>
        </itemtype>

        <itemtype code="MediaFolder"
                  autocreate="true"
                  generate="true"
                  jaloclass="de.hybris.platform.jalo.media.MediaFolder"
                  extends="GenericItem">
            <deployment table="MediaFolders" typecode="54"/>
            <attributes>
                <attribute type="java.lang.String" qualifier="qualifier">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" optional="false" unique="true"/>
                    <custom-properties>
                        <property name="hmcIndexField">
                            <value>"thefield"</value>
                        </property>
                    </custom-properties>
                    <description>Identifies the folder by a qualifier</description>
                </attribute>
                <attribute type="java.lang.String" qualifier="path">
                    <persistence type="property"/>
                    <modifiers read="true" write="false" initial="true" optional="true"/>
                    <custom-properties>
                        <property name="hmcIndexField">
                            <value>"thefield"</value>
                        </property>
                    </custom-properties>
                    <description>The physical path of the folder relative the the media webroot</description>
                </attribute>
            </attributes>
            <indexes>
                <index name="qualifierIdx" unique="true">
                    <key attribute="qualifier"/>
                </index>
            </indexes>
        </itemtype>

        <itemtype code="InMemoryCart"
                  extends="Cart"
                  jaloclass="de.hybris.platform.servicelayer.internal.jalo.order.InMemoryCart"
                  jaloonly="true"
                  autocreate="true"
                  generate="true">
            <custom-properties>
                <property name="legacyPersistence">
                    <value>java.lang.Boolean.TRUE</value>
                </property>
            </custom-properties>
            <attributes>
                <attribute autocreate="true" redeclare="true" qualifier="entries" type="InMemoryCartEntryCollection">
                    <modifiers read="true" write="true" search="true" removable="true" optional="true" partof="false"/>
                </attribute>
            </attributes>
        </itemtype>

        <itemtype code="InMemoryCartEntry"
                  extends="CartEntry"
                  jaloclass="de.hybris.platform.servicelayer.internal.jalo.order.InMemoryCartEntry"
                  jaloonly="true"
                  autocreate="true"
                  generate="true">
            <custom-properties>
                <property name="legacyPersistence">
                    <value>java.lang.Boolean.TRUE</value>
                </property>
            </custom-properties>
            <attributes>
                <attribute autocreate="true" redeclare="true" qualifier="order" type="InMemoryCart">
                    <modifiers read="true" write="false" search="true" removable="true" optional="false" initial="true"
                               unique="true"/>
                </attribute>
            </attributes>
        </itemtype>

        <!-- hmc legacy - Saved Values -->
        <itemtype code="SavedValues"
                  generate="true"
                  jaloclass="de.hybris.platform.hmc.jalo.SavedValues"
                  extends="GenericItem"
                  autocreate="true">
            <deployment table="SavedValues" typecode="334"/>
            <attributes>
                <!-- replaced by 1-n relation !
         <attribute qualifier="modifiedItem" type="Item">
          <description>the modified item</description>
          <modifiers read="true" write="true" search="true" optional="true" />
          <persistence type="property" />
         </attribute>
         -->
                <attribute qualifier="modifiedItemType" type="ComposedType">
                    <description>Type</description>
                    <modifiers read="true" write="true" search="true" optional="false"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="modifiedItemDisplayString" type="java.lang.String">
                    <description>Display String. @since 2.10</description>
                    <modifiers read="true" write="true" search="true" dontOptimize="true" optional="false"/>
                    <persistence type="property"/>
                </attribute>
                <!-- replaced by 1-n relation !
         <attribute qualifier="savedValuesEntries" type="SavedValuesEntriesCollection" autocreate="true" >
          <modifiers read="true" write="false" search="true" optional="true" />
          <persistence type="jalo" />
         </attribute>
         -->
                <attribute qualifier="timestamp" type="java.util.Date">
                    <description>the timestamp of the last modification</description>
                    <modifiers read="true" write="true" search="true" optional="false"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="user" type="User">
                    <description>the user, who has modified/saved/create this item</description>
                    <modifiers read="true" write="true" search="true" optional="false"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="changedAttributes" type="java.lang.String">
                    <description>jalo generated string of changes attributes</description>
                    <modifiers read="true" write="false" search="true"/>
                    <persistence type="jalo"/>
                </attribute>
                <attribute qualifier="numberOfChangedAttributes" type="java.lang.Integer">
                    <description>jalo generated string of changes attributes</description>
                    <modifiers read="true" write="false" search="true"/>
                    <persistence type="jalo"/>
                </attribute>
                <attribute qualifier="modificationType" type="SavedValueEntryType">
                    <description>the type of the 'modification' action (save,create,remove). @since 2.10</description>
                    <modifiers read="true" write="true" search="true" optional="false"/>
                    <persistence type="property"/>
                </attribute>
            </attributes>
            <indexes>
                <index name="savedvalmoditem" unique="false">
                    <key attribute="modifiedItem"/>
                </index>
            </indexes>
        </itemtype>

        <itemtype code="SavedValueEntry" generate="true"
                  jaloclass="de.hybris.platform.hmc.jalo.SavedValueEntry"
                  extends="GenericItem" autocreate="true">
            <deployment table="SavedValueEntry" typecode="335"/>
            <attributes>
                <!-- replaced by 1-n relation !
         <attribute qualifier="parent" type="SavedValues">
          <description>the parent</description>
          <modifiers read="true" write="true" search="true" optional="false" />
          <persistence type="property" />
         </attribute>
         -->
                <attribute qualifier="modifiedAttribute" type="java.lang.String">
                    <description>the modified attribute</description>
                    <modifiers read="true" write="true" search="true" optional="false"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="OldValueAttributeDescriptor" type="AttributeDescriptor">
                    <description>
                        the old attributedescriptor
                    </description>
                    <modifiers read="true" write="true" search="true" optional="false"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="oldValue" type="java.lang.Object">
                    <description>the old value</description>
                    <modifiers read="true" write="true" search="true" dontOptimize="true" optional="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="newValue"
                           type="java.lang.Object">
                    <description>the new value</description>
                    <modifiers read="true" write="true" search="true" dontOptimize="true" optional="true"/>
                    <persistence type="property"/>
                </attribute>
            </attributes>
        </itemtype>

        <itemtype code="UserProfile"
                  extends="GenericItem"
                  jaloclass="de.hybris.platform.hmc.jalo.UserProfile"
                  autocreate="true"
                  generate="true">
            <deployment table="UserProfiles" typecode="1119"/>
            <attributes>
                <attribute autocreate="true" qualifier="readableLanguages" type="LanguageList"
                           isSelectionOf="allReadableLanguages">
                    <modifiers read="true" write="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute autocreate="true" qualifier="writableLanguages" type="LanguageList"
                           isSelectionOf="allWritableLanguages">
                    <modifiers read="true" write="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute autocreate="true" qualifier="allReadableLanguages" type="LanguageCollection">
                    <modifiers read="true" write="false"/>
                    <persistence type="jalo"/>
                </attribute>
                <attribute autocreate="true" qualifier="allWritableLanguages" type="LanguageCollection">
                    <modifiers read="true" write="false"/>
                    <persistence type="jalo"/>
                </attribute>
                <attribute autocreate="true" qualifier="expandInitial" type="java.lang.Boolean">
                    <modifiers read="true" write="true" optional="false"/>
                    <persistence type="property"/>
                    <defaultvalue>Boolean.FALSE</defaultvalue>
                </attribute>
                <attribute autocreate="true" qualifier="owner" type="Principal" redeclare="true">
                    <persistence type="cmp" qualifier="ownerPkString"/>
                    <modifiers read="true" write="false" search="true" optional="true" private="false" initial="true"/>
                </attribute>
            </attributes>
        </itemtype>

        <itemtype code="IndexTestItem" extends="GenericItem"
                  jaloclass="de.hybris.platform.servicelayer.internal.jalo.IndexTestItem"
                  autocreate="true" generate="true">
            <deployment table="IndexTestItem" typecode="7777"/>
            <attributes>
                <attribute autocreate="true" qualifier="column1" type="java.lang.Short">
                    <modifiers read="true" write="false" search="true"
                               removable="true" optional="false" initial="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute autocreate="true" qualifier="column2" type="java.lang.Short">
                    <modifiers read="true" write="false" search="true"
                               removable="true" optional="false" initial="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute autocreate="true" qualifier="column3" type="java.lang.Short">
                    <modifiers read="true" write="false" search="true"
                               removable="true" optional="false" initial="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute autocreate="true" qualifier="column4" type="java.lang.Short">
                    <modifiers read="true" write="false" search="true"
                               removable="true" optional="false" initial="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute autocreate="true" qualifier="column5" type="java.lang.Short">
                    <modifiers read="true" write="false" search="true"
                               removable="true" optional="false" initial="true"/>
                    <persistence type="property"/>
                </attribute>
            </attributes>
            <indexes>
                <index name="OrderIndex">
                    <key attribute="column3"/>
                    <key attribute="column4"/>
                    <key attribute="column1"/>
                    <key attribute="column2"/>
                    <key attribute="column5"/>
                </index>
            </indexes>
        </itemtype>
        <itemtype code="AbstractDynamicContent" abstract="true"
                  jaloclass="de.hybris.platform.jalo.AbstractDynamicContent">
            <deployment table="DynamicContent" typecode="101"/>
            <attributes>
                <attribute qualifier="code" type="java.lang.String">
                    <persistence type="property"/>
                    <modifiers optional="false" initial="true" write="false"/>
                </attribute>
                <attribute qualifier="active" type="java.lang.Boolean">
                    <persistence type="property"/>
                    <modifiers optional="false" initial="true" write="false"/>
                    <defaultvalue>
                        java.lang.Boolean.TRUE
                    </defaultvalue>
                </attribute>
                <attribute qualifier="checksum" type="java.lang.String">
                    <persistence type="property"/>
                    <modifiers optional="true"/>
                </attribute>
                <attribute qualifier="content" type="java.lang.String">
                    <modifiers optional="false"/>
                    <persistence type="property">
                        <columntype database="oracle">
                            <value>CLOB</value>
                        </columntype>
                        <columntype database="sap">
                            <value>NCLOB</value>
                        </columntype>
                        <columntype>
                            <value>HYBRIS.LONG_STRING</value>
                        </columntype>
                    </persistence>
                </attribute>
                <attribute qualifier="version" type="java.lang.Long">
                    <defaultvalue>0L</defaultvalue>
                    <persistence type="property"/>
                    <modifiers optional="false"/>
                </attribute>
            </attributes>
            <indexes>
                <index name="codeVersionActiveIDX" unique="true">
                    <key attribute="code"/>
                    <key attribute="version"/>
                    <key attribute="active"/>
                </index>
            </indexes>
        </itemtype>

        <itemtype code="BruteForceLoginAttempts"
                  extends="GenericItem"
                  jaloclass="de.hybris.platform.jalo.user.BruteForceLoginAttempts"
                  autocreate="true"
                  generate="true">
            <deployment table="BruteForceLoginAttempts" typecode="9450"/>
            <attributes>
                <attribute autocreate="true" qualifier="uid" type="java.lang.String" generate="true">
                    <modifiers read="true" write="false" search="true" initial="true" optional="false" unique="true"/>
                    <description>User identifier</description>
                    <persistence type="property"/>
                </attribute>
                <attribute autocreate="true" qualifier="attempts" type="java.lang.Integer" generate="true">
                    <persistence type="property"/>
                    <modifiers read="true" initial="true" write="true" search="false" optional="false" unique="false"/>
                </attribute>
            </attributes>
            <indexes>
                <index name="uidX" unique="true">
                    <key attribute="uid"/>
                </index>
            </indexes>
        </itemtype>
        <itemtype code="BruteForceLoginDisabledAudit"
                  extends="AbstractUserAudit"
                  jaloclass="de.hybris.platform.jalo.user.BruteForceLoginDisabledAudit"
                  autocreate="true"
                  generate="true">
            <attributes>
                <attribute autocreate="true" qualifier="failedLogins" type="java.lang.Integer" generate="true">
                    <modifiers read="true" write="false" search="true" initial="true" optional="false" unique="false"/>
                    <description>Number of failed logins</description>
                    <persistence type="property"/>
                </attribute>
            </attributes>
        </itemtype>

        <itemtype code="SystemSetupAudit"
                  jaloclass="de.hybris.platform.jalo.initialization.SystemSetupAudit"
                  autocreate="true"
                  generate="true">
            <deployment table="SystemSetupAudit" typecode="120"/>
            <attributes>
                <attribute qualifier="hash" type="java.lang.String">
                    <persistence type="property"/>
                    <modifiers optional="false" initial="true" write="false" unique="true"/>
                </attribute>
                <attribute qualifier="extensionName" type="java.lang.String">
                    <persistence type="property"/>
                    <modifiers optional="false" initial="true" write="false"/>
                </attribute>
                <attribute qualifier="required" type="boolean">
                    <persistence type="property"/>
                    <modifiers optional="false" initial="true" write="false"/>
                </attribute>
                <attribute qualifier="patch" type="boolean">
                    <persistence type="property"/>
                    <modifiers optional="false" initial="true" write="false"/>
                </attribute>
                <attribute qualifier="user" type="User">
                    <persistence type="property"/>
                    <modifiers optional="false" initial="true" write="false"/>
                </attribute>
                <attribute qualifier="name" type="java.lang.String">
                    <persistence type="property"/>
                    <modifiers optional="false" initial="true" write="false"/>
                </attribute>
                <attribute qualifier="className" type="java.lang.String">
                    <persistence type="property"/>
                    <modifiers optional="false" initial="true" write="false"/>
                </attribute>
                <attribute qualifier="methodName" type="java.lang.String">
                    <persistence type="property"/>
                    <modifiers optional="false" initial="true" write="false"/>
                </attribute>
                <attribute qualifier="description" type="java.lang.String">
                    <persistence type="property"/>
                    <modifiers optional="true"/>
                </attribute>
            </attributes>
            <indexes>
                <index name="codeIdx" unique="true">
                    <key attribute="hash"/>
                </index>
            </indexes>
        </itemtype>


        <itemtype code="StoredHttpSession"
                  jaloclass="de.hybris.platform.jalo.web.StoredHttpSession"
                  autocreate="true"
                  generate="true">
            <deployment table="StoredHttpSessions" typecode="121"/>
            <attributes>
                <attribute qualifier="sessionId" type="java.lang.String">
                    <persistence type="property"/>
                    <modifiers optional="false" initial="true" write="false"/>
                </attribute>
                <attribute qualifier="clusterId" type="java.lang.Integer">
                    <persistence type="property"/>
                    <modifiers optional="false" initial="true" write="false"/>
                </attribute>
                <attribute qualifier="extension" type="java.lang.String">
                    <persistence type="property"/>
                    <modifiers optional="true" initial="true" write="false"/>
                </attribute>
                <attribute qualifier="contextRoot" type="java.lang.String">
                    <persistence type="property"/>
                    <modifiers optional="true" initial="true" write="false"/>
                </attribute>
                <attribute qualifier="serializedSession" type="java.lang.Object">
                    <persistence type="property"/>
                    <modifiers optional="true"/>
                </attribute>
            </attributes>
            <indexes>
                <index name="sessionIdIdx" unique="true">
                    <key attribute="sessionId"/>
                </index>
            </indexes>
        </itemtype>

        <itemtype code="AuditReportConfig" extends="AbstractDynamicContent"
                  jaloclass="de.hybris.platform.core.audit.AuditReportConfig"
                  generate="true"
                  autocreate="true"
                  abstract="false">
        </itemtype>

        <itemtype code="CorsConfigurationProperty"
                  jaloclass="de.hybris.platform.jalo.cors.CorsConfigurationProperty"
                  autocreate="true"
                  generate="true">
            <deployment table="CorsConfigProperty" typecode="800"/>
            <attributes>
                <attribute qualifier="context" type="java.lang.String">
                    <persistence type="property"/>
                    <modifiers optional="false" initial="true" write="false" unique="true"/>
                </attribute>
                <attribute qualifier="key" type="java.lang.String">
                    <persistence type="property"/>
                    <modifiers optional="false" initial="true" write="false" unique="true"/>
                </attribute>
                <attribute qualifier="value" type="java.lang.String">
                    <persistence type="property"/>
                    <modifiers optional="false" initial="false" write="true"/>
                </attribute>
            </attributes>
            <indexes>
                <index name="contextKey" unique="true">
                    <key attribute="context"/>
                    <key attribute="key"/>
                </index>
            </indexes>
        </itemtype>

    </itemtypes>

</items>
